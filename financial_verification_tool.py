#!/usr/bin/env python
# -*- coding:utf-8 -*-
"""
金融标的验证工具
测试能否获取行情数据并记录结果
"""

import tkinter as tk
from tkinter import ttk
import sqlite3
import os
import time
import random
import threading

# 数据库设置
DB_PATH = "financial_data.db"

def init_database():
    """初始化数据库"""
    # 确保数据库目录存在
    db_dir = os.path.dirname(DB_PATH)
    if db_dir and not os.path.exists(db_dir):
        os.makedirs(db_dir, exist_ok=True)
    
    conn = sqlite3.connect(DB_PATH)
    c = conn.cursor()
    
    # 创建金融标的表（带测试状态字段）
    c.execute('''CREATE TABLE IF NOT EXISTS instruments (
                 id INTEGER PRIMARY KEY,
                 name TEXT NOT NULL,
                 symbol TEXT NOT NULL,
                 category TEXT NOT NULL,
                 test_status TEXT DEFAULT '未测试',
                 test_time TEXT)''')
    
    # 添加示例数据
    sample_data = [
        ('贵州茅台', '600519', '股票'),
        ('比特币', 'BTC', '加密货币'),
        ('天弘余额宝', '000198', '基金'),
        ('国债2025', 'GB2025', '债券'),
        ('腾讯控股', '00700', '股票'),
        ('以太坊', 'ETH', '加密货币')
    ]
    
    for data in sample_data:
        c.execute("INSERT OR IGNORE INTO instruments (name, symbol, category) VALUES (?,?,?)", data)
    
    conn.commit()
    conn.close()

import akshare as ak
import ccxt

def test_instrument(symbol, category):
    """实际测试能否获取行情数据"""
    try:
        start_time = time.time()
        
        if category == "股票":
            # 获取A股实时行情数据
            df = ak.stock_zh_a_spot_em()
            if symbol in df["代码"].values:
                result = "✅成功"
            else:
                result = "❌代码不存在"
                
        elif category == "基金":
            # 获取基金实时净值
            df = ak.fund_em_open_fund_daily()
            if symbol in df["基金代码"].values:
                result = "✅成功"
            else:
                result = "❌代码不存在"
                
        elif category == "债券":
            # 获取债券实时行情
            df = ak.bond_zh_hs_daily(symbol=symbol)
            if not df.empty:
                result = "✅成功"
            else:
                result = "❌代码不存在"
                
        elif category == "加密货币":
            # 使用CCXT获取加密货币行情
            exchange = ccxt.binance()
            try:
                ticker = exchange.fetch_ticker(f"{symbol}/USDT")
                if ticker["last"]:
                    result = "✅成功"
                else:
                    result = "❌无行情数据"
            except ccxt.BadSymbol:
                result = "❌交易对不存在"
                
        else:
            result = "❌不支持类型"
            
        elapsed = time.time() - start_time
        return result, f"{time.strftime('%Y-%m-%d %H:%M:%S')} ({elapsed:.2f}s)"
        
    except Exception as e:
        return f"❌错误: {str(e)}", time.strftime("%Y-%m-%d %H:%M:%S")

class VerificationTool:
    def __init__(self, root):
        self.root = root
        self.root.title("金融标的验证工具")
        self.root.geometry("800x500")
        
        # 初始化数据库
        init_database()
        
        # 创建界面
        self.create_widgets()
        
        # 显示初始数据
        self.show_data()
        
        # 测试线程状态
        self.testing = False
        self.stop_test = False

    def create_widgets(self):
        """创建界面组件"""
        # 控制面板
        control_frame = ttk.Frame(self.root)
        control_frame.pack(fill=tk.X, padx=10, pady=10)
        
        # 测试按钮
        ttk.Button(control_frame, text="开始测试", command=self.start_test).pack(side=tk.LEFT, padx=5)
        ttk.Button(control_frame, text="停止测试", command=self.stop_testing).pack(side=tk.LEFT, padx=5)
        
        # 状态标签
        self.status_var = tk.StringVar(value="就绪 | 选择'开始测试'进行验证")
        ttk.Label(control_frame, textvariable=self.status_var).pack(side=tk.LEFT, padx=10)
        
        # 搜索面板
        search_frame = ttk.Frame(self.root)
        search_frame.pack(fill=tk.X, padx=10, pady=5)
        
        # 搜索输入框
        self.search_var = tk.StringVar()
        ttk.Entry(search_frame, textvariable=self.search_var, width=30).pack(side=tk.LEFT, padx=5)
        
        # 搜索按钮
        ttk.Button(search_frame, text="搜索", command=self.search_data).pack(side=tk.LEFT, padx=5)
        
        # 结果表格
        columns = ("id", "name", "symbol", "category", "test_status", "test_time")
        self.tree = ttk.Treeview(self.root, columns=columns, show="headings")
        
        # 设置列标题
        self.tree.heading("id", text="ID")
        self.tree.heading("name", text="名称")
        self.tree.heading("symbol", text="代码")
        self.tree.heading("category", text="类别")
        self.tree.heading("test_status", text="测试状态")
        self.tree.heading("test_time", text="测试时间")
        
        # 设置列宽
        self.tree.column("id", width=50)
        self.tree.column("name", width=150)
        self.tree.column("symbol", width=80)
        self.tree.column("category", width=80)
        self.tree.column("test_status", width=80)
        self.tree.column("test_time", width=120)
        
        # 添加滚动条
        scrollbar = ttk.Scrollbar(self.root, orient=tk.VERTICAL, command=self.tree.yview)
        self.tree.configure(yscroll=scrollbar.set)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.tree.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)

    def show_data(self, query=None):
        """显示数据"""
        try:
            self.tree.delete(*self.tree.get_children())
            conn = sqlite3.connect(DB_PATH)
            c = conn.cursor()
            
            if query:
                c.execute("SELECT id, name, symbol, category, test_status, test_time FROM instruments WHERE name LIKE ? OR symbol LIKE ?", 
                         (f"%{query}%", f"%{query}%"))
            else:
                c.execute("SELECT id, name, symbol, category, test_status, test_time FROM instruments")
            
            for row in c.fetchall():
                self.tree.insert("", tk.END, values=row)
            
            conn.close()
        except Exception as e:
            print(f"数据库错误: {str(e)}")

    def search_data(self):
        """搜索数据"""
        query = self.search_var.get().strip()
        self.show_data(query)

    def start_test(self):
        """开始测试所有标的"""
        if self.testing:
            self.status_var.set("测试正在进行中...")
            return
            
        self.testing = True
        self.stop_test = False
        self.status_var.set("测试已开始...")
        
        # 在单独线程中运行测试
        threading.Thread(target=self.run_tests, daemon=True).start()

    def stop_testing(self):
        """停止测试"""
        if self.testing:
            self.stop_test = True
            self.status_var.set("停止测试请求已发送...")

    def run_tests(self):
        """执行测试并更新数据库"""
        try:
            conn = sqlite3.connect(DB_PATH)
            c = conn.cursor()
            
            # 获取所有未测试或需要重新测试的标的
            c.execute("SELECT id, symbol, category FROM instruments")
            instruments = c.fetchall()
            
            total = len(instruments)
            for i, (id, symbol, category) in enumerate(instruments):
                if self.stop_test:
                    break
                    
                self.status_var.set(f"测试中: {i+1}/{total} - {symbol} ({category})")
                
                # 执行测试
                status, test_time = test_instrument(symbol, category)
                
                # 更新数据库
                c.execute("UPDATE instruments SET test_status=?, test_time=? WHERE id=?", 
                         (status, test_time, id))
                conn.commit()
                
                # 更新UI
                self.update_tree_item(id, status, test_time)
            
            self.status_var.set(f"测试完成: 共测试 {i+1} 个标的")
            self.testing = False
            
            conn.close()
        except Exception as e:
            self.status_var.set(f"测试出错: {str(e)}")
            self.testing = False

    def update_tree_item(self, id, status, test_time):
        """更新树视图中的项目"""
        for item in self.tree.get_children():
            values = self.tree.item(item, 'values')
            if values and int(values[0]) == id:
                new_values = list(values)
                new_values[4] = status  # test_status
                new_values[5] = test_time  # test_time
                self.tree.item(item, values=new_values)
                self.tree.update()
                break

if __name__ == "__main__":
    root = tk.Tk()
    app = VerificationTool(root)
    root.mainloop()

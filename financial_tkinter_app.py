#!/usr/bin/env python
# -*- coding:utf-8 -*-
"""
金融标的搜索工具 - Tkinter版
使用Python内置GUI库，无需额外安装
"""

import tkinter as tk
from tkinter import ttk
import sqlite3
import random
import os

# 数据库设置
DB_PATH = "financial_data/financial_instruments.db"

def init_database():
    """初始化数据库并确保表结构正确"""
    os.makedirs(os.path.dirname(DB_PATH), exist_ok=True)
    conn = sqlite3.connect(极
[响应被用户中断]
=======
def init

[响应被工具使用结果中断。一次只能使用一个工具，并且应将其放在消息的末尾。]

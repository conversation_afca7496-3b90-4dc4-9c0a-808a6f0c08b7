#!/usr/bin/env python
# -*- coding:utf-8 -*-
"""
金融标的命令行工具 - 纯标准库实现
无需额外依赖，支持数据收集、验证和搜索
"""

import sys
import sqlite3
import argparse
import time
import random

# 数据库设置
DB_PATH = "financial_data/financial_instruments.db"

def init_database():
    """初始化数据库"""
    import os
    os.makedirs(os.path.dirname(DB_PATH), exist_ok=True)
    conn = sqlite3.connect(DB_PATH)
    c = conn.cursor()
    
    # 创建金融标的表
    c.execute('''CREATE TABLE IF NOT EXISTS instruments (
                 id INTEGER PRIMARY KEY,
                 category TEXT NOT NULL,
                 name TEXT NOT NULL,
                 symbol TEXT NOT NULL,
                 source TEXT NOT NULL,
                 verified INTEGER DEFAULT 0)''')  # 0=未验证, 1=已验证
    
    # 创建索引
    c.execute('''CREATE INDEX IF NOT EXISTS idx_name ON instruments (name)''')
    c.execute('''CREATE INDEX IF NOT EXISTS idx_symbol ON instruments (symbol)''')
    c.execute('''CREATE INDEX IF NOT EXISTS idx_category ON instruments (category)''')
    
    # 添加示例数据（如果不存在）
    c.execute("INSERT OR IGNORE INTO instruments (category, name, symbol, source, verified) VALUES (?,?,?,?,?)",
             ('股票', '贵州茅台', '600519', 'akshare', 1))
    c.execute("INSERT OR IGNORE INTO instruments (category, name, symbol, source, verified) VALUES (?,?,?,?,?)",
             ('加密货币', '比特币', 'BTC/USDT', 'ccxt', 1))
    c.execute("INSERT OR IGNORE INTO instruments (category, name, symbol, source, verified) VALUES (?,?,?,?,?)",
             ('基金', '天弘余额宝', '000198', 'akshare', 1))
    
    conn.commit()
    conn.close()
    return True

def show_all_data():
    """显示所有数据"""
    try:
        conn = sqlite3.connect(DB_PATH)
        c = conn.cursor()
        c.execute("SELECT id, category, name, symbol, verified FROM instruments")
        results = c.fetchall()
        conn.close()
        
        if not results:
            print("数据库中没有数据")
            return False
            
        print("\nID | 类别 | 名称 | 代码 | 状态")
        print("-" * 50)
        for row in results:
            status = "✅已验证" if row[4] else "❌未验证"
            print(f"{row[0]} | {row[1]} | {row[2]} | {row[3]} | {status}")
        print(f"\n共找到 {len(results)} 条记录")
        return True
    except Exception as e:
        print(f"获取数据出错: {str(e)}")
        return False

def search_data(query, category=None):
    """搜索数据"""
    try:
        conn = sqlite3.connect(DB_PATH)
        c = conn.cursor()
        
        if category and category.lower() != "all":
            c.execute("SELECT id, category, name, symbol, verified FROM instruments WHERE (name LIKE ? OR symbol LIKE ?) AND category = ?", 
                     (f"%{query}%", f"%{query}%", category))
        else:
            c.execute("SELECT id, category, name, symbol, verified FROM instruments WHERE name LIKE ? OR symbol LIKE ?", 
                     (f"%{query}%", f"%{query}%"))
        
        results = c.fetchall()
        conn.close()
        
        if not results:
            print("未找到匹配的记录")
            return False
            
        print("\nID | 类别 | 名称 | 代码 | 状态")
        print("-" * 50)
        for row in results:
            status = "✅已验证" if row[4] else "❌未验证"
            print(f"{row[0]} | {row[1]} | {row[2]} | {row[3]} | {status}")
        print(f"\n共找到 {len(results)} 条记录")
        return True
    except Exception as e:
        print(f"搜索出错: {str(e)}")
        return False

def fetch_data():
    """模拟获取数据"""
    try:
        # 模拟获取数据
        new_data = [
            ('股票', '腾讯控股', '00700', 'akshare'),
            ('加密货币', '以太坊', 'ETH/USDT', 'ccxt'),
            ('债券', '国债2025', 'GB2025', 'akshare')
        ]
        
        conn = sqlite3.connect(DB_PATH)
        c = conn.cursor()
        
        for item in new_data:
            # 随机设置验证状态
            verified = 1 if random.random() > 0.3 else 0
            c.execute("INSERT OR IGNORE INTO instruments (category, name, symbol, source, verified) VALUES (?,?,?,?,?)",
                     (item[0], item[1], item[2], item[3], verified))
        
        conn.commit()
        conn.close()
        
        print("已添加新数据:")
        for item in new_data:
            print(f"- {item[1]} ({item[2]})")
        return True
    except Exception as e:
        print(f"获取数据出错: {str(e)}")
        return False

if __name__ == "__main__":
    # 确保数据库初始化
    init_database()
    
    parser = argparse.ArgumentParser(description="金融标的命令行工具")
    subparsers = parser.add_subparsers(dest="command", help="可用命令")
    
    # show_all命令
    show_parser = subparsers.add_parser("show_all", help="显示所有数据")
    
    # search命令
    search_parser = subparsers.add_parser("search", help="搜索金融标的")
    search_parser.add_argument("query", help="搜索关键词")
    search_parser.add_argument("-c", "--category", help="按类别筛选", 
                              choices=["stock", "fund", "crypto", "bond", "all"],
                              default="all")
    
    # fetch命令
    fetch_parser = subparsers.add_parser("fetch", help="获取新数据")
    
    args = parser.parse_args()
    
    if args.command == "show_all":
        show_all_data()
    elif args.command == "search":
        search_data(args.query, args.category)
    elif args.command == "fetch":
        fetch_data()
    else:
        parser.print_help()

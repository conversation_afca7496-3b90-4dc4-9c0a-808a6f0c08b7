#!/usr/bin/env python
# -*- coding:utf-8 -*-
"""
增强版数据修复工具：
1. 增加更多港股数据
2. 只保留USDT交易对的加密货币数据
"""

import sqlite3
import pandas as pd
import os
import time
import logging
from pathlib import Path
import akshare as ak

# 配置
DATA_DIR = Path("financial_data")
DB_PATH = DATA_DIR / "financial_database.db"
CSV_PATH = DATA_DIR / "financial_instruments.csv"
BACKUP_CSV = DATA_DIR / "financial_instruments.csv.bak" 

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

def get_db_data():
    """从数据库获取数据"""
    try:
        conn = sqlite3.connect(DB_PATH)
        df = pd.read_sql_query("SELECT * FROM instruments", conn)
        conn.close()
        logging.info(f"从数据库读取了 {len(df)} 条记录")
        return df
    except Exception as e:
        logging.error(f"从数据库读取数据失败: {e}")
        return pd.DataFrame()

def get_more_hk_stocks():
    """获取更多港股数据 - 使用多种方法"""
    logging.info("尝试获取更多港股数据...")
    
    # 收集所有可能的港股数据
    hk_data_frames = []
    
    # 方法1: 使用stock_hk_spot
    try:
        hk_spot = ak.stock_hk_spot()
        if isinstance(hk_spot, pd.DataFrame) and not hk_spot.empty:
            hk_spot['source'] = 'akshare_hk_spot'
            hk_data_frames.append(hk_spot)
            logging.info(f"方法1: 获取到 {len(hk_spot)} 条港股数据")
    except Exception as e:
        logging.warning(f"获取港股数据方法1失败: {e}")
    
    # 方法2: 使用stock_hk_spot_em (东方财富)
    try:
        hk_spot_em = ak.stock_hk_spot_em()
        if isinstance(hk_spot_em, pd.DataFrame) and not hk_spot_em.empty:
            # 标准化列名
            if '代码' in hk_spot_em.columns:
                hk_spot_em = hk_spot_em.rename(columns={'代码': 'symbol', '名称': 'name'})
            hk_spot_em['source'] = 'akshare_hk_spot_em'
            hk_data_frames.append(hk_spot_em)
            logging.info(f"方法2: 获取到 {len(hk_spot_em)} 条港股数据")
    except Exception as e:
        logging.warning(f"获取港股数据方法2失败: {e}")
    
    # 方法3: 使用stock_hk_hist (历史) - 创建常见港股列表
    try:
        # 常见港股代码
        common_hk_stocks = ["00001", "00002", "00003", "00005", "00006", "00011", "00012", "00016", 
                           "00017", "00019", "00027", "00066", "00101", "00175", "00267", "00386", 
                           "00388", "00669", "00688", "00700", "00762", "00823", "00857", "00883", 
                           "00939", "00941", "01038", "01088", "01109", "01299", "01398", "01810", 
                           "01876", "01928", "01997", "02007", "02018", "02020", "02269", "02318", 
                           "02319", "02328", "02388", "02628", "03690", "03968", "03988", "06098", 
                           "06862", "09618", "09633", "09988", "09999"]
        
        hk_stocks_data = []
        for code in common_hk_stocks:
            try:
                # 获取单个股票的信息
                stock_info = ak.stock_hk_hist(symbol=code, period="daily", start_date="20250101", end_date="20250605")
                if isinstance(stock_info, pd.DataFrame) and not stock_info.empty:
                    # 提取最新的价格数据
                    latest = stock_info.iloc[-1]
                    stock_name = ""
                    
                    # 尝试从其他已获取的数据中找到名称
                    for df in hk_data_frames:
                        if 'symbol' in df.columns and 'name' in df.columns:
                            match = df[df['symbol'] == code]
                            if not match.empty:
                                stock_name = match.iloc[0]['name']
                                break
                    
                    # 如果没有找到名称，使用代码作为名称
                    if not stock_name:
                        stock_name = f"港股-{code}"
                    
                    hk_stocks_data.append({
                        'symbol': code,
                        'name': stock_name,
                        'source': 'akshare_hk_hist'
                    })
            except Exception as e:
                logging.debug(f"获取港股 {code} 信息失败: {e}")
                continue
        
        # 创建数据帧
        if hk_stocks_data:
            manual_df = pd.DataFrame(hk_stocks_data)
            hk_data_frames.append(manual_df)
            logging.info(f"方法3: 获取到 {len(manual_df)} 条港股数据")
    except Exception as e:
        logging.warning(f"获取港股数据方法3失败: {e}")
    
    # 合并所有收集的数据
    if not hk_data_frames:
        logging.warning("没有成功获取任何港股数据")
        return pd.DataFrame()
    
    # 合并数据框
    result_df = pd.concat(hk_data_frames, ignore_index=True)
    
    # 删除重复项
    result_df = result_df.drop_duplicates(subset=['symbol'], keep='first')
    
    # 确保有必要的列
    if 'symbol' not in result_df.columns:
        if '代码' in result_df.columns:
            result_df = result_df.rename(columns={'代码': 'symbol'})
        else:
            logging.error("无法找到股票代码列")
            return pd.DataFrame()
    
    if 'name' not in result_df.columns:
        if '名称' in result_df.columns:
            result_df = result_df.rename(columns={'名称': 'name'})
        else:
            # 使用symbol作为name的备选
            result_df['name'] = result_df['symbol'].apply(lambda x: f"港股-{x}")
    
    # 添加分类和交易所信息
    result_df['category'] = '股票/港股'
    result_df['exchange'] = '港交所'
    
    # 确保所有港股代码格式一致 (5位数，前导0)
    result_df['symbol'] = result_df['symbol'].astype(str).str.replace(r'^(\d+)$', lambda x: x.group(0).zfill(5), regex=True)
    
    logging.info(f"共获取到 {len(result_df)} 条去重后的港股数据")
    return result_df

def filter_crypto_for_usdt_pairs(df):
    """只保留USDT交易对的加密货币数据"""
    # 备份原始数据
    crypto_count_before = len(df[df['category'] == '加密货币'])
    logging.info(f"处理前加密货币数据: {crypto_count_before} 条")
    
    # 只保留包含USDT的交易对
    usdt_filter = df['category'] == '加密货币'
    usdt_filter &= df['symbol'].str.contains('USDT', case=False, na=False)
    
    # 获取符合条件的记录
    usdt_pairs = df[usdt_filter].copy()
    
    # 删除所有加密货币数据
    df = df[df['category'] != '加密货币']
    
    # 合并回符合条件的USDT交易对
    result_df = pd.concat([df, usdt_pairs], ignore_index=True)
    
    crypto_count_after = len(result_df[result_df['category'] == '加密货币'])
    logging.info(f"处理后加密货币数据: {crypto_count_after} 条 (只保留USDT交易对)")
    
    return result_df

def merge_hk_data_to_db(db_df, hk_df):
    """合并港股数据到数据库中"""
    if hk_df.empty:
        logging.warning("没有港股数据可合并")
        return db_df
    
    # 获取已有的港股数量
    existing_hk = len(db_df[db_df['category'] == '股票/港股'])
    logging.info(f"合并前已有 {existing_hk} 条港股数据")
    
    # 获取已存在的符号集合
    existing_symbols = set(db_df['symbol'])
    new_records = []
    
    # 处理新的港股数据
    for _, row in hk_df.iterrows():
        symbol = str(row['symbol']).strip()
        
        # 跳过已存在的符号
        if symbol in existing_symbols:
            continue
            
        # 创建新记录
        new_record = {
            'name': row['name'],
            'symbol': symbol,
            'category': '股票/港股',
            'exchange': '港交所',
            'source': row.get('source', 'akshare'),
            'timestamp': time.strftime("%Y-%m-%d %H:%M:%S")
        }
        
        new_records.append(new_record)
        existing_symbols.add(symbol)  # 添加到已存在集合中，避免重复
    
    # 如果有新记录，合并到原始数据
    if new_records:
        # 获取最大ID
        max_id = db_df['id'].max() if 'id' in db_df.columns and not db_df.empty else 0
        
        # 创建新的DataFrame
        new_df = pd.DataFrame(new_records)
        # 添加ID列
        new_df['id'] = range(max_id + 1, max_id + 1 + len(new_df))
        
        # 合并数据
        result_df = pd.concat([db_df, new_df], ignore_index=True)
        logging.info(f"已添加 {len(new_records)} 条新港股数据")
        
        # 计算合并后的港股数量
        total_hk = len(result_df[result_df['category'] == '股票/港股'])
        logging.info(f"合并后共有 {total_hk} 条港股数据")
        
        return result_df
    else:
        logging.info("没有新的港股数据需要添加")
        return db_df

def save_to_database(df):
    """保存数据到数据库"""
    conn = sqlite3.connect(DB_PATH)
    
    # 删除现有数据
    conn.execute("DELETE FROM instruments")
    conn.commit()
    
    # 导入数据
    df.to_sql('instruments', conn, if_exists='replace', index=False)
    conn.commit()
    conn.close()
    logging.info(f"已将 {len(df)} 条记录保存到数据库")

def save_to_csv(df):
    """保存数据到CSV文件"""
    # 备份现有文件
    if os.path.exists(CSV_PATH):
        import shutil
        shutil.copy2(CSV_PATH, BACKUP_CSV)
        logging.info(f"已备份原CSV文件: {BACKUP_CSV}")
    
    # 保存新数据
    df.to_csv(CSV_PATH, index=False, encoding="utf-8-sig")
    logging.info(f"已将 {len(df)} 条记录保存到CSV文件: {CSV_PATH}")

def main():
    """主函数"""
    logging.info("启动增强版数据修复工具...")
    
    # 1. 获取数据库数据
    df = get_db_data()
    if df.empty:
        logging.error("无法获取数据库数据，退出")
        return
    
    # 2. 获取更多港股数据
    logging.info("正在获取更多港股数据...")
    hk_df = get_more_hk_stocks()
    
    # 3. 合并港股数据
    df = merge_hk_data_to_db(df, hk_df)
    
    # 4. 过滤加密货币，只保留USDT交易对
    logging.info("正在过滤加密货币数据，只保留USDT交易对...")
    df = filter_crypto_for_usdt_pairs(df)
    
    # 5. 保存结果
    save_to_database(df)
    save_to_csv(df)
    
    # 6. 显示数据统计
    category_stats = df['category'].value_counts()
    logging.info("\n数据统计:")
    for category, count in category_stats.items():
        logging.info(f"  {category}: {count}条")
    
    logging.info("数据修复完成!")

if __name__ == "__main__":
    main()

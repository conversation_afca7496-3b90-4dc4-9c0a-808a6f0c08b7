#!/usr/bin/env python
# -*- coding:utf-8 -*-
"""
修复金融数据问题：
1. 补充港股数据
2. 修复A股交易所信息
3. 避免重复获取已有数据
"""

import sqlite3
import pandas as pd
import os
import time
import logging
from pathlib import Path
import akshare as ak

# 配置
DATA_DIR = Path("financial_data")
DB_PATH = DATA_DIR / "financial_database.db"
CSV_PATH = DATA_DIR / "financial_instruments.csv"
BACKUP_CSV = DATA_DIR / "financial_instruments.csv.bak" 

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

def backup_csv():
    """备份原始CSV文件"""
    if os.path.exists(CSV_PATH):
        import shutil
        shutil.copy2(CSV_PATH, BACKUP_CSV)
        logging.info(f"已备份原CSV文件: {BACKUP_CSV}")

def get_existing_data():
    """获取现有数据"""
    if os.path.exists(DB_PATH):
        try:
            conn = sqlite3.connect(DB_PATH)
            df = pd.read_sql_query("SELECT * FROM instruments", conn)
            conn.close()
            logging.info(f"从数据库读取了 {len(df)} 条记录")
            return df
        except Exception as e:
            logging.error(f"从数据库读取数据失败: {e}")
    
    if os.path.exists(CSV_PATH):
        try:
            df = pd.read_csv(CSV_PATH)
            logging.info(f"从CSV文件读取了 {len(df)} 条记录")
            return df
        except Exception as e:
            logging.error(f"从CSV读取数据失败: {e}")
    
    logging.error("未找到现有数据源")
    return pd.DataFrame()

def fix_a_stock_exchange(df):
    """修复A股交易所信息"""
    count = 0
    fixed_count = 0
    
    # 创建新的DataFrame副本，以免修改原始数据
    fixed_df = df.copy()
    
    # 遍历每一行
    for idx, row in fixed_df.iterrows():
        # 只处理A股数据
        if "股票/A股" == row['category']:
            count += 1
            symbol = str(row['symbol']).strip()
            
            # 根据代码前缀判断交易所
            if symbol.startswith('sh') or symbol.startswith('60') or symbol.startswith('68'):
                fixed_df.at[idx, 'exchange'] = "上交所"
                fixed_count += 1
            elif symbol.startswith('sz') or symbol.startswith('00') or symbol.startswith('30'):
                fixed_df.at[idx, 'exchange'] = "深交所"
                fixed_count += 1
            elif symbol.startswith('bj') or symbol.startswith('43') or symbol.startswith('83'):
                fixed_df.at[idx, 'exchange'] = "北交所"
                fixed_count += 1
    
    logging.info(f"共处理A股数据 {count} 条，修复交易所信息 {fixed_count} 条")
    return fixed_df

def collect_hk_stock():
    """收集港股数据"""
    try:
        logging.info("尝试获取港股数据...")
        
        try:
            # 尝试使用stock_hk_spot函数
            hk_stock = ak.stock_hk_spot()
            if isinstance(hk_stock, pd.DataFrame) and not hk_stock.empty:
                logging.info(f"使用stock_hk_spot成功获取 {len(hk_stock)} 条港股数据")
                return hk_stock
        except Exception as e1:
            logging.warning(f"使用stock_hk_spot获取港股失败: {e1}")
            
        try:
            # 尝试使用stock_hk_spot_em函数
            hk_stock = ak.stock_hk_spot_em()
            if isinstance(hk_stock, pd.DataFrame) and not hk_stock.empty:
                logging.info(f"使用stock_hk_spot_em成功获取 {len(hk_stock)} 条港股数据")
                return hk_stock
        except Exception as e2:
            logging.warning(f"使用stock_hk_spot_em获取港股失败: {e2}")
        
        # 如果以上方法都失败，返回一些示例港股数据
        logging.warning("港股数据获取失败，使用示例数据")
        return pd.DataFrame({
            "symbol": ["00001", "00002", "00005", "00700", "03690"],
            "name": ["长和", "中电控股", "汇丰控股", "腾讯控股", "美团"],
            "category": ["股票/港股"] * 5,
            "exchange": ["港交所"] * 5
        })
        
    except Exception as e:
        logging.error(f"港股数据收集完全失败: {e}")
        return pd.DataFrame()

def merge_hk_data(df, hk_df):
    """合并港股数据到现有数据集"""
    if hk_df.empty:
        logging.warning("没有港股数据可合并")
        return df
    
    # 准备合适的数据结构
    new_records = []
    existing_symbols = set(df['symbol'])
    added_count = 0
    
    # 处理港股数据
    for _, row in hk_df.iterrows():
        symbol = str(row.get('symbol', '')).strip()
        
        # 跳过已存在的符号
        if symbol in existing_symbols:
            continue
            
        # 获取名称
        if 'name' in row:
            name = row['name']
        elif '股票名称' in row:
            name = row['股票名称']
        elif '名称' in row:
            name = row['名称']
        else:
            name = f"港股-{symbol}"
        
        # 创建记录
        record = {
            'name': name,
            'symbol': symbol,
            'category': '股票/港股',
            'exchange': '港交所',
            'source': 'akshare',
            'timestamp': time.strftime("%Y-%m-%d %H:%M:%S")
        }
        
        new_records.append(record)
        added_count += 1
    
    # 如果有新记录，合并到原始数据
    if new_records:
        # 获取最大ID
        max_id = df['id'].max() if 'id' in df.columns and not df.empty else 0
        
        # 创建新的DataFrame
        new_df = pd.DataFrame(new_records)
        # 添加ID列
        new_df['id'] = range(max_id+1, max_id+1+len(new_df))
        
        # 合并数据
        result_df = pd.concat([df, new_df], ignore_index=True)
        logging.info(f"已添加 {added_count} 条新港股数据")
        return result_df
    else:
        logging.info("没有新的港股数据需要添加")
        return df

def save_to_database(df):
    """保存数据到数据库"""
    conn = sqlite3.connect(DB_PATH)
    c = conn.cursor()
    
    # 删除现有数据
    c.execute("DELETE FROM instruments")
    conn.commit()
    
    # 导入数据
    df.to_sql('instruments', conn, if_exists='replace', index=False)
    conn.commit()
    conn.close()
    logging.info(f"已将 {len(df)} 条记录保存到数据库")

def save_to_csv(df):
    """保存数据到CSV文件"""
    df.to_csv(CSV_PATH, index=False, encoding="utf-8-sig")
    logging.info(f"已将 {len(df)} 条记录保存到CSV文件: {CSV_PATH}")

def main():
    """主函数"""
    logging.info("开始修复金融数据问题...")
    
    # 1. 备份原始文件
    backup_csv()
    
    # 2. 获取现有数据
    df = get_existing_data()
    if df.empty:
        logging.error("无法获取现有数据，退出")
        return
    
    # 3. 修复A股交易所信息
    logging.info("开始修复A股交易所信息...")
    df = fix_a_stock_exchange(df)
    
    # 4. 获取港股数据
    logging.info("开始获取港股数据...")
    hk_df = collect_hk_stock()
    
    # 5. 合并港股数据
    df = merge_hk_data(df, hk_df)
    
    # 6. 保存结果
    save_to_database(df)
    save_to_csv(df)
    
    logging.info("数据修复完成!")
    
    # 7. 显示数据统计
    category_stats = df['category'].value_counts()
    logging.info("\n数据统计:")
    for category, count in category_stats.items():
        logging.info(f"  {category}: {count}条")

if __name__ == "__main__":
    main()

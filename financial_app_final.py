#!/usr/bin/env python
# -*- coding:utf-8 -*-
"""
金融标的查询应用
UI界面程序
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import pandas as pd
import sqlite3
from pathlib import Path
import os

# 配置
DATA_DIR = Path("financial_data")
DB_PATH = DATA_DIR / "financial_database.db"
CSV_PATH = DATA_DIR / "financial_instruments.csv"

class FinancialApp:
    def __init__(self, root):
        self.root = root
        self.root.title("金融标的查询系统")
        self.root.geometry("900x600")
        self.root.resizable(True, True)
        
        # 数据库路径
        self.db_path = DB_PATH
        self.csv_path = CSV_PATH
        
        # 检查数据库是否存在
        if not os.path.exists(self.db_path) and os.path.exists(self.csv_path):
            self.load_from_csv()
        
        # 创建UI组件
        self.create_widgets()
        
        # 初始加载数据
        self.load_data()
    
    def create_widgets(self):
        """创建UI组件"""
        # 顶部区域 - 搜索栏和按钮
        top_frame = ttk.Frame(self.root, padding="10")
        top_frame.pack(fill=tk.X, padx=10, pady=10)
        
        # 标题
        title_label = ttk.Label(top_frame, text="金融标的查询系统", font=("黑体", 16, "bold"))
        title_label.grid(row=0, column=0, columnspan=5, pady=10)
        
        # 搜索条件
        ttk.Label(top_frame, text="搜索:").grid(row=1, column=0, padx=5, pady=10, sticky="e")
        self.search_var = tk.StringVar()
        search_entry = ttk.Entry(top_frame, textvariable=self.search_var, width=30)
        search_entry.grid(row=1, column=1, padx=5, pady=10, sticky="ew")
        search_entry.bind("<Return>", lambda event: self.search_data())
        
        # 搜索类型选择
        ttk.Label(top_frame, text="搜索类型:").grid(row=1, column=2, padx=5, pady=10, sticky="e")
        self.search_type = tk.StringVar(value="全部")
        search_type_combo = ttk.Combobox(top_frame, textvariable=self.search_type, 
                                          values=["全部", "名称", "代码", "类别", "交易所"], width=10)
        search_type_combo.grid(row=1, column=3, padx=5, pady=10)
        
        # 搜索按钮
        search_btn = ttk.Button(top_frame, text="搜索", command=self.search_data)
        search_btn.grid(row=1, column=4, padx=5, pady=10)
        
        # 过滤区域
        filter_frame = ttk.LabelFrame(self.root, text="过滤器", padding="10")
        filter_frame.pack(fill=tk.X, padx=10, pady=5)
        
        # 类别过滤
        ttk.Label(filter_frame, text="类别:").grid(row=0, column=0, padx=5, pady=5, sticky="e")
        self.category_var = tk.StringVar(value="全部")
        self.category_combo = ttk.Combobox(filter_frame, textvariable=self.category_var, width=15)
        self.category_combo.grid(row=0, column=1, padx=5, pady=5)
        self.category_combo.bind("<<ComboboxSelected>>", lambda event: self.apply_filters())
        
        # 交易所过滤
        ttk.Label(filter_frame, text="交易所:").grid(row=0, column=2, padx=5, pady=5, sticky="e")
        self.exchange_var = tk.StringVar(value="全部")
        self.exchange_combo = ttk.Combobox(filter_frame, textvariable=self.exchange_var, width=15)
        self.exchange_combo.grid(row=0, column=3, padx=5, pady=5)
        self.exchange_combo.bind("<<ComboboxSelected>>", lambda event: self.apply_filters())
        
        # 重置过滤器按钮
        reset_btn = ttk.Button(filter_frame, text="重置过滤器", command=self.reset_filters)
        reset_btn.grid(row=0, column=4, padx=5, pady=5)
        
        # 导出按钮
        export_btn = ttk.Button(filter_frame, text="导出结果", command=self.export_results)
        export_btn.grid(row=0, column=5, padx=5, pady=5)
        
        # 数据列表区域 - 使用Treeview显示表格数据
        columns = ("id", "name", "symbol", "category", "exchange", "source", "timestamp")
        self.tree = ttk.Treeview(self.root, columns=columns, show="headings", height=20)
        
        # 定义列宽和标题
        column_width = {
            "id": 50, 
            "name": 200, 
            "symbol": 100, 
            "category": 150, 
            "exchange": 100, 
            "source": 100, 
            "timestamp": 150
        }
        
        column_text = {
            "id": "ID", 
            "name": "名称", 
            "symbol": "代码", 
            "category": "类别", 
            "exchange": "交易所", 
            "source": "数据源", 
            "timestamp": "时间戳"
        }
        
        # 配置每列
        for col in columns:
            self.tree.heading(col, text=column_text[col], command=lambda c=col: self.sort_by(c))
            self.tree.column(col, width=column_width[col], anchor=tk.W)
        
        # 添加滚动条
        scrollbar = ttk.Scrollbar(self.root, orient=tk.VERTICAL, command=self.tree.yview)
        self.tree.configure(yscroll=scrollbar.set)
        
        # 放置Treeview和滚动条
        self.tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=10, pady=10)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y, padx=(0, 10), pady=10)
        
        # 状态栏
        self.status_var = tk.StringVar()
        status_bar = ttk.Label(self.root, textvariable=self.status_var, relief=tk.SUNKEN, anchor=tk.W)
        status_bar.pack(side=tk.BOTTOM, fill=tk.X, padx=10, pady=5)
        
    def load_data(self):
        """初始加载数据"""
        try:
            # 如果数据库不存在，尝试从CSV加载
            if not os.path.exists(self.db_path) and os.path.exists(self.csv_path):
                self.load_from_csv()
                
            # 连接数据库
            conn = sqlite3.connect(self.db_path)
            query = "SELECT * FROM instruments"  # 加载全部数据
            df = pd.read_sql_query(query, conn)
            conn.close()
            
            # 加载分类和交易所选项
            self.update_filter_options(df)
            
            # 清空原有数据
            for item in self.tree.get_children():
                self.tree.delete(item)
            
            # 添加数据到表格
            for _, row in df.iterrows():
                values = [row[col] if col in row else "" for col in ["id", "name", "symbol", "category", "exchange", "source", "timestamp"]]
                self.tree.insert("", tk.END, values=values)
            
            total_records = len(df)
            self.status_var.set(f"已加载 {total_records} 条记录")
            
        except Exception as e:
            messagebox.showerror("错误", f"加载数据失败: {str(e)}")
            self.status_var.set("加载数据失败")
    
    def update_filter_options(self, df):
        """更新过滤器选项"""
        if "category" in df.columns:
            categories = ["全部"] + sorted(df["category"].dropna().unique().tolist())
            self.category_combo["values"] = categories
        
        if "exchange" in df.columns:
            exchanges = ["全部"] + sorted(df["exchange"].dropna().unique().tolist())
            self.exchange_combo["values"] = exchanges
    
    def search_data(self):
        """搜索数据"""
        search_text = self.search_var.get().strip()
        search_type = self.search_type.get()
        
        try:
            conn = sqlite3.connect(self.db_path)
            
            if not search_text:
                # 无搜索文本，应用分类和交易所过滤器
                query = "SELECT * FROM instruments WHERE 1=1"
                params = []
                
                if self.category_var.get() != "全部":
                    query += " AND category LIKE ?"
                    params.append(f"%{self.category_var.get()}%")
                
                if self.exchange_var.get() != "全部":
                    query += " AND exchange LIKE ?"
                    params.append(f"%{self.exchange_var.get()}%")
                    
                df = pd.read_sql_query(query, conn, params=params)
            else:
                # 有搜索文本，根据搜索类型构建查询
                if search_type == "全部":
                    query = "SELECT * FROM instruments WHERE name LIKE ? OR symbol LIKE ? OR category LIKE ? OR exchange LIKE ?"
                    params = (f"%{search_text}%", f"%{search_text}%", f"%{search_text}%", f"%{search_text}%")
                elif search_type == "名称":
                    query = "SELECT * FROM instruments WHERE name LIKE ?"
                    params = (f"%{search_text}%",)
                elif search_type == "代码":
                    query = "SELECT * FROM instruments WHERE symbol LIKE ?"
                    params = (f"%{search_text}%",)
                elif search_type == "类别":
                    query = "SELECT * FROM instruments WHERE category LIKE ?"
                    params = (f"%{search_text}%",)
                elif search_type == "交易所":
                    query = "SELECT * FROM instruments WHERE exchange LIKE ?"
                    params = (f"%{search_text}%",)
                else:
                    query = "SELECT * FROM instruments WHERE name LIKE ?"
                    params = (f"%{search_text}%",)
                
                # 应用分类和交易所过滤器
                if self.category_var.get() != "全部":
                    query += " AND category LIKE ?"
                    params += (f"%{self.category_var.get()}%",)
                
                if self.exchange_var.get() != "全部":
                    query += " AND exchange LIKE ?"
                    params += (f"%{self.exchange_var.get()}%",)
                
                # 执行查询
                df = pd.read_sql_query(query, conn, params=params)
            
            conn.close()
            
            # 清空原有数据
            for item in self.tree.get_children():
                self.tree.delete(item)
            
            # 添加数据到表格
            for _, row in df.iterrows():
                values = [row[col] if col in row else "" for col in ["id", "name", "symbol", "category", "exchange", "source", "timestamp"]]
                self.tree.insert("", tk.END, values=values)
            
            # 更新状态栏
            self.status_var.set(f"搜索结果: {len(df)} 条记录")
            
        except Exception as e:
            messagebox.showerror("错误", f"搜索失败: {str(e)}")
            self.status_var.set("搜索失败")
    
    def apply_filters(self):
        """应用过滤器"""
        category = self.category_var.get()
        exchange = self.exchange_var.get()
        
        try:
            conn = sqlite3.connect(self.db_path)
            query = "SELECT * FROM instruments WHERE 1=1"
            params = []
            
            if category != "全部":
                query += " AND category LIKE ?"
                params.append(f"%{category}%")
            
            if exchange != "全部":
                query += " AND exchange LIKE ?"
                params.append(f"%{exchange}%")
            
            # 添加搜索条件
            search_text = self.search_var.get().strip()
            search_type = self.search_type.get()
            
            if search_text:
                if search_type == "全部":
                    query += " AND (name LIKE ? OR symbol LIKE ? OR category LIKE ? OR exchange LIKE ?)"
                    params.extend([f"%{search_text}%", f"%{search_text}%", f"%{search_text}%", f"%{search_text}%"])
                elif search_type == "名称":
                    query += " AND name LIKE ?"
                    params.append(f"%{search_text}%")
                elif search_type == "代码":
                    query += " AND symbol LIKE ?"
                    params.append(f"%{search_text}%")
                elif search_type == "类别":
                    query += " AND category LIKE ?"
                    params.append(f"%{search_text}%")
                elif search_type == "交易所":
                    query += " AND exchange LIKE ?"
                    params.append(f"%{search_text}%")
            
            df = pd.read_sql_query(query, conn, params=params)
            conn.close()
            
            # 清空原有数据
            for item in self.tree.get_children():
                self.tree.delete(item)
            
            # 添加数据到表格
            for _, row in df.iterrows():
                values = [row[col] if col in row else "" for col in ["id", "name", "symbol", "category", "exchange", "source", "timestamp"]]
                self.tree.insert("", tk.END, values=values)
            
            # 更新状态栏
            self.status_var.set(f"过滤结果: {len(df)} 条记录")
            
        except Exception as e:
            messagebox.showerror("错误", f"应用过滤器失败: {str(e)}")
            self.status_var.set("过滤失败")
    
    def reset_filters(self):
        """重置所有过滤器"""
        self.search_var.set("")
        self.search_type.set("全部")
        self.category_var.set("全部")
        self.exchange_var.set("全部")
        self.load_data()
    
    def sort_by(self, col):
        """按列排序"""
        # 获取所有数据
        data = [(self.tree.set(item, col), item) for item in self.tree.get_children('')]
        
        # 排序数据
        data.sort(reverse=getattr(self, "sort_reverse", False))
        
        # 更新排序方向
        self.sort_reverse = not getattr(self, "sort_reverse", False)
        
        # 重新插入排序后的数据
        for i, (value, item) in enumerate(data):
            self.tree.move(item, '', i)
        
        # 更新状态栏
        self.status_var.set(f"按 {col} {'降序' if self.sort_reverse else '升序'} 排序")
    
    def export_results(self):
        """导出当前结果到CSV文件"""
        try:
            # 获取当前表格内的所有数据
            items = self.tree.get_children()
            if not items:
                messagebox.showinfo("提示", "没有数据可导出")
                return
            
            # 获取保存文件路径
            file_path = filedialog.asksaveasfilename(
                defaultextension=".csv",
                filetypes=[("CSV文件", "*.csv"), ("所有文件", "*.*")],
                title="导出数据"
            )
            
            if not file_path:
                return
            
            # 从表格中提取数据
            data = []
            columns = ["id", "名称", "代码", "类别", "交易所", "数据源", "时间戳"]
            
            for item in items:
                values = self.tree.item(item, "values")
                data.append(values)
            
            # 创建DataFrame并导出
            df = pd.DataFrame(data, columns=columns)
            df.to_csv(file_path, index=False, encoding="utf-8-sig")
            
            messagebox.showinfo("成功", f"数据已成功导出到: {file_path}")
            self.status_var.set(f"已导出 {len(df)} 条记录到 {file_path}")
            
        except Exception as e:
            messagebox.showerror("错误", f"导出失败: {str(e)}")
            self.status_var.set("导出失败")
    
    def load_from_csv(self):
        """从CSV文件加载数据到SQLite数据库"""
        try:
            if not os.path.exists(self.csv_path):
                messagebox.showerror("错误", f"CSV文件不存在: {self.csv_path}")
                return
                
            # 读取CSV文件
            df = pd.read_csv(self.csv_path)
            
            # 创建数据库连接
            conn = sqlite3.connect(self.db_path)
            c = conn.cursor()
            
            # 创建表
            c.execute('''CREATE TABLE IF NOT EXISTS instruments (
                         id INTEGER PRIMARY KEY,
                         name TEXT NOT NULL,
                         symbol TEXT NOT NULL,
                         category TEXT NOT NULL,
                         exchange TEXT,
                         source TEXT NOT NULL,
                         timestamp TEXT)''')
            
            # 导入数据
            df.to_sql('instruments', conn, if_exists='replace', index=False)
            
            conn.commit()
            conn.close()
            
            messagebox.showinfo("成功", "已从CSV文件成功导入数据到数据库")
            
        except Exception as e:
            messagebox.showerror("错误", f"从CSV导入数据失败: {str(e)}")

# 主入口点
if __name__ == "__main__":
    root = tk.Tk()
    app = FinancialApp(root)
    root.mainloop()

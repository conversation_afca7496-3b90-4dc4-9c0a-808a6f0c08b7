#!/usr/bin/env python
# -*- coding:utf-8 -*-
"""
全面金融标的数据库构建工具
整合AKShare和CCXT的所有可用标的
"""

import akshare as ak
import ccxt
import json
import sqlite3
from pathlib import Path
import time
from tqdm import tqdm
import pandas as pd

# 配置
DB_PATH = "financial_database.db"
DATA_DIR = "financial_data"
Path(DATA_DIR).mkdir(exist_ok=True)

def init_database():
    """初始化数据库"""
    conn = sqlite3.connect(DB_PATH)
    c = conn.cursor()
    
    # 创建标的表
    c.execute('''CREATE TABLE极
[响应被工具使用结果中断。一次只能使用一个工具，并且应将其放在消息的末尾。]

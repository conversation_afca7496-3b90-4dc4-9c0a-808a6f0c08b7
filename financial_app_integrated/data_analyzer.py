#!/usr/bin/env python
# -*- coding:utf-8 -*-
"""
数据分析工具
用于分析数据库中的数据情况
"""

import sys
import os
import sqlite3
from pathlib import Path

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from config import DB_PATH

def analyze_database():
    """分析数据库内容"""
    if not DB_PATH.exists():
        print("❌ 数据库文件不存在")
        return
    
    try:
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()
        
        print("=" * 60)
        print("数据库分析报告")
        print("=" * 60)
        
        # 总数据量
        cursor.execute('SELECT COUNT(*) FROM instruments')
        total = cursor.fetchone()[0]
        print(f"\n📊 总数据量: {total:,} 条记录")
        
        # 按分类统计
        print("\n📈 按分类统计:")
        cursor.execute('SELECT category, COUNT(*) FROM instruments GROUP BY category ORDER BY COUNT(*) DESC')
        categories = cursor.fetchall()
        for cat, count in categories:
            percentage = (count / total * 100) if total > 0 else 0
            print(f"  {cat:<15}: {count:>6,} 条 ({percentage:5.1f}%)")
        
        # 按数据源统计
        print("\n🔗 按数据源统计:")
        cursor.execute('SELECT source, COUNT(*) FROM instruments GROUP BY source ORDER BY COUNT(*) DESC')
        sources = cursor.fetchall()
        for src, count in sources:
            percentage = (count / total * 100) if total > 0 else 0
            print(f"  {src:<10}: {count:>6,} 条 ({percentage:5.1f}%)")
        
        # 按交易所统计
        print("\n🏢 按交易所统计:")
        cursor.execute('SELECT exchange, COUNT(*) FROM instruments GROUP BY exchange ORDER BY COUNT(*) DESC')
        exchanges = cursor.fetchall()
        for exc, count in exchanges:
            if exc:  # 只显示非空的交易所
                percentage = (count / total * 100) if total > 0 else 0
                print(f"  {exc:<20}: {count:>6,} 条 ({percentage:5.1f}%)")
        
        # 数据质量分析
        print("\n🔍 数据质量分析:")
        
        # 有价格信息的记录
        cursor.execute('SELECT COUNT(*) FROM instruments WHERE price IS NOT NULL AND price > 0')
        price_count = cursor.fetchone()[0]
        price_percentage = (price_count / total * 100) if total > 0 else 0
        print(f"  有价格信息: {price_count:>6,} 条 ({price_percentage:5.1f}%)")
        
        # 有成交量信息的记录
        cursor.execute('SELECT COUNT(*) FROM instruments WHERE volume IS NOT NULL AND volume > 0')
        volume_count = cursor.fetchone()[0]
        volume_percentage = (volume_count / total * 100) if total > 0 else 0
        print(f"  有成交量信息: {volume_count:>6,} 条 ({volume_percentage:5.1f}%)")
        
        # 已验证的记录
        cursor.execute('SELECT COUNT(*) FROM instruments WHERE verified = 1')
        verified_count = cursor.fetchone()[0]
        verified_percentage = (verified_count / total * 100) if total > 0 else 0
        print(f"  已验证记录: {verified_count:>6,} 条 ({verified_percentage:5.1f}%)")
        
        # 最新和最旧的记录时间
        print("\n⏰ 时间范围:")
        cursor.execute('SELECT MIN(timestamp), MAX(timestamp) FROM instruments')
        min_time, max_time = cursor.fetchone()
        if min_time and max_time:
            print(f"  最早记录: {min_time}")
            print(f"  最新记录: {max_time}")
        
        # 样本数据
        print("\n📋 样本数据 (前5条):")
        cursor.execute('SELECT name, symbol, category, exchange, source FROM instruments LIMIT 5')
        samples = cursor.fetchall()
        print(f"  {'名称':<20} {'代码':<10} {'分类':<15} {'交易所':<15} {'数据源'}")
        print("  " + "-" * 75)
        for name, symbol, category, exchange, source in samples:
            print(f"  {name:<20} {symbol:<10} {category:<15} {exchange:<15} {source}")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 分析失败: {e}")

def show_category_details():
    """显示分类详细信息"""
    try:
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()
        
        print("\n" + "=" * 60)
        print("分类详细信息")
        print("=" * 60)
        
        # 获取所有分类
        cursor.execute('SELECT DISTINCT category FROM instruments ORDER BY category')
        categories = [row[0] for row in cursor.fetchall()]
        
        for i, category in enumerate(categories, 1):
            print(f"\n{i}. {category}")
            
            # 该分类的统计信息
            cursor.execute('SELECT COUNT(*) FROM instruments WHERE category = ?', (category,))
            count = cursor.fetchone()[0]
            
            # 该分类的交易所分布
            cursor.execute('''
                SELECT exchange, COUNT(*) 
                FROM instruments 
                WHERE category = ? AND exchange != "" 
                GROUP BY exchange 
                ORDER BY COUNT(*) DESC
            ''', (category,))
            exchanges = cursor.fetchall()
            
            print(f"   总数: {count:,} 条")
            if exchanges:
                print("   交易所分布:")
                for exc, exc_count in exchanges:
                    print(f"     {exc}: {exc_count:,} 条")
            
            # 显示几个样本
            cursor.execute('''
                SELECT name, symbol 
                FROM instruments 
                WHERE category = ? 
                LIMIT 3
            ''', (category,))
            samples = cursor.fetchall()
            
            if samples:
                print("   样本:")
                for name, symbol in samples:
                    print(f"     {name} ({symbol})")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 分析失败: {e}")

def export_summary():
    """导出数据摘要"""
    try:
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()
        
        # 生成摘要报告
        summary_file = Path("data_summary.txt")
        
        with open(summary_file, 'w', encoding='utf-8') as f:
            f.write("金融数据库摘要报告\n")
            f.write("=" * 50 + "\n\n")
            
            # 总数据量
            cursor.execute('SELECT COUNT(*) FROM instruments')
            total = cursor.fetchone()[0]
            f.write(f"总数据量: {total:,} 条记录\n\n")
            
            # 按分类统计
            f.write("按分类统计:\n")
            cursor.execute('SELECT category, COUNT(*) FROM instruments GROUP BY category ORDER BY COUNT(*) DESC')
            for cat, count in cursor.fetchall():
                percentage = (count / total * 100) if total > 0 else 0
                f.write(f"  {cat}: {count:,} 条 ({percentage:.1f}%)\n")
            
            f.write("\n按数据源统计:\n")
            cursor.execute('SELECT source, COUNT(*) FROM instruments GROUP BY source ORDER BY COUNT(*) DESC')
            for src, count in cursor.fetchall():
                percentage = (count / total * 100) if total > 0 else 0
                f.write(f"  {src}: {count:,} 条 ({percentage:.1f}%)\n")
        
        conn.close()
        print(f"✅ 摘要报告已导出到: {summary_file}")
        
    except Exception as e:
        print(f"❌ 导出失败: {e}")

def main():
    """主菜单"""
    while True:
        print("\n" + "=" * 50)
        print("数据分析工具")
        print("=" * 50)
        
        print("\n📋 菜单:")
        print("1. 数据库总体分析")
        print("2. 分类详细信息")
        print("3. 导出数据摘要")
        print("0. 退出")
        
        choice = input("\n请选择 (0-3): ").strip()
        
        if choice == "1":
            analyze_database()
        elif choice == "2":
            show_category_details()
        elif choice == "3":
            export_summary()
        elif choice == "0":
            print("👋 再见！")
            break
        else:
            print("❌ 无效选择，请重试")
        
        input("\n按回车键继续...")

if __name__ == "__main__":
    main()

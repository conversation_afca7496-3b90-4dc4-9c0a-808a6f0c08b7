#!/usr/bin/env python
# -*- coding:utf-8 -*-
"""
数据抓取模块
负责从各种数据源获取金融标的数据
"""

import akshare as ak
import ccxt
import pandas as pd
import logging
import time
import concurrent.futures
from typing import Dict, List, Optional, Tuple
from config import FETCH_CONFIG, AKSHARE_CONFIG, CCXT_CONFIG, CATEGORY_MAPPING
from database_manager import DatabaseManager

class DataCollector:
    """数据收集器"""
    
    def __init__(self, db_manager: DatabaseManager):
        """初始化数据收集器
        
        Args:
            db_manager: 数据库管理器实例
        """
        self.db_manager = db_manager
        self.logger = logging.getLogger(__name__)
        
        # 初始化CCXT交易所
        self.exchanges = {}
        self._init_exchanges()
    
    def _init_exchanges(self):
        """初始化加密货币交易所"""
        for exchange_name in CCXT_CONFIG["exchanges"]:
            try:
                exchange_class = getattr(ccxt, exchange_name)
                self.exchanges[exchange_name] = exchange_class({
                    'timeout': FETCH_CONFIG["timeout"] * 1000,
                    'enableRateLimit': True,
                })
                self.logger.info(f"初始化交易所成功: {exchange_name}")
            except Exception as e:
                self.logger.warning(f"初始化交易所失败 {exchange_name}: {e}")
    
    def collect_stock_data(self, market: str = "A股", incremental: bool = True) -> Dict[str, any]:
        """收集股票数据
        
        Args:
            market: 市场类型 ("A股", "港股", "美股")
            incremental: 是否增量更新
            
        Returns:
            Dict: 收集结果统计
        """
        self.logger.info(f"开始收集{market}数据...")
        
        # 获取已存在的标的代码（如果是增量更新）
        existing_symbols = set()
        if incremental:
            category = CATEGORY_MAPPING["股票"][market]
            existing_symbols = self.db_manager.get_existing_symbols(category)
            self.logger.info(f"{market}已存在 {len(existing_symbols)} 个标的")
        
        try:
            # 根据市场类型获取数据
            if market == "A股":
                df = self._get_a_stock_data()
            elif market == "港股":
                df = self._get_hk_stock_data()
            elif market == "美股":
                df = self._get_us_stock_data()
            else:
                self.logger.error(f"不支持的市场类型: {market}")
                return {"success": False, "message": "不支持的市场类型"}
            
            if df is None or df.empty:
                return {"success": False, "message": f"未获取到{market}数据"}
            
            # 处理和插入数据
            instruments = []
            new_count = 0
            skip_count = 0
            
            for _, row in df.iterrows():
                try:
                    # 提取标的信息
                    symbol, name, exchange = self._extract_stock_info(row, market)
                    
                    if not symbol or not name:
                        continue
                    
                    # 检查是否已存在
                    if incremental and symbol in existing_symbols:
                        skip_count += 1
                        continue
                    
                    # 构建标的数据
                    instrument = {
                        "name": name,
                        "symbol": symbol,
                        "category": CATEGORY_MAPPING["股票"][market],
                        "exchange": exchange,
                        "source": "akshare",
                        "verified": 1
                    }
                    
                    # 添加价格信息（如果有）
                    if "price" in row.index and pd.notna(row["price"]):
                        instrument["price"] = float(row["price"])
                    elif "close" in row.index and pd.notna(row["close"]):
                        instrument["price"] = float(row["close"])
                    elif "最新价" in row.index and pd.notna(row["最新价"]):
                        instrument["price"] = float(row["最新价"])
                    
                    # 添加成交量信息（如果有）
                    if "volume" in row.index and pd.notna(row["volume"]):
                        instrument["volume"] = float(row["volume"])
                    elif "成交量" in row.index and pd.notna(row["成交量"]):
                        instrument["volume"] = float(row["成交量"])
                    
                    instruments.append(instrument)
                    new_count += 1
                    
                except Exception as e:
                    self.logger.warning(f"处理{market}数据行失败: {e}")
                    continue
            
            # 批量插入数据库
            if instruments:
                success_count = self.db_manager.batch_insert_instruments(instruments)
                self.logger.info(f"{market}数据收集完成: 新增 {success_count}/{new_count}, 跳过 {skip_count}")
                
                return {
                    "success": True,
                    "market": market,
                    "total": len(df),
                    "new": success_count,
                    "skipped": skip_count
                }
            else:
                return {"success": False, "message": f"{market}没有新数据需要添加"}
                
        except Exception as e:
            self.logger.error(f"收集{market}数据失败: {e}")
            return {"success": False, "message": str(e)}
    
    def _get_a_stock_data(self) -> Optional[pd.DataFrame]:
        """获取A股数据"""
        try:
            # 尝试多种方法获取A股数据
            methods = [
                lambda: ak.stock_zh_a_spot_em(),
                lambda: ak.stock_zh_a_spot(),
                lambda: ak.stock_sh_a_spot_em(),
            ]
            
            for method in methods:
                try:
                    df = method()
                    if isinstance(df, pd.DataFrame) and not df.empty:
                        self.logger.info(f"成功获取A股数据: {len(df)}条")
                        return df.head(AKSHARE_CONFIG["max_records_per_type"])
                except Exception as e:
                    self.logger.warning(f"A股数据获取方法失败: {e}")
                    continue
            
            return None
            
        except Exception as e:
            self.logger.error(f"获取A股数据失败: {e}")
            return None
    
    def _get_hk_stock_data(self) -> Optional[pd.DataFrame]:
        """获取港股数据"""
        try:
            methods = [
                lambda: ak.stock_hk_spot_em(),
                lambda: ak.stock_hk_spot(),
                lambda: ak.stock_hk_main_board_spot_em(),
            ]
            
            for method in methods:
                try:
                    df = method()
                    if isinstance(df, pd.DataFrame) and not df.empty:
                        self.logger.info(f"成功获取港股数据: {len(df)}条")
                        return df.head(AKSHARE_CONFIG["max_records_per_type"])
                except Exception as e:
                    self.logger.warning(f"港股数据获取方法失败: {e}")
                    continue
            
            return None
            
        except Exception as e:
            self.logger.error(f"获取港股数据失败: {e}")
            return None
    
    def _get_us_stock_data(self) -> Optional[pd.DataFrame]:
        """获取美股数据"""
        try:
            methods = [
                lambda: ak.stock_us_spot_em(),
                lambda: ak.stock_us_spot(),
            ]
            
            for method in methods:
                try:
                    df = method()
                    if isinstance(df, pd.DataFrame) and not df.empty:
                        self.logger.info(f"成功获取美股数据: {len(df)}条")
                        return df.head(AKSHARE_CONFIG["max_records_per_type"])
                except Exception as e:
                    self.logger.warning(f"美股数据获取方法失败: {e}")
                    continue
            
            return None
            
        except Exception as e:
            self.logger.error(f"获取美股数据失败: {e}")
            return None
    
    def _extract_stock_info(self, row: pd.Series, market: str) -> Tuple[str, str, str]:
        """从数据行中提取股票信息
        
        Args:
            row: 数据行
            market: 市场类型
            
        Returns:
            Tuple[str, str, str]: (代码, 名称, 交易所)
        """
        symbol = ""
        name = ""
        exchange = ""
        
        # 提取代码
        for col in ["代码", "symbol", "Symbol", "股票代码"]:
            if col in row.index and pd.notna(row[col]):
                symbol = str(row[col]).strip()
                break
        
        # 提取名称
        for col in ["名称", "name", "Name", "股票名称", "简称"]:
            if col in row.index and pd.notna(row[col]):
                name = str(row[col]).strip()
                break
        
        # 设置交易所
        if market == "A股":
            if symbol.startswith("6"):
                exchange = "上海证券交易所"
            elif symbol.startswith(("0", "3")):
                exchange = "深圳证券交易所"
            elif symbol.startswith("8"):
                exchange = "北京证券交易所"
            else:
                exchange = "A股"
        elif market == "港股":
            exchange = "香港交易所"
        elif market == "美股":
            exchange = "美国交易所"
        
        return symbol, name, exchange
    
    def collect_fund_data(self, fund_type: str = "公募基金", incremental: bool = True) -> Dict[str, any]:
        """收集基金数据
        
        Args:
            fund_type: 基金类型
            incremental: 是否增量更新
            
        Returns:
            Dict: 收集结果统计
        """
        self.logger.info(f"开始收集{fund_type}数据...")
        
        # 获取已存在的标的代码
        existing_symbols = set()
        if incremental:
            category = CATEGORY_MAPPING["基金"][fund_type]
            existing_symbols = self.db_manager.get_existing_symbols(category)
        
        try:
            # 获取基金数据
            if fund_type == "公募基金":
                df = self._get_public_fund_data()
            elif fund_type == "ETF基金":
                df = self._get_etf_fund_data()
            else:
                return {"success": False, "message": "不支持的基金类型"}
            
            if df is None or df.empty:
                return {"success": False, "message": f"未获取到{fund_type}数据"}
            
            # 处理和插入数据
            instruments = []
            new_count = 0
            skip_count = 0
            
            for _, row in df.iterrows():
                try:
                    symbol, name = self._extract_fund_info(row)
                    
                    if not symbol or not name:
                        continue
                    
                    if incremental and symbol in existing_symbols:
                        skip_count += 1
                        continue
                    
                    instrument = {
                        "name": name,
                        "symbol": symbol,
                        "category": CATEGORY_MAPPING["基金"][fund_type],
                        "exchange": "基金公司" if fund_type == "公募基金" else "交易所",
                        "source": "akshare",
                        "verified": 1
                    }
                    
                    instruments.append(instrument)
                    new_count += 1
                    
                except Exception as e:
                    self.logger.warning(f"处理{fund_type}数据行失败: {e}")
                    continue
            
            # 批量插入
            if instruments:
                success_count = self.db_manager.batch_insert_instruments(instruments)
                self.logger.info(f"{fund_type}数据收集完成: 新增 {success_count}/{new_count}, 跳过 {skip_count}")
                
                return {
                    "success": True,
                    "fund_type": fund_type,
                    "total": len(df),
                    "new": success_count,
                    "skipped": skip_count
                }
            else:
                return {"success": False, "message": f"{fund_type}没有新数据需要添加"}
                
        except Exception as e:
            self.logger.error(f"收集{fund_type}数据失败: {e}")
            return {"success": False, "message": str(e)}
    
    def _get_public_fund_data(self) -> Optional[pd.DataFrame]:
        """获取公募基金数据"""
        try:
            df = ak.fund_em_fund_name()
            if isinstance(df, pd.DataFrame) and not df.empty:
                self.logger.info(f"成功获取公募基金数据: {len(df)}条")
                return df.head(AKSHARE_CONFIG["max_records_per_type"])
            return None
        except Exception as e:
            self.logger.error(f"获取公募基金数据失败: {e}")
            return None
    
    def _get_etf_fund_data(self) -> Optional[pd.DataFrame]:
        """获取ETF基金数据"""
        try:
            # 尝试获取ETF数据
            methods = [
                lambda: ak.fund_etf_category_sina("ETF基金"),
                lambda: ak.fund_etf_fund_info_em(),
            ]
            
            for method in methods:
                try:
                    df = method()
                    if isinstance(df, pd.DataFrame) and not df.empty:
                        self.logger.info(f"成功获取ETF基金数据: {len(df)}条")
                        return df.head(AKSHARE_CONFIG["max_records_per_type"])
                except Exception as e:
                    self.logger.warning(f"ETF数据获取方法失败: {e}")
                    continue
            
            return None
            
        except Exception as e:
            self.logger.error(f"获取ETF基金数据失败: {e}")
            return None
    
    def _extract_fund_info(self, row: pd.Series) -> Tuple[str, str]:
        """从数据行中提取基金信息"""
        symbol = ""
        name = ""
        
        # 提取代码
        for col in ["基金代码", "代码", "symbol", "Symbol"]:
            if col in row.index and pd.notna(row[col]):
                symbol = str(row[col]).strip()
                break
        
        # 提取名称
        for col in ["基金简称", "基金名称", "名称", "name", "Name"]:
            if col in row.index and pd.notna(row[col]):
                name = str(row[col]).strip()
                break
        
        return symbol, name
    
    def collect_crypto_data(self, exchange_name: str = "binance", incremental: bool = True) -> Dict[str, any]:
        """收集加密货币数据
        
        Args:
            exchange_name: 交易所名称
            incremental: 是否增量更新
            
        Returns:
            Dict: 收集结果统计
        """
        self.logger.info(f"开始收集{exchange_name}加密货币数据...")
        
        if exchange_name not in self.exchanges:
            return {"success": False, "message": f"交易所 {exchange_name} 未初始化"}
        
        # 获取已存在的标的代码
        existing_symbols = set()
        if incremental:
            category = CATEGORY_MAPPING["加密货币"]["现货"]
            existing_symbols = self.db_manager.get_existing_symbols(category)
        
        try:
            exchange = self.exchanges[exchange_name]
            markets = exchange.load_markets()
            
            instruments = []
            new_count = 0
            skip_count = 0
            
            for symbol, market in markets.items():
                try:
                    # 只收集USDT交易对
                    if not any(symbol.endswith(f"/{quote}") for quote in CCXT_CONFIG["quote_currencies"]):
                        continue
                    
                    if incremental and symbol in existing_symbols:
                        skip_count += 1
                        continue
                    
                    # 获取ticker数据
                    ticker = exchange.fetch_ticker(symbol)
                    
                    instrument = {
                        "name": market["base"],
                        "symbol": symbol,
                        "category": CATEGORY_MAPPING["加密货币"]["现货"],
                        "exchange": exchange_name.upper(),
                        "source": "ccxt",
                        "price": ticker.get("last"),
                        "volume": ticker.get("baseVolume"),
                        "verified": 1
                    }
                    
                    instruments.append(instrument)
                    new_count += 1
                    
                    # 限制数量
                    if new_count >= CCXT_CONFIG["max_symbols"]:
                        break
                    
                    # 添加延迟避免频率限制
                    time.sleep(FETCH_CONFIG["delay"] / 1000)
                    
                except Exception as e:
                    self.logger.warning(f"处理加密货币 {symbol} 失败: {e}")
                    continue
            
            # 批量插入
            if instruments:
                success_count = self.db_manager.batch_insert_instruments(instruments)
                self.logger.info(f"{exchange_name}加密货币数据收集完成: 新增 {success_count}/{new_count}, 跳过 {skip_count}")
                
                return {
                    "success": True,
                    "exchange": exchange_name,
                    "total": len(markets),
                    "new": success_count,
                    "skipped": skip_count
                }
            else:
                return {"success": False, "message": f"{exchange_name}没有新数据需要添加"}
                
        except Exception as e:
            self.logger.error(f"收集{exchange_name}加密货币数据失败: {e}")
            return {"success": False, "message": str(e)}
    
    def collect_all_data(self, incremental: bool = True) -> Dict[str, any]:
        """收集所有类型的数据
        
        Args:
            incremental: 是否增量更新
            
        Returns:
            Dict: 收集结果统计
        """
        self.logger.info("开始收集所有数据...")
        results = {}
        
        # 使用线程池并行收集数据
        with concurrent.futures.ThreadPoolExecutor(max_workers=FETCH_CONFIG["max_workers"]) as executor:
            futures = {}
            
            # 提交股票数据收集任务
            for market in AKSHARE_CONFIG["stock_markets"]:
                future = executor.submit(self.collect_stock_data, market, incremental)
                futures[f"stock_{market}"] = future
            
            # 提交基金数据收集任务
            for fund_type in AKSHARE_CONFIG["fund_types"]:
                future = executor.submit(self.collect_fund_data, fund_type, incremental)
                futures[f"fund_{fund_type}"] = future
            
            # 提交加密货币数据收集任务
            for exchange_name in CCXT_CONFIG["exchanges"]:
                if exchange_name in self.exchanges:
                    future = executor.submit(self.collect_crypto_data, exchange_name, incremental)
                    futures[f"crypto_{exchange_name}"] = future
            
            # 收集结果
            for key, future in futures.items():
                try:
                    result = future.result(timeout=FETCH_CONFIG["timeout"])
                    results[key] = result
                except Exception as e:
                    self.logger.error(f"收集任务 {key} 失败: {e}")
                    results[key] = {"success": False, "message": str(e)}
        
        # 统计总体结果
        total_new = sum(r.get("new", 0) for r in results.values() if r.get("success"))
        total_skipped = sum(r.get("skipped", 0) for r in results.values() if r.get("success"))
        
        self.logger.info(f"数据收集完成: 新增 {total_new}, 跳过 {total_skipped}")
        
        return {
            "success": True,
            "total_new": total_new,
            "total_skipped": total_skipped,
            "details": results
        }

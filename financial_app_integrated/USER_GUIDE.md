# 金融标的数据管理系统 - 使用指南

## 🎯 主要改进

### ✅ 已解决的问题
1. **显示限制问题**: 现在显示所有数据，不再限制在1000条
2. **分类筛选**: 新增按交易所分组的筛选功能
3. **数据收集**: 按交易所分类的数据收集按钮
4. **实时搜索**: 支持实时搜索和筛选

### 🏢 交易所分组
- **上证**: 上海证券交易所（6开头的A股）
- **深证**: 深圳证券交易所（0、3开头的A股）
- **港交所**: 香港交易所（港股）
- **美国交易所**: 美股
- **加密货币**: 各种数字货币交易对
- **其他**: 基金、债券等其他金融产品

## 🚀 使用方法

### 1. 启动应用
```bash
cd financial_app_integrated
python run.py
```

### 2. 数据收集
#### 按交易所收集：
- **收集上证数据**: 收集上海证券交易所的股票
- **收集深证数据**: 收集深圳证券交易所的股票  
- **收集港交所数据**: 收集香港交易所的股票
- **收集美股数据**: 收集美国交易所的股票
- **收集加密货币**: 收集数字货币交易对
- **收集基金数据**: 收集基金产品
- **收集所有数据**: 一次性收集所有类型的数据

### 3. 数据筛选和搜索
#### 交易所筛选：
- 使用下拉菜单选择特定交易所
- 选择"全部"显示所有数据

#### 搜索功能：
- 在搜索框输入关键词（股票名称或代码）
- 支持实时搜索（输入时自动筛选）
- 可以组合交易所筛选和关键词搜索

#### 操作按钮：
- **立即搜索**: 手动触发搜索
- **清除筛选**: 清空所有筛选条件，显示全部数据
- **刷新数据**: 重新加载所有数据
- **导出CSV**: 将当前显示的数据导出为CSV文件

### 4. 数据显示
- **完整显示**: 现在显示所有数据，不再有1000条限制
- **实时统计**: 底部状态栏显示各交易所的数据统计
- **筛选状态**: 显示当前筛选条件和结果数量

## 🔧 配置管理

### 修改数据收集限制
```bash
python config_manager.py
```
选择"修改数据收集限制"可以：
- 设置每类数据的最大收集数量
- 取消限制（获取所有可用数据）
- 调整线程数等参数

### 数据分析
```bash
python data_analyzer.py
```
可以查看：
- 数据库总体统计
- 各分类详细信息
- 数据质量分析
- 导出数据摘要

## 📊 界面说明

### 数据收集区域
```
┌─ 数据收集 ─────────────────────────────────────┐
│ [收集上证数据] [收集深证数据] [收集港交所数据] [收集美股数据] │
│ [收集加密货币] [收集基金数据] [收集所有数据]              │
└─────────────────────────────────────────────┘
```

### 筛选和搜索区域
```
┌─ 筛选和搜索 ───────────────────────────────────┐
│ 交易所: [全部 ▼]     搜索: [输入关键词...]        │
│ [立即搜索] [清除筛选] [刷新数据] [导出CSV]         │
└─────────────────────────────────────────────┘
```

### 数据表格
显示所有金融标的信息：
- **ID**: 数据库记录ID
- **名称**: 标的名称
- **代码**: 标的代码/符号
- **类别**: 详细分类（如"股票/A股"）
- **交易所**: 具体交易所名称
- **数据源**: 数据来源（akshare/ccxt）
- **时间**: 数据收集时间

### 状态栏
显示当前状态和统计信息：
```
状态: 已加载 3000 条记录 | 总计: 3000 | 上证: 420 | 深证: 522 | 港交所: 1000 | 美国交易所: 1000 | 其他: 58
```

## 💡 使用技巧

### 1. 快速查找特定股票
1. 选择对应的交易所（如"上证"）
2. 在搜索框输入股票名称或代码
3. 实时显示匹配结果

### 2. 查看特定交易所的所有数据
1. 在交易所下拉菜单选择目标交易所
2. 清空搜索框
3. 查看该交易所的所有标的

### 3. 导出特定数据
1. 使用筛选功能显示需要的数据
2. 点击"导出CSV"按钮
3. 选择保存位置

### 4. 数据收集策略
- **首次使用**: 点击"收集所有数据"获取完整数据集
- **日常更新**: 按需收集特定交易所的数据
- **增量更新**: 系统自动跳过已存在的数据

## ⚠️ 注意事项

1. **数据量**: 取消限制后可能收集大量数据，请确保有足够存储空间
2. **网络**: 数据收集需要稳定的网络连接
3. **时间**: 收集所有数据可能需要较长时间
4. **频率**: 避免频繁收集同一类型数据，以免对数据源造成压力

## 🔍 故障排除

### 问题：界面只显示部分数据
**解决**: 点击"刷新数据"按钮，现在会显示所有数据

### 问题：搜索结果不准确
**解决**: 
1. 检查交易所筛选是否正确
2. 尝试使用"清除筛选"重置条件
3. 使用"立即搜索"手动触发搜索

### 问题：数据收集失败
**解决**:
1. 检查网络连接
2. 查看日志文件：`data/app.log`
3. 尝试收集单个交易所的数据
4. 使用配置管理工具调整参数

### 问题：程序运行缓慢
**解决**:
1. 使用交易所筛选减少显示的数据量
2. 在配置管理工具中调整线程数
3. 定期清理旧数据

## 📞 技术支持

如遇到问题，请查看：
1. 应用日志：`data/app.log`
2. 运行数据分析工具检查数据状态
3. 使用配置管理工具调整设置

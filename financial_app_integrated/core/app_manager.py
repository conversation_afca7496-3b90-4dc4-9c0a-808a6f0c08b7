#!/usr/bin/env python
# -*- coding:utf-8 -*-
"""
应用管理器
负责协调各个模块，管理应用生命周期
"""

import tkinter as tk
from tkinter import messagebox
import threading
import logging
from typing import Optional

from config import APP_NAME, APP_VERSION
from .logger import setup_logging, get_logger
from data.database import DatabaseManager
from data.collectors import create_data_collector
from ui.main_window import MainWindow

class AppManager:
    """应用管理器"""
    
    def __init__(self):
        """初始化应用管理器"""
        # 设置日志
        setup_logging()
        self.logger = get_logger(__name__)
        
        # 初始化组件
        self.root: Optional[tk.Tk] = None
        self.db_manager: Optional[DatabaseManager] = None
        self.data_collector = None
        self.main_window: Optional[MainWindow] = None
        
        # 应用状态
        self.is_running = False
        
        self.logger.info(f"应用管理器初始化完成 - {APP_NAME} v{APP_VERSION}")
    
    def initialize(self) -> bool:
        """初始化应用组件
        
        Returns:
            bool: 是否成功初始化
        """
        try:
            self.logger.info("开始初始化应用组件...")
            
            # 初始化数据库管理器
            self.db_manager = DatabaseManager()
            if not self.db_manager.init_database():
                raise Exception("数据库初始化失败")
            
            # 初始化数据收集器
            self.data_collector = create_data_collector(self.db_manager)
            
            # 初始化UI
            self.root = tk.Tk()
            self.main_window = MainWindow(
                self.root, 
                self.db_manager, 
                self.data_collector,
                self
            )
            
            self.logger.info("应用组件初始化完成")
            return True
            
        except Exception as e:
            self.logger.error(f"应用初始化失败: {e}")
            if self.root:
                messagebox.showerror("初始化错误", f"应用初始化失败:\n{str(e)}")
            return False
    
    def run(self):
        """运行应用"""
        if not self.initialize():
            return
        
        try:
            self.is_running = True
            self.logger.info("应用开始运行")
            
            # 设置窗口关闭事件
            self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
            
            # 启动主循环
            self.root.mainloop()
            
        except Exception as e:
            self.logger.error(f"应用运行时错误: {e}")
            messagebox.showerror("运行错误", f"应用运行时发生错误:\n{str(e)}")
        finally:
            self.cleanup()
    
    def on_closing(self):
        """窗口关闭事件处理"""
        if messagebox.askokcancel("退出", "确定要退出应用吗？"):
            self.is_running = False
            self.root.quit()
    
    def cleanup(self):
        """清理资源"""
        self.logger.info("开始清理应用资源...")
        
        try:
            # 清理数据库连接
            if self.db_manager:
                # 数据库管理器会自动关闭连接
                pass
            
            # 清理UI资源
            if self.root:
                self.root.destroy()
            
            self.logger.info("应用资源清理完成")
            
        except Exception as e:
            self.logger.error(f"资源清理时发生错误: {e}")
    
    def show_error(self, title: str, message: str):
        """显示错误消息
        
        Args:
            title: 错误标题
            message: 错误消息
        """
        self.logger.error(f"{title}: {message}")
        if self.root:
            messagebox.showerror(title, message)
    
    def show_info(self, title: str, message: str):
        """显示信息消息
        
        Args:
            title: 信息标题
            message: 信息内容
        """
        self.logger.info(f"{title}: {message}")
        if self.root:
            messagebox.showinfo(title, message)
    
    def show_warning(self, title: str, message: str):
        """显示警告消息
        
        Args:
            title: 警告标题
            message: 警告内容
        """
        self.logger.warning(f"{title}: {message}")
        if self.root:
            messagebox.showwarning(title, message)
    
    def run_in_background(self, func, *args, **kwargs):
        """在后台线程中运行函数
        
        Args:
            func: 要运行的函数
            *args: 函数参数
            **kwargs: 函数关键字参数
        """
        def wrapper():
            try:
                func(*args, **kwargs)
            except Exception as e:
                self.logger.error(f"后台任务执行失败: {e}")
                # 在主线程中显示错误
                self.root.after(0, lambda: self.show_error("后台任务错误", str(e)))
        
        thread = threading.Thread(target=wrapper, daemon=True)
        thread.start()
        return thread

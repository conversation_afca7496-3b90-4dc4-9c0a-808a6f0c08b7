#!/usr/bin/env python
# -*- coding:utf-8 -*-
"""
日志管理模块
提供统一的日志配置和管理功能
"""

import logging
import logging.handlers
import os
from pathlib import Path
from config import LOGGING_CONFIG

def setup_logging():
    """设置应用日志配置"""
    
    # 确保日志目录存在
    log_file = Path(LOGGING_CONFIG["file"])
    log_file.parent.mkdir(parents=True, exist_ok=True)
    
    # 创建根日志器
    logger = logging.getLogger()
    logger.setLevel(getattr(logging, LOGGING_CONFIG["level"]))
    
    # 清除现有处理器
    for handler in logger.handlers[:]:
        logger.removeHandler(handler)
    
    # 创建格式器
    formatter = logging.Formatter(LOGGING_CONFIG["format"])
    
    # 控制台处理器
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)
    console_handler.setFormatter(formatter)
    logger.addHandler(console_handler)
    
    # 文件处理器（带轮转）
    file_handler = logging.handlers.RotatingFileHandler(
        log_file,
        maxBytes=LOGGING_CONFIG["max_size"],
        backupCount=LOGGING_CONFIG["backup_count"],
        encoding='utf-8'
    )
    file_handler.setLevel(getattr(logging, LOGGING_CONFIG["level"]))
    file_handler.setFormatter(formatter)
    logger.addHandler(file_handler)
    
    # 记录启动信息
    logger.info("日志系统初始化完成")
    logger.info(f"日志文件: {log_file}")
    
    return logger

def get_logger(name: str):
    """获取指定名称的日志器
    
    Args:
        name: 日志器名称
        
    Returns:
        logging.Logger: 日志器实例
    """
    return logging.getLogger(name)

#!/usr/bin/env python
# -*- coding:utf-8 -*-
"""
金融应用配置文件
"""

from pathlib import Path
import os

# 应用基本配置
APP_NAME = "金融标的数据管理系统"
APP_VERSION = "1.0.0"

# 数据目录配置
BASE_DIR = Path(__file__).parent
DATA_DIR = BASE_DIR / "data"
DB_PATH = DATA_DIR / "financial_database.db"
CSV_PATH = DATA_DIR / "financial_instruments.csv"
BACKUP_DIR = DATA_DIR / "backups"

# 确保目录存在
DATA_DIR.mkdir(exist_ok=True)
BACKUP_DIR.mkdir(exist_ok=True)

# 数据库配置
DATABASE_CONFIG = {
    "path": DB_PATH,
    "timeout": 30,
    "check_same_thread": False
}

# 数据抓取配置
FETCH_CONFIG = {
    "max_workers": 4,  # 最大线程数
    "timeout": 30,     # 请求超时时间
    "retry_times": 3,  # 重试次数
    "delay": 1,        # 请求间隔
}

# AKShare 数据源配置
AKSHARE_CONFIG = {
    "stock_markets": ["A股", "港股", "美股"],
    "fund_types": ["公募基金", "ETF基金"],
    "max_records_per_type": 1000,
}

# CCXT 加密货币配置
CCXT_CONFIG = {
    "exchanges": ["binance", "okx", "huobi"],
    "quote_currencies": ["USDT", "BTC", "ETH"],
    "max_symbols": 200,
}

# UI界面配置
UI_CONFIG = {
    "window_size": "1200x800",
    "theme": "default",
    "font_family": "微软雅黑",
    "font_size": 10,
}

# 日志配置
LOGGING_CONFIG = {
    "level": "INFO",
    "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    "file": DATA_DIR / "app.log",
    "max_size": 10 * 1024 * 1024,  # 10MB
    "backup_count": 5,
}

# 数据表结构
TABLE_SCHEMA = """
CREATE TABLE IF NOT EXISTS instruments (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL,
    symbol TEXT NOT NULL,
    category TEXT NOT NULL,
    exchange TEXT,
    source TEXT NOT NULL,
    timestamp TEXT NOT NULL,
    market_cap REAL,
    price REAL,
    volume REAL,
    verified INTEGER DEFAULT 0,
    UNIQUE(symbol, category)
)
"""

# 数据分类映射
CATEGORY_MAPPING = {
    "股票": {
        "A股": "股票/A股",
        "港股": "股票/港股", 
        "美股": "股票/美股",
    },
    "基金": {
        "公募基金": "基金/公募基金",
        "ETF基金": "基金/ETF基金",
    },
    "加密货币": {
        "现货": "加密货币/现货",
        "期货": "加密货币/期货",
    }
}

#!/usr/bin/env python
# -*- coding:utf-8 -*-
"""
主窗口
应用的主界面
"""

import tkinter as tk
from tkinter import ttk, messagebox
import logging
from config import APP_NAME, UI_CONFIG

class MainWindow:
    """主窗口类"""
    
    def __init__(self, root, db_manager, data_collector, app_manager):
        """初始化主窗口
        
        Args:
            root: Tkinter根窗口
            db_manager: 数据库管理器
            data_collector: 数据收集器
            app_manager: 应用管理器
        """
        self.root = root
        self.db_manager = db_manager
        self.data_collector = data_collector
        self.app_manager = app_manager
        self.logger = logging.getLogger(__name__)
        
        # 设置窗口
        self.setup_window()
        
        # 创建UI组件
        self.create_widgets()
        
        # 初始化数据
        self.load_initial_data()
    
    def setup_window(self):
        """设置窗口属性"""
        self.root.title(APP_NAME)
        self.root.geometry(UI_CONFIG["window_size"])
        self.root.resizable(True, True)
        
        # 设置窗口图标（如果有的话）
        try:
            # self.root.iconbitmap("icon.ico")
            pass
        except:
            pass
    
    def create_widgets(self):
        """创建UI组件"""
        # 创建主框架
        self.main_frame = ttk.Frame(self.root)
        self.main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 创建顶部控制面板
        self.create_control_panel()
        
        # 创建中间数据显示区域
        self.create_data_area()
        
        # 创建底部状态栏
        self.create_status_bar()
    
    def create_control_panel(self):
        """创建控制面板"""
        control_frame = ttk.LabelFrame(self.main_frame, text="数据管理", padding="10")
        control_frame.pack(fill=tk.X, pady=(0, 10))
        
        # 数据收集按钮
        collect_frame = ttk.Frame(control_frame)
        collect_frame.pack(fill=tk.X, pady=(0, 5))
        
        ttk.Button(collect_frame, text="收集股票数据", 
                  command=self.collect_stock_data).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(collect_frame, text="收集基金数据", 
                  command=self.collect_fund_data).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(collect_frame, text="收集加密货币", 
                  command=self.collect_crypto_data).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(collect_frame, text="收集所有数据", 
                  command=self.collect_all_data).pack(side=tk.LEFT, padx=(0, 5))
        
        # 搜索框
        search_frame = ttk.Frame(control_frame)
        search_frame.pack(fill=tk.X, pady=(5, 0))
        
        ttk.Label(search_frame, text="搜索:").pack(side=tk.LEFT, padx=(0, 5))
        self.search_var = tk.StringVar()
        search_entry = ttk.Entry(search_frame, textvariable=self.search_var, width=30)
        search_entry.pack(side=tk.LEFT, padx=(0, 5))
        search_entry.bind("<Return>", lambda e: self.search_data())
        
        ttk.Button(search_frame, text="搜索", 
                  command=self.search_data).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(search_frame, text="刷新", 
                  command=self.refresh_data).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(search_frame, text="导出CSV", 
                  command=self.export_data).pack(side=tk.LEFT)
    
    def create_data_area(self):
        """创建数据显示区域"""
        # 创建Treeview显示数据
        columns = ("id", "name", "symbol", "category", "exchange", "source", "timestamp")
        self.tree = ttk.Treeview(self.main_frame, columns=columns, show="headings", height=20)
        
        # 设置列标题和宽度
        column_config = {
            "id": ("ID", 50),
            "name": ("名称", 200),
            "symbol": ("代码", 100),
            "category": ("类别", 150),
            "exchange": ("交易所", 100),
            "source": ("数据源", 80),
            "timestamp": ("时间", 150)
        }
        
        for col, (text, width) in column_config.items():
            self.tree.heading(col, text=text, command=lambda c=col: self.sort_by_column(c))
            self.tree.column(col, width=width, anchor=tk.W)
        
        # 添加滚动条
        scrollbar_y = ttk.Scrollbar(self.main_frame, orient=tk.VERTICAL, command=self.tree.yview)
        scrollbar_x = ttk.Scrollbar(self.main_frame, orient=tk.HORIZONTAL, command=self.tree.xview)
        self.tree.configure(yscrollcommand=scrollbar_y.set, xscrollcommand=scrollbar_x.set)
        
        # 布局
        self.tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar_y.pack(side=tk.RIGHT, fill=tk.Y)
        scrollbar_x.pack(side=tk.BOTTOM, fill=tk.X)
    
    def create_status_bar(self):
        """创建状态栏"""
        self.status_frame = ttk.Frame(self.main_frame)
        self.status_frame.pack(fill=tk.X, pady=(10, 0))
        
        self.status_var = tk.StringVar()
        self.status_var.set("就绪")
        status_label = ttk.Label(self.status_frame, textvariable=self.status_var)
        status_label.pack(side=tk.LEFT)
        
        # 统计信息
        self.stats_var = tk.StringVar()
        stats_label = ttk.Label(self.status_frame, textvariable=self.stats_var)
        stats_label.pack(side=tk.RIGHT)
    
    def load_initial_data(self):
        """加载初始数据"""
        self.refresh_data()
    
    def refresh_data(self):
        """刷新数据显示"""
        try:
            self.status_var.set("正在加载数据...")
            self.root.update()
            
            # 清空现有数据
            for item in self.tree.get_children():
                self.tree.delete(item)
            
            # 从数据库获取数据
            instruments = self.db_manager.search_instruments(limit=1000)
            
            # 显示数据
            for instrument in instruments:
                values = (
                    instrument.id or "",
                    instrument.name,
                    instrument.symbol,
                    instrument.category,
                    instrument.exchange,
                    instrument.source,
                    instrument.timestamp
                )
                self.tree.insert("", tk.END, values=values)
            
            # 更新统计信息
            self.update_statistics()
            self.status_var.set(f"已加载 {len(instruments)} 条记录")
            
        except Exception as e:
            self.logger.error(f"刷新数据失败: {e}")
            self.status_var.set("数据加载失败")
            messagebox.showerror("错误", f"数据加载失败:\n{str(e)}")
    
    def search_data(self):
        """搜索数据"""
        keyword = self.search_var.get().strip()
        if not keyword:
            self.refresh_data()
            return
        
        try:
            self.status_var.set("正在搜索...")
            self.root.update()
            
            # 清空现有数据
            for item in self.tree.get_children():
                self.tree.delete(item)
            
            # 搜索数据
            instruments = self.db_manager.search_instruments(keyword=keyword, limit=1000)
            
            # 显示搜索结果
            for instrument in instruments:
                values = (
                    instrument.id or "",
                    instrument.name,
                    instrument.symbol,
                    instrument.category,
                    instrument.exchange,
                    instrument.source,
                    instrument.timestamp
                )
                self.tree.insert("", tk.END, values=values)
            
            self.status_var.set(f"搜索到 {len(instruments)} 条记录")
            
        except Exception as e:
            self.logger.error(f"搜索失败: {e}")
            self.status_var.set("搜索失败")
            messagebox.showerror("错误", f"搜索失败:\n{str(e)}")
    
    def collect_stock_data(self):
        """收集股票数据"""
        self.app_manager.run_in_background(self._collect_stock_data_bg)
    
    def _collect_stock_data_bg(self):
        """后台收集股票数据"""
        try:
            self.root.after(0, lambda: self.status_var.set("正在收集股票数据..."))
            
            results = []
            for market in self.data_collector.get_available_markets():
                result = self.data_collector.collect_stock_data(market)
                results.append(f"{market}: {result.get('new', 0)}条新数据")
            
            message = "股票数据收集完成:\n" + "\n".join(results)
            self.root.after(0, lambda: self.app_manager.show_info("收集完成", message))
            self.root.after(0, self.refresh_data)
            
        except Exception as e:
            self.root.after(0, lambda: self.app_manager.show_error("收集失败", str(e)))
    
    def collect_fund_data(self):
        """收集基金数据"""
        self.app_manager.run_in_background(self._collect_fund_data_bg)
    
    def _collect_fund_data_bg(self):
        """后台收集基金数据"""
        try:
            self.root.after(0, lambda: self.status_var.set("正在收集基金数据..."))
            
            results = []
            for fund_type in self.data_collector.get_available_fund_types():
                result = self.data_collector.collect_fund_data(fund_type)
                results.append(f"{fund_type}: {result.get('new', 0)}条新数据")
            
            message = "基金数据收集完成:\n" + "\n".join(results)
            self.root.after(0, lambda: self.app_manager.show_info("收集完成", message))
            self.root.after(0, self.refresh_data)
            
        except Exception as e:
            self.root.after(0, lambda: self.app_manager.show_error("收集失败", str(e)))
    
    def collect_crypto_data(self):
        """收集加密货币数据"""
        self.app_manager.run_in_background(self._collect_crypto_data_bg)
    
    def _collect_crypto_data_bg(self):
        """后台收集加密货币数据"""
        try:
            self.root.after(0, lambda: self.status_var.set("正在收集加密货币数据..."))
            
            result = self.data_collector.collect_crypto_data("binance")
            message = f"加密货币数据收集完成: {result.get('new', 0)}条新数据"
            
            self.root.after(0, lambda: self.app_manager.show_info("收集完成", message))
            self.root.after(0, self.refresh_data)
            
        except Exception as e:
            self.root.after(0, lambda: self.app_manager.show_error("收集失败", str(e)))
    
    def collect_all_data(self):
        """收集所有数据"""
        self.app_manager.run_in_background(self._collect_all_data_bg)
    
    def _collect_all_data_bg(self):
        """后台收集所有数据"""
        try:
            self.root.after(0, lambda: self.status_var.set("正在收集所有数据..."))
            
            result = self.data_collector.collect_all_data()
            message = f"数据收集完成: 新增 {result.get('total_new', 0)} 条数据"
            
            self.root.after(0, lambda: self.app_manager.show_info("收集完成", message))
            self.root.after(0, self.refresh_data)
            
        except Exception as e:
            self.root.after(0, lambda: self.app_manager.show_error("收集失败", str(e)))
    
    def export_data(self):
        """导出数据"""
        from tkinter import filedialog
        
        try:
            file_path = filedialog.asksaveasfilename(
                title="导出数据",
                defaultextension=".csv",
                filetypes=[("CSV文件", "*.csv"), ("所有文件", "*.*")]
            )
            
            if file_path:
                success = self.db_manager.export_to_csv(file_path)
                if success:
                    self.app_manager.show_info("导出成功", f"数据已导出到:\n{file_path}")
                else:
                    self.app_manager.show_error("导出失败", "数据导出失败")
                    
        except Exception as e:
            self.app_manager.show_error("导出错误", str(e))
    
    def sort_by_column(self, col):
        """按列排序"""
        # 简单的排序实现
        pass
    
    def update_statistics(self):
        """更新统计信息"""
        try:
            stats = self.db_manager.get_statistics()
            total = stats.get("total", 0)
            self.stats_var.set(f"总计: {total} 条记录")
        except Exception as e:
            self.logger.error(f"更新统计信息失败: {e}")
            self.stats_var.set("统计信息获取失败")

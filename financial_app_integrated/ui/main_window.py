#!/usr/bin/env python
# -*- coding:utf-8 -*-
"""
主窗口
应用的主界面
"""

import tkinter as tk
from tkinter import ttk, messagebox
import logging
from config import APP_NAME, UI_CONFIG

class MainWindow:
    """主窗口类"""

    def __init__(self, root, db_manager, data_collector, app_manager):
        """初始化主窗口

        Args:
            root: Tkinter根窗口
            db_manager: 数据库管理器
            data_collector: 数据收集器
            app_manager: 应用管理器
        """
        self.root = root
        self.db_manager = db_manager
        self.data_collector = data_collector
        self.app_manager = app_manager
        self.logger = logging.getLogger(__name__)

        # 设置窗口
        self.setup_window()

        # 创建UI组件
        self.create_widgets()

        # 初始化数据
        self.load_initial_data()

    def setup_window(self):
        """设置窗口属性"""
        self.root.title(APP_NAME)
        self.root.geometry(UI_CONFIG["window_size"])
        self.root.resizable(True, True)

        # 设置窗口图标（如果有的话）
        try:
            # self.root.iconbitmap("icon.ico")
            pass
        except:
            pass

    def create_widgets(self):
        """创建UI组件"""
        # 创建主框架
        self.main_frame = ttk.Frame(self.root)
        self.main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # 创建顶部控制面板
        self.create_control_panel()

        # 创建中间数据显示区域
        self.create_data_area()

        # 创建底部状态栏
        self.create_status_bar()

    def create_control_panel(self):
        """创建控制面板"""
        control_frame = ttk.LabelFrame(self.main_frame, text="数据管理", padding="10")
        control_frame.pack(fill=tk.X, pady=(0, 10))

        # 数据收集按钮 - 按交易所分组
        collect_frame = ttk.LabelFrame(control_frame, text="数据收集", padding="5")
        collect_frame.pack(fill=tk.X, pady=(0, 10))

        # 第一行：主要交易所
        row1 = ttk.Frame(collect_frame)
        row1.pack(fill=tk.X, pady=(0, 5))

        ttk.Button(row1, text="收集上证数据", width=12,
                  command=lambda: self.collect_exchange_data("上证")).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(row1, text="收集深证数据", width=12,
                  command=lambda: self.collect_exchange_data("深证")).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(row1, text="收集港交所数据", width=12,
                  command=lambda: self.collect_exchange_data("港交所")).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(row1, text="收集美股数据", width=12,
                  command=lambda: self.collect_exchange_data("美国交易所")).pack(side=tk.LEFT, padx=(0, 5))

        # 第二行：其他类型
        row2 = ttk.Frame(collect_frame)
        row2.pack(fill=tk.X, pady=(0, 5))

        ttk.Button(row2, text="收集加密货币", width=12,
                  command=lambda: self.collect_exchange_data("加密货币")).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(row2, text="收集基金数据", width=12,
                  command=self.collect_fund_data).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(row2, text="收集所有数据", width=12,
                  command=self.collect_all_data).pack(side=tk.LEFT, padx=(0, 5))

        # 筛选和搜索区域
        filter_frame = ttk.LabelFrame(control_frame, text="筛选和搜索", padding="5")
        filter_frame.pack(fill=tk.X, pady=(0, 10))

        # 第一行：交易所筛选
        filter_row1 = ttk.Frame(filter_frame)
        filter_row1.pack(fill=tk.X, pady=(0, 5))

        ttk.Label(filter_row1, text="交易所:").pack(side=tk.LEFT, padx=(0, 5))
        self.exchange_var = tk.StringVar()
        exchange_combo = ttk.Combobox(filter_row1, textvariable=self.exchange_var, width=15, state="readonly")
        exchange_combo['values'] = ("全部", "上证", "深证", "港交所", "美国交易所", "加密货币", "其他")
        exchange_combo.set("全部")
        exchange_combo.pack(side=tk.LEFT, padx=(0, 10))
        exchange_combo.bind('<<ComboboxSelected>>', lambda e: self.filter_data())

        ttk.Label(filter_row1, text="搜索:").pack(side=tk.LEFT, padx=(0, 5))
        self.search_var = tk.StringVar()
        search_entry = ttk.Entry(filter_row1, textvariable=self.search_var, width=25)
        search_entry.pack(side=tk.LEFT, padx=(0, 5))
        search_entry.bind("<Return>", lambda e: self.filter_data())
        search_entry.bind("<KeyRelease>", lambda e: self.root.after(500, self.filter_data))  # 实时搜索

        # 第二行：操作按钮
        filter_row2 = ttk.Frame(filter_frame)
        filter_row2.pack(fill=tk.X)

        ttk.Button(filter_row2, text="立即搜索",
                  command=self.filter_data).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(filter_row2, text="清除筛选",
                  command=self.clear_filters).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(filter_row2, text="刷新数据",
                  command=self.refresh_data).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(filter_row2, text="导出CSV",
                  command=self.export_data).pack(side=tk.LEFT, padx=(0, 5))

    def create_data_area(self):
        """创建数据显示区域"""
        # 创建数据区域框架
        data_frame = ttk.LabelFrame(self.main_frame, text="数据列表", padding="10")
        data_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))

        # 创建进度显示区域
        progress_frame = ttk.Frame(data_frame)
        progress_frame.pack(fill=tk.X, pady=(0, 10))

        # 进度条
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(progress_frame, variable=self.progress_var,
                                          maximum=100, length=400)
        self.progress_bar.pack(side=tk.LEFT, padx=(0, 10))

        # 进度文本
        self.progress_text = tk.StringVar()
        self.progress_text.set("就绪")
        progress_label = ttk.Label(progress_frame, textvariable=self.progress_text)
        progress_label.pack(side=tk.LEFT)

        # 创建表格区域
        table_frame = ttk.Frame(data_frame)
        table_frame.pack(fill=tk.BOTH, expand=True)

        # 创建Treeview显示数据
        columns = ("id", "name", "symbol", "category", "exchange", "source", "timestamp")
        self.tree = ttk.Treeview(table_frame, columns=columns, show="headings")

        # 设置列标题和宽度（优化宽度）
        column_config = {
            "id": ("ID", 60),
            "name": ("名称", 250),
            "symbol": ("代码", 120),
            "category": ("类别", 140),
            "exchange": ("交易所", 140),
            "source": ("数据源", 100),
            "timestamp": ("时间", 160)
        }

        for col, (text, width) in column_config.items():
            self.tree.heading(col, text=text, command=lambda c=col: self.sort_by_column(c))
            self.tree.column(col, width=width, anchor=tk.W)

        # 添加滚动条
        scrollbar_y = ttk.Scrollbar(table_frame, orient=tk.VERTICAL, command=self.tree.yview)
        scrollbar_x = ttk.Scrollbar(table_frame, orient=tk.HORIZONTAL, command=self.tree.xview)
        self.tree.configure(yscrollcommand=scrollbar_y.set, xscrollcommand=scrollbar_x.set)

        # 使用grid布局以获得更好的控制
        self.tree.grid(row=0, column=0, sticky="nsew")
        scrollbar_y.grid(row=0, column=1, sticky="ns")
        scrollbar_x.grid(row=1, column=0, sticky="ew")

        # 设置权重
        table_frame.grid_rowconfigure(0, weight=1)
        table_frame.grid_columnconfigure(0, weight=1)

    def create_status_bar(self):
        """创建状态栏"""
        self.status_frame = ttk.Frame(self.main_frame)
        self.status_frame.pack(fill=tk.X, pady=(10, 0))

        self.status_var = tk.StringVar()
        self.status_var.set("就绪")
        status_label = ttk.Label(self.status_frame, textvariable=self.status_var)
        status_label.pack(side=tk.LEFT)

        # 统计信息
        self.stats_var = tk.StringVar()
        stats_label = ttk.Label(self.status_frame, textvariable=self.stats_var)
        stats_label.pack(side=tk.RIGHT)

    def load_initial_data(self):
        """加载初始数据"""
        self.refresh_data()

    def refresh_data(self):
        """刷新数据显示（显示所有数据）"""
        try:
            self.status_var.set("正在加载数据...")
            self.root.update()

            # 清空现有数据
            for item in self.tree.get_children():
                self.tree.delete(item)

            # 从数据库获取所有数据（不限制数量）
            instruments = self.db_manager.search_instruments(limit=None)

            # 显示数据
            for instrument in instruments:
                values = (
                    instrument.id or "",
                    instrument.name,
                    instrument.symbol,
                    instrument.category,
                    instrument.exchange,
                    instrument.source,
                    instrument.timestamp
                )
                self.tree.insert("", tk.END, values=values)

            # 更新统计信息
            self.update_statistics()
            self.status_var.set(f"已加载 {len(instruments)} 条记录")

            # 清除筛选条件
            self.exchange_var.set("全部")
            self.search_var.set("")

        except Exception as e:
            self.logger.error(f"刷新数据失败: {e}")
            self.status_var.set("数据加载失败")
            messagebox.showerror("错误", f"数据加载失败:\n{str(e)}")

    def filter_data(self):
        """筛选数据（根据交易所和搜索关键词）"""
        try:
            self.status_var.set("正在筛选数据...")
            self.root.update()

            # 获取筛选条件
            exchange_group = self.exchange_var.get()
            keyword = self.search_var.get().strip()

            # 清空现有数据
            for item in self.tree.get_children():
                self.tree.delete(item)

            # 构建查询条件
            search_params = {
                "keyword": keyword if keyword else "",
                "exchange_group": exchange_group if exchange_group != "全部" else "",
                "limit": None  # 不限制数量
            }

            # 搜索数据
            instruments = self.db_manager.search_instruments(**search_params)

            # 显示结果
            for instrument in instruments:
                values = (
                    instrument.id or "",
                    instrument.name,
                    instrument.symbol,
                    instrument.category,
                    instrument.exchange,
                    instrument.source,
                    instrument.timestamp
                )
                self.tree.insert("", tk.END, values=values)

            # 更新状态
            if exchange_group and exchange_group != "全部":
                if keyword:
                    self.status_var.set(f"{exchange_group} - 搜索“{keyword}”: {len(instruments)} 条记录")
                else:
                    self.status_var.set(f"{exchange_group}: {len(instruments)} 条记录")
            elif keyword:
                self.status_var.set(f"搜索“{keyword}”: {len(instruments)} 条记录")
            else:
                self.status_var.set(f"全部数据: {len(instruments)} 条记录")

        except Exception as e:
            self.logger.error(f"筛选数据失败: {e}")
            self.status_var.set("筛选失败")
            messagebox.showerror("错误", f"筛选失败:\n{str(e)}")

    def clear_filters(self):
        """清除筛选条件"""
        self.exchange_var.set("全部")
        self.search_var.set("")
        self.refresh_data()

    def search_data(self):
        """搜索数据（兼容旧方法）"""
        self.filter_data()

    def collect_exchange_data(self, exchange_group):
        """按交易所分组收集数据"""
        self.app_manager.run_in_background(self._collect_exchange_data_bg, exchange_group)

    def _collect_exchange_data_bg(self, exchange_group):
        """后台按交易所收集数据"""
        try:
            # 初始化进度
            self.root.after(0, lambda: self.update_progress(0, f"正在收集{exchange_group}数据..."))

            if exchange_group == "上证" or exchange_group == "深证":
                # 收集A股数据
                self.root.after(0, lambda: self.update_progress(10, f"正在获取A股数据..."))
                result = self.data_collector.collect_stock_data("A股")
                message = f"A股数据收集完成: {result.get('new', 0)}条新数据"
            elif exchange_group == "港交所":
                # 收集港股数据
                self.root.after(0, lambda: self.update_progress(10, f"正在获取港股数据..."))
                result = self.data_collector.collect_stock_data("港股")
                message = f"港股数据收集完成: {result.get('new', 0)}条新数据"
            elif exchange_group == "美国交易所":
                # 收集美股数据
                self.root.after(0, lambda: self.update_progress(10, f"正在获取美股数据..."))
                result = self.data_collector.collect_stock_data("美股")
                message = f"美股数据收集完成: {result.get('new', 0)}条新数据"
            elif exchange_group == "加密货币":
                # 收集加密货币数据
                self.root.after(0, lambda: self.update_progress(10, f"正在获取加密货币数据..."))
                result = self.data_collector.collect_crypto_data("binance")
                message = f"加密货币数据收集完成: {result.get('new', 0)}条新数据"
            else:
                message = f"不支持的交易所类型: {exchange_group}"
                self.root.after(0, lambda: self.app_manager.show_warning("警告", message))
                return

            # 完成进度
            self.root.after(0, lambda: self.update_progress(100, "收集完成"))
            self.root.after(0, lambda: self.app_manager.show_info("收集完成", message))
            self.root.after(0, self.refresh_data)

            # 重置进度条
            self.root.after(2000, lambda: self.update_progress(0, "就绪"))

        except Exception as e:
            self.root.after(0, lambda: self.update_progress(0, "收集失败"))
            self.root.after(0, lambda: self.app_manager.show_error("收集失败", str(e)))

    def collect_stock_data(self):
        """收集股票数据"""
        self.app_manager.run_in_background(self._collect_stock_data_bg)

    def _collect_stock_data_bg(self):
        """后台收集股票数据"""
        try:
            self.root.after(0, lambda: self.status_var.set("正在收集股票数据..."))

            results = []
            for market in self.data_collector.get_available_markets():
                result = self.data_collector.collect_stock_data(market)
                results.append(f"{market}: {result.get('new', 0)}条新数据")

            message = "股票数据收集完成:\n" + "\n".join(results)
            self.root.after(0, lambda: self.app_manager.show_info("收集完成", message))
            self.root.after(0, self.refresh_data)

        except Exception as e:
            self.root.after(0, lambda: self.app_manager.show_error("收集失败", str(e)))

    def collect_fund_data(self):
        """收集基金数据"""
        self.app_manager.run_in_background(self._collect_fund_data_bg)

    def _collect_fund_data_bg(self):
        """后台收集基金数据"""
        try:
            self.root.after(0, lambda: self.status_var.set("正在收集基金数据..."))

            results = []
            for fund_type in self.data_collector.get_available_fund_types():
                result = self.data_collector.collect_fund_data(fund_type)
                results.append(f"{fund_type}: {result.get('new', 0)}条新数据")

            message = "基金数据收集完成:\n" + "\n".join(results)
            self.root.after(0, lambda: self.app_manager.show_info("收集完成", message))
            self.root.after(0, self.refresh_data)

        except Exception as e:
            self.root.after(0, lambda: self.app_manager.show_error("收集失败", str(e)))

    def collect_crypto_data(self):
        """收集加密货币数据"""
        self.app_manager.run_in_background(self._collect_crypto_data_bg)

    def _collect_crypto_data_bg(self):
        """后台收集加密货币数据"""
        try:
            self.root.after(0, lambda: self.status_var.set("正在收集加密货币数据..."))

            result = self.data_collector.collect_crypto_data("binance")
            message = f"加密货币数据收集完成: {result.get('new', 0)}条新数据"

            self.root.after(0, lambda: self.app_manager.show_info("收集完成", message))
            self.root.after(0, self.refresh_data)

        except Exception as e:
            self.root.after(0, lambda: self.app_manager.show_error("收集失败", str(e)))

    def collect_all_data(self):
        """收集所有数据"""
        self.app_manager.run_in_background(self._collect_all_data_bg)

    def _collect_all_data_bg(self):
        """后台收集所有数据"""
        try:
            self.root.after(0, lambda: self.status_var.set("正在收集所有数据..."))

            result = self.data_collector.collect_all_data()
            message = f"数据收集完成: 新增 {result.get('total_new', 0)} 条数据"

            self.root.after(0, lambda: self.app_manager.show_info("收集完成", message))
            self.root.after(0, self.refresh_data)

        except Exception as e:
            self.root.after(0, lambda: self.app_manager.show_error("收集失败", str(e)))

    def export_data(self):
        """导出数据"""
        from tkinter import filedialog

        try:
            file_path = filedialog.asksaveasfilename(
                title="导出数据",
                defaultextension=".csv",
                filetypes=[("CSV文件", "*.csv"), ("所有文件", "*.*")]
            )

            if file_path:
                success = self.db_manager.export_to_csv(file_path)
                if success:
                    self.app_manager.show_info("导出成功", f"数据已导出到:\n{file_path}")
                else:
                    self.app_manager.show_error("导出失败", "数据导出失败")

        except Exception as e:
            self.app_manager.show_error("导出错误", str(e))

    def update_progress(self, value, text=""):
        """更新进度条和文本"""
        self.progress_var.set(value)
        if text:
            self.progress_text.set(text)
        self.root.update_idletasks()

    def sort_by_column(self, col):
        """按列排序"""
        # 简单的排序实现
        pass

    def update_statistics(self):
        """更新统计信息"""
        try:
            # 获取交易所分组统计
            exchange_stats = self.db_manager.get_exchange_group_statistics()
            total = sum(exchange_stats.values())

            # 构建统计信息字符串
            stats_parts = [f"总计: {total}"]

            for exchange, count in exchange_stats.items():
                if count > 0:
                    stats_parts.append(f"{exchange}: {count}")

            self.stats_var.set(" | ".join(stats_parts))

        except Exception as e:
            self.logger.error(f"更新统计信息失败: {e}")
            self.stats_var.set("统计信息获取失败")

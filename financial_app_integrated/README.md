# 金融标的数据管理系统

## 项目结构

```
financial_app_integrated/
├── main.py                 # 主程序入口
├── config.py              # 全局配置
├── requirements.txt       # 依赖包列表
├── README.md             # 项目说明
├── core/                 # 核心模块
│   ├── __init__.py
│   ├── app_manager.py    # 应用管理器
│   └── logger.py         # 日志管理
├── data/                 # 数据模块
│   ├── __init__.py
│   ├── database.py       # 数据库基础操作
│   ├── collectors/       # 数据收集器
│   │   ├── __init__.py
│   │   ├── base.py       # 基础收集器
│   │   ├── stock.py      # 股票数据收集
│   │   ├── fund.py       # 基金数据收集
│   │   └── crypto.py     # 加密货币数据收集
│   └── models.py         # 数据模型
├── ui/                   # 界面模块
│   ├── __init__.py
│   ├── main_window.py    # 主窗口
│   ├── components/       # UI组件
│   │   ├── __init__.py
│   │   ├── search_panel.py    # 搜索面板
│   │   ├── data_table.py      # 数据表格
│   │   ├── control_panel.py   # 控制面板
│   │   └── status_bar.py      # 状态栏
│   └── dialogs/          # 对话框
│       ├── __init__.py
│       ├── settings.py   # 设置对话框
│       └── progress.py   # 进度对话框
└── utils/                # 工具模块
    ├── __init__.py
    ├── helpers.py        # 辅助函数
    └── validators.py     # 数据验证
```

## 模块调用关系

```
main.py
├── core.app_manager.AppManager
│   ├── core.logger.setup_logging()
│   ├── data.database.DatabaseManager
│   ├── data.collectors.*.DataCollector
│   └── ui.main_window.MainWindow
│       ├── ui.components.search_panel.SearchPanel
│       ├── ui.components.data_table.DataTable
│       ├── ui.components.control_panel.ControlPanel
│       └── ui.components.status_bar.StatusBar
└── config (全局配置)
```

## 功能特性

1. **数据抓取**: 支持股票、基金、加密货币数据的自动抓取
2. **数据管理**: SQLite数据库存储，支持增量更新
3. **数据查看**: 直观的表格界面，支持搜索和过滤
4. **数据导出**: 支持CSV格式导出
5. **多线程**: 并行数据抓取，提高效率
6. **日志记录**: 完整的操作日志记录

## 安装和使用

### 方法一：直接运行
1. 安装依赖：`pip install -r requirements.txt`
2. 运行程序：`python main.py`

### 方法二：使用启动脚本
1. 运行启动脚本：`python run.py`
   - 启动脚本会自动检查依赖包
   - 如果缺少依赖会提示安装

## 模块说明

### 核心模块 (core/)
- `app_manager.py`: 应用管理器，负责协调各个模块
- `logger.py`: 日志管理，提供统一的日志配置

### 数据模块 (data/)
- `database.py`: 数据库管理，提供SQLite数据库操作
- `models.py`: 数据模型，定义金融标的数据结构
- `collectors/`: 数据收集器模块
  - `base.py`: 基础收集器，定义通用接口
  - `stock.py`: 股票数据收集器
  - `fund.py`: 基金数据收集器
  - `crypto.py`: 加密货币数据收集器
  - `manager.py`: 收集器管理器，统一管理各种收集器

### UI模块 (ui/)
- `main_window.py`: 主窗口，提供图形界面

### 工具模块 (utils/)
- `helpers.py`: 辅助函数，提供通用工具函数
- `validators.py`: 数据验证器，验证数据格式

## 调用关系

```
main.py
└── AppManager (core/app_manager.py)
    ├── Logger (core/logger.py)
    ├── DatabaseManager (data/database.py)
    ├── CollectorManager (data/collectors/manager.py)
    │   ├── StockCollector (data/collectors/stock.py)
    │   ├── FundCollector (data/collectors/fund.py)
    │   └── CryptoCollector (data/collectors/crypto.py)
    └── MainWindow (ui/main_window.py)
```

## 数据流程

1. **数据收集**: 各个收集器从外部API获取数据
2. **数据处理**: 将原始数据转换为标准的Instrument模型
3. **数据存储**: 通过DatabaseManager存储到SQLite数据库
4. **数据展示**: MainWindow从数据库读取数据并在UI中显示
5. **数据导出**: 支持将数据导出为CSV格式

#!/usr/bin/env python
# -*- coding:utf-8 -*-
"""
数据模型
定义金融标的数据结构
"""

from dataclasses import dataclass
from typing import Optional
import time

@dataclass
class Instrument:
    """金融标的数据模型"""
    
    name: str                    # 标的名称
    symbol: str                  # 标的代码
    category: str                # 分类
    exchange: str = ""           # 交易所
    source: str = ""             # 数据源
    market_cap: Optional[float] = None    # 市值
    price: Optional[float] = None         # 价格
    volume: Optional[float] = None        # 成交量
    verified: int = 0            # 验证状态
    timestamp: Optional[str] = None       # 时间戳
    id: Optional[int] = None     # 数据库ID
    
    def __post_init__(self):
        """初始化后处理"""
        if self.timestamp is None:
            self.timestamp = time.strftime("%Y-%m-%d %H:%M:%S")
    
    def to_dict(self) -> dict:
        """转换为字典"""
        return {
            'name': self.name,
            'symbol': self.symbol,
            'category': self.category,
            'exchange': self.exchange,
            'source': self.source,
            'market_cap': self.market_cap,
            'price': self.price,
            'volume': self.volume,
            'verified': self.verified,
            'timestamp': self.timestamp
        }
    
    @classmethod
    def from_dict(cls, data: dict) -> 'Instrument':
        """从字典创建实例"""
        return cls(
            id=data.get('id'),
            name=data.get('name', ''),
            symbol=data.get('symbol', ''),
            category=data.get('category', ''),
            exchange=data.get('exchange', ''),
            source=data.get('source', ''),
            market_cap=data.get('market_cap'),
            price=data.get('price'),
            volume=data.get('volume'),
            verified=data.get('verified', 0),
            timestamp=data.get('timestamp')
        )
    
    @classmethod
    def from_row(cls, row) -> 'Instrument':
        """从数据库行创建实例"""
        return cls(
            id=row['id'] if 'id' in row.keys() else None,
            name=row['name'],
            symbol=row['symbol'],
            category=row['category'],
            exchange=row['exchange'] or '',
            source=row['source'] or '',
            market_cap=row['market_cap'] if 'market_cap' in row.keys() else None,
            price=row['price'] if 'price' in row.keys() else None,
            volume=row['volume'] if 'volume' in row.keys() else None,
            verified=row['verified'] if 'verified' in row.keys() else 0,
            timestamp=row['timestamp']
        )
    
    def is_stock(self) -> bool:
        """是否为股票"""
        return self.category.startswith('股票/')
    
    def is_fund(self) -> bool:
        """是否为基金"""
        return self.category.startswith('基金/')
    
    def is_crypto(self) -> bool:
        """是否为加密货币"""
        return self.category.startswith('加密货币/')
    
    def get_market(self) -> str:
        """获取市场类型"""
        if '/' in self.category:
            return self.category.split('/', 1)[1]
        return self.category
    
    def __str__(self) -> str:
        """字符串表示"""
        return f"{self.name}({self.symbol}) - {self.category}"

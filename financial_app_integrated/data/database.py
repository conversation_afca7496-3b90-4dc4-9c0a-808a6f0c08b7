#!/usr/bin/env python
# -*- coding:utf-8 -*-
"""
数据库管理模块
负责数据库的基础操作
"""

import sqlite3
import pandas as pd
import logging
import shutil
import time
from pathlib import Path
from typing import List, Dict, Optional
from config import DATABASE_CONFIG, TABLE_SCHEMA, DB_PATH, BACKUP_DIR
from .models import Instrument

class DatabaseManager:
    """数据库管理器"""

    def __init__(self, db_path: str = None):
        """初始化数据库管理器"""
        self.db_path = db_path or DB_PATH
        self.logger = logging.getLogger(__name__)

    def init_database(self) -> bool:
        """初始化数据库"""
        try:
            self.db_path.parent.mkdir(parents=True, exist_ok=True)

            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # 创建表
            cursor.execute(TABLE_SCHEMA)

            # 创建索引
            indexes = [
                "CREATE INDEX IF NOT EXISTS idx_symbol ON instruments(symbol)",
                "CREATE INDEX IF NOT EXISTS idx_category ON instruments(category)",
                "CREATE INDEX IF NOT EXISTS idx_source ON instruments(source)"
            ]

            for index_sql in indexes:
                cursor.execute(index_sql)

            conn.commit()
            conn.close()

            self.logger.info("数据库初始化成功")
            return True

        except Exception as e:
            self.logger.error(f"数据库初始化失败: {e}")
            return False

    def get_connection(self) -> sqlite3.Connection:
        """获取数据库连接"""
        conn = sqlite3.connect(
            self.db_path,
            timeout=DATABASE_CONFIG["timeout"],
            check_same_thread=DATABASE_CONFIG["check_same_thread"]
        )
        conn.row_factory = sqlite3.Row
        return conn

    def insert_instrument(self, instrument: Instrument) -> bool:
        """插入单个标的"""
        try:
            conn = self.get_connection()
            cursor = conn.cursor()

            data = instrument.to_dict()
            fields = list(data.keys())
            values = list(data.values())
            placeholders = ",".join(["?" for _ in fields])

            query = f"INSERT OR REPLACE INTO instruments ({','.join(fields)}) VALUES ({placeholders})"
            cursor.execute(query, values)

            conn.commit()
            conn.close()
            return True

        except Exception as e:
            self.logger.error(f"插入标的失败: {e}")
            return False

    def batch_insert_instruments(self, instruments: List[Instrument]) -> int:
        """批量插入标的"""
        success_count = 0

        try:
            conn = self.get_connection()
            cursor = conn.cursor()

            for instrument in instruments:
                try:
                    data = instrument.to_dict()
                    fields = list(data.keys())
                    values = list(data.values())
                    placeholders = ",".join(["?" for _ in fields])

                    query = f"INSERT OR REPLACE INTO instruments ({','.join(fields)}) VALUES ({placeholders})"
                    cursor.execute(query, values)
                    success_count += 1

                except Exception as e:
                    self.logger.warning(f"插入单条数据失败: {e}")

            conn.commit()
            conn.close()

        except Exception as e:
            self.logger.error(f"批量插入失败: {e}")

        return success_count

    def search_instruments(self, keyword: str = "", category: str = "",
                          source: str = "", exchange_group: str = "", limit: int = None) -> List[Instrument]:
        """搜索标的

        Args:
            keyword: 搜索关键词
            category: 分类过滤
            source: 数据源过滤
            exchange_group: 交易所分组过滤
            limit: 结果数量限制，None表示不限制
        """
        try:
            query = "SELECT * FROM instruments WHERE 1=1"
            params = []

            if keyword:
                query += " AND (name LIKE ? OR symbol LIKE ?)"
                params.extend([f"%{keyword}%", f"%{keyword}%"])

            if category:
                query += " AND category = ?"
                params.append(category)

            if source:
                query += " AND source = ?"
                params.append(source)

            # 根据交易所分组筛选
            if exchange_group:
                if exchange_group == "上证":
                    query += " AND exchange = '上海证券交易所'"
                elif exchange_group == "深证":
                    query += " AND exchange = '深圳证券交易所'"
                elif exchange_group == "港交所":
                    query += " AND exchange = '香港交易所'"
                elif exchange_group == "美国交易所":
                    query += " AND exchange = '美国交易所'"
                elif exchange_group == "加密货币":
                    query += " AND category LIKE '加密货币%'"
                elif exchange_group == "其他":
                    query += " AND exchange NOT IN ('上海证券交易所', '深圳证券交易所', '香港交易所', '美国交易所') AND category NOT LIKE '加密货币%'"

            query += " ORDER BY timestamp DESC"

            if limit and limit > 0:
                query += " LIMIT ?"
                params.append(limit)

            conn = self.get_connection()
            cursor = conn.cursor()
            cursor.execute(query, params)
            rows = cursor.fetchall()
            conn.close()

            return [Instrument.from_row(row) for row in rows]

        except Exception as e:
            self.logger.error(f"搜索标的失败: {e}")
            return []

    def get_statistics(self) -> Dict[str, int]:
        """获取统计信息"""
        stats = {}

        try:
            conn = self.get_connection()
            cursor = conn.cursor()

            # 总数量
            cursor.execute("SELECT COUNT(*) as total FROM instruments")
            result = cursor.fetchone()
            stats["total"] = result["total"] if result else 0

            # 按分类统计
            cursor.execute("SELECT category, COUNT(*) as count FROM instruments GROUP BY category")
            for row in cursor.fetchall():
                stats[row["category"]] = row["count"]

            # 按数据源统计
            cursor.execute("SELECT source, COUNT(*) as count FROM instruments GROUP BY source")
            for row in cursor.fetchall():
                stats[f"source_{row['source']}"] = row["count"]

            conn.close()

        except Exception as e:
            self.logger.error(f"获取统计信息失败: {e}")

        return stats

    def get_exchange_group_statistics(self) -> Dict[str, int]:
        """获取交易所分组统计信息"""
        stats = {}

        try:
            conn = self.get_connection()
            cursor = conn.cursor()

            # 上证
            cursor.execute("SELECT COUNT(*) FROM instruments WHERE exchange = '上海证券交易所'")
            stats["上证"] = cursor.fetchone()[0]

            # 深证
            cursor.execute("SELECT COUNT(*) FROM instruments WHERE exchange = '深圳证券交易所'")
            stats["深证"] = cursor.fetchone()[0]

            # 港交所
            cursor.execute("SELECT COUNT(*) FROM instruments WHERE exchange = '香港交易所'")
            stats["港交所"] = cursor.fetchone()[0]

            # 美国交易所
            cursor.execute("SELECT COUNT(*) FROM instruments WHERE exchange = '美国交易所'")
            stats["美国交易所"] = cursor.fetchone()[0]

            # 加密货币
            cursor.execute("SELECT COUNT(*) FROM instruments WHERE category LIKE '加密货币%'")
            stats["加密货币"] = cursor.fetchone()[0]

            # 其他
            cursor.execute("""
                SELECT COUNT(*) FROM instruments
                WHERE exchange NOT IN ('上海证券交易所', '深圳证券交易所', '香港交易所', '美国交易所')
                AND category NOT LIKE '加密货币%'
            """)
            stats["其他"] = cursor.fetchone()[0]

            conn.close()

        except Exception as e:
            self.logger.error(f"获取交易所分组统计失败: {e}")

        return stats

    def export_to_csv(self, file_path: str, category: str = None) -> bool:
        """导出到CSV"""
        try:
            query = "SELECT * FROM instruments"
            params = None

            if category:
                query += " WHERE category = ?"
                params = (category,)

            conn = self.get_connection()
            df = pd.read_sql_query(query, conn, params=params)
            conn.close()

            df.to_csv(file_path, index=False, encoding='utf-8-sig')
            self.logger.info(f"数据导出成功: {file_path}")
            return True

        except Exception as e:
            self.logger.error(f"数据导出失败: {e}")
            return False

    def backup_database(self) -> str:
        """备份数据库"""
        try:
            timestamp = time.strftime("%Y%m%d_%H%M%S")
            backup_path = BACKUP_DIR / f"financial_database_backup_{timestamp}.db"

            shutil.copy2(self.db_path, backup_path)
            self.logger.info(f"数据库备份成功: {backup_path}")
            return str(backup_path)

        except Exception as e:
            self.logger.error(f"数据库备份失败: {e}")
            return ""

    def get_existing_symbols(self, category: str = None) -> set:
        """获取已存在的标的代码"""
        try:
            query = "SELECT symbol FROM instruments"
            params = None

            if category:
                query += " WHERE category = ?"
                params = (category,)

            conn = self.get_connection()
            cursor = conn.cursor()
            cursor.execute(query, params)
            rows = cursor.fetchall()
            conn.close()

            return set(row["symbol"] for row in rows)

        except Exception as e:
            self.logger.error(f"获取已存在标的失败: {e}")
            return set()

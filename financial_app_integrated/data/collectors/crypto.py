#!/usr/bin/env python
# -*- coding:utf-8 -*-
"""
加密货币数据收集器
负责收集各交易所的加密货币数据
"""

import ccxt
import time
from typing import Dict, List, Optional
from config import CCXT_CONFIG, CATEGORY_MAPPING, FETCH_CONFIG
from .base import BaseCollector
from ..models import Instrument

class CryptoCollector(BaseCollector):
    """加密货币数据收集器"""
    
    def __init__(self, db_manager):
        """初始化加密货币收集器"""
        super().__init__(db_manager)
        self.exchanges = {}
        self._init_exchanges()
    
    def _init_exchanges(self):
        """初始化交易所"""
        for exchange_name in CCXT_CONFIG["exchanges"]:
            try:
                exchange_class = getattr(ccxt, exchange_name)
                self.exchanges[exchange_name] = exchange_class({
                    'timeout': self.timeout * 1000,
                    'enableRateLimit': True,
                })
                self.logger.info(f"初始化交易所成功: {exchange_name}")
            except Exception as e:
                self.logger.warning(f"初始化交易所失败 {exchange_name}: {e}")
    
    def collect_data(self, exchange_name: str = "binance", incremental: bool = True, **kwargs) -> Dict[str, any]:
        """收集加密货币数据
        
        Args:
            exchange_name: 交易所名称
            incremental: 是否增量更新
            
        Returns:
            Dict: 收集结果
        """
        self.logger.info(f"开始收集{exchange_name}加密货币数据...")
        
        if exchange_name not in self.exchanges:
            return self.create_result(False, message=f"交易所 {exchange_name} 未初始化")
        
        try:
            # 获取已存在的标的代码
            existing_symbols = set()
            if incremental:
                category = CATEGORY_MAPPING["加密货币"]["现货"]
                existing_symbols = self.get_existing_symbols(category)
                self.logger.info(f"{exchange_name}已存在 {len(existing_symbols)} 个标的")
            
            # 获取市场数据
            exchange = self.exchanges[exchange_name]
            markets = self.retry_on_failure(exchange.load_markets)
            
            # 处理数据
            instruments = self._process_crypto_data(exchange, markets, existing_symbols)
            
            # 保存数据
            if instruments:
                success_count = self.save_instruments(instruments)
                result = self.create_result(
                    True,
                    exchange=exchange_name,
                    total=len(markets),
                    new=success_count,
                    skipped=len(markets) - len(instruments)
                )
            else:
                result = self.create_result(False, message=f"{exchange_name}没有新数据需要添加")
            
            self.log_collection_result(result)
            return result
            
        except Exception as e:
            result = self.create_result(False, message=str(e))
            self.log_collection_result(result)
            return result
    
    def _process_crypto_data(self, exchange, markets: dict, existing_symbols: set) -> List[Instrument]:
        """处理加密货币数据
        
        Args:
            exchange: 交易所对象
            markets: 市场数据
            existing_symbols: 已存在的标的代码
            
        Returns:
            List[Instrument]: 处理后的标的列表
        """
        instruments = []
        processed_count = 0
        
        for symbol, market in markets.items():
            try:
                # 只收集指定的报价货币交易对
                if not any(symbol.endswith(f"/{quote}") for quote in CCXT_CONFIG["quote_currencies"]):
                    continue
                
                # 检查是否已存在
                if symbol in existing_symbols:
                    continue
                
                # 获取ticker数据
                ticker = self._get_ticker_safely(exchange, symbol)
                
                # 创建标的对象
                instrument = Instrument(
                    name=market["base"],
                    symbol=symbol,
                    category=CATEGORY_MAPPING["加密货币"]["现货"],
                    exchange=exchange.name.upper(),
                    source="ccxt",
                    price=ticker.get("last") if ticker else None,
                    volume=ticker.get("baseVolume") if ticker else None,
                    verified=1
                )
                
                instruments.append(instrument)
                processed_count += 1
                
                # 限制数量
                if processed_count >= CCXT_CONFIG["max_symbols"]:
                    break
                
                # 添加延迟避免频率限制
                time.sleep(FETCH_CONFIG["delay"] / 1000)
                
            except Exception as e:
                self.logger.warning(f"处理加密货币 {symbol} 失败: {e}")
                continue
        
        return instruments
    
    def _get_ticker_safely(self, exchange, symbol: str) -> Optional[dict]:
        """安全地获取ticker数据
        
        Args:
            exchange: 交易所对象
            symbol: 交易对符号
            
        Returns:
            Optional[dict]: ticker数据
        """
        try:
            return exchange.fetch_ticker(symbol)
        except Exception as e:
            self.logger.warning(f"获取 {symbol} ticker失败: {e}")
            return None

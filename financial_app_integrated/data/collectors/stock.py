#!/usr/bin/env python
# -*- coding:utf-8 -*-
"""
股票数据收集器
负责收集A股、港股、美股数据
"""

import akshare as ak
import pandas as pd
from typing import Dict, List, Optional, Tuple
from config import AKSHARE_CONFIG, CATEGORY_MAPPING
from .base import BaseCollector
from ..models import Instrument

class StockCollector(BaseCollector):
    """股票数据收集器"""

    def collect_data(self, market: str = "A股", incremental: bool = True, **kwargs) -> Dict[str, any]:
        """收集股票数据

        Args:
            market: 市场类型 ("A股", "港股", "美股")
            incremental: 是否增量更新

        Returns:
            Dict: 收集结果
        """
        self.logger.info(f"开始收集{market}数据...")

        try:
            # 获取已存在的标的代码
            existing_symbols = set()
            if incremental:
                category = CATEGORY_MAPPING["股票"][market]
                existing_symbols = self.get_existing_symbols(category)
                self.logger.info(f"{market}已存在 {len(existing_symbols)} 个标的")

            # 获取股票数据
            df = self._get_stock_data(market)
            if df is None or df.empty:
                return self.create_result(False, message=f"未获取到{market}数据")

            # 处理数据
            instruments = self._process_stock_data(df, market, existing_symbols)

            # 保存数据
            if instruments:
                success_count = self.save_instruments(instruments)
                result = self.create_result(
                    True,
                    market=market,
                    total=len(df),
                    new=success_count,
                    skipped=len(df) - len(instruments)
                )
            else:
                result = self.create_result(False, message=f"{market}没有新数据需要添加")

            self.log_collection_result(result)
            return result

        except Exception as e:
            result = self.create_result(False, message=str(e))
            self.log_collection_result(result)
            return result

    def _get_stock_data(self, market: str) -> Optional[pd.DataFrame]:
        """获取股票数据

        Args:
            market: 市场类型

        Returns:
            Optional[pd.DataFrame]: 股票数据
        """
        methods = {
            "A股": [
                lambda: ak.stock_zh_a_spot_em(),
                lambda: ak.stock_zh_a_spot(),
                lambda: ak.stock_sh_a_spot_em(),
            ],
            "港股": [
                lambda: ak.stock_hk_spot_em(),
                lambda: ak.stock_hk_spot(),
                lambda: ak.stock_hk_main_board_spot_em(),
            ],
            "美股": [
                lambda: ak.stock_us_spot_em(),
                lambda: ak.stock_us_spot(),
            ]
        }

        if market not in methods:
            self.logger.error(f"不支持的市场类型: {market}")
            return None

        for method in methods[market]:
            try:
                df = self.retry_on_failure(method)
                if isinstance(df, pd.DataFrame) and not df.empty:
                    self.logger.info(f"成功获取{market}数据: {len(df)}条")
                    max_records = AKSHARE_CONFIG["max_records_per_type"]
                    if max_records and max_records > 0:
                        return df.head(max_records)
                    else:
                        return df  # 不限制数量
            except Exception as e:
                self.logger.warning(f"{market}数据获取方法失败: {e}")
                continue

        return None

    def _process_stock_data(self, df: pd.DataFrame, market: str, existing_symbols: set) -> List[Instrument]:
        """处理股票数据

        Args:
            df: 原始数据
            market: 市场类型
            existing_symbols: 已存在的标的代码

        Returns:
            List[Instrument]: 处理后的标的列表
        """
        instruments = []

        for _, row in df.iterrows():
            try:
                symbol, name, exchange = self._extract_stock_info(row, market)

                if not symbol or not name:
                    continue

                # 检查是否已存在
                if symbol in existing_symbols:
                    continue

                # 创建标的对象
                instrument = Instrument(
                    name=name,
                    symbol=symbol,
                    category=CATEGORY_MAPPING["股票"][market],
                    exchange=exchange,
                    source="akshare",
                    verified=1
                )

                # 添加价格信息
                self._add_price_info(instrument, row)

                instruments.append(instrument)

            except Exception as e:
                self.logger.warning(f"处理{market}数据行失败: {e}")
                continue

        return instruments

    def _extract_stock_info(self, row: pd.Series, market: str) -> Tuple[str, str, str]:
        """提取股票信息

        Args:
            row: 数据行
            market: 市场类型

        Returns:
            Tuple[str, str, str]: (代码, 名称, 交易所)
        """
        symbol = ""
        name = ""
        exchange = ""

        # 提取代码
        for col in ["代码", "symbol", "Symbol", "股票代码"]:
            if col in row.index and pd.notna(row[col]):
                symbol = str(row[col]).strip()
                break

        # 提取名称
        for col in ["名称", "name", "Name", "股票名称", "简称"]:
            if col in row.index and pd.notna(row[col]):
                name = str(row[col]).strip()
                break

        # 设置交易所
        exchange = self._get_exchange(symbol, market)

        return symbol, name, exchange

    def _get_exchange(self, symbol: str, market: str) -> str:
        """根据代码和市场确定交易所

        Args:
            symbol: 股票代码
            market: 市场类型

        Returns:
            str: 交易所名称
        """
        if market == "A股":
            if symbol.startswith("6"):
                return "上海证券交易所"
            elif symbol.startswith(("0", "3")):
                return "深圳证券交易所"
            elif symbol.startswith("8"):
                return "北京证券交易所"
            else:
                return "A股"
        elif market == "港股":
            return "香港交易所"
        elif market == "美股":
            return "美国交易所"
        else:
            return market

    def _add_price_info(self, instrument: Instrument, row: pd.Series):
        """添加价格信息

        Args:
            instrument: 标的对象
            row: 数据行
        """
        # 添加价格
        for col in ["price", "close", "最新价", "现价"]:
            if col in row.index and pd.notna(row[col]):
                try:
                    instrument.price = float(row[col])
                    break
                except (ValueError, TypeError):
                    continue

        # 添加成交量
        for col in ["volume", "成交量", "成交额"]:
            if col in row.index and pd.notna(row[col]):
                try:
                    instrument.volume = float(row[col])
                    break
                except (ValueError, TypeError):
                    continue

#!/usr/bin/env python
# -*- coding:utf-8 -*-
"""
基础数据收集器
定义数据收集的基础接口和通用功能
"""

import logging
import time
from abc import ABC, abstractmethod
from typing import List, Dict, Optional
from config import FETCH_CONFIG
from ..models import Instrument

class BaseCollector(ABC):
    """基础数据收集器抽象类"""
    
    def __init__(self, db_manager):
        """初始化收集器
        
        Args:
            db_manager: 数据库管理器
        """
        self.db_manager = db_manager
        self.logger = logging.getLogger(self.__class__.__name__)
        
        # 收集配置
        self.timeout = FETCH_CONFIG["timeout"]
        self.retry_times = FETCH_CONFIG["retry_times"]
        self.delay = FETCH_CONFIG["delay"]
    
    @abstractmethod
    def collect_data(self, incremental: bool = True, **kwargs) -> Dict[str, any]:
        """收集数据的抽象方法
        
        Args:
            incremental: 是否增量更新
            **kwargs: 其他参数
            
        Returns:
            Dict: 收集结果
        """
        pass
    
    def get_existing_symbols(self, category: str) -> set:
        """获取已存在的标的代码
        
        Args:
            category: 分类
            
        Returns:
            set: 已存在的标的代码集合
        """
        return self.db_manager.get_existing_symbols(category)
    
    def save_instruments(self, instruments: List[Instrument]) -> int:
        """保存标的数据
        
        Args:
            instruments: 标的列表
            
        Returns:
            int: 成功保存的数量
        """
        if not instruments:
            return 0
        
        return self.db_manager.batch_insert_instruments(instruments)
    
    def retry_on_failure(self, func, *args, **kwargs):
        """失败重试装饰器
        
        Args:
            func: 要执行的函数
            *args: 函数参数
            **kwargs: 函数关键字参数
            
        Returns:
            函数执行结果
        """
        last_exception = None
        
        for attempt in range(self.retry_times):
            try:
                return func(*args, **kwargs)
            except Exception as e:
                last_exception = e
                self.logger.warning(f"第{attempt + 1}次尝试失败: {e}")
                if attempt < self.retry_times - 1:
                    time.sleep(self.delay * (attempt + 1))  # 递增延迟
        
        # 所有重试都失败
        self.logger.error(f"重试{self.retry_times}次后仍然失败: {last_exception}")
        raise last_exception
    
    def log_collection_result(self, result: Dict[str, any]):
        """记录收集结果
        
        Args:
            result: 收集结果字典
        """
        if result.get("success"):
            self.logger.info(
                f"数据收集成功 - 新增: {result.get('new', 0)}, "
                f"跳过: {result.get('skipped', 0)}, "
                f"总计: {result.get('total', 0)}"
            )
        else:
            self.logger.error(f"数据收集失败: {result.get('message', '未知错误')}")
    
    def create_result(self, success: bool, **kwargs) -> Dict[str, any]:
        """创建标准的结果字典
        
        Args:
            success: 是否成功
            **kwargs: 其他结果数据
            
        Returns:
            Dict: 结果字典
        """
        result = {"success": success}
        result.update(kwargs)
        return result

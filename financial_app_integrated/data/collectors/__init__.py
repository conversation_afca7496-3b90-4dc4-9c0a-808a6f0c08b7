"""
数据收集器模块
包含各种数据源的收集器
"""

from .base import BaseCollector
from .stock import StockCollector
from .fund import FundCollector
from .crypto import CryptoCollector

def create_data_collector(db_manager):
    """创建数据收集器工厂函数"""
    from .manager import CollectorManager
    return CollectorManager(db_manager)

__all__ = ['BaseCollector', 'StockCollector', 'FundCollector', 'CryptoCollector', 'create_data_collector']

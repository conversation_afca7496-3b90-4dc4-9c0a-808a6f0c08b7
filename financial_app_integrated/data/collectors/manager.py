#!/usr/bin/env python
# -*- coding:utf-8 -*-
"""
数据收集器管理器
统一管理各种数据收集器
"""

import concurrent.futures
import logging
from typing import Dict, List
from config import FETCH_CONFIG, AKSHARE_CONFIG, CCXT_CONFIG
from .stock import StockCollector
from .fund import FundCollector
from .crypto import CryptoCollector

class CollectorManager:
    """数据收集器管理器"""
    
    def __init__(self, db_manager):
        """初始化收集器管理器
        
        Args:
            db_manager: 数据库管理器
        """
        self.db_manager = db_manager
        self.logger = logging.getLogger(__name__)
        
        # 初始化各种收集器
        self.stock_collector = StockCollector(db_manager)
        self.fund_collector = FundCollector(db_manager)
        self.crypto_collector = CryptoCollector(db_manager)
    
    def collect_stock_data(self, market: str = "A股", incremental: bool = True) -> Dict[str, any]:
        """收集股票数据
        
        Args:
            market: 市场类型
            incremental: 是否增量更新
            
        Returns:
            Dict: 收集结果
        """
        return self.stock_collector.collect_data(market, incremental)
    
    def collect_fund_data(self, fund_type: str = "公募基金", incremental: bool = True) -> Dict[str, any]:
        """收集基金数据
        
        Args:
            fund_type: 基金类型
            incremental: 是否增量更新
            
        Returns:
            Dict: 收集结果
        """
        return self.fund_collector.collect_data(fund_type, incremental)
    
    def collect_crypto_data(self, exchange_name: str = "binance", incremental: bool = True) -> Dict[str, any]:
        """收集加密货币数据
        
        Args:
            exchange_name: 交易所名称
            incremental: 是否增量更新
            
        Returns:
            Dict: 收集结果
        """
        return self.crypto_collector.collect_data(exchange_name, incremental)
    
    def collect_all_data(self, incremental: bool = True) -> Dict[str, any]:
        """收集所有类型的数据
        
        Args:
            incremental: 是否增量更新
            
        Returns:
            Dict: 收集结果统计
        """
        self.logger.info("开始收集所有数据...")
        results = {}
        
        # 使用线程池并行收集数据
        with concurrent.futures.ThreadPoolExecutor(max_workers=FETCH_CONFIG["max_workers"]) as executor:
            futures = {}
            
            # 提交股票数据收集任务
            for market in AKSHARE_CONFIG["stock_markets"]:
                future = executor.submit(self.collect_stock_data, market, incremental)
                futures[f"stock_{market}"] = future
            
            # 提交基金数据收集任务
            for fund_type in AKSHARE_CONFIG["fund_types"]:
                future = executor.submit(self.collect_fund_data, fund_type, incremental)
                futures[f"fund_{fund_type}"] = future
            
            # 提交加密货币数据收集任务
            for exchange_name in CCXT_CONFIG["exchanges"]:
                future = executor.submit(self.collect_crypto_data, exchange_name, incremental)
                futures[f"crypto_{exchange_name}"] = future
            
            # 收集结果
            for key, future in futures.items():
                try:
                    result = future.result(timeout=FETCH_CONFIG["timeout"])
                    results[key] = result
                except Exception as e:
                    self.logger.error(f"收集任务 {key} 失败: {e}")
                    results[key] = {"success": False, "message": str(e)}
        
        # 统计总体结果
        total_new = sum(r.get("new", 0) for r in results.values() if r.get("success"))
        total_skipped = sum(r.get("skipped", 0) for r in results.values() if r.get("success"))
        
        self.logger.info(f"数据收集完成: 新增 {total_new}, 跳过 {total_skipped}")
        
        return {
            "success": True,
            "total_new": total_new,
            "total_skipped": total_skipped,
            "details": results
        }
    
    def get_available_markets(self) -> List[str]:
        """获取可用的股票市场列表"""
        return AKSHARE_CONFIG["stock_markets"]
    
    def get_available_fund_types(self) -> List[str]:
        """获取可用的基金类型列表"""
        return AKSHARE_CONFIG["fund_types"]
    
    def get_available_exchanges(self) -> List[str]:
        """获取可用的加密货币交易所列表"""
        return CCXT_CONFIG["exchanges"]

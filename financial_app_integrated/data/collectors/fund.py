#!/usr/bin/env python
# -*- coding:utf-8 -*-
"""
基金数据收集器
负责收集公募基金、ETF基金数据
"""

import akshare as ak
import pandas as pd
from typing import Dict, List, Optional, Tuple
from config import AKSHARE_CONFIG, CATEGORY_MAPPING
from .base import BaseCollector
from ..models import Instrument

class FundCollector(BaseCollector):
    """基金数据收集器"""
    
    def collect_data(self, fund_type: str = "公募基金", incremental: bool = True, **kwargs) -> Dict[str, any]:
        """收集基金数据
        
        Args:
            fund_type: 基金类型 ("公募基金", "ETF基金")
            incremental: 是否增量更新
            
        Returns:
            Dict: 收集结果
        """
        self.logger.info(f"开始收集{fund_type}数据...")
        
        try:
            # 获取已存在的标的代码
            existing_symbols = set()
            if incremental:
                category = CATEGORY_MAPPING["基金"][fund_type]
                existing_symbols = self.get_existing_symbols(category)
                self.logger.info(f"{fund_type}已存在 {len(existing_symbols)} 个标的")
            
            # 获取基金数据
            df = self._get_fund_data(fund_type)
            if df is None or df.empty:
                return self.create_result(False, message=f"未获取到{fund_type}数据")
            
            # 处理数据
            instruments = self._process_fund_data(df, fund_type, existing_symbols)
            
            # 保存数据
            if instruments:
                success_count = self.save_instruments(instruments)
                result = self.create_result(
                    True,
                    fund_type=fund_type,
                    total=len(df),
                    new=success_count,
                    skipped=len(df) - len(instruments)
                )
            else:
                result = self.create_result(False, message=f"{fund_type}没有新数据需要添加")
            
            self.log_collection_result(result)
            return result
            
        except Exception as e:
            result = self.create_result(False, message=str(e))
            self.log_collection_result(result)
            return result
    
    def _get_fund_data(self, fund_type: str) -> Optional[pd.DataFrame]:
        """获取基金数据
        
        Args:
            fund_type: 基金类型
            
        Returns:
            Optional[pd.DataFrame]: 基金数据
        """
        if fund_type == "公募基金":
            return self._get_public_fund_data()
        elif fund_type == "ETF基金":
            return self._get_etf_fund_data()
        else:
            self.logger.error(f"不支持的基金类型: {fund_type}")
            return None
    
    def _get_public_fund_data(self) -> Optional[pd.DataFrame]:
        """获取公募基金数据"""
        try:
            df = self.retry_on_failure(ak.fund_em_fund_name)
            if isinstance(df, pd.DataFrame) and not df.empty:
                self.logger.info(f"成功获取公募基金数据: {len(df)}条")
                return df.head(AKSHARE_CONFIG["max_records_per_type"])
            return None
        except Exception as e:
            self.logger.error(f"获取公募基金数据失败: {e}")
            return None
    
    def _get_etf_fund_data(self) -> Optional[pd.DataFrame]:
        """获取ETF基金数据"""
        methods = [
            lambda: ak.fund_etf_category_sina("ETF基金"),
            lambda: ak.fund_etf_fund_info_em(),
        ]
        
        for method in methods:
            try:
                df = self.retry_on_failure(method)
                if isinstance(df, pd.DataFrame) and not df.empty:
                    self.logger.info(f"成功获取ETF基金数据: {len(df)}条")
                    return df.head(AKSHARE_CONFIG["max_records_per_type"])
            except Exception as e:
                self.logger.warning(f"ETF数据获取方法失败: {e}")
                continue
        
        return None
    
    def _process_fund_data(self, df: pd.DataFrame, fund_type: str, existing_symbols: set) -> List[Instrument]:
        """处理基金数据
        
        Args:
            df: 原始数据
            fund_type: 基金类型
            existing_symbols: 已存在的标的代码
            
        Returns:
            List[Instrument]: 处理后的标的列表
        """
        instruments = []
        
        for _, row in df.iterrows():
            try:
                symbol, name = self._extract_fund_info(row)
                
                if not symbol or not name:
                    continue
                
                # 检查是否已存在
                if symbol in existing_symbols:
                    continue
                
                # 创建标的对象
                instrument = Instrument(
                    name=name,
                    symbol=symbol,
                    category=CATEGORY_MAPPING["基金"][fund_type],
                    exchange="基金公司" if fund_type == "公募基金" else "交易所",
                    source="akshare",
                    verified=1
                )
                
                instruments.append(instrument)
                
            except Exception as e:
                self.logger.warning(f"处理{fund_type}数据行失败: {e}")
                continue
        
        return instruments
    
    def _extract_fund_info(self, row: pd.Series) -> Tuple[str, str]:
        """提取基金信息
        
        Args:
            row: 数据行
            
        Returns:
            Tuple[str, str]: (代码, 名称)
        """
        symbol = ""
        name = ""
        
        # 提取代码
        for col in ["基金代码", "代码", "symbol", "Symbol"]:
            if col in row.index and pd.notna(row[col]):
                symbol = str(row[col]).strip()
                break
        
        # 提取名称
        for col in ["基金简称", "基金名称", "名称", "name", "Name"]:
            if col in row.index and pd.notna(row[col]):
                name = str(row[col]).strip()
                break
        
        return symbol, name

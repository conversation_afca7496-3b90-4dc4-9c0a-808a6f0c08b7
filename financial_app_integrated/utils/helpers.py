#!/usr/bin/env python
# -*- coding:utf-8 -*-
"""
辅助函数
提供各种通用的辅助功能
"""

import re
from typing import Optional, Union
from datetime import datetime

def format_number(value: Union[int, float, str], decimal_places: int = 2) -> str:
    """格式化数字显示
    
    Args:
        value: 数字值
        decimal_places: 小数位数
        
    Returns:
        str: 格式化后的字符串
    """
    try:
        if value is None or value == "":
            return "-"
        
        num = float(value)
        if num == 0:
            return "0"
        
        # 处理大数字
        if abs(num) >= 1e8:
            return f"{num/1e8:.{decimal_places}f}亿"
        elif abs(num) >= 1e4:
            return f"{num/1e4:.{decimal_places}f}万"
        else:
            return f"{num:.{decimal_places}f}"
            
    except (ValueError, TypeError):
        return str(value)

def format_date(date_str: str, input_format: str = "%Y-%m-%d %H:%M:%S", 
                output_format: str = "%Y-%m-%d") -> str:
    """格式化日期显示
    
    Args:
        date_str: 日期字符串
        input_format: 输入格式
        output_format: 输出格式
        
    Returns:
        str: 格式化后的日期字符串
    """
    try:
        if not date_str:
            return "-"
        
        dt = datetime.strptime(date_str, input_format)
        return dt.strftime(output_format)
        
    except (ValueError, TypeError):
        return str(date_str)

def safe_float(value: Union[str, int, float]) -> Optional[float]:
    """安全转换为浮点数
    
    Args:
        value: 要转换的值
        
    Returns:
        Optional[float]: 转换后的浮点数，失败返回None
    """
    try:
        if value is None or value == "":
            return None
        return float(value)
    except (ValueError, TypeError):
        return None

def clean_string(text: str) -> str:
    """清理字符串
    
    Args:
        text: 原始字符串
        
    Returns:
        str: 清理后的字符串
    """
    if not text:
        return ""
    
    # 移除多余的空白字符
    text = re.sub(r'\s+', ' ', text.strip())
    
    # 移除特殊字符
    text = re.sub(r'[^\w\s\-\.\(\)（）]', '', text)
    
    return text

def truncate_string(text: str, max_length: int = 50) -> str:
    """截断字符串
    
    Args:
        text: 原始字符串
        max_length: 最大长度
        
    Returns:
        str: 截断后的字符串
    """
    if not text:
        return ""
    
    if len(text) <= max_length:
        return text
    
    return text[:max_length-3] + "..."

#!/usr/bin/env python
# -*- coding:utf-8 -*-
"""
数据验证器
提供各种数据验证功能
"""

import re
from typing import bool

def validate_symbol(symbol: str) -> bool:
    """验证标的代码格式
    
    Args:
        symbol: 标的代码
        
    Returns:
        bool: 是否有效
    """
    if not symbol or not isinstance(symbol, str):
        return False
    
    symbol = symbol.strip()
    
    # 基本长度检查
    if len(symbol) < 1 or len(symbol) > 20:
        return False
    
    # A股代码格式检查
    if re.match(r'^[0-9]{6}$', symbol):
        return True
    
    # 港股代码格式检查
    if re.match(r'^[0-9]{5}$', symbol):
        return True
    
    # 美股代码格式检查
    if re.match(r'^[A-Z]{1,5}$', symbol):
        return True
    
    # 基金代码格式检查
    if re.match(r'^[0-9]{6}$', symbol):
        return True
    
    # 加密货币交易对格式检查
    if re.match(r'^[A-Z]+/[A-Z]+$', symbol):
        return True
    
    return False

def validate_category(category: str) -> bool:
    """验证分类格式
    
    Args:
        category: 分类
        
    Returns:
        bool: 是否有效
    """
    if not category or not isinstance(category, str):
        return False
    
    valid_categories = [
        "股票/A股", "股票/港股", "股票/美股",
        "基金/公募基金", "基金/ETF基金",
        "加密货币/现货", "加密货币/期货"
    ]
    
    return category in valid_categories

def validate_price(price) -> bool:
    """验证价格
    
    Args:
        price: 价格值
        
    Returns:
        bool: 是否有效
    """
    try:
        if price is None:
            return True  # 允许空值
        
        price_float = float(price)
        return price_float >= 0
        
    except (ValueError, TypeError):
        return False

def validate_volume(volume) -> bool:
    """验证成交量
    
    Args:
        volume: 成交量值
        
    Returns:
        bool: 是否有效
    """
    try:
        if volume is None:
            return True  # 允许空值
        
        volume_float = float(volume)
        return volume_float >= 0
        
    except (ValueError, TypeError):
        return False

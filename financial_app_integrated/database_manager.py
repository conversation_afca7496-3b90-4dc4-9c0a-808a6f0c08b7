#!/usr/bin/env python
# -*- coding:utf-8 -*-
"""
数据库管理模块
负责数据库的创建、连接、数据操作等功能
"""

import sqlite3
import pandas as pd
import logging
import time
import shutil
from pathlib import Path
from typing import List, Dict, Optional, Tuple
from config import DATABASE_CONFIG, TABLE_SCHEMA, DB_PATH, BACKUP_DIR

class DatabaseManager:
    """数据库管理器"""
    
    def __init__(self, db_path: str = None):
        """初始化数据库管理器
        
        Args:
            db_path: 数据库文件路径，默认使用配置中的路径
        """
        self.db_path = db_path or DB_PATH
        self.logger = logging.getLogger(__name__)
        self.init_database()
    
    def init_database(self) -> bool:
        """初始化数据库，创建表结构
        
        Returns:
            bool: 是否成功初始化
        """
        try:
            # 确保数据库目录存在
            self.db_path.parent.mkdir(parents=True, exist_ok=True)
            
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 创建表
            cursor.execute(TABLE_SCHEMA)
            
            # 创建索引以提高查询性能
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_symbol ON instruments(symbol)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_category ON instruments(category)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_source ON instruments(source)")
            
            conn.commit()
            conn.close()
            
            self.logger.info("数据库初始化成功")
            return True
            
        except Exception as e:
            self.logger.error(f"数据库初始化失败: {e}")
            return False
    
    def get_connection(self) -> sqlite3.Connection:
        """获取数据库连接
        
        Returns:
            sqlite3.Connection: 数据库连接对象
        """
        conn = sqlite3.connect(
            self.db_path,
            timeout=DATABASE_CONFIG["timeout"],
            check_same_thread=DATABASE_CONFIG["check_same_thread"]
        )
        conn.row_factory = sqlite3.Row  # 使结果可以按列名访问
        return conn
    
    def execute_query(self, query: str, params: tuple = None) -> List[sqlite3.Row]:
        """执行查询语句
        
        Args:
            query: SQL查询语句
            params: 查询参数
            
        Returns:
            List[sqlite3.Row]: 查询结果
        """
        try:
            conn = self.get_connection()
            cursor = conn.cursor()
            
            if params:
                cursor.execute(query, params)
            else:
                cursor.execute(query)
            
            results = cursor.fetchall()
            conn.close()
            return results
            
        except Exception as e:
            self.logger.error(f"查询执行失败: {e}")
            return []
    
    def execute_update(self, query: str, params: tuple = None) -> bool:
        """执行更新语句
        
        Args:
            query: SQL更新语句
            params: 更新参数
            
        Returns:
            bool: 是否成功执行
        """
        try:
            conn = self.get_connection()
            cursor = conn.cursor()
            
            if params:
                cursor.execute(query, params)
            else:
                cursor.execute(query)
            
            conn.commit()
            conn.close()
            return True
            
        except Exception as e:
            self.logger.error(f"更新执行失败: {e}")
            return False
    
    def insert_instrument(self, name: str, symbol: str, category: str, 
                         exchange: str = "", source: str = "", **kwargs) -> bool:
        """插入金融标的数据
        
        Args:
            name: 标的名称
            symbol: 标的代码
            category: 分类
            exchange: 交易所
            source: 数据源
            **kwargs: 其他字段
            
        Returns:
            bool: 是否成功插入
        """
        timestamp = time.strftime("%Y-%m-%d %H:%M:%S")
        
        # 构建插入语句
        fields = ["name", "symbol", "category", "exchange", "source", "timestamp"]
        values = [name, symbol, category, exchange, source, timestamp]
        
        # 添加其他字段
        for key, value in kwargs.items():
            if key in ["market_cap", "price", "volume", "verified"]:
                fields.append(key)
                values.append(value)
        
        placeholders = ",".join(["?" for _ in fields])
        query = f"INSERT OR REPLACE INTO instruments ({','.join(fields)}) VALUES ({placeholders})"
        
        return self.execute_update(query, tuple(values))
    
    def batch_insert_instruments(self, instruments: List[Dict]) -> int:
        """批量插入金融标的数据
        
        Args:
            instruments: 标的数据列表
            
        Returns:
            int: 成功插入的数量
        """
        success_count = 0
        timestamp = time.strftime("%Y-%m-%d %H:%M:%S")
        
        try:
            conn = self.get_connection()
            cursor = conn.cursor()
            
            for instrument in instruments:
                try:
                    # 设置默认值
                    instrument.setdefault("exchange", "")
                    instrument.setdefault("source", "")
                    instrument.setdefault("timestamp", timestamp)
                    instrument.setdefault("verified", 0)
                    
                    # 构建插入语句
                    fields = list(instrument.keys())
                    values = list(instrument.values())
                    placeholders = ",".join(["?" for _ in fields])
                    
                    query = f"INSERT OR REPLACE INTO instruments ({','.join(fields)}) VALUES ({placeholders})"
                    cursor.execute(query, values)
                    success_count += 1
                    
                except Exception as e:
                    self.logger.warning(f"插入单条数据失败: {e}, 数据: {instrument}")
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            self.logger.error(f"批量插入失败: {e}")
        
        return success_count
    
    def search_instruments(self, keyword: str = "", category: str = "", 
                          source: str = "", limit: int = 1000) -> List[sqlite3.Row]:
        """搜索金融标的
        
        Args:
            keyword: 搜索关键词（名称或代码）
            category: 分类过滤
            source: 数据源过滤
            limit: 结果数量限制
            
        Returns:
            List[sqlite3.Row]: 搜索结果
        """
        query = "SELECT * FROM instruments WHERE 1=1"
        params = []
        
        if keyword:
            query += " AND (name LIKE ? OR symbol LIKE ?)"
            params.extend([f"%{keyword}%", f"%{keyword}%"])
        
        if category:
            query += " AND category = ?"
            params.append(category)
        
        if source:
            query += " AND source = ?"
            params.append(source)
        
        query += " ORDER BY timestamp DESC"
        
        if limit:
            query += " LIMIT ?"
            params.append(limit)
        
        return self.execute_query(query, tuple(params))
    
    def get_statistics(self) -> Dict[str, int]:
        """获取数据库统计信息
        
        Returns:
            Dict[str, int]: 统计信息
        """
        stats = {}
        
        # 总数量
        result = self.execute_query("SELECT COUNT(*) as total FROM instruments")
        stats["total"] = result[0]["total"] if result else 0
        
        # 按分类统计
        result = self.execute_query("SELECT category, COUNT(*) as count FROM instruments GROUP BY category")
        for row in result:
            stats[row["category"]] = row["count"]
        
        # 按数据源统计
        result = self.execute_query("SELECT source, COUNT(*) as count FROM instruments GROUP BY source")
        for row in result:
            stats[f"source_{row['source']}"] = row["count"]
        
        return stats
    
    def export_to_csv(self, file_path: str, category: str = None) -> bool:
        """导出数据到CSV文件
        
        Args:
            file_path: 导出文件路径
            category: 可选的分类过滤
            
        Returns:
            bool: 是否成功导出
        """
        try:
            query = "SELECT * FROM instruments"
            params = None
            
            if category:
                query += " WHERE category = ?"
                params = (category,)
            
            conn = self.get_connection()
            df = pd.read_sql_query(query, conn, params=params)
            conn.close()
            
            df.to_csv(file_path, index=False, encoding='utf-8-sig')
            self.logger.info(f"数据导出成功: {file_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"数据导出失败: {e}")
            return False
    
    def import_from_csv(self, file_path: str) -> int:
        """从CSV文件导入数据
        
        Args:
            file_path: CSV文件路径
            
        Returns:
            int: 成功导入的数量
        """
        try:
            df = pd.read_csv(file_path, encoding='utf-8-sig')
            instruments = df.to_dict('records')
            return self.batch_insert_instruments(instruments)
            
        except Exception as e:
            self.logger.error(f"CSV导入失败: {e}")
            return 0
    
    def backup_database(self) -> str:
        """备份数据库
        
        Returns:
            str: 备份文件路径
        """
        try:
            timestamp = time.strftime("%Y%m%d_%H%M%S")
            backup_path = BACKUP_DIR / f"financial_database_backup_{timestamp}.db"
            
            shutil.copy2(self.db_path, backup_path)
            self.logger.info(f"数据库备份成功: {backup_path}")
            return str(backup_path)
            
        except Exception as e:
            self.logger.error(f"数据库备份失败: {e}")
            return ""
    
    def clear_database(self) -> bool:
        """清空数据库
        
        Returns:
            bool: 是否成功清空
        """
        return self.execute_update("DELETE FROM instruments")
    
    def get_existing_symbols(self, category: str = None) -> set:
        """获取已存在的标的代码
        
        Args:
            category: 可选的分类过滤
            
        Returns:
            set: 已存在的symbol集合
        """
        query = "SELECT symbol FROM instruments"
        params = None
        
        if category:
            query += " WHERE category = ?"
            params = (category,)
        
        results = self.execute_query(query, params)
        return set(row["symbol"] for row in results)

#!/usr/bin/env python3
"""
测试美股代码修复结果
"""

import sqlite3
from pathlib import Path

def test_us_stock_codes():
    """测试美股代码修复结果"""
    
    # 数据库路径
    db_path = Path("data/financial_database.db")
    
    if not db_path.exists():
        print("❌ 数据库文件不存在")
        return
    
    try:
        # 连接数据库
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 查询美股总数
        cursor.execute("""
            SELECT COUNT(*) FROM instruments 
            WHERE exchange = '美国交易所'
        """)
        total_us_stocks = cursor.fetchone()[0]
        
        # 查询仍有数字前缀的美股代码
        cursor.execute("""
            SELECT COUNT(*) FROM instruments 
            WHERE exchange = '美国交易所' 
            AND symbol LIKE '%.%'
        """)
        remaining_with_prefix = cursor.fetchone()[0]
        
        # 查询一些示例
        cursor.execute("""
            SELECT symbol, name FROM instruments 
            WHERE exchange = '美国交易所' 
            ORDER BY id 
            LIMIT 10
        """)
        examples = cursor.fetchall()
        
        conn.close()
        
        print(f"📊 美股代码修复结果:")
        print(f"   总美股数量: {total_us_stocks}")
        print(f"   仍有数字前缀: {remaining_with_prefix}")
        print(f"   修复成功率: {((total_us_stocks - remaining_with_prefix) / total_us_stocks * 100):.1f}%")
        
        print(f"\n📝 示例美股代码:")
        for symbol, name in examples:
            print(f"   {symbol} - {name}")
        
        if remaining_with_prefix == 0:
            print("\n✅ 所有美股代码已成功修复！")
        else:
            print(f"\n⚠️  仍有 {remaining_with_prefix} 个代码需要进一步处理")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")

if __name__ == "__main__":
    test_us_stock_codes()

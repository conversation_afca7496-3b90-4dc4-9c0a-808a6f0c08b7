#!/usr/bin/env python
# -*- coding:utf-8 -*-
"""
配置管理工具
用于调整应用的各种配置参数
"""

import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from config import AKSHARE_CONFIG, CCXT_CONFIG, FETCH_CONFIG

def show_current_config():
    """显示当前配置"""
    print("=" * 50)
    print("当前配置信息")
    print("=" * 50)
    
    print("\n📊 数据收集配置:")
    print(f"  - 最大线程数: {FETCH_CONFIG['max_workers']}")
    print(f"  - 请求超时: {FETCH_CONFIG['timeout']}秒")
    print(f"  - 重试次数: {FETCH_CONFIG['retry_times']}")
    print(f"  - 请求间隔: {FETCH_CONFIG['delay']}秒")
    
    print("\n🏢 AKShare配置:")
    print(f"  - 支持市场: {', '.join(AKSHARE_CONFIG['stock_markets'])}")
    print(f"  - 基金类型: {', '.join(AKSHARE_CONFIG['fund_types'])}")
    print(f"  - 每类最大记录数: {AKSHARE_CONFIG['max_records_per_type']}")
    
    print("\n💰 加密货币配置:")
    print(f"  - 支持交易所: {', '.join(CCXT_CONFIG['exchanges'])}")
    print(f"  - 报价货币: {', '.join(CCXT_CONFIG['quote_currencies'])}")
    print(f"  - 最大交易对数: {CCXT_CONFIG['max_symbols']}")

def modify_max_records():
    """修改最大记录数限制"""
    print("\n🔧 修改数据收集限制")
    print("-" * 30)
    
    current = AKSHARE_CONFIG['max_records_per_type']
    print(f"当前限制: {current}条/类型")
    print("\n选项:")
    print("1. 设置为5000条")
    print("2. 设置为10000条") 
    print("3. 取消限制（获取所有数据）")
    print("4. 自定义数量")
    print("0. 返回")
    
    choice = input("\n请选择 (0-4): ").strip()
    
    if choice == "1":
        new_value = 5000
    elif choice == "2":
        new_value = 10000
    elif choice == "3":
        new_value = None
    elif choice == "4":
        try:
            new_value = int(input("请输入自定义数量 (输入0表示不限制): "))
            if new_value == 0:
                new_value = None
        except ValueError:
            print("❌ 输入无效，操作取消")
            return
    elif choice == "0":
        return
    else:
        print("❌ 无效选择")
        return
    
    # 修改配置文件
    try:
        with open('config.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 替换配置值
        old_line = f'"max_records_per_type": {current},'
        if new_value is None:
            new_line = '"max_records_per_type": None,  # 不限制数量'
        else:
            new_line = f'"max_records_per_type": {new_value},  # 每类最大记录数'
        
        content = content.replace(old_line, new_line)
        
        with open('config.py', 'w', encoding='utf-8') as f:
            f.write(content)
        
        print(f"✅ 配置已更新: {new_value if new_value else '不限制'}")
        print("⚠️  请重启应用以使配置生效")
        
    except Exception as e:
        print(f"❌ 配置更新失败: {e}")

def modify_thread_config():
    """修改线程配置"""
    print("\n🔧 修改线程配置")
    print("-" * 30)
    
    current = FETCH_CONFIG['max_workers']
    print(f"当前最大线程数: {current}")
    
    try:
        new_value = int(input("请输入新的最大线程数 (1-8): "))
        if not 1 <= new_value <= 8:
            print("❌ 线程数应在1-8之间")
            return
        
        # 修改配置文件
        with open('config.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        old_line = f'"max_workers": {current},'
        new_line = f'"max_workers": {new_value},'
        
        content = content.replace(old_line, new_line)
        
        with open('config.py', 'w', encoding='utf-8') as f:
            f.write(content)
        
        print(f"✅ 线程配置已更新: {new_value}")
        print("⚠️  请重启应用以使配置生效")
        
    except ValueError:
        print("❌ 输入无效")
    except Exception as e:
        print(f"❌ 配置更新失败: {e}")

def main():
    """主菜单"""
    while True:
        print("\n" + "=" * 50)
        print("金融应用配置管理器")
        print("=" * 50)
        
        print("\n📋 菜单:")
        print("1. 查看当前配置")
        print("2. 修改数据收集限制")
        print("3. 修改线程配置")
        print("0. 退出")
        
        choice = input("\n请选择 (0-3): ").strip()
        
        if choice == "1":
            show_current_config()
        elif choice == "2":
            modify_max_records()
        elif choice == "3":
            modify_thread_config()
        elif choice == "0":
            print("👋 再见！")
            break
        else:
            print("❌ 无效选择，请重试")
        
        input("\n按回车键继续...")

if __name__ == "__main__":
    main()

#!/usr/bin/env python
# -*- coding:utf-8 -*-
"""
金融标的数据管理系统
主程序入口
"""

import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from core.app_manager import AppManager

def main():
    """主函数"""
    try:
        # 创建应用管理器
        app = AppManager()
        
        # 运行应用
        app.run()
        
    except KeyboardInterrupt:
        print("\n程序被用户中断")
    except Exception as e:
        print(f"程序运行时发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()

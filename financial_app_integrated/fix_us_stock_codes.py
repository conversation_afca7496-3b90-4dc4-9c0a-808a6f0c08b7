#!/usr/bin/env python3
"""
修复美股代码中的数字前缀问题
"""

import sqlite3
from pathlib import Path

def fix_us_stock_codes():
    """修复数据库中美股代码的数字前缀问题"""

    # 数据库路径
    db_path = Path("data/financial_database.db")

    if not db_path.exists():
        print("❌ 数据库文件不存在")
        return

    try:
        # 连接数据库
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        # 查找需要修复的美股代码
        cursor.execute("""
            SELECT id, symbol, name FROM instruments
            WHERE exchange = '美国交易所'
            AND symbol LIKE '%.%'
        """)

        records = cursor.fetchall()

        if not records:
            print("✅ 没有发现需要修复的美股代码")
            conn.close()
            return

        print(f"🔍 发现 {len(records)} 条需要修复的美股代码:")

        fixed_count = 0

        for record_id, old_symbol, name in records:
            # 提取正确的代码（去掉数字前缀）
            if "." in old_symbol:
                parts = old_symbol.split(".")
                if len(parts) == 2 and parts[0].isdigit():
                    new_symbol = parts[1]

                    # 检查新代码是否已存在
                    cursor.execute("""
                        SELECT COUNT(*) FROM instruments
                        WHERE symbol = ? AND exchange = '美国交易所' AND id != ?
                    """, (new_symbol, record_id))

                    if cursor.fetchone()[0] == 0:
                        # 更新代码
                        cursor.execute("""
                            UPDATE instruments
                            SET symbol = ?
                            WHERE id = ?
                        """, (new_symbol, record_id))

                        print(f"  ✅ {old_symbol} -> {new_symbol} ({name})")
                        fixed_count += 1
                    else:
                        # 如果新代码已存在，删除重复记录
                        cursor.execute("DELETE FROM instruments WHERE id = ?", (record_id,))
                        print(f"  🗑️  删除重复记录: {old_symbol} ({name})")
                        fixed_count += 1

        # 提交更改
        conn.commit()
        conn.close()

        print(f"\n🎉 修复完成！共处理 {fixed_count} 条记录")

    except Exception as e:
        print(f"❌ 修复失败: {e}")

if __name__ == "__main__":
    print("🔧 开始修复美股代码...")
    fix_us_stock_codes()

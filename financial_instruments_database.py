#!/usr/bin/env python
# -*- coding:utf-8 -*-
"""
金融标的数据库构建工具
支持通过AKShare和CCXT获取股票和加密货币数据
新功能：多线程获取数据，避免重复获取已有数据
"""

import akshare as ak
import ccxt
import sqlite3
import pandas as pd
import os
import time
import concurrent.futures
import logging
from pathlib import Path

# 配置
DB_PATH = Path("financial_data") / "financial_database.db"
DATA_DIR = Path("financial_data")

# 日志配置
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
DATA_DIR.mkdir(exist_ok=True)

def init_database(recreate=False):
    """初始化数据库
    
    Args:
        recreate (bool): 是否重新创建数据库表，默认False
    
    Returns:
        bool: 是否有现有数据
    """
    os.makedirs(os.path.dirname(DB_PATH), exist_ok=True)
    conn = sqlite3.connect(DB_PATH)
    c = conn.cursor()
    
    # 检查表是否存在及数据量
    has_data = False
    try:
        result = c.execute("SELECT COUNT(*) FROM instruments").fetchone()
        if result and result[0] > 0:
            data_count = result[0]
            has_data = True
            logging.info(f"数据库中已有 {data_count} 条数据记录")
    except sqlite3.OperationalError:
        logging.info("数据表不存在，将创建新表")
    
    # 如果要求重建或表不存在，则创建新表
    if recreate or not has_data:
        if recreate and has_data:
            try:
                c.execute("DROP TABLE IF EXISTS instruments")
                logging.info("原有数据库表已删除，准备重建")
            except Exception as e:
                logging.error(f"删除表时发生错误: {e}")
        
        # 创建表
        c.execute('''CREATE TABLE IF NOT EXISTS instruments (
                     id INTEGER PRIMARY KEY,
                     name TEXT NOT NULL,
                     symbol TEXT NOT NULL,
                     category TEXT NOT NULL,
                     exchange TEXT,
                     source TEXT NOT NULL,
                     timestamp TEXT)''')
        has_data = False
        logging.info(f"数据库已初始化: {DB_PATH}")
        logging.info("数据库结构: id, name, symbol, category, exchange, source, timestamp")
    
    conn.commit()
    conn.close()
    
    return has_data


# 单独的数据收集函数，便于多线程调用
def collect_a_stock_data():
    """收集A股数据，带重试机制"""
    # 使用替代方案收集A股数据 - 更可靠
    # 前期测试发现stock_info_a_code_name()经常失败
    try:
        max_retries = 3
        retry_count = 0
        
        while retry_count < max_retries:
            try:
                logging.info(f"正在获取A股数据... (尝试 {retry_count + 1}/{max_retries})")
                
                # 尝试从多个源获取A股数据
                try:
                    # 尝试使用stock_info_a_code_name函数
                    a_stock = ak.stock_info_a_code_name()
                except Exception as e1:
                    logging.warning(f"使用stock_info_a_code_name获取失败: {str(e1)}，尝试备用方法")
                    try:
                        # 尝试使用stock_zh_a_spot函数
                        a_stock = ak.stock_zh_a_spot()
                    except Exception as e2:
                        logging.warning(f"使用stock_zh_a_spot获取失败: {str(e2)}，尝试第三种方法")
                        try:
                            # 尝试使用stock_sh_a_spot_em获取上交所数据
                            sh_stock = ak.stock_sh_a_spot_em()
                            # 尝试使用stock_sz_a_spot_em获取深交所数据
                            sz_stock = ak.stock_sz_a_spot_em()
                            # 合并两者
                            a_stock = pd.concat([sh_stock, sz_stock])
                        except Exception as e3:
                            logging.error(f"所有A股数据获取方法均失败")
                            raise e3
                
                # 验证获取的数据
                if isinstance(a_stock, pd.DataFrame) and not a_stock.empty:
                    logging.info(f"成功获取A股数据: {len(a_stock)}条记录")
                    
                    # 打印列名帮助调试
                    logging.info(f"A股数据列名: {a_stock.columns.tolist()}")
                    
                    # 统一列名
                    if '代码' in a_stock.columns and '名称' in a_stock.columns:
                        a_stock = a_stock.rename(columns={'代码': 'symbol', '名称': 'name'})
                    elif '代码' in a_stock.columns and '名称' not in a_stock.columns and '名称' in a_stock.columns:
                        a_stock = a_stock.rename(columns={'代码': 'symbol', '名称': 'name'})
                    elif 'code' in a_stock.columns and 'name' in a_stock.columns:
                        a_stock = a_stock.rename(columns={'code': 'symbol'})
                    elif '证券代码' in a_stock.columns and '证券简称' in a_stock.columns:
                        a_stock = a_stock.rename(columns={'证券代码': 'symbol', '证券简称': 'name'})
                    
                    # 确保有symbol和name列
                    if 'symbol' in a_stock.columns and 'name' in a_stock.columns:
                        return a_stock[['symbol', 'name']]
                    else:
                        # 如果列名不匹配预期，采用替代方案
                        logging.warning(f"A股数据列名不匹配，尝试自动构造")
                        # 尝试使用前两列作为代码和名称
                        cols = a_stock.columns.tolist()
                        if len(cols) >= 2:
                            new_data = pd.DataFrame({
                                'symbol': a_stock[cols[0]],
                                'name': a_stock[cols[1]]
                            })
                            return new_data
                
                # 如果没有成功返回，再次尝试
                retry_count += 1
                time.sleep(1)  # 等待1秒再试
            
            except Exception as e:
                logging.warning(f"第{retry_count + 1}次尝试失败: {str(e)}")
                retry_count += 1
                time.sleep(2)  # 出错后等待时间更长
        
        # 所有重试均失败，使用备选方案
        logging.warning("所有A股数据获取尝试均失败，使用扩展备选方案")
        # 提供更多的样例数据
        return pd.DataFrame({
            "symbol": ["000001", "600000", "300001", "688001", "601398", "601288", "600519", "000651", "000333", "601766"],
            "name": ["平安银行", "浦发银行", "特锐德", "华兴源创", "工商银行", "农业银行", "贵州茅台", "格力电器", "美的集团", "中国中车"]
        })
    
    except Exception as e:
        logging.error(f"A股数据收集完全失败: {str(e)}，使用基本备选方案")
        # 基本备选方案
        return pd.DataFrame({
            "symbol": ["000001", "600000", "300001", "688001", "601398", "601288"],
            "name": ["平安银行", "浦发银行", "特锐德", "华兴源创", "工商银行", "农业银行"]
        })

def collect_hk_stock_data():
    """收集港股数据"""
    try:
        logging.info("正在获取港股数据...")
        hk_stock = ak.stock_hk_spot()
        if isinstance(hk_stock, pd.DataFrame) and not hk_stock.empty:
            logging.info(f"成功获取港股数据: {len(hk_stock)}条记录")
            return hk_stock
        return pd.DataFrame()
    except Exception as e:
        logging.error(f"港股数据收集失败: {str(e)}")
        return pd.DataFrame()

def collect_us_stock_data():
    """收集美股数据"""
    try:
        logging.info("正在获取美股数据...")
        us_stock = ak.stock_us_spot()
        if isinstance(us_stock, pd.DataFrame) and not us_stock.empty:
            logging.info(f"成功获取美股数据: {len(us_stock)}条记录")
            return us_stock
        return pd.DataFrame()
    except Exception as e:
        logging.error(f"美股数据收集失败: {str(e)}")
        return pd.DataFrame()

def collect_fund_data():
    """收集基金数据"""
    try:
        logging.info("正在获取基金数据...")
        try:
            fund_list = ak.fund_em_fund_name()
            if isinstance(fund_list, pd.DataFrame) and not fund_list.empty:
                logging.info(f"成功获取基金数据: {len(fund_list)}条记录")
                return fund_list.head(100)  # 增加获取的基金数量
            else:
                raise Exception("基金数据为空")
        except Exception as e:
            logging.warning(f"基金数据收集失败，使用备选方案: {str(e)}")
            # 备选方案：手动创建示例基金数据
            return pd.DataFrame({
                "基金简称": ["易方达创业板ETF", "华夏上证50ETF", "南方中证500ETF", "博时沪深300ETF"],
                "基金代码": ["159915", "510050", "510500", "159919"]
            })
    except Exception as e:
        logging.error(f"基金数据收集失败: {str(e)}")
        return pd.DataFrame()

def collect_akshare_data():
    """多线程收集AKShare数据"""
    logging.info("开始多线程并行收集AKShare数据...")
    data = {}
    data["stocks"] = {}
    data["funds"] = {}
    
    # 使用线程池并行执行数据收集任务
    with concurrent.futures.ThreadPoolExecutor(max_workers=3) as executor:
        # 提交数据收集任务
        future_a_stock = executor.submit(collect_a_stock_data)
        future_hk_stock = executor.submit(collect_hk_stock_data)
        future_us_stock = executor.submit(collect_us_stock_data)
        future_fund = executor.submit(collect_fund_data)
        
        # 获取数据结果
        a_stock_data = future_a_stock.result()
        if not a_stock_data.empty:
            data["stocks"]["A股"] = a_stock_data
        
        hk_stock_data = future_hk_stock.result()
        if not hk_stock_data.empty:
            data["stocks"]["港股"] = hk_stock_data
            
        us_stock_data = future_us_stock.result()
        if not us_stock_data.empty:
            data["stocks"]["美股"] = us_stock_data
        
        fund_data = future_fund.result()
        if not fund_data.empty:
            data["funds"]["公募基金"] = fund_data
    
    # 汇总数据收集结果
    for market, df in data["stocks"].items():
        if isinstance(df, pd.DataFrame) and not df.empty:
            logging.info(f"已获取{market}数据: {len(df)}条记录")
    
    for fund_type, df in data["funds"].items():
        if isinstance(df, pd.DataFrame) and not df.empty:
            logging.info(f"已获取{fund_type}数据: {len(df)}条记录")
    
    return data

def collect_ccxt_data():
    """收集CCXT数据"""
    print("收集CCXT数据...")
    try:
        exchange = ccxt.binance()
        markets = exchange.load_markets()
        return list(markets.keys())[:100]  # 只取前100个交易对测试
    except Exception as e:
        print(f"加密货币数据收集失败: {str(e)}")
        return []

def get_existing_symbols(category=None):
    """从数据库中获取已存在的标的代码
    
    Args:
        category (str, optional): 可选的分类过滤，如"股票/A股"
        
    Returns:
        set: 已存在的symbol集合
    """
    conn = sqlite3.connect(DB_PATH)
    c = conn.cursor()
    
    try:
        if category:
            c.execute("SELECT symbol FROM instruments WHERE category = ?", (category,))
        else:
            c.execute("SELECT symbol FROM instruments")
        
        # 将结果转换为集合
        symbols = set([row[0] for row in c.fetchall()])
        conn.close()
        return symbols
    except Exception as e:
        logging.error(f"获取已存在标的时出错: {e}")
        conn.close()
        return set()

def import_to_database(recreate=False, incremental=True):
    """导入数据到数据库
    
    Args:
        recreate (bool): 是否重新创建数据库并导入所有数据，默认False
        incremental (bool): 是否为增量更新，即跳过已存在的标的，默认True
    """
    # 首先检查数据库是否已初始化以及是否有数据
    has_data = init_database(recreate)
    
    # 如果数据库中已有数据且不需要重建且不需要更新，则直接返回
    if has_data and not recreate and not incremental:
        logging.info("数据库中已有数据，已设置不需要更新")
        return
    
    conn = sqlite3.connect(DB_PATH)
    c = conn.cursor()
    
    # 获取已存在的记录
    existing_symbols = {}
    if incremental and not recreate:
        # 按分类获取已有标的
        categories = ['股票/A股', '股票/港股', '股票/美股', '基金/公募基金', '加密货币']
        for category in categories:
            existing_symbols[category] = get_existing_symbols(category)
            logging.info(f"数据库中已有 {category} 数据: {len(existing_symbols[category])} 条记录")
    
    # 如果需要重建，首先清空数据库
    if recreate:
        try:
            c.execute("DELETE FROM instruments")
            logging.info("已清空现有数据，准备重新导入")
            # 重建时清空已存在的记录集合
            existing_symbols = {}
        except Exception as e:
            logging.error(f"清空数据表失败: {e}")
    
    # 并行收集数据
    logging.info("开始多线程收集数据...")
    ak_data = collect_akshare_data()
    
    # 计数器用于统计数据
    counters = {"A股": 0, "港股": 0, "美股": 0, "基金": 0, "加密货币": 0}
    # 新数据计数器
    new_counters = {"A股": 0, "港股": 0, "美股": 0, "基金": 0, "加密货币": 0}
    # 跳过数据计数器
    skip_counters = {"A股": 0, "港股": 0, "美股": 0, "基金": 0, "加密货币": 0}
    
    # 导入股票
    for market, stocks in ak_data.get("stocks", {}).items():
        if isinstance(stocks, pd.DataFrame) and not stocks.empty:
            category_key = f"股票/{market}"
            logging.info(f"开始导入{market}数据: 共 {len(stocks)} 条记录")
            print(f"正在导入{market}数据: {len(stocks)}条记录")
            
            # 打印首行数据的列名，便于调试
            if len(stocks) > 0:
                logging.info(f"{market}数据列名: {stocks.columns.tolist()}")
                if market == "A股":
                    print(f"A股数据列名: {stocks.columns.tolist()}")
            
            for _, row in stocks.iterrows():
                try:
                    # 根据不同市场处理不同的列名
                    if market == "A股":
                        name = row.get("name") if "name" in row else row.get("名称") if "名称" in row else ""
                        symbol = row.get("symbol") if "symbol" in row else row.get("代码") if "代码" in row else ""
                    elif market == "港股":
                        name = row.get("name") or ""
                        symbol = row.get("symbol") or ""
                    elif market == "美股":
                        name = row.get("name") or ""
                        symbol = row.get("symbol") or ""
                    else:
                        name = row.get("name") or row.get("名称") or ""
                        symbol = row.get("symbol") or row.get("代码") or ""
                    
                    if name and symbol:
                        # 检查是否已存在，如果增量更新模式
                        if incremental and not recreate and category_key in existing_symbols and symbol in existing_symbols[category_key]:
                            skip_counters[market] += 1
                            continue
                        
                        # 添加交易所信息
                        exchange = ""
                        if market == "A股":
                            if symbol.startswith("6"):
                                exchange = "上交所"
                            elif symbol.startswith("0") or symbol.startswith("3"):
                                exchange = "深交所"
                            elif symbol.startswith("4") or symbol.startswith("8"):
                                exchange = "北交所"
                            else:
                                exchange = "其他"
                        elif market == "港股":
                            exchange = "港交所"
                        elif market == "美股":
                            exchange = "美国市场"
                        
                        # 获取当前时间戳
                        timestamp = time.strftime("%Y-%m-%d %H:%M:%S")
                        
                        c.execute("INSERT INTO instruments (name, symbol, category, exchange, source, timestamp) VALUES (?,?,?,?,?,?)", 
                                 (name, symbol, f"股票/{market}", exchange, "akshare", timestamp))
                        counters[market] += 1
                        new_counters[market] += 1
                except Exception as e:
                    logging.error(f"导入{market}数据时出错: {e}")
                    print(f"导入{market}数据时出错: {e}, 行数据: {row}")
    
    # 导入基金
    for fund_type, funds in ak_data.get("funds", {}).items():
        if isinstance(funds, pd.DataFrame) and not funds.empty:
            category_key = f"基金/{fund_type}"
            logging.info(f"开始导入{fund_type}数据: 共 {len(funds)} 条记录")
            print(f"正在导入{fund_type}数据: {len(funds)}条记录")
            
            # 打印首行数据的列名，便于调试
            if len(funds) > 0:
                logging.info(f"{fund_type}列名: {funds.columns.tolist()}")
            
            for _, row in funds.iterrows():
                try:
                    name = row.get("基金简称") or ""
                    symbol = row.get("基金代码") or ""
                    
                    # 使用替代列名，如果标准列名不存在
                    if not name or not symbol:
                        for col in funds.columns:
                            if "简称" in col or "名称" in col:
                                name = row.get(col) or ""
                            if "代码" in col:
                                symbol = row.get(col) or ""
                    
                    if name and symbol:
                        # 检查是否已存在，如果增量更新模式
                        if incremental and not recreate and category_key in existing_symbols and symbol in existing_symbols[category_key]:
                            skip_counters["基金"] += 1
                            continue
                            
                        # 添加交易所信息
                        exchange = ""
                        if "ETF" in name or "ETF" in fund_type:
                            exchange = "交易所"
                        
                        timestamp = time.strftime("%Y-%m-%d %H:%M:%S")
                        c.execute("INSERT INTO instruments (name, symbol, category, exchange, source, timestamp) VALUES (?,?,?,?,?,?)", 
                                 (name, symbol, category_key, exchange, "akshare", timestamp))
                        counters["基金"] += 1
                        new_counters["基金"] += 1
                except Exception as e:
                    logging.error(f"导入{fund_type}数据时出错: {e}")
                    print(f"导入{fund_type}数据时出错: {e}")
    
    # 导入加密货币
    crypto_pairs = collect_ccxt_data()
    category_key = "加密货币"
    if crypto_pairs:
        logging.info(f"开始导入加密货币数据: 共 {len(crypto_pairs)} 条记录")
        print(f"正在导入加密货币数据: {len(crypto_pairs)}条记录")
        
        for pair in crypto_pairs:
            try:
                parts = pair.split('/')
                name = parts[0] if len(parts) > 0 else pair
                symbol = pair
                
                # 检查是否已存在，如果增量更新模式
                if incremental and not recreate and category_key in existing_symbols and symbol in existing_symbols[category_key]:
                    skip_counters["加密货币"] += 1
                    continue
                    
                exchange = "Binance"  # 默认使用Binance作为交易所
                timestamp = time.strftime("%Y-%m-%d %H:%M:%S")
                c.execute("INSERT INTO instruments (name, symbol, category, exchange, source, timestamp) VALUES (?,?,?,?,?,?)", 
                         (name, symbol, category_key, exchange, "ccxt", timestamp))
                counters["加密货币"] += 1
                new_counters["加密货币"] += 1
            except Exception as e:
                logging.error(f"导入加密货币数据时出错: {e}")
                print(f"导入加密货币数据时出错: {e}")
    
    # 提交事务并关闭连接
    conn.commit()
    conn.close()
    
    # 输出数据统计
    print("\n导入统计:")
    if incremental and not recreate:
        print("[增量更新模式]")
        print(f"  A股: {new_counters['A股']}条新数据，{skip_counters['A股']}条已存在跳过")
        print(f"  港股: {new_counters['港股']}条新数据，{skip_counters['港股']}条已存在跳过")
        print(f"  美股: {new_counters['美股']}条新数据，{skip_counters['美股']}条已存在跳过")
        print(f"  基金: {new_counters['基金']}条新数据，{skip_counters['基金']}条已存在跳过")
        print(f"  加密货币: {new_counters['加密货币']}条新数据，{skip_counters['加密货币']}条已存在跳过")
        total_new = sum(new_counters.values())
        total_skip = sum(skip_counters.values())
        print(f"  本次新增: {total_new}条记录")
        print(f"  本次跳过: {total_skip}条记录")
    else:
        print("[全量导入模式]")
        print(f"  A股: {counters['A股']}条记录")
        print(f"  港股: {counters['港股']}条记录")
        print(f"  美股: {counters['美股']}条记录")
        print(f"  基金: {counters['基金']}条记录")
        print(f"  加密货币: {counters['加密货币']}条记录")
    
    # 总计
    print(f"  总共: {sum(counters.values())}条记录")
    
    # 数据量比较
    if incremental and not recreate:
        conn = sqlite3.connect(DB_PATH)
        c = conn.cursor()
        c.execute("SELECT COUNT(*) FROM instruments")
        total_records = c.fetchone()[0]
        conn.close()
        print(f"  数据库总记录数: {total_records}条")
    
    # 将数据保存为CSV形式导出
    if 'export' in locals():
        export_to_csv = export
    else:
        export_to_csv = False
        
    if export_to_csv:
        export_to_csv_file()
        print("  数据已导出CSV文件")
    
    # 输出结束信息
    logging.info(f"\n数据导入完成，共 {sum(counters.values())} 条记录")

def export_to_csv_file():
    """导出数据库到CSV"""
    conn = sqlite3.connect(DB_PATH)
    df = pd.read_sql_query("SELECT * FROM instruments", conn)
    csv_path = DATA_DIR / "financial_instruments.csv"
    df.to_csv(csv_path, index=False, encoding="utf-8-sig")
    conn.close()
    print(f"数据已导出到 CSV: {csv_path}")

import argparse

def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='金融标的数据库构建工具')
    parser.add_argument('-r', '--recreate', action='store_true', 
                      help='重新创建数据库并导入所有数据，如果不指定则会判断数据库已有数据时跳过获取')
    parser.add_argument('-c', '--csv', action='store_true', 
                      help='导出数据到CSV文件')
    parser.add_argument('-i', '--incremental', action='store_true', 
                      help='增量更新模式，跳过已存在的标的')
    parser.add_argument('-f', '--full', action='store_true', 
                      help='全量更新模式，不跳过已存在的标的')
    return parser.parse_args()

if __name__ == "__main__":
    start_time = time.time()
    
    # 记录开始时间
    logging.info("==============================================")
    logging.info("金融标的数据库构建工具 (多线程版)")
    logging.info("==============================================")
    
    # 解析命令行参数
    args = parse_args()
    
    # 确定是否为增量更新模式
    incremental = True  # 默认为增量更新
    if args.full:  # 如果使用-f指定了全量模式
        incremental = False
    elif args.incremental:  # 更明确指定了增量模式
        incremental = True
    
    # 如果重新创建数据库，则增量标志没有意义
    if args.recreate:
        print("重建数据库模式已指定，将执行全量导入")
    
    # 导入数据到数据库
    import_to_database(args.recreate, incremental)
    
    # 如果需要导出为CSV
    if args.csv:
        export_to_csv_file()
    
    end_time = time.time()
    elapsed_time = end_time - start_time
    logging.info(f"\n任务完成! 总耗时: {elapsed_time:.2f}秒")
    logging.info(f"数据库文件: {DB_PATH}")

#!/usr/bin/env python
# -*- coding:utf-8 -*-
"""
金融标的验证工具
测试能否获取行情数据并记录结果
包含数据库结构自动更新功能
"""

import tkinter as tk
from tkinter import ttk, messagebox
import sqlite3
import os
import time
import akshare as ak
import ccxt
import threading

# 数据库设置
DB_PATH = "financial_data.db"

def init_database():
    """初始化数据库并确保表结构最新"""
    # 确保数据库目录存在
    db_dir = os.path.dirname(DB_PATH)
    if db_dir and not os.path.exists(db_dir):
        os.makedirs(db_dir, exist_ok=True)
    
    conn = sqlite极
[响应被用户中断]
=======
import tkinter as tk
from tkinter import ttk, messagebox
import sqlite3
import os
import time
import akshare as ak
import ccxt
import threading

# 数据库设置
DB_PATH = "financial_data.db"

def init_database():
    """初始化数据库并确保表结构最新"""
    # 确保数据库目录存在
    db_dir = os.path.dirname(DB_PATH)
    if db_dir and not os.path.exists(db_dir):
        os.makedirs(db_dir, exist_ok=True)
    
    conn = sqlite3.connect(DB_PATH)
    c = conn.cursor()
    
    # 创建或更新金融标的表
    c.execute('''CREATE TABLE IF NOT EXISTS instruments (
                 id INTEGER PRIMARY KEY,
                 name TEXT NOT NULL,
                 symbol TEXT NOT NULL,
                 category TEXT NOT NULL)''')
    
    # 添加测试状态列（如果不存在）
    try:
        c.execute("ALTER TABLE instruments ADD COLUMN test_status TEXT DEFAULT '未测试'")
    except sqlite3.OperationalError:
        pass  # 列已存在
    
    # 添加测试时间列（如果不存在）
    try:
        c.execute("ALTER TABLE instruments ADD COLUMN test_time TEXT")
    except sqlite3.OperationalError:
        pass  # 列已存在
    
    # 添加示例数据
    sample_data = [
        ('贵州茅台', '600519', '股票'),
        ('比特币', 'BTC', '加密货币'),
        ('天弘余额宝', '000198', '基金'),
        ('国债2025', 'GB2025', '债券'),
        ('腾讯控股', '00700', '股票'),
        ('以太坊', 'ETH', '加密货币')
    ]
    
    for data in sample_data:
        c.execute("INSERT OR IGNORE INTO instruments (name, symbol, category) VALUES (?,?,?)", data)
    
    conn.commit()
    conn.close()

def test_instrument(symbol, category):
    """实际测试能否获取行情数据"""
    try:
        start_time = time.time()
        result = "❌未知错误"
        
        if category == "股票":
            # 获取A股实时行情数据
            df = ak.stock_zh_a_spot_em()
            if symbol in df["代码"].values:
                result = "✅成功"
            else:
                result = "❌代码不存在"
                
        elif category == "基金":
            # 获取基金实时净值
            df = ak.fund_em_open_fund_daily()
            if symbol in df["基金代码"].values:
                result = "✅成功"
            else:
                result = "❌代码不存在"
                
        elif category == "债券":
            # 获取债券实时行情
            df = ak.bond_zh_hs_daily(symbol=symbol)
            if not df.empty:
                result = "✅成功"
            else:
                result = "❌代码不存在"
                
        elif category == "加密货币":
            # 使用CCXT获取加密货币行情
            exchange = ccxt.binance()
            try:
                ticker = exchange.fetch_ticker(f"{symbol}/USDT")
                if ticker["last"]:
                    result = "✅成功"
                else:
                    result = "❌无行情数据"
            except ccxt.BadSymbol:
                result = "❌交易对不存在"
            except Exception as e:
                result = f"❌错误: {str(e)}"
                
        else:
            result = "❌不支持类型"
            
        elapsed = time.time() - start_time
        return result, f"{time.strftime('%Y-%m-%d %H:%M:%极
[响应被工具使用结果中断。一次只能使用一个工具，并且应将其放在消息的末尾。]

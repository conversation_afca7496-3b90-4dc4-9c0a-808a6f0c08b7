---
name: AKShare-问题报告
about: 反馈 AKShare 接口相关的问题
title: AKShare 接口问题报告
labels: bug
assignees: ''

---

> 由于开源项目维护工作量较大，本 issue 只接受接口报错问题，如有更多问题，网络上有很多相关方案，

## 重要前提

遇到任何 AKShare 使用问题，请先将您本地的 AKShare 升级到**最新版**，可以通过如下命令升级：

```
pip install akshare --upgrade  # Python 版本需要大于等于 3.9
```

## 如何提交问题

请提交以下相关信息，以更精准的解决问题。**不符合提交规范的 issue 会被关闭！**

**详细问题描述**

1. 请先详细阅读 AKShare 文档中对应接口的使用方式：https://akshare.akfamily.xyz
2. 请务必将 AKShare 升级到最新版本
3. 请检查操作系统版本，目前只支持 64 位主流操作系统
4. 请检查 Python 版本，目前只支持 3.9 以上的版本
5. 请提交相关接口的名称和相应的调用代码
6. 接口报错的截图或描述
7. 期望获得的正确结果

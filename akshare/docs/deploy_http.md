# [AKShare](https://github.com/akfamily/akshare) HTTP 部署

## 说明

AKShare HTTP 版本的部署主要依赖 AKTools、AKShare、FastAPI、Uvicorn、Typer 等开源 Python 库，部署的核心程序在 AKTools 中。
非常感谢给 [AKTools 项目](https://github.com/akfamily/aktools) 点 Star，您的支持是我们持续开发最大的动力！

## 快速启动

### 安装库

```shell
pip install aktools
```

### 运行库

```shell
python -m aktools
```

## 版本说明

1. 仅体验 HTTP API 功能则只需要安装：`pip install aktools==0.0.68` 版本；
2. 体验完整功能请安装最新版，支持用于认证、权限、可视化页面等更多功能。

## 更多详情

[参见 AKTools 项目文档](https://aktools.akfamily.xyz/)

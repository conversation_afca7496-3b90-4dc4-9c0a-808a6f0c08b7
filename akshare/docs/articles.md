# [AKShare](https://github.com/akfamily/akshare) 相关文章

本专栏主要展示利用 [AKShare](https://github.com/akfamily/akshare) 进行数据获取、分析和案例展示相关的文章，
供大家参考和更好的使用 [AKShare](https://github.com/akfamily/akshare) ！

## AKShare 的评价

1. [少八卦，多学习！国内量化开源的顶流项目](https://mp.weixin.qq.com/s?__biz=MzAxNTc0Mjg0Mg==&mid=2653325383&idx=1&sn=de3ea07b5f8d28d63edffa410bf58790&chksm=802d4a52b75ac34478d2e3209adf0a44ba5d353559eb04f47ca8896ba6a40d44dda21dc89503&mpshare=1&scene=23&srcid=0210t8JATFZcRr7QU0VdRjJx&sharer_sharetime=1646818936986&sharer_shareid=a4c6299b7a875e1e5ddbc56b4c71e4dd#rd)

## 公众号文章

1. [用 Python 快速获取基金的股票持仓增减情况 | 更新版](https://mp.weixin.qq.com/s?__biz=MzU1MTM4OTk0MQ==&mid=2247487084&idx=1&sn=b307075af1702d8c1dbda7d9e219a9b5&chksm=fb9351f6cce4d8e0a647c902b25eca89804f8c3169eecd1216f0c5c7d90e64bee900cc7ed2c1&mpshare=1&scene=23&srcid=0309GDbmEHx70KjI19ionXfv&sharer_sharetime=1646819026253&sharer_shareid=a4c6299b7a875e1e5ddbc56b4c71e4dd#rd)
2. [AkShare之ETF历史行情](https://mp.weixin.qq.com/s?__biz=MzkyMDIxMzU1Nw==&mid=2247484881&idx=1&sn=5c721cd1e65e64ba084684a8a6970063&chksm=c197048bf6e08d9d5d69568dc00262d6fc44df354eca5cfb02966136f158e15423d5f766b2f0&mpshare=1&scene=23&srcid=0311iSQrYF4NjKbkUaRmu8H3&sharer_sharetime=1647008212548&sharer_shareid=a4c6299b7a875e1e5ddbc56b4c71e4dd#rd)
3. [零基础量化交易学习（3）Pandas](https://mp.weixin.qq.com/s?__biz=MzkzMjIzMzE0MA==&mid=2247483696&idx=1&sn=88082c9cdef709930736f933c4464d49&chksm=c25fabebf52822fd2744477754fdf107594c7ad67458594927d6396be88dd1ccb6736c3cf3dc&mpshare=1&scene=23&srcid=0311JEHUF088jvBKila4T2dZ&sharer_sharetime=1647008240525&sharer_shareid=a4c6299b7a875e1e5ddbc56b4c71e4dd#rd)
4. [时间序列的平稳性检验方法汇总](https://mp.weixin.qq.com/s?__biz=MzUzODYwMDAzNA==&mid=2247557469&idx=1&sn=4bcaddbc7004079c2687696360414448&chksm=fad69650cda11f46ce8929a3a3db613ea968fe934a53b6384bb56839213de82c4541f8432736&mpshare=1&scene=23&srcid=0311InxSRDsJOivi58BoOqTg&sharer_sharetime=1647008260414&sharer_shareid=a4c6299b7a875e1e5ddbc56b4c71e4dd#rd)
5. [用Python搞了个基金查询机器人，还可以拓展！](https://mp.weixin.qq.com/s?__biz=MzUyOTAwMzI4NA==&mid=2247525637&idx=1&sn=a0842b1c2d6f3d531947be6087a4d46e&chksm=fa65837ecd120a68b1c83c702081e507e1fdfc6f566018414237150a70ac3d8bddc60926f822&mpshare=1&scene=23&srcid=03115P2wMtTWbeYwHdng9W7R&sharer_sharetime=1647008305826&sharer_shareid=a4c6299b7a875e1e5ddbc56b4c71e4dd#rd)

## 博客文章

1. [akshare做mfi策略](https://blog.csdn.net/qq_26742269/article/details/123024482)
2. [akshare写etf动量滚动策略](https://blog.csdn.net/qq_26742269/article/details/123490942)
3. [akshare改写公募基金轮动策略](https://blog.csdn.net/qq_26742269/article/details/123488096)
4. [价值投资/指标选股（akshare）](https://blog.csdn.net/qq_26742269/article/details/123377668)
5. [akshare sma策略](https://blog.csdn.net/qq_26742269/article/details/122916796)
6. [akshare 布林通道策略](https://blog.csdn.net/qq_26742269/article/details/122916783)
7. [akshare 配对策略](https://blog.csdn.net/qq_26742269/article/details/122916800)
8. [获取财经数据神器akshare 基本使用总结](https://blog.csdn.net/fyfugoyfa/article/details/113131184)
9. [股债收益模型 量化实战篇（一）](https://bbs.csdn.net/topics/605332332)
10. [配对交易（一）：期货品种相关性研究](https://blog.csdn.net/anddyyhuang/article/details/124307032)
11. [用AkShare库获取A股股票数据—获取实时A股数据](https://blog.csdn.net/malishizu222/article/details/124181141?spm=1001.2014.3001.5502)
12. [用AkShare获取沪深京A股所有股票历史数据](https://blog.csdn.net/malishizu222/article/details/124203415?spm=1001.2014.3001.5502)
13. [用AkShare获取沪深京A股分钟级K线数据](https://blog.csdn.net/malishizu222/article/details/124223018?spm=1001.2014.3001.5502)
14. [用AkShare获取实盘沪深可转债数据](https://blog.csdn.net/malishizu222/article/details/124272719?spm=1001.2014.3001.5502)
15. [用AkShare获取沪深可转债分时数据](https://blog.csdn.net/malishizu222/article/details/124280083?spm=1001.2014.3001.5502)
16. [akshare股市新闻情绪判断](https://www.cnblogs.com/xingnie/p/16123269.html)
17. [akshare量化股票市场情绪指标ARBR](https://www.cnblogs.com/xingnie/p/16123257.html)
18. [Akshare 获取日线策略并发送邮件](https://www.cnblogs.com/xingnie/p/16099214.html)

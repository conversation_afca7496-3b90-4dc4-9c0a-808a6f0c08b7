## [AKShare](https://github.com/akfamily/akshare) 期货数据

### 期货基础信息

主要提供金融期货和商品期货相关的数据

#### 期货交易所

| 交易所名称                                | 交易所代码 | 合约后缀  | 首页地址                    |
|--------------------------------------|-------|-------|-------------------------|
| [中国金融期货交易所](http://www.cffex.com.cn) | CFFEX | .CFX  | http://www.cffex.com.cn |
| [上海期货交易所](https://www.shfe.com.cn)   | SHFE  | .SHF  | https://www.shfe.com.cn |
| [上海国际能源交易中心](https://www.ine.cn)     | INE   | .INE  | https://www.ine.cn      |
| [郑州商品交易所](http://www.czce.com.cn)    | CZCE  | .ZCE  | http://www.czce.com.cn  |
| [大连商品交易所](http://www.dce.com.cn)     | DCE   | .DCE  | http://www.dce.com.cn   |
| [广州期货交易所](http://www.gfex.com.cn)    | GFEX  | .GFEX | http://www.gfex.com.cn  |

#### 期货交易时间

**本表格更新于 20241118**

| 交易所        | 交易所代码 | 品种名称     | 品种代码 | 集合竞价                     | 日盘时间                                  | 夜盘时间        |
|------------|-------|----------|------|--------------------------|---------------------------------------|-------------|
| 上海期货交易所    | SHFE  | 螺纹钢      | rb   | 20:55-21:00, 08:55-09:00 | 09:00-10:15, 10:30-11:30, 13:30-15:00 | 21:00-23:00 |
| 上海期货交易所    | SHFE  | 热轧卷板     | hc   | 20:55-21:00, 08:55-09:00 | 09:00-10:15, 10:30-11:30, 13:30-15:00 | 21:00-23:00 |
| 上海期货交易所    | SHFE  | 石油沥青     | bu   | 20:55-21:00, 08:55-09:00 | 09:00-10:15, 10:30-11:30, 13:30-15:00 | 21:00-23:00 |
| 上海期货交易所    | SHFE  | 天然橡胶     | ru   | 20:55-21:00, 08:55-09:00 | 09:00-10:15, 10:30-11:30, 13:30-15:00 | 21:00-23:00 |
| 上海期货交易所    | SHFE  | 合成橡胶     | br   | 20:55-21:00, 08:55-09:00 | 09:00-10:15, 10:30-11:30, 13:30-15:00 | 21:00-23:00 |
| 上海期货交易所    | SHFE  | 燃料油      | fu   | 20:55-21:00, 08:55-09:00 | 09:00-10:15, 10:30-11:30, 13:30-15:00 | 21:00-23:00 |
| 上海期货交易所    | SHFE  | 纸浆       | sp   | 20:55-21:00, 08:55-09:00 | 09:00-10:15, 10:30-11:30, 13:30-15:00 | 21:00-23:00 |
| 上海期货交易所    | SHFE  | 铜        | cu   | 20:55-21:00, 08:55-09:00 | 09:00-10:15, 10:30-11:30, 13:30-15:00 | 21:00-01:00 |
| 上海期货交易所    | SHFE  | 铝        | al   | 20:55-21:00, 08:55-09:00 | 09:00-10:15, 10:30-11:30, 13:30-15:00 | 21:00-01:00 |
| 上海期货交易所    | SHFE  | 氧化铝      | ao   | 20:55-21:00, 08:55-09:00 | 09:00-10:15, 10:30-11:30, 13:30-15:00 | 21:00-01:00 |
| 上海期货交易所    | SHFE  | 铅        | pb   | 20:55-21:00, 08:55-09:00 | 09:00-10:15, 10:30-11:30, 13:30-15:00 | 21:00-01:00 |
| 上海期货交易所    | SHFE  | 锌        | zn   | 20:55-21:00, 08:55-09:00 | 09:00-10:15, 10:30-11:30, 13:30-15:00 | 21:00-01:00 |
| 上海期货交易所    | SHFE  | 锡        | sn   | 20:55-21:00, 08:55-09:00 | 09:00-10:15, 10:30-11:30, 13:30-15:00 | 21:00-01:00 |
| 上海期货交易所    | SHFE  | 镍        | ni   | 20:55-21:00, 08:55-09:00 | 09:00-10:15, 10:30-11:30, 13:30-15:00 | 21:00-01:00 |
| 上海期货交易所    | SHFE  | 不锈钢      | ss   | 20:55-21:00, 08:55-09:00 | 09:00-10:15, 10:30-11:30, 13:30-15:00 | 21:00-01:00 |
| 上海期货交易所    | SHFE  | 黄金       | au   | 20:55-21:00, 08:55-09:00 | 09:00-10:15, 10:30-11:30, 13:30-15:00 | 21:00-02:30 |
| 上海期货交易所    | SHFE  | 白银       | ag   | 20:55-21:00, 08:55-09:00 | 09:00-10:15, 10:30-11:30, 13:30-15:00 | 21:00-02:30 |
| 上海期货交易所    | SHFE  | 线材       | wr   | 08:55-09:00              | 09:00-10:15, 10:30-11:30, 13:30-15:00 | -           |
| 上海国际能源交易中心 | INE   | 20号胶     | nr   | 20:55-21:00, 08:55-09:00 | 09:00-10:15, 10:30-11:30, 13:30-15:00 | 21:00-23:00 |
| 上海国际能源交易中心 | INE   | 低硫燃料油    | lu   | 20:55-21:00, 08:55-09:00 | 09:00-10:15, 10:30-11:30, 13:30-15:00 | 21:00-23:00 |
| 上海国际能源交易中心 | INE   | 国际铜      | bc   | 20:55-21:00, 08:55-09:00 | 09:00-10:15, 10:30-11:30, 13:30-15:00 | 21:00-01:00 |
| 上海国际能源交易中心 | INE   | 原油       | sc   | 20:55-21:00, 08:55-09:00 | 09:00-10:15, 10:30-11:30, 13:30-15:00 | 21:00-02:30 |
| 上海国际能源交易中心 | INE   | 集运指数(欧线) | ec   | 08:55-09:00              | 09:00-10:15, 10:30-11:30, 13:30-15:00 | -           |
| 大连商品交易所    | DCE   | 黄大豆1号    | a    | 20:55-21:00, 08:55-09:00 | 09:00-10:15, 10:30-11:30, 13:30-15:00 | 21:00-23:00 |
| 大连商品交易所    | DCE   | 黄大豆2号    | b    | 20:55-21:00, 08:55-09:00 | 09:00-10:15, 10:30-11:30, 13:30-15:00 | 21:00-23:00 |
| 大连商品交易所    | DCE   | 黄玉米      | c    | 20:55-21:00, 08:55-09:00 | 09:00-10:15, 10:30-11:30, 13:30-15:00 | 21:00-23:00 |
| 大连商品交易所    | DCE   | 玉米淀粉     | cs   | 20:55-21:00, 08:55-09:00 | 09:00-10:15, 10:30-11:30, 13:30-15:00 | 21:00-23:00 |
| 大连商品交易所    | DCE   | 豆粕       | m    | 20:55-21:00, 08:55-09:00 | 09:00-10:15, 10:30-11:30, 13:30-15:00 | 21:00-23:00 |
| 大连商品交易所    | DCE   | 豆油       | y    | 20:55-21:00, 08:55-09:00 | 09:00-10:15, 10:30-11:30, 13:30-15:00 | 21:00-23:00 |
| 大连商品交易所    | DCE   | 棕榈油      | p    | 20:55-21:00, 08:55-09:00 | 09:00-10:15, 10:30-11:30, 13:30-15:00 | 21:00-23:00 |
| 大连商品交易所    | DCE   | 铁矿石      | i    | 20:55-21:00, 08:55-09:00 | 09:00-10:15, 10:30-11:30, 13:30-15:00 | 21:00-23:00 |
| 大连商品交易所    | DCE   | 焦炭       | j    | 20:55-21:00, 08:55-09:00 | 09:00-10:15, 10:30-11:30, 13:30-15:00 | 21:00-23:00 |
| 大连商品交易所    | DCE   | 焦煤       | jm   | 20:55-21:00, 08:55-09:00 | 09:00-10:15, 10:30-11:30, 13:30-15:00 | 21:00-23:00 |
| 大连商品交易所    | DCE   | 聚乙烯      | l    | 20:55-21:00, 08:55-09:00 | 09:00-10:15, 10:30-11:30, 13:30-15:00 | 21:00-23:00 |
| 大连商品交易所    | DCE   | 聚氯乙烯     | v    | 20:55-21:00, 08:55-09:00 | 09:00-10:15, 10:30-11:30, 13:30-15:00 | 21:00-23:00 |
| 大连商品交易所    | DCE   | 聚丙烯      | pp   | 20:55-21:00, 08:55-09:00 | 09:00-10:15, 10:30-11:30, 13:30-15:00 | 21:00-23:00 |
| 大连商品交易所    | DCE   | 乙二醇      | eg   | 20:55-21:00, 08:55-09:00 | 09:00-10:15, 10:30-11:30, 13:30-15:00 | 21:00-23:00 |
| 大连商品交易所    | DCE   | 粳米       | rr   | 20:55-21:00, 08:55-09:00 | 09:00-10:15, 10:30-11:30, 13:30-15:00 | 21:00-23:00 |
| 大连商品交易所    | DCE   | 苯乙烯      | eb   | 20:55-21:00, 08:55-09:00 | 09:00-10:15, 10:30-11:30, 13:30-15:00 | 21:00-23:00 |
| 大连商品交易所    | DCE   | 液化石油气    | pg   | 20:55-21:00, 08:55-09:00 | 09:00-10:15, 10:30-11:30, 13:30-15:00 | 21:00-23:00 |
| 大连商品交易所    | DCE   | 鸡蛋       | jd   | 08:55-09:00              | 09:00-10:15, 10:30-11:30, 13:30-15:00 | -           |
| 大连商品交易所    | DCE   | 纤维板      | fb   | 08:55-09:00              | 09:00-10:15, 10:30-11:30, 13:30-15:00 | -           |
| 大连商品交易所    | DCE   | 胶合板      | bb   | 08:55-09:00              | 09:00-10:15, 10:30-11:30, 13:30-15:00 | -           |
| 大连商品交易所    | DCE   | 生猪       | lh   | 08:55-09:00              | 09:00-10:15, 10:30-11:30, 13:30-15:00 | -           |
| 大连商品交易所    | DCE   | 原木       | lg   | 08:55-09:00              | 09:00-10:15, 10:30-11:30, 13:30-15:00 | -           |
| 郑州商品交易所    | CZCE  | 菜粕       | RM   | 20:55-21:00              | 09:00-10:15, 10:30-11:30, 13:30-15:00 | 21:00-23:00 |
| 郑州商品交易所    | CZCE  | 菜籽油      | OI   | 20:55-21:00              | 09:00-10:15, 10:30-11:30, 13:30-15:00 | 21:00-23:00 |
| 郑州商品交易所    | CZCE  | 一号棉花     | CF   | 20:55-21:00              | 09:00-10:15, 10:30-11:30, 13:30-15:00 | 21:00-23:00 |
| 郑州商品交易所    | CZCE  | 精对苯二甲酸   | TA   | 20:55-21:00              | 09:00-10:15, 10:30-11:30, 13:30-15:00 | 21:00-23:00 |
| 郑州商品交易所    | CZCE  | 对二甲苯     | PX   | 20:55-21:00              | 09:00-10:15, 10:30-11:30, 13:30-15:00 | 21:00-23:00 |
| 郑州商品交易所    | CZCE  | 白砂糖      | SR   | 20:55-21:00              | 09:00-10:15, 10:30-11:30, 13:30-15:00 | 21:00-23:00 |
| 郑州商品交易所    | CZCE  | 甲醇       | MA   | 20:55-21:00              | 09:00-10:15, 10:30-11:30, 13:30-15:00 | 21:00-23:00 |
| 郑州商品交易所    | CZCE  | 玻璃       | FG   | 20:55-21:00              | 09:00-10:15, 10:30-11:30, 13:30-15:00 | 21:00-23:00 |
| 郑州商品交易所    | CZCE  | 动力煤      | ZC   | 20:55-21:00              | 09:00-10:15, 10:30-11:30, 13:30-15:00 | 21:00-23:00 |
| 郑州商品交易所    | CZCE  | 棉纱       | CY   | 20:55-21:00              | 09:00-10:15, 10:30-11:30, 13:30-15:00 | 21:00-23:00 |
| 郑州商品交易所    | CZCE  | 纯碱       | SA   | 20:55-21:00              | 09:00-10:15, 10:30-11:30, 13:30-15:00 | 21:00-23:00 |
| 郑州商品交易所    | CZCE  | 烧碱       | SH   | 20:55-21:00              | 09:00-10:15, 10:30-11:30, 13:30-15:00 | 21:00-23:00 |
| 郑州商品交易所    | CZCE  | 短纤       | PF   | 20:55-21:00              | 09:00-10:15, 10:30-11:30, 13:30-15:00 | 21:00-23:00 |
| 郑州商品交易所    | CZCE  | 瓶片       | PR   | 08:55-09:00              | 09:00-10:15, 10:30-11:30, 13:30-15:00 | 21:00-23:00 |
| 郑州商品交易所    | CZCE  | 粳稻       | JR   | 08:55-09:00              | 09:00-10:15, 10:30-11:30, 13:30-15:00 | -           |
| 郑州商品交易所    | CZCE  | 菜籽       | RS   | 08:55-09:00              | 09:00-10:15, 10:30-11:30, 13:30-15:00 | -           |
| 郑州商品交易所    | CZCE  | 普通小麦     | PM   | 08:55-09:00              | 09:00-10:15, 10:30-11:30, 13:30-15:00 | -           |
| 郑州商品交易所    | CZCE  | 强麦       | WH   | 08:55-09:00              | 09:00-10:15, 10:30-11:30, 13:30-15:00 | -           |
| 郑州商品交易所    | CZCE  | 早籼稻      | RI   | 08:55-09:00              | 09:00-10:15, 10:30-11:30, 13:30-15:00 | -           |
| 郑州商品交易所    | CZCE  | 晚籼稻      | LR   | 08:55-09:00              | 09:00-10:15, 10:30-11:30, 13:30-15:00 | -           |
| 郑州商品交易所    | CZCE  | 硅铁       | SF   | 08:55-09:00              | 09:00-10:15, 10:30-11:30, 13:30-15:00 | -           |
| 郑州商品交易所    | CZCE  | 锰硅       | SM   | 08:55-09:00              | 09:00-10:15, 10:30-11:30, 13:30-15:00 | -           |
| 郑州商品交易所    | CZCE  | 苹果       | AP   | 08:55-09:00              | 09:00-10:15, 10:30-11:30, 13:30-15:00 | -           |
| 郑州商品交易所    | CZCE  | 红枣       | CJ   | 08:55-09:00              | 09:00-10:15, 10:30-11:30, 13:30-15:00 | -           |
| 郑州商品交易所    | CZCE  | 尿素       | UR   | 08:55-09:00              | 09:00-10:15, 10:30-11:30, 13:30-15:00 | -           |
| 郑州商品交易所    | CZCE  | 花生       | PK   | 08:55-09:00              | 09:00-10:15, 10:30-11:30, 13:30-15:00 | -           |
| 广州期货交易所    | GFEX  | 工业硅      | SI   | 08:55-09:00              | 09:00-10:15, 10:30-11:30, 13:30-15:00 | -           |
| 广州期货交易所    | GFEX  | 碳酸锂      | LC   | 08:55-09:00              | 09:00-10:15, 10:30-11:30, 13:30-15:00 | -           |
| 广州期货交易所    | GFEX  | 多晶硅      | PS   | 08:55-09:00              | 09:00-10:15, 10:30-11:30, 13:30-15:00 | -           |
| 中国金融期货交易所  | CFFEX | 沪深300指数  | IF   | 09:25-09:30              | 09:30-11:30, 13:00-15:00              | -           |
| 中国金融期货交易所  | CFFEX | 上证50指数   | IH   | 09:25-09:30              | 09:30-11:30, 13:00-15:00              | -           |
| 中国金融期货交易所  | CFFEX | 中证500指数  | IC   | 09:25-09:30              | 09:30-11:30, 13:00-15:00              | -           |
| 中国金融期货交易所  | CFFEX | 中证1000指数 | IM   | 09:25-09:30              | 09:30-11:30, 13:00-15:00              | -           |
| 中国金融期货交易所  | CFFEX | 2年期国债    | TS   | 09:25-09:30              | 09:30-11:30, 13:00-15:15              | -           |
| 中国金融期货交易所  | CFFEX | 5年期国债    | TF   | 09:25-09:30              | 09:30-11:30, 13:00-15:15              | -           |
| 中国金融期货交易所  | CFFEX | 10年期国债   | T    | 09:25-09:30              | 09:30-11:30, 13:00-15:15              | -           |
| 中国金融期货交易所  | CFFEX | 30年期国债   | TL   | 09:25-09:30              | 09:30-11:30, 13:00-15:15              | -           |

#### 金融期货

##### 中国金融期货交易所

**本表更新于 20241118**

|   | 交易所简称     | 交易品种       | 证券代码 | 标准合约上市日    |
|--:|:----------|:-----------|:-----|:-----------|
| 1 | 中国金融期货交易所 | 沪深300股指期货  | IF   | 2010-04-16 |
| 2 | 中国金融期货交易所 | 5年期国债期货    | TF   | 2013-09-06 |
| 3 | 中国金融期货交易所 | 10年期国债期货   | T    | 2015-03-20 |
| 4 | 中国金融期货交易所 | 中证500股指期货  | IC   | 2015-04-16 |
| 5 | 中国金融期货交易所 | 上证50股指期货   | IH   | 2015-04-16 |
| 6 | 中国金融期货交易所 | 2年期国债期货    | TS   | 2017-02-27 |
| 7 | 中国金融期货交易所 | 中证1000股指期货 | IM   | 2022-07-22 |
| 8 | 中国金融期货交易所 | 30年期国债期货   | TL   | 2023-04-21 |

#### 商品期货

##### 上海国际能源交易中心

**本表更新于 20241118**

|   | 交易所简称      | 交易品种     | 证券代码 | 标准合约上市日    |
|--:|:-----------|:---------|:-----|:-----------|
| 1 | 上海国际能源交易中心 | 原油       | sc   | 2018-03-26 |
| 2 | 上海国际能源交易中心 | 20号胶     | nr   | 2019-08-12 |
| 3 | 上海国际能源交易中心 | 低硫燃料油    | lu   | 2020-06-22 |
| 4 | 上海国际能源交易中心 | 国际铜      | bc   | 2020-11-19 |
| 5 | 上海国际能源交易中心 | 集运指数(欧线) | ec   | 2023-08-18 |

##### 上海期货交易所

**本表更新于 20241118**

|    | 交易所简称   | 交易品种  | 证券代码 | 标准合约上市日    |
|---:|:--------|:------|:-----|:-----------|
|  1 | 上海期货交易所 | 橡胶    | RU   | 1999-05-04 |
|  2 | 上海期货交易所 | 沪铜    | CU   | 1999-06-18 |
|  3 | 上海期货交易所 | 沪铝    | AL   | 1999-08-04 |
|  4 | 上海期货交易所 | 燃油    | FU   | 2004-08-25 |
|  5 | 上海期货交易所 | 沪锌    | ZN   | 2007-03-26 |
|  6 | 上海期货交易所 | 沪金    | AU   | 2008-01-09 |
|  7 | 上海期货交易所 | 螺纹钢   | RB   | 2009-03-27 |
|  8 | 上海期货交易所 | 线材    | WR   | 2009-03-27 |
|  9 | 上海期货交易所 | 沪铅    | PB   | 2011-03-24 |
| 10 | 上海期货交易所 | 沪银    | AG   | 2012-05-10 |
| 11 | 上海期货交易所 | 沥青    | BU   | 2013-10-09 |
| 12 | 上海期货交易所 | 热卷    | HC   | 2014-03-21 |
| 13 | 上海期货交易所 | 锡     | SN   | 2015-03-27 |
| 14 | 上海期货交易所 | 镍     | NI   | 2015-03-27 |
| 15 | 上海期货交易所 | 纸浆    | SP   | 2018-11-27 |
| 16 | 上海期货交易所 | 不锈钢   | SS   | 2019-09-25 |
| 17 | 上海期货交易所 | 氧化铝   | AO   | 2023-06-19 |
| 18 | 上海期货交易所 | 丁二烯橡胶 | BR   | 2023-07-28 |

##### 郑州商品交易所

**本表更新于 20241118**

|    | 交易所简称   | 交易品种 | 证券代码 | 标准合约上市日    |
|---:|:--------|:-----|:-----|:-----------|
|  1 | 郑州商品交易所 | 郑棉   | CF   | 2004-06-01 |
|  2 | 郑州商品交易所 | 白糖   | SR   | 2006-01-06 |
|  3 | 郑州商品交易所 | PTA  | TA   | 2006-12-18 |
|  4 | 郑州商品交易所 | 普麦   | PM   | 2012-01-17 |
|  5 | 郑州商品交易所 | 玻璃   | FG   | 2012-12-03 |
|  6 | 郑州商品交易所 | 菜粕   | RM   | 2012-12-28 |
|  7 | 郑州商品交易所 | 菜籽   | RS   | 2012-12-28 |
|  8 | 郑州商品交易所 | 菜油   | OI   | 2013-05-16 |
|  9 | 郑州商品交易所 | 早籼   | RI   | 2013-05-24 |
| 10 | 郑州商品交易所 | 强麦   | WH   | 2013-05-24 |
| 11 | 郑州商品交易所 | 郑煤   | ZC   | 2013-09-26 |
| 12 | 郑州商品交易所 | 粳稻   | JR   | 2013-11-18 |
| 13 | 郑州商品交易所 | 晚籼   | LR   | 2014-07-08 |
| 14 | 郑州商品交易所 | 锰硅   | SM   | 2014-08-08 |
| 15 | 郑州商品交易所 | 硅铁   | SF   | 2014-08-08 |
| 16 | 郑州商品交易所 | 甲醇   | MA   | 2015-05-18 |
| 17 | 郑州商品交易所 | 棉纱   | CY   | 2017-08-18 |
| 18 | 郑州商品交易所 | 苹果   | AP   | 2017-12-22 |
| 19 | 郑州商品交易所 | 红枣   | CJ   | 2019-04-30 |
| 20 | 郑州商品交易所 | 尿素   | UR   | 2019-08-09 |
| 21 | 郑州商品交易所 | 纯碱   | SA   | 2019-12-06 |
| 22 | 郑州商品交易所 | 短纤   | PF   | 2020-10-12 |
| 23 | 郑州商品交易所 | 花生   | PK   | 2021-02-01 |
| 24 | 郑州商品交易所 | 烧碱   | SH   | 2023-09-15 |
| 25 | 郑州商品交易所 | 对二甲苯 | PX   | 2023-09-15 |
| 26 | 郑州商品交易所 | 瓶片   | PR   | 2024-08-30 |

##### 大连商品交易所

**本表更新于 20241118**

|    | 交易所简称   | 交易品种  | 证券代码 | 标准合约上市日    |
|---:|:--------|:------|:-----|:-----------|
|  1 | 大连商品交易所 | 豆一    | A    | 1999-01-18 |
|  2 | 大连商品交易所 | 豆粕    | M    | 2000-07-17 |
|  3 | 大连商品交易所 | 玉米    | C    | 2004-09-22 |
|  4 | 大连商品交易所 | 豆二    | B    | 2004-12-22 |
|  5 | 大连商品交易所 | 豆油    | Y    | 2006-01-09 |
|  6 | 大连商品交易所 | 塑料    | L    | 2007-07-31 |
|  7 | 大连商品交易所 | 棕榈    | P    | 2007-10-29 |
|  8 | 大连商品交易所 | PVC   | V    | 2009-05-25 |
|  9 | 大连商品交易所 | 焦炭    | J    | 2011-04-15 |
| 10 | 大连商品交易所 | 焦煤    | JM   | 2013-03-22 |
| 11 | 大连商品交易所 | 铁矿石   | I    | 2013-10-18 |
| 12 | 大连商品交易所 | 鸡蛋    | JD   | 2013-11-08 |
| 13 | 大连商品交易所 | 纤维板   | FB   | 2013-12-06 |
| 14 | 大连商品交易所 | 胶合板   | BB   | 2013-12-06 |
| 15 | 大连商品交易所 | 聚丙烯   | PP   | 2014-02-28 |
| 16 | 大连商品交易所 | 玉米淀粉  | CS   | 2014-12-19 |
| 17 | 大连商品交易所 | 乙二醇   | EG   | 2018-12-10 |
| 18 | 大连商品交易所 | 粳米    | RR   | 2019-08-16 |
| 19 | 大连商品交易所 | 苯乙烯   | EB   | 2019-09-26 |
| 20 | 大连商品交易所 | 液化石油气 | PG   | 2020-03-30 |
| 21 | 大连商品交易所 | 生猪    | LH   | 2021-01-08 |
| 22 | 大连商品交易所 | 原木    | LG   | 2024-11-18 |

##### 广州期货交易所

**本表更新于 20241118**

|   | 交易所简称   | 交易品种 | 证券代码 | 标准合约上市日    |
|--:|:--------|:-----|:-----|:-----------|
| 1 | 广州期货交易所 | 工业硅  | si   | 2022-12-22 |
| 2 | 广州期货交易所 | 碳酸锂  | lc   | 2023-07-21 |
| 3 | 广州期货交易所 | 多晶硅  | ps   | 2024-12-26 |

### 期货基础名词

#### 连续合约

需要注意, 由于期货合约存续的特殊性, 针对每一品种的期货合约, 系统中都增加了主力连续合约以及指数连续合约两个人工合成的合约来满足使用需求.

#### 主力连续合约

主力连续合约: 合约首次上市时, 以当日收盘同品种持仓量最大者作为从第二个交易日开始的主力合约.
当同品种其他合约持仓量在收盘后超过当前主力合约 1.1 倍时, 从第二个交易日开始进行主力合约的切换.
日内不会进行主力合约的切换.
主力连续合约是由该品种期货不同时期主力合约接续而成, 代码以 88 或 888 结尾结尾, 例如 IF88 或 IF888.
前者为合约量价数据的简单拼接, 未做平滑处理;
后者对价格进行了”平滑”处理, 处理规则如下:
以主力合约切换前一天(T-1日)新、旧两个主力合约收盘价做差,
之后将 T-1 日及以前的主力连续合约的所有价格水平整体加上或减去该价差,
以”整体抬升”或”整体下降”主力合约的价格水平, 成交量、持仓量均不作调整, 成交额统一设置为 0.

#### 指数连续合约

指数连续合约: 由当前品种全部可交易合约以累计持仓量为权重加权平均得到, 代码以 99 结尾, 例如 IF99.

在期货市场中，指数连续合约（Continuous Futures Contract）是一种将多个到期月份的期货合约连接起来，形成一个连续的价格数据序列。
它的主要目的是为了解决期货合约到期换仓的问题，便于投资者进行长期分析和交易。

由于期货合约具有到期限制，投资者在进行长期持仓时需要在合约到期前将持有的合约平仓，并买入下一个到期的合约。
这个过程称为“展期”或“换仓”。在这个过程中，不同到期合约之间的价格差异可能导致投资收益的波动。为了解决这一问题，期货市场上出现了指数连续合约的概念。

指数连续合约的构建方法有很多种，其中最常见的是基于成交量和持仓量的加权平均。
在这种方法中，将不同到期合约的价格按照成交量或持仓量进行加权平均，以得到一个连续的价格序列。这样，投资者可以通过观察指数连续合约的走势，来更好地了解整个期货市场的动态。

需要注意的是，指数连续合约并非真实存在的交易品种，它只是一种数据处理方法，用于帮助投资者分析和制定交易策略。
实际上，投资者仍然需要在各个到期月份的期货合约之间进行展期或换仓操作。

总之，指数连续合约是一种在期货市场中广泛应用的分析工具，有助于投资者进行长期市场分析和交易策略制定。

#### 展期收益率

期货市场中的展期收益率（Rolling Yield）是指在期货合约到期时，投资者将持有的合约换成下一个到期的合约时可能产生的收益。
展期收益率在期货市场中具有重要意义，因为它反映了期货市场的期限结构，也可以作为投资者制定投资策略的依据。

展期收益率的计算方法是将当前期货合约的价格与下一个到期的期货合约的价格进行比较。具体计算公式如下：

展期收益率 = (下一个到期合约价格 - 当前到期合约价格) / 当前到期合约价格

展期收益率可以是正数，也可以是负数。正数表示下一个到期的合约价格高于当前到期的合约价格，投资者在展期时可以获得正向收益；
负数表示下一个到期的合约价格低于当前到期的合约价格，投资者在展期时可能面临损失。

期货市场的期限结构通常分为三种类型：

1. 远期溢价（Contango）：远期合约价格高于近期合约价格，展期收益率为正。
2. 远期折价（Backwardation）：远期合约价格低于近期合约价格，展期收益率为负。
3. 平价（Flat）：远期合约价格与近期合约价格相同，展期收益率接近于零。

投资者可以根据不同的期限结构和展期收益率来调整其期货交易策略，以实现收益最大化或风险最小化。

### 期货基础数据

#### 期货交易费用参照表

接口: futures_fees_info

目标地址: http://openctp.cn/fees.html

描述: openctp 期货交易费用参照表

限量: 单次返回所有数据

输入参数

| 名称 | 类型 | 描述 |
|----|----|----|
| -  | -  | -  |

输出参数

| 名称          | 类型      | 描述 |
|-------------|---------|----|
| 交易所         | object  | -  |
| 合约代码        | object  | -  |
| 合约名称        | object  | -  |
| 品种代码        | object  | -  |
| 品种名称        | object  | -  |
| 合约乘数        | int64   | -  |
| 最小跳动        | float64 | -  |
| 开仓费率（按金额）   | float64 | -  |
| 开仓费用（按手）    | float64 | -  |
| 平仓费率（按金额）   | float64 | -  |
| 平仓费用（按手）    | float64 | -  |
| 平今费率（按金额）   | float64 | -  |
| 平今费用（按手）    | float64 | -  |
| 做多保证金率（按金额） | float64 | -  |
| 做多保证金（按手）   | int64   | -  |
| 做空保证金率（按金额） | float64 | -  |
| 做空保证金（按手）   | int64   | -  |
| 上日结算价       | float64 | -  |
| 上日收盘价       | float64 | -  |
| 最新价         | float64 | -  |
| 成交量         | int64   | -  |
| 持仓量         | int64   | -  |
| 1手开仓费用      | float64 | -  |
| 1手平仓费用      | float64 | -  |
| 1手平今费用      | float64 | -  |
| 做多1手保证金     | float64 | -  |
| 做空1手保证金     | float64 | -  |
| 1Tick平仓盈亏   | float64 | -  |
| 2Tick平仓盈亏   | float64 | -  |
| 1Tick平仓收益率  | object  | -  |
| 2Tick平仓收益率  | object  | -  |
| 1Tick平今盈亏   | float64 | -  |
| 2Tick平今盈亏   | float64 | -  |
| 1Tick平今收益率  | object  | -  |
| 2Tick平今收益率  | object  | -  |
| 更新时间        | object  | -  |

接口示例

```python
import akshare as ak

futures_fees_info_df = ak.futures_fees_info()
print(futures_fees_info_df)
```

数据示例

```
      交易所    合约代码    合约名称  ... 1Tick平今收益率 2Tick平今收益率                 更新时间
0    CZCE   AP410   苹果10月  ...    -0.147%    -0.049%  2024-07-05 15:12:27
1    CZCE   AP411   苹果11月  ...    -0.153%    -0.051%  2024-07-05 15:12:27
2    CZCE   AP412   苹果12月  ...     -0.15%     -0.05%  2024-07-05 15:12:27
3    CZCE   AP501    苹果1月  ...    -0.148%     -0.05%  2024-07-05 15:12:27
4    CZCE   AP503    苹果3月  ...    -0.148%    -0.049%  2024-07-05 15:12:27
..    ...     ...     ...  ...        ...        ...                  ...
764  SHFE  zn2502  zn2502  ...     0.138%     0.295%  2024-07-05 15:12:27
765  SHFE  zn2503  zn2503  ...     0.138%     0.295%  2024-07-05 15:12:27
766  SHFE  zn2504  zn2504  ...     0.138%     0.295%  2024-07-05 15:12:27
767  SHFE  zn2505  zn2505  ...     0.138%     0.296%  2024-07-05 15:12:27
768  SHFE  zn2506  zn2506  ...     0.138%     0.295%  2024-07-05 15:12:27
[769 rows x 36 columns]
```

#### 期货手续费与保证金

接口: futures_comm_info

目标地址: https://www.9qihuo.com/qihuoshouxufei

描述: 九期网-期货手续费数据

限量: 单次返回指定 symbol 的所有数据

输入参数

| 名称     | 类型  | 描述                                                                                                   |
|--------|-----|------------------------------------------------------------------------------------------------------|
| symbol | str | symbol="所有"; choice of {"所有", "上海期货交易所", "大连商品交易所", "郑州商品交易所", "上海国际能源交易中心", "中国金融期货交易所", "广州期货交易所"} |

输出参数

| 名称           | 类型      | 描述      |
|--------------|---------|---------|
| 交易所名称        | object  | -       |
| 合约名称         | object  | -       |
| 合约代码         | object  | -       |
| 现价           | float64 | -       |
| 涨停板          | float64 | -       |
| 跌停板          | float64 | -       |
| 保证金-买开       | float64 | 注意单位: % |
| 保证金-卖开       | float64 | 注意单位: % |
| 保证金-每手       | float64 | 注意单位: 元 |
| 手续费标准-开仓-万分之 | float64 | -       |
| 手续费标准-开仓-元   | object  | -       |
| 手续费标准-平昨-万分之 | float64 | -       |
| 手续费标准-平昨-元   | object  | -       |
| 手续费标准-平今-万分之 | float64 | -       |
| 手续费标准-平今-元   | object  | -       |
| 每跳毛利         | int64   | 注意单位: 元 |
| 手续费          | float64 | 注意: 开+平 |
| 每跳净利         | float64 | 注意单位: 元 |
| 备注           | object  | 是否主力合约  |
| 手续费更新时间      | object  | -       |
| 价格更新时间       | object  | -       |

接口示例

```python
import akshare as ak

futures_comm_info_df = ak.futures_comm_info(symbol="所有")
print(futures_comm_info_df)
```

数据示例

```
         交易所名称          合约名称  ...                  手续费更新时间                   价格更新时间
0      上海期货交易所        沪银2404  ...  2024-04-15 22:59:13.785  2024-04-15 22:58:45.832
1      上海期货交易所        沪银2405  ...  2024-04-15 22:59:13.785  2024-04-15 22:58:45.832
2      上海期货交易所        沪银2406  ...  2024-04-15 22:59:13.785  2024-04-15 22:58:45.832
3      上海期货交易所        沪银2407  ...  2024-04-15 22:59:13.785  2024-04-15 22:58:45.832
4      上海期货交易所        沪银2408  ...  2024-04-15 22:59:13.785  2024-04-15 22:58:45.832
..         ...           ...  ...                      ...                      ...
695  中国金融期货交易所  30年期国债期货2409  ...  2024-04-15 22:59:37.599  2024-04-15 22:58:45.832
696  中国金融期货交易所  30年期国债期货2412  ...  2024-04-15 22:59:37.599  2024-04-15 22:58:45.832
697  中国金融期货交易所     2年期国债2406  ...  2024-04-15 22:59:37.599  2024-04-15 22:58:45.832
698  中国金融期货交易所     2年期国债2409  ...  2024-04-15 22:59:37.599  2024-04-15 22:58:45.832
699  中国金融期货交易所     2年期国债2412  ...  2024-04-15 22:59:37.599  2024-04-15 22:58:45.832
[700 rows x 21 columns]
```

#### 期货规则-交易日历表

接口: futures_rule

目标地址: https://www.gtjaqh.com/pc/calendar.html

描述: 国泰君安期货-交易日历数据表

限量: 单次返回指定交易日所有合约的交易日历数据

输入参数

| 名称   | 类型  | 描述                                 |
|------|-----|------------------------------------|
| date | str | date="20231205"; 需要指定为交易日, 且是近期的日期 |

输出参数

| 名称          | 类型      | 描述      |
|-------------|---------|---------|
| 交易所         | object  | -       |
| 品种          | object  | -       |
| 代码          | object  | -       |
| 交易保证金比例     | float64 | 注意单位: % |
| 涨跌停板幅度      | float64 | 注意单位: % |
| 合约乘数        | int64   | -       |
| 最小变动价位      | float64 | -       |
| 限价单每笔最大下单手数 | int64   | -       |
| 特殊合约参数调整    | object  | -       |
| 调整备注        | object  | -       |

接口示例

```python
import akshare as ak

futures_rule_df = ak.futures_rule(date="20231205")
print(futures_rule_df)
```

数据示例

```
     交易所          品种  ...                                      特殊合约参数调整  调整备注
0    上期所           铜  ...  CU2312合约交易保证金比例为20.0%; CU2401合约交易保证金比例为15.0%   NaN
1    上期所         铜期权  ...    期权卖方交易保证金中涉及标的期货合约的公司交易保证金按照对应的期货合约保证金标准收取   NaN
2    上期所           铝  ...  AL2312合约交易保证金比例为20.0%; AL2401合约交易保证金比例为15.0%   NaN
3    上期所         铝期权  ...    期权卖方交易保证金中涉及标的期货合约的公司交易保证金按照对应的期货合约保证金标准收取   NaN
4    上期所           锌  ...  ZN2312合约交易保证金比例为20.0%; ZN2401合约交易保证金比例为15.0%   NaN
..   ...         ...  ...                                           ...   ...
117  中金所  中证1000股指期权  ...                       保证金调整系数为14%，最低保障系数为0.50   NaN
118  中金所       2年期国债  ...                          TS2312合约交易保证金比例为1.3%   NaN
119  中金所       5年期国债  ...                          TF2312合约交易保证金比例为2.5%   NaN
120  中金所      30年期国债  ...                          TL2312合约交易保证金比例为6.5%   NaN
121  中金所      10年期国债  ...                           T2312合约交易保证金比例为3.5%   NaN
[122 rows x 10 columns]
```

#### 库存数据-99期货网

接口: futures_inventory_99

目标地址: https://www.99qh.com/data/stockIn?productId=61

描述: 99 期货网-大宗商品库存数据

限量: 单次返回指定 symbol 的具体品种的期货库存数据, 仓单日报数据

输入参数

| 名称     | 类型  | 描述                                                                                                     |
|--------|-----|--------------------------------------------------------------------------------------------------------|
| symbol | str | symbol='豆一'; 交易所对应的具体品种中文名称或者英文代码; 如：大连商品交易所的豆一; 具体品种查询：https://www.99qh.com/data/stockIn?productId=61 |

输出参数

| 名称  | 类型      | 描述 |
|-----|---------|----|
| 日期  | object  | -  |
| 收盘价 | float64 | -  |
| 库存  | int64   | -  |

接口示例

```python
import akshare as ak

futures_inventory_99_df = ak.futures_inventory_99(symbol='豆一')
print(futures_inventory_99_df)
```

数据示例

```
         日期     收盘价     库存
0     2005-01-14  3000.0  13107
1     2005-01-21  2627.0  22695
2     2005-01-28  2631.0  25031
3     2005-02-04  2637.0  20023
4     2005-02-18  2680.0  18454
...          ...     ...    ...
4014  2025-02-27  4232.0  37696
4015  2025-02-28  4218.0  37696
4016  2025-03-03  4239.0  37536
4017  2025-03-04  4252.0  37536
4018  2025-03-05  4175.0  37536
[4019 rows x 3 columns]
```

#### 库存数据-东方财富

接口: futures_inventory_em

目标地址: http://data.eastmoney.com/ifdata/kcsj.html

描述: 东方财富网-期货数据-库存数据; 近 60 个交易日的期货库存日频率数据

限量: 返回指定交易所指定品种的期货库存数据, 仓单日报数据

输入参数

| 名称     | 类型  | 描述                                                                         |
|--------|-----|----------------------------------------------------------------------------|
| symbol | str | symbol="A"; 支持品种代码和中文名称，中文名称参见：https://data.eastmoney.com/ifdata/kcsj.html |

输出参数

| 名称  | 类型      | 描述          |
|-----|---------|-------------|
| 日期  | object  | 日期          |
| 库存  | int64   | 库存数据        |
| 增减  | float64 | 相对前一个交易日的增减 |

接口示例

```python
import akshare as ak

futures_inventory_em_df = ak.futures_inventory_em(symbol="A")
print(futures_inventory_em_df)
```

数据示例

```
        日期     库存     增减
0   2024-11-19  15522    NaN
1   2024-11-20  15522    0.0
2   2024-11-21  16322  800.0
3   2024-11-22  16902  580.0
4   2024-11-25  17139  237.0
..         ...    ...    ...
61  2025-02-21  38028    0.0
62  2025-02-24  38146  118.0
63  2025-02-25  38146    0.0
64  2025-02-26  37696 -450.0
65  2025-02-27  37696    0.0
[66 rows x 3 columns]
```

#### 展期收益率

展期收益率是由不同交割月的价差除以相隔月份数计算得来, 它反映了市场对该品种在近期交割和远期交割的价差预期.

在 [AKShare](https://github.com/akfamily/akshare) 中可以通过 **get_roll_yield_bar** 接口下载展期收益率数据.

这里展期收益率列表的序列类型分为三种, 分别可以通过:

    1. type_method = "date"  # 某商品品种在不同日期的主力合约和次主力合约的价差组成
    2. type_method = "symbol"  # 某商品品种在某天的所有交割月合约价格组成, 可以很方便的观察该品种从近期到远期的展期结构
    3. type_method = "var"  # 某交易日, 所有品种的主力次主力合约展期收益率的组合, 可以方便的找到展期收益率高的品种和低的品种

来获取.

其中 "date" 类型, 调用方法例子为:

```python
import akshare as ak

ak.get_roll_yield_bar(type_method="date", var="RB", start_day="20191009", end_day="20191030")  # 需要收盘收运行
```

其中 "symbol" 类型, 调用方法例子为:

```python
import akshare as ak

ak.get_roll_yield_bar(type_method="symbol", var="RB", date="20191008")  # 需要收盘收运行
```

其中 "var" 类型, 调用方法例子为:

```python
import akshare as ak

ak.get_roll_yield_bar(type_method="var", date="20191008")  # 需要收盘收运行
```

利用 **get_roll_yield** 接口, 可以找到特定合约特定日期的主力合约次主力合约展期收益率, 或通过 symbol1 和 symbol2 变量自定义某两个合约的展期收益率.

```python
import akshare as ak

ak.get_roll_yield(date="20180718", var="IF", symbol1="IF1812", symbol2="IF1811")  # 需要收盘收运行
```

注意: 1. 主力合约和次主力合约的定义, 是由该日的各交割月合约持仓量由大到小排序得到.

#### 注册仓单

注册仓单是由各交易所的公布的日级数据, 在一定程度上可以反映市场的库存变化. 调用例子如下:

```python
import akshare as ak

ak.get_receipt(start_date="20180712", end_date="20180719", vars_list=["CU", "NI"])
```

注意:

    1. vars_list 变量接上需要爬取的品种列表, 即使是一个品种, 也需要以列表形式输入;

    2. 在研究仓单的方向变化时, 需要考虑一些品种的年度周期性, 如农产品的收割季、工业品的开工季等;

    3. 需考虑到交割日的仓单变化.

#### 现货价格和基差

基差是商品期货非常重要的基本面因素, AKShare 在这里提供 3 中获取基差的方法:

第一种：获取近期交易日的基差数据

```python
import akshare as ak

futures_spot_price_df = ak.futures_spot_price("20240430")
print(futures_spot_price_df)
```

```
   symbol spot_price near_contract  ... near_basis_rate dom_basis_rate      date
0       C    2298.57         c2405  ...        0.037167       0.038037  20240430
1      CS     3042.0        cs2405  ...       -0.081854      -0.086128  20240430
2       A     4730.0         a2405  ...       -0.013953      -0.014588  20240430
3       M     3340.0         m2405  ...       -0.018862       0.013174  20240430
4       Y     7840.0         y2405  ...       -0.029337      -0.025765  20240430
5       P     7960.0         p2405  ...       -0.023618      -0.071608  20240430
6      JD     3455.0        jd2405  ...       -0.146744       0.133719  20240430
7       L    8407.14         l2405  ...        0.008191       0.010807  20240430
8       V     5556.0         v2405  ...        0.035997       0.069834  20240430
9      PP    7842.86        pp2405  ...       -0.031603      -0.027268  20240430
10      I     892.11         i2405  ...       -0.005168      -0.021421  20240430
11     EG    4481.67        eg2405  ...       -0.037412      -0.006397  20240430
12     EB     9530.0        eb2405  ...       -0.007135       -0.01532  20240430
13     PG     5052.0        pg2405  ...       -0.087688      -0.088282  20240430
14     LH      14.85        lh2405  ...      976.777778     1169.03367  20240430
15     WH     2616.0         WH405  ...        0.222477       0.222477  20240430
16     PM     2616.0         PM405  ...        0.193425       0.193425  20240430
17     CF   16640.17         CF405  ...       -0.070622        -0.0565  20240430
18     SR     6650.0         SR405  ...       -0.029925      -0.073534  20240430
19     TA     5940.5         TA405  ...       -0.006481      -0.003114  20240430
20     OI    8398.33         OI405  ...       -0.015042       0.009486  20240430
21     MA    2651.67         MA405  ...       -0.020994      -0.036079  20240430
22     FG     1492.0         FG405  ...        0.018097       0.039544  20240430
23     RS     5996.0         RS407  ...       -0.077051      -0.074383  20240430
24     RM    2803.33         RM405  ...       -0.040427      -0.010463  20240430
25     SF    6464.29         SF405  ...        0.064309       0.099889  20240430
26     SM     5955.0         SM405  ...        0.293367       0.311167  20240430
27     CY    24950.0         CY405  ...       -0.175752      -0.147695  20240430
28     UR    2435.83         UR405  ...       -0.119397      -0.157577  20240430
29     SA     2210.0         SA405  ...       -0.026244      -0.003167  20240430
30     PF     7652.0         PF405  ...       -0.064297      -0.034762  20240430
31     SH      790.0         SH405  ...        1.910127       2.382278  20240430
32     CU   81593.33        cu2405  ...        0.000695       0.002165  20240430
33     AL    20540.0        al2405  ...        0.001217       0.003165  20240430
34     ZN    23378.0        zn2405  ...       -0.001625      -0.000342  20240430
35     PB    17000.0        pb2405  ...        0.020882       0.019412  20240430
36     NI  144166.67        ni2405  ...       -0.005318      -0.001503  20240430
37     SN   262825.0        sn2405  ...       -0.009455      -0.005574  20240430
38     AU     547.64        au2405  ...        0.003469       0.009422  20240430
39     AG    7054.67        ag2405  ...        0.002457      -0.000237  20240430
40     RB    3586.22        rb2405  ...        -0.01735       0.023362  20240430
41     WR     3845.2        wr2405  ...       -0.059867      -0.059867  20240430
42     HC     3896.0        hc2405  ...       -0.022331      -0.020021  20240430
43     FU     5816.0        fu2405  ...       -0.352132      -0.397008  20240430
44     BU    3680.86        bu2405  ...        0.008732       0.026391  20240430
45     RU    13560.0        ru2405  ...        0.022493        0.04351  20240430
46     SP     6400.0        sp2405  ...       -0.025312      -0.000313  20240430
47     SS    14267.5        ss2405  ...       -0.020852        0.00403  20240430
48     BR    13260.0        br2405  ...       -0.005279      -0.009427  20240430
49     SI    13660.0        SI2405  ...       -0.127379      -0.118228  20240430
50     LC   115400.0        LC2405  ...       -0.023397      -0.018631  20240430
[51 rows x 11 columns]
```

返回值分别为品种、现货价格、最近交割合约、最近交割合约价格、主力合约、主力合约价格、最近合约基差值、主力合约基差值、最近合约基差率、主力合约基差率.

第二种：获取历史交易日的基差数据

```python
import akshare as ak

futures_spot_price_previous_df = ak.futures_spot_price_previous('20240430')
print(futures_spot_price_previous_df)
```

```
       商品     现货价格 主力合约代码  主力合约价格  ...  主力合约变动百分比 180日内主力基差最高 180日内主力基差最低 180日内主力基差平均
0       铜  81593.3   2406   81770  ...      -0.22     1050.00     -791.67      104.24
1     螺纹钢  3586.22   2410    3670  ...      -2.31      113.89     -143.78      -14.75
2       锌    23378   2406   23370  ...       0.03      319.00     -370.00       17.61
3       铝    20540   2406   20605  ...      -0.32      276.67     -178.33       13.24
4      黄金   547.64   2408   552.8  ...      -0.91        5.92       -8.03       -1.03
5      线材   3845.2   2405    3615  ...       5.98      230.20     -135.00       -2.20
6     燃料油     5816   2409    3507  ...      39.70     2618.00     2059.00     2340.99
7    天然橡胶    13610   2409   14150  ...      -3.97     -505.00    -1540.00     -931.96
8       铅    17000   2406   17330  ...      -1.94      115.00     -381.25      -99.64
9      白银  7054.67   2406    7053  ...       0.02      181.00     -197.00      -15.58
10   石油沥青  3680.86   2409    3778  ...      -2.64      123.33     -295.00     -121.34
11   热轧卷板     3896   2410    3818  ...       2.00      132.00      -62.00       26.62
12      镍   144167   2406  143950  ...       0.15     5543.33     -885.00     1265.46
13      锡   262825   2406  261360  ...       0.56     4060.00    -6432.50     -160.25
14     纸浆     6400   2409    6398  ...       0.03      470.00      -96.00      107.12
15    不锈钢  14267.5   2409   14325  ...      -0.40      690.00     -290.00      181.46
16  丁二烯橡胶    13260   2406   13135  ...       0.94      590.00     -760.00       67.67
17    PTA   5940.5   2409    5922  ...       0.30      121.56     -140.50       -2.87
18     白糖     6650   2409    6161  ...       7.35      831.00      121.00      393.34
19     棉花  16640.2   2409   15700  ...       5.65     1534.33      677.67     1012.39
20     普麦     2616   2405    3122  ...     -19.34     -130.00     -506.00     -300.03
21  菜籽油OI  8398.33   2409    8478  ...      -0.94      319.33      -93.67      114.28
22     玻璃    18.65   2409    1551  ...      -3.95      185.80     -226.40       -2.20
23    菜籽粕  2803.33   2409    2774  ...       1.03      398.00       10.67      156.70
24    油菜籽     5996   2411    5550  ...       7.44      621.00     -263.00      243.08
25     硅铁  6464.29   2409    7110  ...      -9.98       44.29     -645.71     -183.63
26     锰硅     5955   2409    7808  ...     -31.12      -31.67    -1853.00     -320.69
27   甲醇MA  2651.67   2409    2556  ...       3.58      219.00      -32.00       90.06
28     棉纱    24950   2409   21265  ...      14.77     6110.00     3135.00     4259.29
29     尿素  2435.83   2409    2052  ...      15.72      634.00      113.67      356.79
30     纯碱     2210   2409    2203  ...       0.32     1091.00     -126.00      448.78
31   涤纶短纤     7652   2406    7386  ...       3.48      380.00       -6.00      209.43
32     PX     8700   2409    8486  ...       2.46      410.00     -354.00       86.27
33     烧碱      790   2409    2672  ...      -8.22      228.50     -371.25     -120.92
34    棕榈油     7960   2409    7390  ...       7.16      800.00      -90.00      144.17
35   聚氯乙烯     5556   2409    5944  ...      -6.98      -36.00     -422.00     -271.19
36    聚乙烯  8407.14   2409    8498  ...      -1.07      250.57     -116.57       32.63
37     豆一     4730   2407    4661  ...       1.46      237.00     -151.00        8.09
38     豆粕     3340   2409    3384  ...      -1.32      650.00      -50.00      328.38
39     豆油     7840   2409    7638  ...       2.58      608.00      116.00      390.83
40     玉米  2298.57   2407    2386  ...      -3.78      147.00     -116.14        2.84
41    铁矿石   892.11   2409     873  ...       2.13       93.28       -2.33       48.35
42     鸡蛋     6.91   2409    3917  ...     -13.37     1227.00     -523.00      374.66
43    聚丙烯  7842.86   2409    7629  ...       2.72      359.86       19.71      198.15
44   玉米淀粉     3042   2407    2780  ...       8.61      468.00      236.00      324.03
45    乙二醇  4481.67   2409    4453  ...       0.62      102.00     -290.33      -36.81
46    苯乙烯     9530   2406    9384  ...       1.53      417.00     -299.00       55.10
47  液化石油气     5052   2406    4606  ...       8.83      843.00     -113.00      314.14
48     生猪    14.85   2409   17375  ...     -17.00     2050.00    -3275.00     -441.79
49    工业硅    13660   2406   12045  ...      11.82     2140.00      820.00     1609.08
50    碳酸锂   115400   2407  113250  ...       1.86    34500.00   -14750.00     6372.50
[51 rows x 9 columns]
```

第三种：获取历史某段时间的基差值

```python
import akshare as ak

futures_spot_price_daily_df = ak.futures_spot_price_daily(start_day="20240415", end_day="20240418", vars_list=["CU", "RB"])
print(futures_spot_price_daily_df)
```

```
  symbol spot_price near_contract  ... near_basis_rate dom_basis_rate      date
0     CU    76290.0        cu2404  ...             0.0       0.010355  20240415
1     RB    3512.89        rb2404  ...       -0.019895       0.027929  20240415
2     CU    76800.0        cu2405  ...        0.001562       0.003906  20240416
3     RB     3500.0        rb2405  ...           0.014          0.032  20240416
4     CU    76380.0        cu2405  ...        0.000916       0.003404  20240417
5     RB     3524.0        rb2405  ...        0.014472       0.032633  20240417
6     CU   76878.33        cu2405  ...        0.007306       0.010298  20240418
7     RB    3588.44        rb2405  ...         0.00573       0.027187  20240418
[8 rows x 11 columns]
```

注意: 1. 现货价格是从生意社网站采集的, 仅支持从 2011 年至今每个交易日数据.

#### 会员持仓排名

自从**蜘蛛网策略**问世以来, 会员持仓数据日益受到关注. 数据的获取方式如下所示:
获取某段时间的会员持仓排名前 5、前 10、前 15、前 20 等总和.

```python
import akshare as ak

get_rank_sum_daily_df = ak.get_rank_sum_daily(start_day="20180718", end_day="20180719", vars_list=["IF", "C"])
print(get_rank_sum_daily_df)
```

获取某交易日某品种的持仓排名榜

```python
import akshare as ak

ak.get_dce_rank_table()
ak.get_cffex_rank_table()
ak.get_czce_rank_table()
ak.get_shfe_rank_table()
```

代码示例:

```python
import akshare as ak

get_czce_rank_table_df = ak.get_czce_rank_table(date='20200213')
print(get_czce_rank_table_df)
```

注意:

    1. 因为每个交易所公布的持仓排名不同: 大连商品交易所只公布品种的总持仓排名;

    2. 没有按不同交割月划分;上海、中金交易所公布了每个交割月的持仓排名, 没有公布品种所有合约总排名;

    3. 因此这里的品种排名和是各合约加总计算得来;郑州交易所公布了各合约排名和品种排名, 因此这里都是交易所原始数据.

##### 大连商品交易所

接口: futures_dce_position_rank

目标地址: http://www.dce.com.cn/dalianshangpin/xqsj/tjsj26/rtj/rcjccpm/index.html

描述: 大连商品交易所指定交易日的具体合约的持仓排名

限量: 单次返回所有合约的持仓排名数据, 返回以合约名字为键, 具体排名数据为值的字典

输入参数

| 名称        | 类型   | 描述                                                                                                                    |
|-----------|------|-----------------------------------------------------------------------------------------------------------------------|
| date      | str  | date="20200511"; 指定交易日, 该数据接口可以获取从 2000 年开始的数据, 20160104 由于交易所数据问题，返回为空可以调用 **futures_dce_position_rank_other** 来返回数据 |
| vars_list | list | vars_list=cons.contract_symbols; 指定品种，比如：["C", "CS"]                                                                  |

P.S. **futures_dce_position_rank_other** 函数只返回页面显示的活跃合约，返回格式同 **futures_dce_position_rank**

输出参数-字典

P.S. 这里仅列出值(pandas.DataFrame)的字段信息

| 名称                      | 类型      | 描述      |
|-------------------------|---------|---------|
| long_open_interest      | object  | 持买单量    |
| long_open_interest_chg  | float64 | 持买单量-增减 |
| long_party_name         | object  | 会员简称    |
| rank                    | float64 | 名次      |
| short_open_interest     | float64 | 持卖单量    |
| short_open_interest_chg | float64 | 持买单量-增减 |
| short_party_name        | object  | 会员简称    |
| vol                     | float64 | 成交量     |
| vol_chg                 | float64 | 成交量-增减  |
| vol_party_name          | object  | 会员简称    |
| symbol                  | object  | 具体合约    |
| variety                 | object  | 品种      |

接口示例

```python
import akshare as ak

futures_dce_detail_dict = ak.futures_dce_position_rank(date="20200513")
print(futures_dce_detail_dict)
```

数据示例-字典

```
{'jm2009':    long_open_interest long_open_interest_chg  ...  symbol  variety
0               9,063                   -253  ...  jm2009       JM
1               8,255                     65  ...  jm2009       JM
2               5,954                   -216  ...  jm2009       JM
3               3,691                   -127  ...  jm2009       JM
4               3,387                    115  ...  jm2009       JM
..                ...                    ...  ...     ...      ...
15              1,539                   -120  ...  jm2009       JM
16              1,457                    158  ...  jm2009       JM
17              1,391                     92  ...  jm2009       JM
18              1,377                    -60  ...  jm2009       JM
19              1,343                    -40  ...  jm2009       JM
}
```

##### 广州期货交易所

接口: futures_gfex_position_rank

目标地址: http://www.gfex.com.cn/gfex/rcjccpm/hqsj_tjsj.shtml

描述: 广州期货交易所-日成交持仓排名

限量: 单次返回所有合约的日成交持仓排名数据, 返回以合约名字为键, 具体排名数据为值的字典

输入参数

| 名称        | 类型   | 描述                                                       |
|-----------|------|----------------------------------------------------------|
| date      | str  | date="20231113"; 指定交易日, 该数据接口可以获取从 20231110 开始的日成交持仓排名数据 |
| vars_list | list | vars_list=None; 指定品种，比如：['SI', 'LC']                     |

输出参数-字典

P.S. 这里仅列出值(pandas.DataFrame)的字段信息

| 名称                      | 类型     | 描述      |
|-------------------------|--------|---------|
| rank                    | int64  | 名次      |
| vol_party_name          | object | 会员简称    |
| vol                     | int64  | 成交量     |
| vol_chg                 | int64  | 成交量-增减  |
| long_party_name         | object | 会员简称    |
| long_open_interest      | int64  | 持买单量    |
| long_open_interest_chg  | int64  | 持买单量-增减 |
| short_party_name        | object | 会员简称    |
| short_open_interest     | int64  | 持卖单量    |
| short_open_interest_chg | int64  | 持卖单量-增减 |
| symbol                  | object | 具体合约    |
| variety                 | object | 品种      |

接口示例

```python
import akshare as ak

futures_gfex_position_rank_dict = ak.futures_gfex_position_rank(date="20231113")
print(futures_gfex_position_rank_dict)
```

数据示例-字典

```
{'si2312':     rank vol_party_name   vol  ...  short_open_interest_chg  symbol  variety
0      1           中信期货  2940  ...                       50  SI2312       SI
1      2           国海良时  1013  ...                        1  SI2312       SI
2      3           永安期货   956  ...                     -376  SI2312       SI
3      4           宝城期货   901  ...                      120  SI2312       SI
4      5           浙商期货   648  ...                      -10  SI2312       SI
5      6         国投安信期货   640  ...                     -460  SI2312       SI
6      7         国泰君安期货   620  ...                      -94  SI2312       SI
7      8           东证期货   515  ...                        1  SI2312       SI
8      9           广发期货   352  ...                       52  SI2312       SI
9     10           南华期货   294  ...                      895  SI2312       SI
10    11         物产中大期货   275  ...                      -90  SI2312       SI
11    12           华金期货   263  ...                       24  SI2312       SI
12    13           广州期货   211  ...                       -4  SI2312       SI
13    14           华泰期货   205  ...                        0  SI2312       SI
14    15           国贸期货   185  ...                        0  SI2312       SI
15    16           中粮期货   178  ...                        2  SI2312       SI
16    17           申银万国   128  ...                        0  SI2312       SI
17    18           中原期货   118  ...                      -20  SI2312       SI
18    19           海证期货   113  ...                      -10  SI2312       SI
19    20           兴证期货   108  ...                      -22  SI2312       SI
[20 rows x 12 columns], 'si2401':     rank vol_party_name    vol  ...  short_open_interest_chg  symbol  variety
0      1           中信期货  11711  ...                     -195  SI2401       SI
1      2           广州期货   8079  ...                       96  SI2401       SI
2      3           中信建投   7222  ...                      535  SI2401       SI
3      4           广发期货   4361  ...                     1045  SI2401       SI
4      5           华泰期货   3014  ...                       97  SI2401       SI
5      6           东证期货   2252  ...                       89  SI2401       SI
6      7         国投安信期货   1897  ...                      -70  SI2401       SI
7      8           永安期货   1757  ...                       25  SI2401       SI
8      9           海通期货   1723  ...                      -27  SI2401       SI
9     10         国泰君安期货   1514  ...                       16  SI2401       SI
10    11           国海良时   1456  ...                      611  SI2401       SI
11    12           长安期货   1441  ...                      418  SI2401       SI
12    13           华闻期货   1307  ...                    -1199  SI2401       SI
13    14           方正中期   1247  ...                       20  SI2401       SI
14    15           宝城期货   1158  ...                      317  SI2401       SI
15    16           华金期货   1052  ...                       35  SI2401       SI
16    17           信达期货    924  ...                      -45  SI2401       SI
17    18           兴证期货    866  ...                        2  SI2401       SI
18    19           浙商期货    777  ...                       19  SI2401       SI
19    20           徽商期货    761  ...                        5  SI2401       SI
[20 rows x 12 columns], 'lc2401':     rank vol_party_name     vol  ...  short_open_interest_chg  symbol  variety
0      1           中信期货  164523  ...                    -1741  LC2401       LC
1      2           华闻期货   50941  ...                     -284  LC2401       LC
2      3           海通期货   47107  ...                     -822  LC2401       LC
3      4           徽商期货   23884  ...                     1523  LC2401       LC
4      5         东方财富期货   22906  ...                      205  LC2401       LC
5      6         国泰君安期货   21530  ...                     -277  LC2401       LC
6      7           中信建投   21033  ...                      541  LC2401       LC
7      8           渤海期货   20804  ...                     -146  LC2401       LC
8      9           宝城期货   18449  ...                      568  LC2401       LC
9     10           东证期货   16656  ...                      -76  LC2401       LC
10    11           华泰期货   16101  ...                      325  LC2401       LC
11    12           广发期货   15262  ...                      146  LC2401       LC
12    13           方正中期   15198  ...                     1664  LC2401       LC
13    14           银河期货   11187  ...                      863  LC2401       LC
14    15           东吴期货   10747  ...                      723  LC2401       LC
15    16           平安期货    9572  ...                      -54  LC2401       LC
16    17           华安期货    9340  ...                      296  LC2401       LC
17    18           国信期货    9284  ...                      -44  LC2401       LC
18    19           民生期货    9213  ...                      -91  LC2401       LC
19    20           国富期货    9171  ...                     -174  LC2401       LC
[20 rows x 12 columns], 'lc2407':     rank vol_party_name    vol  ...  short_open_interest_chg  symbol  variety
0      1           中信期货  36637  ...                      244  LC2407       LC
1      2           海通期货   4366  ...                       30  LC2407       LC
2      3           中信建投   3499  ...                       10  LC2407       LC
3      4         国泰君安期货   3314  ...                      308  LC2407       LC
4      5           广发期货   3123  ...                       43  LC2407       LC
5      6         东方财富期货   2976  ...                       82  LC2407       LC
6      7           东证期货   2782  ...                     -133  LC2407       LC
7      8           徽商期货   2716  ...                      411  LC2407       LC
8      9           永安期货   2269  ...                       -7  LC2407       LC
9     10           华泰期货   2076  ...                       23  LC2407       LC
10    11           平安期货   2065  ...                       -7  LC2407       LC
11    12           方正中期   1991  ...                      301  LC2407       LC
12    13           华闻期货   1956  ...                       -3  LC2407       LC
13    14           华安期货   1766  ...                       76  LC2407       LC
14    15           安粮期货   1758  ...                     -190  LC2407       LC
15    16           申银万国   1481  ...                       19  LC2407       LC
16    17           东吴期货   1398  ...                       18  LC2407       LC
17    18           中泰期货   1175  ...                      -93  LC2407       LC
18    19           先锋期货   1144  ...                      -19  LC2407       LC
19    20           银河期货   1100  ...                     -218  LC2407       LC
[20 rows x 12 columns]}
```

#### 仓单日报

##### 仓单日报-郑州商品交易所

接口: futures_czce_warehouse_receipt

目标地址: http://www.czce.com.cn/cn/jysj/cdrb/H770310index_1.htm

描述: 郑州商品交易所-交易数据-仓单日报

限量: 单次返回当前交易日的所有仓单日报数据

输入参数

| 名称   | 类型  | 描述                   |
|------|-----|----------------------|
| date | str | date="20200702"; 交易日 |

输出参数

| 名称    | 类型   | 描述                                     |
|-------|------|----------------------------------------|
| 键值对字典 | dict | 键值对, 键为品种代码, 值为 pandas.DataFrame 格式的数据 |

接口示例

```python
import akshare as ak

futures_czce_warehouse_receipt_df = ak.futures_czce_warehouse_receipt(date="20200702")
print(futures_czce_warehouse_receipt_df)
```

数据示例

```
{'SR':     仓库编号       仓库简称    年度   等级   品牌   仓单数量 当日增减 有效预报   升贴水
0   0103       藁城永安   NaN  NaN  NaN      0    0  NaN   100
1   0112       津军粮城   NaN  NaN  NaN      0    0  NaN    80
2   0201      南京铁心桥   NaN  NaN  NaN      0    0  NaN   200
3   0404       荣桂钦州   NaN  NaN  NaN      0    0  NaN     0
4   0407       柳州桂糖   NaN  NaN  NaN      0    0  NaN     0
5   0409       云南广大  1920    1   康白    180    0  NaN  -170
6    NaN        NaN  1920    1  黎山雪     80    0  NaN   NaN
7    NaN        NaN  1920    1  三菁山     20    0  NaN   NaN
8    NaN        NaN  1920    1  仙人山     50    0  NaN   NaN
9     小计        NaN   NaN  NaN  NaN    330    0    0   NaN
10  0411       佛山华商   NaN  NaN  NaN      0    0  NaN    80
11  0415       营口港务   NaN  NaN  NaN      0    0  NaN    50
12  0417       中糖湖北  1920    1   绿原    407    0  NaN   240
13   NaN        NaN  1920    1   西沁    363    0  NaN   NaN
14    小计        NaN   NaN  NaN  NaN    770    0  230   NaN
15  0428      郑州南阳寨  1920    1   中糖   1431    0  NaN   140
16    小计        NaN   NaN  NaN  NaN   1431    0   79   NaN
17  0433       荣桂来宾   NaN  NaN  NaN      0    0  NaN     0
18  0434       广西贵港   NaN  NaN  NaN      0    0  NaN     0
19  0435       广西弘信   NaN  NaN  NaN      0    0  NaN     0
20  0436       营口北方   NaN  NaN  NaN      0    0  NaN    50
21  0437  日照凌云海(厂库)  1920    1  ALL    440  -50  NaN    50
22    小计        NaN   NaN  NaN  NaN    440  -50    0   NaN
23  0438      广东北部湾  1920    1   甘岭   1640    0  NaN     0
24    小计        NaN   NaN  NaN  NaN   1640    0    0   NaN
25  0440      中粮曹妃甸   NaN  NaN  NaN      0    0  NaN    50
26  0441       弘信扶绥   NaN  NaN  NaN      0    0  NaN     0
27  0444       中糖南通   NaN  NaN  NaN      0    0  NaN   180
28  0445   中粮屯河(厂库)  1920    1  ALL   5000    0  NaN    50
29    小计        NaN   NaN  NaN  NaN   5000    0    0   NaN
30  0447   星光糖业(厂库)   NaN  NaN  NaN      0    0  NaN   100
31  0449       陕西咸阳  1920    1   晶菱     50    0  NaN    80
32   NaN        NaN  1920    1   中糖   1490    0  NaN   NaN
33    小计        NaN   NaN  NaN  NaN   1540    0    0   NaN
34  0450       冀盛物流   NaN  NaN  NaN      0    0  NaN    50
35  0451       中糖北京   NaN  NaN  NaN      0    0  NaN    50
36  0452       云南陆航  1920    1  大湾江     40   40  NaN  -100
37    小计        NaN   NaN  NaN  NaN     40   40    0   NaN
38  0454       云鸥物流   NaN  NaN  NaN      0    0  NaN     0
39  0508       平湖华瑞   NaN  NaN  NaN      0    0  NaN   180
40    总计        NaN   NaN  NaN  NaN  11191  -10  309   NaN, 'CF':      仓库编号  仓库简称    年度    等级   产地   仓单数量  当日增减  有效预报
0    0301  河南国储  1920  1128   新疆      1     0   NaN
1     NaN   NaN  1920  1129   新疆      1     0   NaN
2     NaN   NaN  1920  1228   新疆      7     0   NaN
3     NaN   NaN  1920  1229   新疆      5     0   NaN
4     NaN   NaN  1920  2127   新疆      2     0   NaN
..    ...   ...   ...   ...  ...    ...   ...   ...
368   NaN   NaN  1920  3129   新疆     41     0   NaN
369   NaN   NaN  1920  3130   新疆      8     0   NaN
370   NaN   NaN  1920  4129   新疆      5     0   NaN
371    小计   NaN   NaN   NaN  NaN     86     0     1
372    总计   NaN   NaN   NaN  NaN  20641  -261  2203}
```

##### 仓单日报-大连商品交易所

接口: futures_dce_warehouse_receipt

目标地址: http://www.dce.com.cn/dalianshangpin/xqsj/tjsj26/rtj/cdrb/index.html

描述: 大连商品交易所-行情数据-统计数据-日统计-仓单日报

限量: 单次返回当前交易日的所有仓单日报数据

输入参数

| 名称   | 类型  | 描述                   |
|------|-----|----------------------|
| date | str | date="20200702"; 交易日 |

输出参数

| 名称    | 类型   | 描述                                     |
|-------|------|----------------------------------------|
| 键值对字典 | dict | 键值对, 键为品种代码, 值为 pandas.DataFrame 格式的数据 |

接口示例

```python
import akshare as ak

futures_dce_warehouse_receipt_df = ak.futures_dce_warehouse_receipt(date="20200702")
print(futures_dce_warehouse_receipt_df)
```

数据示例

```
{'豆一':      品种   仓库/分库  昨日仓单量  今日仓单量  增减
0    豆一  桦南宏安粮贸      8      8   0
1  豆一小计  桦南宏安粮贸      8      8   0, '豆二':      品种 仓库/分库  昨日仓单量  今日仓单量  增减
0    豆二  南通嘉吉   1600   1600   0
1  豆二小计  南通嘉吉   1600   1600   0, '玉米':       品种    仓库/分库  昨日仓单量  今日仓单量    增减
0     玉米     中粮贸易   1850   1850     0
1     玉米    松原中转库   1850   1850     0
2     玉米    吉林云天化  13605  13605     0
3     玉米    吉林云天化  13605  13605     0
4     玉米     厦门象屿   2876   2576  -300
5     玉米     绥化象屿   2876   2576  -300
6     玉米     河北粮产    650    650     0
7     玉米    公主岭禾丰    650    650     0
8     玉米      锦州港   3310   2540  -770
9     玉米   锦州港主库区   1290    520  -770
10    玉米     龙江江源   2020   2020     0
11    玉米     锦阳锦州    596    596     0
12    玉米      良运库   5756   5656  -100
13    玉米   良运库主库区   4006   3906  -100
14    玉米     西丰良运   1750   1750     0
15    玉米     大连中船   6091   6091     0
16    玉米  大连中船主库区   5300   5300     0
17    玉米    七台河兴粮    791    791     0
18    玉米      北良港  10931   6061 -4870
19    玉米   北良港主库区  10931   6061 -4870
20    玉米    营口百丰泰   1034    689  -345
21  玉米小计    营口百丰泰  46699  40314 -6385}
```

##### 仓单日报-上海期货交易所

接口: futures_shfe_warehouse_receipt

目标地址: https://tsite.shfe.com.cn/statements/dataview.html?paramid=dailystock&paramdate=20200703

描述: 提供上海期货交易所指定交割仓库期货仓单日报

限量: 单次返回当前交易日的所有仓单日报数据

输入参数

| 名称   | 类型  | 描述                   |
|------|-----|----------------------|
| date | str | date="20200702"; 交易日 |

输出参数

| 名称    | 类型   | 描述                                     |
|-------|------|----------------------------------------|
| 键值对字典 | dict | 键值对, 键为品种代码, 值为 pandas.DataFrame 格式的数据 |

接口示例

```python
import akshare as ak

futures_shfe_warehouse_receipt_df = ak.futures_shfe_warehouse_receipt(date="20200702")
print(futures_shfe_warehouse_receipt_df)
```

数据示例

```
{'线材':     VARNAME VARSORT REGNAME    REGSORT  ... WRTWGHTS WRTCHANGE ROWORDER ROWSTATUS
179      线材      80      江苏          2  ...        0         0       26         0
180      线材      80      江苏          2  ...        0         0       76         0
181      线材      80      江苏          2  ...        0         0   100000         1
182      线材      80      浙江          3  ...        0         0       19         0
183      线材      80      浙江          3  ...        0         0       68         0
184      线材      80      浙江          3  ...        0         0   100000         1
185      线材      80      天津          8  ...        0         0       40         0
186      线材      80      天津          8  ...        0         0       89         0
187      线材      80      天津          8  ...        0         0   100000         1
188      线材      80          999999999  ...        0         0   200000         2
[10 rows x 11 columns], '铝':    VARNAME VARSORT REGNAME    REGSORT  ... WRTWGHTS WRTCHANGE ROWORDER ROWSTATUS
30       铝      20      上海          0  ...     3539         0       16         0
31       铝      20      上海          0  ...        0         0       17         0
32       铝      20      上海          0  ...      650         0       25         0
33       铝      20      上海          0  ...      374         0       27         0
34       铝      20      上海          0  ...        0         0       28         0
35       铝      20      上海          0  ...       51         0       29         0
36       铝      20      上海          0  ...      572         0       38         0
37       铝      20      上海          0  ...     2431         0      340         0
38       铝      20      上海          0  ...        0         0      344         0
39       铝      20      上海          0  ...        0         0      346         0
40       铝      20      上海          0  ...        0         0      383         0
41       铝      20      上海          0  ...     7617         0   100000         1
42       铝      20      广东          1  ...     1777         0        6         0
43       铝      20      广东          1  ...        0         0        8         0
44       铝      20      广东          1  ...     4230         0       12         0
45       铝      20      广东          1  ...        0         0      342         0
46       铝      20      广东          1  ...      778         0      368         0
47       铝      20      广东          1  ...     6785         0   100000         1
48       铝      20      江苏          2  ...     8750         0       26         0
49       铝      20      江苏          2  ...     4586         0       84         0
50       铝      20      江苏          2  ...    11405         0      318         0
51       铝      20      江苏          2  ...       76         0      345         0
52       铝      20      江苏          2  ...        0         0      348         0
53       铝      20      江苏          2  ...     1326         0      349         0
54       铝      20      江苏          2  ...    11267         0      352         0
55       铝      20      江苏          2  ...        0         0      353         0
56       铝      20      江苏          2  ...    37410         0   100000         1
57       铝      20      浙江          3  ...     1907         0       19         0
58       铝      20      浙江          3  ...        0         0      321         0
59       铝      20      浙江          3  ...        0         0      324         0
60       铝      20      浙江          3  ...    17983         0      327         0
61       铝      20      浙江          3  ...    11110       603      354         0
62       铝      20      浙江          3  ...    31000       603   100000         1
63       铝      20      山东          4  ...     5509         0      363         0
64       铝      20      山东          4  ...        0         0      364         0
65       铝      20      山东          4  ...      650         0      365         0
66       铝      20      山东          4  ...     6159         0   100000         1
67       铝      20      天津          8  ...        0         0       40         0
68       铝      20      天津          8  ...        0         0       42         0
69       铝      20      天津          8  ...        0         0      338         0
70       铝      20      天津          8  ...        0         0   100000         1
71       铝      20      河南       9999  ...        0         0      333         0
72       铝      20      河南       9999  ...      829         0      355         0
73       铝      20      河南       9999  ...        0         0      356         0
74       铝      20      河南       9999  ...        0         0      357         0
75       铝      20      河南       9999  ...        0         0      358         0
76       铝      20      河南       9999  ...      528         0      367         0
77       铝      20      河南       9999  ...     1357         0   100000         1
78       铝      20      辽宁       9999  ...        0         0      369         0
79       铝      20      重庆       9999  ...        0         0      359         0
80       铝      20      重庆       9999  ...        0         0      360         0
81       铝      20      重庆       9999  ...        0         0   100000         1
82       铝      20          999999999  ...        0         0   180000         2
83       铝      20          999999999  ...    90328       603   190000         2
84       铝      20          999999999  ...    90328       603   200000         2}
```

##### 仓单日报-广州期货交易所

接口: futures_gfex_warehouse_receipt

目标地址: http://www.gfex.com.cn/gfex/cdrb/hqsj_tjsj.shtml

描述: 广州期货交易所-行情数据-仓单日报

限量: 单次返回当前交易日的所有仓单日报数据

输入参数

| 名称   | 类型  | 描述                   |
|------|-----|----------------------|
| date | str | date="20240122"; 交易日 |

输出参数

| 名称    | 类型   | 描述                                     |
|-------|------|----------------------------------------|
| 键值对字典 | dict | 键值对, 键为品种代码, 值为 pandas.DataFrame 格式的数据 |

接口示例

```python
import akshare as ak

futures_gfex_warehouse_receipt_df = ak.futures_gfex_warehouse_receipt(date="20240122")
print(futures_gfex_warehouse_receipt_df)
```

数据示例

```
{'SI':      品种        仓库/分库  昨日仓单量  今日仓单量   增减
0   工业硅         中储吴淞    696    696    0
1   工业硅      中储大场137    732    732    0
2   工业硅      中储大场310   3192   3204   12
3   工业硅       中远海运宝山    828    828    0
4   工业硅       象屿速传上海   3144   3144    0
5   工业硅         中储无锡    240    240    0
6   工业硅         江苏诚通   2870   2906   36
7   工业硅        尖峰供应链    958   1210  252
8   工业硅        广州港物流    204    204    0
9   工业硅        国储830   2483   2483    0
10  工业硅         建发东莞   2096   2096    0
11  工业硅         广东炬申   1116   1116    0
12  工业硅         中储成都   1330   1330    0
13  工业硅         成都中集    500    500    0
14  工业硅         外运新都   1297   1297    0
15  工业硅        外运龙泉驿   1284   1320   36
16  工业硅         中储新港   1632   1668   36
17  工业硅         外运天津   1116   1164   48
18  工业硅       中远海运滨海    952    952    0
19  工业硅         建发天津   1968   2004   36
20  工业硅         外运昆明   3383   3323    0
21  工业硅        青岛港物流   2924   2924    0
22  工业硅       国贸泰达昆明    648    828  180
23  工业硅         云铝物流   4741   4729    0
24  工业硅  厦门国贸（青岛港物流）   1200   1200    0
25  工业硅   厦门国贸（国贸泰达）    840    840    0
26  工业硅   厦门国贸（外运天津）    660    660    0, 'LC':      品种       仓库/分库  昨日仓单量  今日仓单量   增减
0   碳酸锂      象屿速传上海   1954   2054  100
1   碳酸锂       外运龙泉驿    561    561    0
2   碳酸锂        中储临港    120    120    0
3   碳酸锂        建发上海    619    665   90
4   碳酸锂      中远海运镇江   1161   1151    0
5   碳酸锂        五矿无锡   1346   1346    0
6   碳酸锂      江苏奔牛港务     67     67    0
7   碳酸锂        遂宁天诚     87     87    0
8   碳酸锂  九岭锂业（宜春奉新）   1500   1500    0
9   碳酸锂        永兴特钢    280    280    0
10  碳酸锂    盛新锂能（遂宁）    970    970    0
11  碳酸锂      中远海运南昌   3389   3399   10
12  碳酸锂  九岭锂业（宜春宜丰）   1500   1500    0}
```

#### 期转现-大商所

接口: futures_to_spot_dce

目标地址: http://www.dce.com.cn/dalianshangpin/xqsj/tjsj26/jgtj/qzxcx/index.html

描述: 大连商品交易所-期转现统计数据

限量: 单次返回指定交易日的期转现统计数据

输入参数

| 名称   | 类型  | 描述                  |
|------|-----|---------------------|
| date | str | date="202312"; 交易年月 |

输出参数

| 名称      | 类型     | 描述      |
|---------|--------|---------|
| 合约代码    | object | -       |
| 期转现发生日期 | object | -       |
| 期转现数量   | int64  | 注意单位: 手 |

接口示例

```python
import akshare as ak

futures_to_spot_dce_df = ak.futures_to_spot_dce(date="202312")
print(futures_to_spot_dce_df)
```

数据示例

```
     合约代码     期转现发生日期  期转现数量
0   c2401  2023-12-19   1480
1   c2401  2023-12-26   1490
2  eb2312  2023-12-20    176
```

#### 期转现-郑商所

接口: futures_to_spot_czce

目标地址: http://www.czce.com.cn/cn/jysj/qzxtj/H770311index_1.htm

描述: 郑州商品交易所-期转现统计数据

限量: 单次返回指定交易日的期转现统计数据

输入参数

| 名称   | 类型  | 描述                   |
|------|-----|----------------------|
| date | str | date="20210112"; 交易日 |

输出参数

| 名称   | 类型     | 描述       |
|------|--------|----------|
| 合约代码 | object | -        |
| 合约数量 | int64  | 注意: 单边计算 |

接口示例

```python
import akshare as ak

futures_to_spot_czce_df = ak.futures_to_spot_czce(date="20231228")
print(futures_to_spot_czce_df)
```

数据示例

```
    合约代码  合约数量
0  CF401   496
1  CJ401    10
```

#### 期转现-上期所

接口: futures_to_spot_shfe

目标地址: https://tsite.shfe.com.cn/statements/dataview.html?paramid=kx

描述: 上海期货交易所-期转现数据

限量: 单次返回指定交易月份的期转现数据

输入参数

| 名称   | 类型  | 描述                  |
|------|-----|---------------------|
| date | str | date="202312"; 交易月份 |

输出参数

| 名称   | 类型      | 描述 |
|------|---------|----|
| 日期   | object  | -  |
| 合约   | object  | -  |
| 交割量  | float64 | -  |
| 期转现量 | float64 | -  |

注意:

1 铜、铜(BC)、铝、锌、铅、镍、锡、螺纹钢、线材、热轧卷板、天然橡胶、20号胶、低硫燃料油、燃料油、石油沥青、纸浆、不锈钢的数量单位为：吨；黄金的数量单位为：克；白银的数量单位为：千克；原油的数量单位为：桶。
2 交割量、期转现量为单向计算。

接口示例

```python
import akshare as ak

futures_to_spot_shfe_df = ak.futures_to_spot_shfe(date="202312")
print(futures_to_spot_shfe_df)
```

数据示例

```
       日期      合约       交割量    期转现量
0   2023-12-15  cu2312    7950.0     0.0
1   2023-12-11  bc2312    2750.0  2750.0
2   2023-12-13  bc2312     700.0   700.0
3   2023-12-15  bc2312    1900.0     0.0
4   2023-12-08  bc2312    1500.0  1500.0
5   2023-12-15  al2312    9400.0     0.0
6   2023-12-25  al2401     400.0   400.0
7   2023-12-26  al2401      25.0    25.0
8   2023-12-15  zn2312    4375.0     0.0
9   2023-12-15  pb2312   37800.0     0.0
10  2023-12-15  ni2312    2442.0     0.0
11  2023-12-15  sn2312     470.0     0.0
12  2023-12-15  au2312  702000.0     0.0
13  2023-12-15  ag2312  141990.0     0.0
14  2023-12-15  rb2312    5100.0     0.0
15  2023-12-15  hc2312   46200.0     0.0
16  2023-12-19  hc2401     900.0   900.0
17  2023-12-15  ss2312   11040.0     0.0
18  2023-12-12  ss2312     120.0   120.0
19  2023-12-29  sc2401  105000.0     0.0
20  2023-12-29  lu2401   15700.0     0.0
21  2023-12-29  fu2401  124540.0     0.0
22  2023-12-15  bu2312    5180.0     0.0
23  2023-12-01  nr2312    2000.0  2000.0
24  2023-12-04  nr2312     900.0   900.0
25  2023-12-15  nr2312    3600.0     0.0
26  2023-12-11  nr2312     900.0   900.0
27  2023-12-13  nr2402    1000.0  1000.0
28  2023-12-15  ao2312   28800.0     0.0
29  2023-12-15  sp2312   70060.0     0.0
```

#### 交割统计-大商所

接口: futures_delivery_dce

目标地址: http://www.dce.com.cn/dalianshangpin/xqsj/tjsj26/jgtj/jgsj/index.html

描述: 大连商品交易所-交割统计

限量: 单次返回指定交易月份的交割统计数据

输入参数

| 名称   | 类型  | 描述                  |
|------|-----|---------------------|
| date | str | date="202312"; 交易月份 |

输出参数

| 名称   | 类型     | 描述 |
|------|--------|----|
| 品种   | object | -  |
| 合约   | object | -  |
| 交割日期 | object | -  |
| 交割量  | int64  | -  |
| 交割金额 | int64  | -  |

接口示例

```python
import akshare as ak

futures_delivery_dce_df = ak.futures_delivery_dce(date="202312")
print(futures_delivery_dce_df)
```

数据示例

```
       品种    合约    交割日期    交割量       交割金额
0      豆二   b2312  2023-12-19   1400   61586000
1     苯乙烯  eb2312  2023-12-21   1220   49324600
2     乙二醇  eg2312  2023-12-05   1503   61021800
3     乙二醇  eg2312  2023-12-07    100    4027000
4     乙二醇  eg2312  2023-12-08    100    4027000
5     乙二醇  eg2312  2023-12-11    300   12081000
6     铁矿石   i2312  2023-12-05    100   10140000
7     铁矿石   i2312  2023-12-19   1100  111595000
8      焦炭   j2312  2023-12-05     50   12650000
9      鸡蛋  jd2312  2023-12-22      5     213350
10     鸡蛋  jd2312  2023-12-26      5     221800
11     鸡蛋  jd2312  2023-12-27      7     309470
12     鸡蛋  jd2312  2023-12-29     30    1299000
13     焦煤  jm2312  2023-12-14    200   29040000
14    聚乙烯   l2312  2023-12-19   1084   42525320
15     豆粕   m2312  2023-12-06   2751  107123940
16     豆粕   m2312  2023-12-11     14     550340
17     豆粕   m2312  2023-12-19    187    7242510
18    棕榈油   p2312  2023-12-19   1979  139499920
19  液化石油气  pg2312  2023-12-05    176   16892480
20  液化石油气  pg2312  2023-12-08     88    8349440
21  液化石油气  pg2312  2023-12-29    876   85550160
22    聚丙烯  pp2312  2023-12-19    329   12107200
23     粳米  rr2312  2023-12-06    350   12215000
24   聚氯乙烯   v2312  2023-12-19  20399  583717385
25     豆油   y2312  2023-12-11     28    2268000
26     豆油   y2312  2023-12-14      5     405000
27     豆油   y2312  2023-12-19     67    5496680
```

#### 交割统计-郑商所

接口: futures_delivery_czce

目标地址: http://www.czce.com.cn/cn/jysj/ydjgcx/H770316index_1.htm

描述: 郑州商品交易所-交割统计

限量: 单次返回指定交易月份的交割统计数据

输入参数

| 名称   | 类型  | 描述                   |
|------|-----|----------------------|
| date | str | date="20210112"; 交易日 |

输出参数

| 名称   | 类型     | 描述             |
|------|--------|----------------|
| 品种   | object | -              |
| 交割数量 | int64  | 按单边统计          |
| 交割额  | int64  | 注意单位: 元; 按单边统计 |

接口示例

```python
import akshare as ak

futures_delivery_monthly_czce_df = ak.futures_delivery_czce(date="20210112")
print(futures_delivery_monthly_czce_df)
```

数据示例

```
      品种  交割数量        交割额
0     鲜苹果   138    8221920
1      棉花  1416  106332600
2    干制红枣    76    3697150
3      棉纱     4     418600
4  精对苯二甲酸   216    3974400
5     动力煤   200   15276000
6      合计  2050  137920670
```

#### 交割统计-上期所

接口: futures_delivery_shfe

目标地址: https://tsite.shfe.com.cn/statements/dataview.html?paramid=kx

描述: 上海期货交易所-交割统计

限量: 单次返回指定交易月份的交割统计数据

输入参数

| 名称   | 类型  | 描述                  |
|------|-----|---------------------|
| date | str | date="202312"; 交易月份 |

输出参数

| 名称       | 类型      | 描述                                        |
|----------|---------|-------------------------------------------|
| 品种       | object  | -                                         |
| 交割量-本月   | int64   | 注意单位: 手; 交割量单边计算; 交割数据统计期为上月 16 日到本月 15 日 |
| 交割量-比重   | float64 | 注意单位: %                                   |
| 交割量-本年累计 | int64   | 注意单位: 手; 交割量单边计算; 交割数据统计期为上月 16 日到本月 15 日 |
| 交割量-累计同比 | float64 | 注意单位: %                                   |

接口示例

```python
import akshare as ak

futures_delivery_shfe_df = ak.futures_delivery_shfe(date="202312")
print(futures_delivery_shfe_df)
```

数据示例

```
         品种  交割量-本月  交割量-比重  交割量-本年累计    交割量-累计同比
0         铜    1590    3.399833     57280    6.527804
1     铜(BC)    1470    3.143242     21835    4.398757
2         铝    1880    4.019929     74710  -33.431346
3         锌     875    1.870977     34845  -49.216644
4         铅    7560   16.165245     77230   16.126607
5         镍    2442    5.221631     26634   41.234489
6         锡     470    1.004982     24122   33.432902
7       氧化铝    1440    3.079094      5250         NaN
8        黄金    1365    2.918725      5418    5.552309
9        白银    9466   20.240768    124720   -6.371990
10      螺纹钢     510    1.090513     42330   95.700416
11       线材       0    0.000000       180         NaN
12     热轧卷板    4620    9.878761     92520   44.044839
13      不锈钢    2232    4.772596     73932   66.558529
14       原油     301    0.643616     40608   68.700926
15    低硫燃料油     372    0.795433     14172  -48.957320
16      燃料油    1730    3.699190     41964  406.200241
17     石油沥青     518    1.107619     24412  -41.923205
18    丁二烯橡胶       0         NaN         0         NaN
19     天然橡胶       0         NaN     32656  123.456959
20     20号胶     920    1.967199     16681    5.850625
21       纸浆    7006   14.980649    107528   73.214343
22  SCFIS欧线       0         NaN         0         NaN
23       总计   46767  100.000000    939027   14.213102
```

#### 交割配对-大商所

接口: futures_delivery_match_dce

目标地址: http://www.dce.com.cn/dalianshangpin/xqsj/tjsj26/jgtj/jgsj/index.html

描述: 大连商品交易所-交割配对

限量: 单次返回指定品种的的交割配对数据

输入参数

| 名称     | 类型  | 描述               |
|--------|-----|------------------|
| symbol | str | symbol="a"; 交易品种 |

输出参数

| 名称    | 类型      | 描述 |
|-------|---------|----|
| 合约号   | object  | -  |
| 配对日期  | object  | -  |
| 买会员号  | object  | -  |
| 配对手数  | int64   | -  |
| 卖会员号  | object  | -  |
| 交割结算价 | float64 | -  |

注意:

1 价格：自2019年12月02日起，纤维板报价单位由元/张改为元/立方米
2 配对手数：手
3 交割结算价：元/吨，鸡蛋为元/500千克，纤维板为元/立方米、胶合板为元/张
4 *为非期货公司会员

接口示例

```python
import akshare as ak

futures_delivery_match_dce_df = ak.futures_delivery_match_dce(symbol="a")
print(futures_delivery_match_dce_df)
```

数据示例

```
      合约号   配对日期  买会员号  配对手数   卖会员号   交割结算价
0     a1401  2014-01-02  0014     5  196.0  4425.0
1     a1401  2014-01-02  0014    26   51.0  4425.0
2     a1401  2014-01-02  0014     2   97.0  4425.0
3     a1401  2014-01-02  0014    20   52.0  4425.0
4     a1401  2014-01-02  0021     1  125.0  4425.0
     ...         ...   ...   ...    ...     ...
1415  a2401  2024-01-12  0051    78   51.0  4757.0
1416  a2401  2024-01-12  0110    21   51.0  4757.0
1417  a2401  2024-01-12  0173   200   51.0  4757.0
1418  a2401  2024-01-12  0196    40   51.0  4757.0
1419  a2401  2024-01-17  0196    10   30.0  4793.0
[1420 rows x 6 columns]
```

#### 交割配对-郑商所

接口: futures_delivery_match_czce

目标地址: http://www.czce.com.cn/cn/jysj/jgpd/H770308index_1.htm

描述: 郑州商品交易所-交割配对

限量: 单次返回指定品种的的交割配对数据

输入参数

| 名称   | 类型  | 描述                   |
|------|-----|----------------------|
| date | str | date="20210106"; 交易日 |

输出参数

| 名称        | 类型      | 描述            |
|-----------|---------|---------------|
| 卖方会员      | object  | -             |
| 卖方会员-会员简称 | object  | -             |
| 买方会员      | object  | -             |
| 买方会员-会员简称 | object  | -             |
| 交割量       | float64 | 注意单位: 手(单边计算) |
| 配对日期      | object  | -             |
| 合约代码      | object  | -             |

接口示例

```python
import akshare as ak

futures_delivery_match_czce_df = ak.futures_delivery_match_czce(date="20210106")
print(futures_delivery_match_czce_df)
```

数据示例

```
    卖方会员 卖方会员-会员简称  买方会员 买方会员-会员简称 交割量        配对日期   合约代码
0   0038      申银万国  0198      英大期货   6  2021-01-06  AP101
1   0121      瑞达期货  0198      英大期货   4  2021-01-06  AP101
2   0202      中辉期货  0163    五矿经易期货   2  2021-01-06  AP101
3   0202      中辉期货  0058      鲁证期货   2  2021-01-06  AP101
4   0203      宏源期货  0163    五矿经易期货   2  2021-01-06  AP101
5   0262      一德期货  0058      鲁证期货   2  2021-01-06  AP101
6   0262      一德期货  0058      鲁证期货   2  2021-01-06  AP101
7   0203      宏源期货  0188      永安期货   8  2021-01-06  CF101
8   0203      宏源期货  0188      永安期货   8  2021-01-06  CF101
9   0209      华安期货  0088      中原期货   8  2021-01-06  CF101
10  0209      华安期货  0088      中原期货   8  2021-01-06  CF101
11  0068      中粮期货  0188      永安期货   9  2021-01-06  CJ101
12  0068      中粮期货  0038      申银万国   7  2021-01-06  CJ101
13  0068      中粮期货  0262      一德期货   7  2021-01-06  CJ101
14  0068      中粮期货  0188      永安期货   6  2021-01-06  CJ101
15  0068      中粮期货  0188      永安期货   6  2021-01-06  CJ101
16  0068      中粮期货  0188      永安期货   5  2021-01-06  CJ101
17  0068      中粮期货  0262      一德期货   4  2021-01-06  CJ101
18  0068      中粮期货  0262      一德期货   3  2021-01-06  CJ101
19  0068      中粮期货  0262      一德期货   3  2021-01-06  CJ101
20  0068      中粮期货  0188      永安期货   2  2021-01-06  CJ101
21  0068      中粮期货  0234      新湖期货   2  2021-01-06  CJ101
22  0068      中粮期货  0188      永安期货   2  2021-01-06  CJ101
23  0068      中粮期货  0068      中粮期货   1  2021-01-06  CJ101
24  0068      中粮期货  0038      申银万国   1  2021-01-06  CJ101
25  0068      中粮期货  0038      申银万国   1  2021-01-06  CJ101
26  0068      中粮期货  0188      永安期货   1  2021-01-06  CJ101
```

#### 库存周报

##### 上海期货交易所

接口: futures_stock_shfe_js

目标地址: https://datacenter.jin10.com/reportType/dc_shfe_weekly_stock

描述: 金十财经-上海期货交易所指定交割仓库库存周报

限量: 单次返回指定 date 的库存周报数据

输入参数

| 名称   | 类型  | 描述                                    |
|------|-----|---------------------------------------|
| date | str | date="20240419"; 库存周报只在每周的最后一个交易日公布数据 |

输出参数

| 名称           | 类型      | 描述 |
|--------------|---------|----|
| 商品           | object  | -  |
| 期货总量{随日期变动}  | int64   | -  |
| 期货总量{随日期变动}} | int64   | -  |
| 增减           | int64   | -  |
| 增减幅度         | float64 | -  |

接口示例

```python
import akshare as ak

futures_stock_shfe_js_df = ak.futures_stock_shfe_js(date="20240419")
print(futures_stock_shfe_js_df)
```

数据示例

```
           商品  期货总量2024-04-19  期货总量2024-04-12      增减   增减幅度
0          黄金            3078            3105     -27  -0.87
1           镍           23200           22774     426   1.87
2           锡           14486           14098     388   2.75
3           锌          131747          127634    4113   3.22
4           铝          228537          231765   -3228  -1.39
5           铜          300045          299723     322   0.11
6           铅           51746           60215   -8469 -14.06
7         螺纹钢          523732          521700    2032   0.39
8          线材          162052          162502    -450  -0.28
9          纸浆          451596          436461   15135   3.47
10     白银(千克)         4544581         4494032   50549   1.12
11        燃料油          183300          183300       0   0.00
12       热轧卷板          749487          744419    5068   0.68
13       沥青厂库          672180          672180       0   0.00
14       沥青仓库           75553           82453   -6900  -8.37
15       天然橡胶          216891          217721    -830  -0.38
16  中质含硫原油(桶)         5628000         6308000 -680000 -10.78
17       20号胶          141624          140737     887   0.63
```

#### 成交持仓

接口: futures_hold_pos_sina

目标地址: https://vip.stock.finance.sina.com.cn/q/view/vFutures_Positions_cjcc.php

描述: 新浪财经-期货-成交持仓

限量: 单次返回指定合约的成交持仓数据

输入参数

| 名称       | 类型  | 描述                                              |
|----------|-----|-------------------------------------------------|
| symbol   | str | symbol="成交量"; choice of {"成交量", "多单持仓", "空单持仓"} |
| contract | str | contract="OI2501"; 只限于商品期货                      |
| date     | str | date="20240223"                                 |

输出参数

| 名称     | 类型     | 描述 |
|--------|--------|----|
| 名次     | int64  | -  |
| 会员简称   | object | -  |
| 成交量    | int64  | -  |
| 比上交易增减 | int64  | -  |

接口示例

```python
import akshare as ak

futures_hold_pos_sina_df = ak.futures_hold_pos_sina(symbol="成交量", contract="OI2501", date="20241016")
print(futures_hold_pos_sina_df)
```

数据示例

```
    名次    会员简称    成交量   比上交易增减
0    1    中信期货（代客）  78197 -15458.0
1    2    东证期货（代客）  67129  -6540.0
2    3    国泰君安（代客）  53632 -12474.0
3    4    银河期货（代客）  21916 -10902.0
4    5    华闻期货（代客）  21807  -4777.0
5    6    徽商期货（代客）  21615  -7861.0
6    7    海通期货（代客）  18847  -4165.0
7    8    中信建投（代客）  15285  -2470.0
8    9    华泰期货（代客）  14610  -3036.0
9   10    南华期货（代客）  11990   -554.0
10  11    华西期货（代客）  11324   -795.0
11  12    方正中期（代客）  10958  -4512.0
12  13    瑞达期货（代客）   9038  -2121.0
13  14    国金期货（代客）   9026   3334.0
14  15    广发期货（代客）   8775  -5333.0
15  16    中辉期货（代客）   7746  -4127.0
16  17  国投安信期货（代客）   7739   1295.0
17  18    安粮期货（代客）   7671  -1946.0
18  19    东吴期货（代客）   7639  -4308.0
19  20    东方财富（代客）   7220  -1248.0
```

#### 现期图

接口: futures_spot_sys

目标地址: https://www.100ppi.com/sf/792.html

描述: 生意社-商品与期货-现期图

限量: 单次返回指定品种的现期图数据

输入参数

| 名称       | 类型  | 描述                                                  |
|----------|-----|-----------------------------------------------------|
| symbol   | str | symbol="铜"; 期货品种                                    |
| contract | str | indicator="市场价格"; choice of {"市场价格", "基差率", "主力基差"} |

输出参数-市场价格

| 名称   | 类型      | 描述 |
|------|---------|----|
| 日期   | object  | -  |
| 现货价格 | float64 | -  |
| 主力合约 | float64 | -  |
| 最近合约 | float64 | -  |

接口示例-市场价格

```python
import akshare as ak

futures_spot_sys_df = ak.futures_spot_sys(symbol="铜", indicator="市场价格")
print(futures_spot_sys_df)
```

数据示例-市场价格

```
     日期   现货价格    主力合约     最近合约
0   11-26  68661.67      NaN      NaN
1   12-05  69005.00  68200.0  68570.0
2   12-14  68613.33  68030.0  68570.0
3   12-23  69418.33      NaN      NaN
4   01-01  69250.00      NaN      NaN
5   01-10  68200.00  67920.0  68110.0
6   01-19  67813.33  67630.0  67680.0
7   01-28  68943.33      NaN      NaN
8   02-06  68001.67  68130.0  68010.0
9   02-15  67710.00      NaN      NaN
10  02-24  69351.67      NaN      NaN
```

输出参数-基差率

| 名称  | 类型      | 描述 |
|-----|---------|----|
| 日期  | object  | -  |
| 基差率 | float64 | -  |

接口示例-基差率

```python
import akshare as ak

futures_spot_sys_df = ak.futures_spot_sys(symbol="铜", indicator="基差率")
print(futures_spot_sys_df)
```

数据示例-基差率

```
     日期   基差率
0   11-26   NaN
1   12-05  1.17
2   12-14  0.85
3   12-23   NaN
4   01-01   NaN
5   01-10  0.41
6   01-19  0.27
7   01-28   NaN
8   02-06 -0.19
9   02-15   NaN
10  02-24   NaN
```

输出参数-主力基差

| 名称   | 类型      | 描述 |
|------|---------|----|
| 日期   | object  | -  |
| 主力基差 | float64 | -  |

接口示例-主力基差

```python
import akshare as ak

futures_spot_sys_df = ak.futures_spot_sys(symbol="铜", indicator="主力基差")
print(futures_spot_sys_df)
```

数据示例-主力基差

```
    日期    主力基差
0   11-26     NaN
1   12-05  805.00
2   12-14  583.33
3   12-23     NaN
4   01-01     NaN
5   01-10  280.00
6   01-19  183.33
7   01-28     NaN
8   02-06 -128.33
9   02-15     NaN
10  02-24     NaN
```

#### 合约信息

##### 上海期货交易所

接口: futures_contract_info_shfe

目标地址: https://tsite.shfe.com.cn/bourseService/businessdata/summaryinquiry/

描述: 上海期货交易所-交易所服务-业务数据-交易参数汇总查询

限量: 单次返回指定 date 的期货合约信息数据

输入参数

| 名称   | 类型  | 描述                   |
|------|-----|----------------------|
| date | str | date="20240513"; 交易日 |

输出参数

| 名称    | 类型      | 描述 |
|-------|---------|----|
| 合约代码  | object  | -  |
| 上市日   | object  | -  |
| 到期日   | object  | -  |
| 开始交割日 | object  | -  |
| 最后交割日 | object  | -  |
| 挂牌基准价 | float64 | -  |
| 交易日   | object  | -  |
| 更新时间  | object  | -  |

接口示例

```python
import akshare as ak

futures_contract_info_shfe_df = ak.futures_contract_info_shfe(date="20240513")
print(futures_contract_info_shfe_df)
```

数据示例

```
     合约代码  上市日       到期日  ...    挂牌基准价    交易日               更新时间
0    cu2405  2023-05-16  2024-05-15  ...  64490.0  2024-05-13  2024-05-13 18:21:10
1    cu2406  2023-06-16  2024-06-17  ...  66930.0  2024-05-13  2024-05-13 18:21:10
2    cu2407  2023-07-18  2024-07-15  ...  68220.0  2024-05-13  2024-05-13 18:21:10
3    cu2408  2023-08-16  2024-08-15  ...  67270.0  2024-05-13  2024-05-13 18:21:10
4    cu2409  2023-09-18  2024-09-18  ...  68900.0  2024-05-13  2024-05-13 18:21:10
..      ...         ...         ...  ...      ...         ...                  ...
271  ec2408  2023-08-18  2024-08-26  ...    780.0  2024-05-13  2024-05-13 18:21:10
272  ec2410  2023-08-18  2024-10-28  ...    780.0  2024-05-13  2024-05-13 18:21:10
273  ec2412  2023-08-18  2024-12-30  ...    780.0  2024-05-13  2024-05-13 18:21:10
274  ec2502  2024-02-27  2025-02-24  ...   1359.8  2024-05-13  2024-05-13 18:21:10
275  ec2504  2024-04-30  2025-04-28  ...   2158.3  2024-05-13  2024-05-13 18:21:10
[276 rows x 8 columns]
```

##### 上海国际能源交易中心

接口: futures_contract_info_ine

目标地址: https://www.ine.cn/bourseService/summary/?name=currinstrumentprop

描述: 上海国际能源交易中心-业务指南-交易参数汇总(期货)

限量: 单次返回指定 date 的期货合约信息数据

输入参数

| 名称   | 类型  | 描述                   |
|------|-----|----------------------|
| date | str | date="20241129"; 交易日 |

输出参数

| 名称    | 类型      | 描述 |
|-------|---------|----|
| 合约代码  | object  | -  |
| 上市日   | object  | -  |
| 到期日   | object  | -  |
| 开始交割日 | object  | -  |
| 最后交割日 | object  | -  |
| 挂牌基准价 | float64 | -  |
| 交易日   | object  | -  |

接口示例

```python
import akshare as ak

futures_contract_info_ine_df = ak.futures_contract_info_ine(date="20241129")
print(futures_contract_info_ine_df)
```

数据示例

```
    合约代码  上市日       到期日      开始交割日    最后交割日   挂牌基准价    交易日
0   sc2412  2021-12-01  2024-11-29  2024-12-02  2024-12-06   420.0  2024-11-29
1   sc2501  2024-01-02  2024-12-31  2025-01-06  2025-01-10   551.7  2024-11-29
2   sc2502  2024-02-01  2025-01-31  2025-02-03  2025-02-07   567.2  2024-11-29
3   sc2503  2022-03-01  2025-02-28  2025-03-03  2025-03-07   494.0  2024-11-29
4   sc2504  2024-04-01  2025-03-31  2025-04-01  2025-04-07   593.4  2024-11-29
..     ...         ...         ...         ...         ...     ...         ...
57  ec2502  2024-02-27  2025-02-24  2025-02-24  2025-02-24  1359.8  2024-11-29
58  ec2504  2024-04-30  2025-04-28  2025-04-28  2025-04-28  2158.3  2024-11-29
59  ec2506  2024-06-25  2025-06-30  2025-06-30  2025-06-30  2403.8  2024-11-29
60  ec2508  2024-08-27  2025-08-25  2025-08-25  2025-08-25  1509.4  2024-11-29
61  ec2510  2024-10-29  2025-10-27  2025-10-27  2025-10-27  1920.9  2024-11-29
[62 rows x 7 columns]
```

##### 大连商品交易所

接口: futures_contract_info_dce

目标地址: http://www.dce.com.cn/dalianshangpin/ywfw/ywcs/jycs/hyxxcx/index.html

描述: 大连商品交易所-业务/服务-业务参数-交易参数-合约信息查询

限量: 单次返回最近交易日的期货合约信息数据

输入参数

| 名称 | 类型 | 描述 |
|----|----|----|
| -  | -  | -  |

输出参数

| 名称     | 类型      | 描述 |
|--------|---------|----|
| 品种     | object  | -  |
| 合约代码   | object  | -  |
| 交易单位   | int64   | -  |
| 最小变动价位 | float64 | -  |
| 开始交易日  | object  | -  |
| 最后交易日  | object  | -  |
| 最后交割日  | object  | -  |

接口示例

```python
import akshare as ak

futures_contract_info_dce_df = ak.futures_contract_info_dce()
print(futures_contract_info_dce_df)
```

数据示例

```
     品种  合约代码  交易单位 最小变动价位  开始交易日  最后交易日  最后交割日
0    豆一  a2403    10     1.0  2023-03-15  2024-03-14  2024-03-19
1    豆一  a2405    10     1.0  2023-05-18  2024-05-17  2024-05-22
2    豆一  a2407    10     1.0  2023-07-17  2024-07-12  2024-07-17
3    豆一  a2409    10     1.0  2023-09-15  2024-09-13  2024-09-20
4    豆一  a2411    10     1.0  2023-11-15  2024-11-14  2024-11-19
..   ..    ...   ...     ...         ...         ...         ...
215  豆油  y2408    10     2.0  2023-08-15  2024-08-14  2024-08-19
216  豆油  y2409    10     2.0  2023-09-15  2024-09-13  2024-09-20
217  豆油  y2411    10     2.0  2023-11-15  2024-11-14  2024-11-19
218  豆油  y2412    10     2.0  2023-12-15  2024-12-13  2024-12-18
219  豆油  y2501    10     2.0  2024-01-16  2025-01-15  2025-01-20
[220 rows x 7 columns]
```

##### 郑州商品交易所

接口: futures_contract_info_czce

目标地址: http://www.czce.com.cn/cn/jysj/cksj/H770322index_1.htm

描述: 郑州商品交易所-交易数据-参考数据

限量: 单次返回指定 date 的期货合约信息数据

输入参数

| 名称   | 类型  | 描述                   |
|------|-----|----------------------|
| date | str | date="20240228"; 交易日 |

输出参数

| 名称                        | 类型      | 描述 |
|---------------------------|---------|----|
| 产品名称                      | object  | -  |
| 合约代码                      | object  | -  |
| 产品代码                      | object  | -  |
| 产品类型                      | object  | -  |
| 交易所MIC编码                  | object  | -  |
| 交易场所                      | object  | -  |
| 交易时间节假日除外                 | object  | -  |
| 交易国家ISO编码                 | object  | -  |
| 交易币种ISO编码                 | object  | -  |
| 结算币种ISO编码                 | object  | -  |
| 到期时间待国家公布2025年节假日安排后进行调整  | object  | -  |
| 结算方式                      | object  | -  |
| 挂牌频率                      | object  | -  |
| 最小变动价位                    | object  | -  |
| 最小变动价值                    | object  | -  |
| 交易单位                      | object  | -  |
| 计量单位                      | object  | -  |
| 最大下单量                     | object  | -  |
| 日持仓限额期货公司会员不限仓            | object  | -  |
| 大宗交易最小规模                  | object  | -  |
| 是否受CESR监管                 | object  | -  |
| 是否为灵活合约                   | object  | -  |
| 上市周期该产品的所有合约月份            | object  | -  |
| 交割通知日                     | object  | -  |
| 第一交易日                     | object  | -  |
| 最后交易日待国家公布2025年节假日安排后进行调整 | object  | -  |
| 交割结算日                     | object  | -  |
| 月份代码                      | object  | -  |
| 年份代码                      | object  | -  |
| 最后交割日                     | object  | -  |
| 车（船）板最后交割日                | object  | -  |
| 合约交割月份本合约交割月份             | object  | -  |
| 交易保证金率                    | object  | -  |
| 涨跌停板                      | object  | -  |
| 费用币种ISO编码                 | object  | -  |
| 交易手续费                     | float64 | -  |
| 手续费收取方式                   | object  | -  |
| 交割手续费                     | float64 | -  |
| 平今仓手续费                    | float64 | -  |
| 交易限额                      | float64 | -  |

接口示例

```python
import akshare as ak

futures_contract_info_czce_df = ak.futures_contract_info_czce(date="20240228")
print(futures_contract_info_czce_df)
```

数据示例

```
      产品名称  合约代码 产品代码 产品类型 交易所MIC编码  ...  交易手续费 手续费收取方式 交割手续费 平今仓手续费  交易限额
0    鲜苹果期货  AP403   AP   期货     XZCE  ...    5.0     绝对值   0.0   20.0   NaN
1    鲜苹果期货  AP404   AP   期货     XZCE  ...    5.0     绝对值   0.0   20.0   NaN
2    鲜苹果期货  AP405   AP   期货     XZCE  ...    5.0     绝对值   0.0   20.0   NaN
3    鲜苹果期货  AP410   AP   期货     XZCE  ...    5.0     绝对值   0.0   20.0   NaN
4    鲜苹果期货  AP411   AP   期货     XZCE  ...    5.0     绝对值   0.0   20.0   NaN
..     ...    ...  ...  ...      ...  ...    ...     ...   ...    ...   ...
213  动力煤期货  ZC410   ZC   期货     XZCE  ...  150.0     绝对值   0.0  150.0  20.0
214  动力煤期货  ZC411   ZC   期货     XZCE  ...  150.0     绝对值   0.0  150.0  20.0
215  动力煤期货  ZC412   ZC   期货     XZCE  ...  150.0     绝对值   0.0  150.0  20.0
216  动力煤期货  ZC501   ZC   期货     XZCE  ...  150.0     绝对值   0.0  150.0  20.0
217  动力煤期货  ZC502   ZC   期货     XZCE  ...  150.0     绝对值   0.0  150.0  20.0
[218 rows x 40 columns]
```

##### 广州期货交易所

接口: futures_contract_info_gfex

目标地址: http://www.gfex.com.cn/gfex/hyxx/ywcs.shtml

描述: 广州期货交易所-业务/服务-合约信息

限量: 单次返回最近交易日的期货合约信息数据

输入参数

| 名称 | 类型 | 描述 |
|----|----|----|
| -  | -  | -  |

输出参数

| 名称     | 类型     | 描述 |
|--------|--------|----|
| 品种     | object | -  |
| 合约代码   | object | -  |
| 交易单位   | int64  | -  |
| 最小变动单位 | int64  | -  |
| 开始交易日  | object | -  |
| 最后交易日  | object | -  |
| 最后交割日  | object | -  |

接口示例

```python
import akshare as ak

futures_contract_info_gfex_df = ak.futures_contract_info_gfex()
print(futures_contract_info_gfex_df)
```

数据示例

```
     品种    合约代码  交易单位  最小变动单位       开始交易日       最后交易日       最后交割日
0   碳酸锂  lc2403     1      50  2023-07-21  2024-03-14  2024-03-19
1   碳酸锂  lc2404     1      50  2023-07-21  2024-04-16  2024-04-19
2   碳酸锂  lc2405     1      50  2023-07-21  2024-05-17  2024-05-22
3   碳酸锂  lc2406     1      50  2023-07-21  2024-06-17  2024-06-20
4   碳酸锂  lc2407     1      50  2023-07-21  2024-07-12  2024-07-17
5   碳酸锂  lc2408     1      50  2023-08-15  2024-08-14  2024-08-19
6   碳酸锂  lc2409     1      50  2023-09-15  2024-09-13  2024-09-20
7   碳酸锂  lc2410     1      50  2023-10-23  2024-10-21  2024-10-24
8   碳酸锂  lc2411     1      50  2023-11-15  2024-11-14  2024-11-19
9   碳酸锂  lc2412     1      50  2023-12-15  2024-12-13  2024-12-18
10  碳酸锂  lc2501     1      50  2024-01-16  2025-01-15  2025-01-20
11  碳酸锂  lc2502     1      50  2024-02-23  2025-02-24  2025-02-27
12  工业硅  si2403     5       5  2023-03-15  2024-03-14  2024-03-19
13  工业硅  si2404     5       5  2023-08-15  2024-04-16  2024-04-19
14  工业硅  si2405     5       5  2023-09-15  2024-05-17  2024-05-22
15  工业硅  si2406     5       5  2023-10-23  2024-06-17  2024-06-20
16  工业硅  si2407     5       5  2023-11-15  2024-07-12  2024-07-17
17  工业硅  si2408     5       5  2023-12-15  2024-08-14  2024-08-19
18  工业硅  si2409     5       5  2024-01-16  2024-09-13  2024-09-20
19  工业硅  si2410     5       5  2024-02-23  2024-10-21  2024-10-24
```

##### 中国金融期货交易所

接口: futures_contract_info_cffex

目标地址: http://www.gfex.com.cn/gfex/hyxx/ywcs.shtml

描述: 中国金融期货交易所-数据-交易参数

限量: 单次返回指定 date 的期货合约信息数据

输入参数

| 名称   | 类型  | 描述                   |
|------|-----|----------------------|
| date | str | date="20240228"; 交易日 |

输出参数

| 名称    | 类型      | 描述 |
|-------|---------|----|
| 合约代码  | object  | -  |
| 合约月份  | object  | -  |
| 挂盘基准价 | float64 | -  |
| 上市日   | object  | -  |
| 最后交易日 | object  | -  |
| 涨停板幅度 | object  | -  |
| 跌停板幅度 | object  | -  |
| 涨停板价位 | float64 | -  |
| 跌停板价位 | float64 | -  |
| 持仓限额  | int64   | -  |
| 品种    | object  | -  |
| 查询交易日 | object  | -  |

接口示例

```python
import akshare as ak

futures_contract_info_cffex_df = ak.futures_contract_info_cffex(date="20240228")
print(futures_contract_info_cffex_df)
```

数据示例

```
        合约代码    合约月份    挂盘基准价 上市日  ...  跌停板价位 持仓限额 品种  查询交易日
0           TS2409  2409  100.974  2023-12-11  ...  101.056  2000  TS  2024-02-28
1           TS2406  2406  101.065  2023-09-11  ...  101.020  2000  TS  2024-02-28
2           TS2403  2403  101.145  2023-06-12  ...  100.858  2000  TS  2024-02-28
3           TL2409  2409   99.330  2023-12-11  ...  102.990  2000  TL  2024-02-28
4           TL2406  2406   98.750  2023-09-11  ...  102.870  2000  TL  2024-02-28
..             ...   ...      ...         ...  ...      ...   ...  ..         ...
831  HO2403-C-2075  2403  193.200  2023-12-18  ...   83.800  1200  HO  2024-02-28
832  HO2403-C-2050  2403  253.400  2023-12-06  ...  115.000  1200  HO  2024-02-28
833  HO2403-C-2025  2403  234.800  2023-12-18  ...  133.600  1200  HO  2024-02-28
834  HO2403-C-2000  2403  286.200  2023-12-08  ...  158.600  1200  HO  2024-02-28
835  HO2403-C-1975  2403  241.200  2024-01-18  ...  189.600  1200  HO  2024-02-28
[836 rows x 12 columns]
```

### 期货行情数据

#### 内盘-实时行情数据

接口: futures_zh_spot

目标地址: https://finance.sina.com.cn/futuremarket/

描述: 新浪财经-期货页面的实时行情数据

限量: 单次返回当日可以订阅的所有期货品种数据

输入参数

| 名称             | 类型  | 描述                                                    |
|----------------|-----|-------------------------------------------------------|
| subscribe_list | str | 需要订阅的合约代码; e.g., 按照示例获取                               |
| market         | str | market="CF"; market="CF": 商品期货, market="FF": 金融期货     |
| adjust         | str | adjust='0'; adjust='1': 返回合约、交易所和最小变动单位的实时数据, 返回数据会变慢 |

输出参数

| 名称                | 类型      | 描述                            |
|-------------------|---------|-------------------------------|
| symbol            | object  | 品种                            |
| time              | object  | 时间, e.g., 144050表示下午14点40分50秒 |
| open              | float64 | 开盘                            |
| high              | float64 | 高                             |
| low               | float64 | 低                             |
| current_price     | float64 | 当前价格(买价)                      |
| bid_price         | float64 | 买                             |
| ask_price         | float64 | 卖价                            |
| buy_vol           | int64   | 买量                            |
| sell_vol          | int64   | 卖量                            |
| hold              | float64 | 持仓量                           |
| volume            | int64   | 成交量                           |
| avg_price         | float64 | 均价                            |
| last_close        | float64 | 上一个交易日的收盘价                    |
| last_settle_price | float64 | 上一个交易日的结算价                    |

接口示例-单品种获取

```python
import akshare as ak

futures_zh_spot_df = ak.futures_zh_spot(symbol='V2205', market="CF", adjust='0')
print(futures_zh_spot_df)
```

数据示例-单品种获取

```
    symbol    time    open  ...  avg_price  last_close  last_settle_price
0  PVC2205  151039  8280.0  ...     8449.0      8423.0             8397.0
```

接口示例-多品种获取

```python
import akshare as ak

# 此处的合约需要是近期的合约, 否则会报错
futures_zh_spot_df = ak.futures_zh_spot(symbol='V2205, P2205, B2201, M2205', market="CF", adjust='0')
print(futures_zh_spot_df)
```

数据示例-多品种获取

```
    symbol    time    open  ...  avg_price  last_close  last_settle_price
0  PVC2205  151039  8280.0  ...     8449.0      8423.0             8397.0
1  棕榈油2205  151039  7690.0  ...     7844.0      7926.0             7848.0
2   豆二2201  151039  4165.0  ...     4193.0      4203.0             4203.0
3   豆粕2205  151039  3151.0  ...     3164.0      3153.0             3159.0
```

接口示例-订阅所有商品期货(大商所, 上期所, 郑商所主力合约)

```python
import time
import akshare as ak

dce_text = ak.match_main_contract(symbol="dce")
czce_text = ak.match_main_contract(symbol="czce")
shfe_text = ak.match_main_contract(symbol="shfe")
gfex_text = ak.match_main_contract(symbol="gfex")

while True:
    time.sleep(3)
    futures_zh_spot_df = ak.futures_zh_spot(
        symbol=",".join([dce_text, czce_text, shfe_text, gfex_text]),
        market="CF",
        adjust='0')
    print(futures_zh_spot_df)
```

数据示例-商品期货

```
        symbol    time       open  ...  avg_price  last_close  last_settle_price
0     PVC2309  150118    5994.00  ...    6009.00     6025.00            6006.00
1     棕榈油2309  150118    6850.00  ...    6842.00     6846.00            6960.00
2      豆二2306  150118    3926.00  ...    3976.00     3999.00            3979.00
3      豆粕2309  150118    3441.00  ...    3478.00     3497.00            3457.00
4     铁矿石2309  150919     712.00  ...     715.00      714.00             721.00
5      鸡蛋2309  150118    4216.00  ...    4194.00     4187.00            4216.00
6      塑料2309  150118    8050.00  ...    8046.00     8039.00            8124.00
7     聚丙烯2309  150118    7415.00  ...    7427.00     7424.00            7471.00
8     纤维板2305  150118    1146.50  ...    1144.00     1158.00            1158.00
9      豆油2309  150118    7452.00  ...    7470.00     7468.00            7558.00
10     玉米2307  150919    2660.00  ...    2643.00     2644.00            2673.00
11     豆一2307  150118    4849.00  ...    4822.00     4837.00            4878.00
12     焦炭2309  150118    2160.00  ...    2147.00     2149.00            2194.50
13     焦煤2309  150118    1408.00  ...    1384.50     1385.00            1439.50
14     淀粉2307  150118    2983.00  ...    2964.00     2973.00            2993.00
15    乙二醇2309  150118    4156.00  ...    4172.00     4162.00            4197.00
16     粳米2307  150118    3432.00  ...    3427.00     3419.00            3438.00
17    苯乙烯2306  150118    8230.00  ...    8240.00     8251.00            8285.00
18     生猪2307  150118   16375.00  ...   16480.00    16520.00           16300.00
19    PTA2309  145959    5586.00  ...    5590.00     5620.00            5640.00
20     菜油2309  145959    8041.00  ...    8078.00     8047.00            8108.00
21     菜籽2311  145944    5536.00  ...    5470.00     5503.00            5476.00
22     菜粕2309  145959    2920.00  ...    2964.00     2998.00            2928.00
26     白糖2307  145959    7007.00  ...    6981.00     7035.00            6903.00
27     棉花2309  145959   15285.00  ...   15370.00    15485.00           15275.00
28     甲醇2309  145959    2332.00  ...    2337.00     2334.00            2375.00
29     玻璃2309  145959    1753.00  ...    1763.00     1793.00            1779.00
30     硅铁2306  145959    7340.00  ...    7312.00     7314.00            7350.00
31     锰硅2306  145959    7150.00  ...    7150.00     7170.00            6976.00
32     棉纱2309  145959   22380.00  ...   22565.00    22625.00           22455.00
33     苹果2310  145959    8546.00  ...    8573.00     8622.00            8513.00
34     红枣2309  145959   10225.00  ...   10490.00    10645.00           10255.00
35     尿素2309  145959    1989.00  ...    1997.00     1994.00            1989.00
36     纯碱2309  145959    2141.00  ...    2120.00     2094.00            2147.00
37     短纤2306  145959    7304.00  ...    7318.00     7364.00            7382.00
38     花生2310  145959   10510.00  ...   10612.00    10680.00           10526.00
39    燃料油2309  150000    2944.00  ...    2950.00     2973.00            3000.00
40   上海原油2306  150000     525.90  ...     528.00      531.70             540.70
41      铝2306  150000   18415.00  ...   18440.00    18470.00           18570.00
42   天然橡胶2309  150000   11750.00  ...   11765.00    11770.00           11850.00
43     沪锌2306  150000   21075.00  ...   21200.00    21280.00           21230.00
44      铜2306  150000   66400.00  ...   66900.00    67410.00           66770.00
45     黄金2306  150000     444.18  ...     444.06      444.04             446.94
46    螺纹钢2310  150000    3691.00  ...    3683.00     3660.00            3728.00
47     线材2305  150000    4380.00  ...    4524.00     4476.00            4487.00
48      铅2306  150000   15280.00  ...   15255.00    15255.00           15285.00
49     白银2306  150000    5611.00  ...    5587.00     5619.00            5629.00
50     沥青2307  150000    3654.00  ...    3679.00     3699.00            3696.00
51   热轧卷板2310  150000    3730.00  ...    3738.00     3721.00            3781.00
52      锡2306  150000  207010.00  ...  209320.00   212380.00          208140.00
53      镍2306  150000  179960.00  ...  182090.00   182320.00          179980.00
54     纸浆2309  150000    5096.00  ...    5076.00     5058.00            5126.00
55   20号胶2307  150000    9535.00  ...    9600.00     9620.00            9655.00
56    不锈钢2306  150000   15160.00  ...   15225.00    15300.00           15155.00
57  低硫燃料油2308  150000    3777.00  ...    3821.00     3840.00            3825.00
58    国际铜2307  150000   58760.00  ...   59150.00    59660.00           59100.00
59    工业硅2308  150015   15170.00  ...   15165.00    15190.00           15290.00
```

接口示例-订阅所有金融期货(中金所主力合约)

```python
import time
import akshare as ak

cffex_text = ak.match_main_contract(symbol="cffex")

while True:
    time.sleep(3)
    futures_zh_spot_df = ak.futures_zh_spot(symbol=cffex_text, market="FF", adjust='0')
    print(futures_zh_spot_df)
```

数据示例-金融期货

```
        symbol      time      open      high       low current_price  \
0  沪深300指数1912  15:00:00  3902.800  3915.000  3871.200      3879.000
1    5年期国债1912  15:15:00    99.680    99.715    99.590        99.700
2   上证50指数1912  15:00:00  2972.800  2977.600  2948.800      2957.200
3  中证500指数1912  15:00:00  4841.200  4885.000  4800.800      4820.400
         hold volume         amount
0   93225.000  71712  279062965.600
1   18853.000   9786     975248.030
2   41928.000  25164   74617285.400
3  113365.000  73086  353463074.600
```

#### 内盘-实时行情数据(品种)

接口: futures_zh_realtime

目标地址: https://vip.stock.finance.sina.com.cn/quotes_service/view/qihuohangqing.html#titlePos_1

描述: 新浪财经-期货实时行情数据

限量: 单次返回指定 symbol 的数据

输入参数

| 名称     | 类型  | 描述                                                        |
|--------|-----|-----------------------------------------------------------|
| symbol | str | symbol="白糖", 品种名称；可以通过 ak.futures_symbol_mark() 获取所有品种命名表 |

输出参数

| 名称             | 类型      | 描述     |
|----------------|---------|--------|
| symbol         | object  | 合约代码   |
| exchange       | object  | 交易所    |
| name           | object  | 合约中文名称 |
| trade          | float64 | 最新价    |
| settlement     | float64 | 动态结算   |
| presettlement  | float64 | 昨日结算   |
| open           | float64 | 今开     |
| high           | float64 | 最高     |
| low            | float64 | 最低     |
| close          | float64 | 收盘     |
| bidprice1      | float64 | 买入     |
| askprice1      | float64 | 卖出     |
| bidvol1        | int64   | 买量     |
| askvol1        | int64   | 卖量     |
| volume         | int64   | 成交量    |
| position       | int64   | 持仓量    |
| ticktime       | object  | 时间     |
| tradedate      | object  | 日期     |
| preclose       | float64 | 前收盘价   |
| changepercent  | float64 | 涨跌幅    |
| bid            | float64 | -      |
| ask            | float64 | -      |
| prevsettlement | float64 | 前结算价   |

接口示例

```python
import akshare as ak

futures_zh_realtime_df = ak.futures_zh_realtime(symbol="白糖")
print(futures_zh_realtime_df)
```

数据示例

```
   symbol exchange    name   trade  ...  changepercent  bid  ask  prevsettlement
0     SR0     czce    白糖连续  6090.0  ...       0.004122  0.0  0.0          6065.0
1  SR2209     czce  白糖2209  6090.0  ...       0.004122  0.0  0.0          6065.0
2  SR2211     czce  白糖2211  6121.0  ...       0.004595  0.0  0.0          6093.0
3  SR2301     czce  白糖2301  6229.0  ...       0.003544  0.0  0.0          6207.0
4  SR2207     czce  白糖2207  6041.0  ...       0.003155  0.0  0.0          6022.0
5  SR2303     czce  白糖2303  6200.0  ...       0.000484  0.0  0.0          6197.0
6  SR2305     czce  白糖2305  6208.0  ...       0.000806  0.0  0.0          6203.0
```

接口示例-所有期货品种的所有合约（请注意数据获取频率）

```python
import akshare as ak
import pandas as pd

futures_symbol_mark_df = ak.futures_symbol_mark()

big_df = pd.DataFrame()
for item in futures_symbol_mark_df['symbol']:
    print(item)
    futures_zh_realtime_df = ak.futures_zh_realtime(symbol=item)
    big_df = pd.concat([big_df, futures_zh_realtime_df], ignore_index=True)

print(big_df)
```

数据示例-所有期货品种的所有合约

```
     symbol exchange           name  ...  bid  ask  prevsettlement
0       TA0     czce          PTA连续  ...  0.0  0.0         6744.00
1    TA2209     czce        PTA2209  ...  0.0  0.0         6744.00
2    TA2208     czce        PTA2208  ...  0.0  0.0         6816.00
3    TA2301     czce        PTA2301  ...  0.0  0.0         6424.00
4    TA2210     czce          PTA连续  ...  0.0  0.0         6654.00
..      ...      ...            ...  ...  ...  ...             ...
565  IC2207    cffex  中证500指数期货2207  ...  0.0  0.0         5904.20
566     TS0    cffex      2年期国债期货连续  ...  0.0  0.0          101.04
567  TS2209    cffex    2年期国债期货2209  ...  0.0  0.0          101.04
568  TS2206    cffex    2年期国债期货2206  ...  0.0  0.0          101.28
569  TS2212    cffex    2年期国债期货2212  ...  0.0  0.0          100.92
```

#### 内盘-分时行情数据

接口: futures_zh_minute_sina

目标地址: http://vip.stock.finance.sina.com.cn/quotes_service/view/qihuohangqing.html#titlePos_3

描述: 新浪财经-期货-分时数据

限量: 单次返回指定 symbol 和 period 的分时数据

输入参数

| 名称     | 类型  | 描述                                                                                              |
|--------|-----|-------------------------------------------------------------------------------------------------|
| symbol | str | symbol="IF2008"; 具体合约(期货品种符号需要大写), 可以通过调用 ak.match_main_contract(symbol="cffex") 接口获取, 或者访问网页获取 |
| period | str | period="1"; choice of {"1": "1分钟", "5": "5分钟", "15": "15分钟", "30": "30分钟", "60": "60分钟"}        |

输出参数

| 名称       | 类型      | 描述  |
|----------|---------|-----|
| datetime | object  | -   |
| open     | float64 | -   |
| high     | float64 | -   |
| low      | float64 | -   |
| close    | float64 | -   |
| volume   | int64   | -   |
| hold     | int64   | 持仓量 |

接口示例

```python
import akshare as ak

futures_zh_minute_sina_df = ak.futures_zh_minute_sina(symbol="RB0", period="1")
print(futures_zh_minute_sina_df)
```

数据示例

```
                 datetime    open    high     low   close  volume     hold
0     2025-01-14 21:13:00  3278.0  3280.0  3278.0  3279.0    3485  1739947
1     2025-01-14 21:14:00  3279.0  3279.0  3277.0  3278.0    5880  1741617
2     2025-01-14 21:15:00  3278.0  3278.0  3276.0  3276.0    4947  1740980
3     2025-01-14 21:16:00  3276.0  3277.0  3276.0  3277.0    3766  1741117
4     2025-01-14 21:17:00  3277.0  3278.0  3276.0  3278.0    3606  1741199
...                   ...     ...     ...     ...     ...     ...      ...
1018  2025-01-17 14:56:00  3373.0  3373.0  3371.0  3372.0    4021  1816948
1019  2025-01-17 14:57:00  3371.0  3374.0  3371.0  3374.0    8199  1816486
1020  2025-01-17 14:58:00  3373.0  3374.0  3372.0  3373.0    7900  1816404
1021  2025-01-17 14:59:00  3373.0  3374.0  3371.0  3372.0   13951  1813334
1022  2025-01-17 15:00:00  3371.0  3373.0  3371.0  3372.0   13335  1809583
[1023 rows x 7 columns]
```

#### 内盘-历史行情数据-东财

接口: futures_hist_em

目标地址: https://qhweb.eastmoney.com/quote

描述: 东方财富网-期货行情-行情数据

限量: 单次返回指定 symbol 的所有数据; 只能获取当期合约;

输入参数

| 名称         | 类型  | 描述                                                                 |
|------------|-----|--------------------------------------------------------------------|
| symbol     | str | symbol="热卷主连"; 具体合约可以通过 ak.futures_hist_table_em() 获取所有当期能获取数据的合约表 |
| period     | str | period="daily"; choice of {"daily", "weekly", "monthly"}           |
| start_date | str | start_date="19900101";                                             |
| end_date   | str | end_date="20500101";                                               |

输出参数

| 名称  | 类型      | 描述      |
|-----|---------|---------|
| 时间  | object  | -       |
| 开盘  | int64   | -       |
| 最高  | int64   | -       |
| 最低  | int64   | -       |
| 收盘  | int64   | -       |
| 涨跌  | int64   | -       |
| 涨跌幅 | float64 | 注意单位: % |
| 成交量 | int64   | -       |
| 成交额 | int64   | -       |
| 持仓量 | int64   | -       |

接口示例

```python
import akshare as ak

futures_hist_em_df = ak.futures_hist_em(symbol="热卷主连", period="daily")
print(futures_hist_em_df)
```

数据示例

```
          时间    开盘    最高    最低  ...   涨跌幅     成交量   成交额   持仓量
0     2014-03-21  3252  3336  3230  ...  0.00   93739            0    26015
1     2014-03-24  3314  3330  3266  ...  0.18   43405            0    23808
2     2014-03-25  3326  3400  3320  ...  1.44   59927            0    30628
3     2014-03-26  3370  3382  3354  ... -0.06   18938            0    30280
4     2014-03-27  3368  3386  3350  ... -0.41   20115            0    30715
...          ...   ...   ...   ...  ...   ...     ...          ...      ...
2636  2025-01-13  3316  3365  3313  ...  1.45  507472  16973149696  1170370
2637  2025-01-14  3357  3416  3348  ...  1.94  510296  17280815616  1167989
2638  2025-01-15  3400  3428  3391  ...  0.92  415364  14167324160  1158782
2639  2025-01-16  3415  3450  3411  ...  1.09  465334  15972707584  1157046
2640  2025-01-17  3447  3488  3436  ...  1.52  471470  16326870760  1196880
[2641 rows x 10 columns]
```

#### 内盘-历史行情数据-新浪

接口: futures_zh_daily_sina

目标地址: https://finance.sina.com.cn/futures/quotes/V2105.shtml

描述: 新浪财经-期货-日频数据

限量: 单次返回指定 symbol 的所有日频数据; 期货连续合约为 品种代码+0，比如螺纹钢连续合约为 RB0;

输入参数

| 名称     | 类型  | 描述                                                                    |
|--------|-----|-----------------------------------------------------------------------|
| symbol | str | symbol="RB0"; 具体合约可以通过 ak.match_main_contract(symbol="shfe") 获取或者访问网页 |

输出参数

| 名称     | 类型      | 描述  |
|--------|---------|-----|
| date   | object  | -   |
| open   | float64 | 开盘价 |
| high   | float64 | 最高价 |
| low    | float64 | 最低价 |
| close  | float64 | 收盘价 |
| volume | int64   | 成交量 |
| hold   | int64   | 持仓量 |
| settle | float64 | 结算价 |

接口示例-连续

```python
import akshare as ak

futures_zh_daily_sina_df = ak.futures_zh_daily_sina(symbol="RB0")
print(futures_zh_daily_sina_df)
```

数据示例-连续

```
            date    open    high     low   close   volume     hold  settle
0     2009-03-27  3550.0  3663.0  3513.0  3561.0   354590    45548     0.0
1     2009-03-30  3550.0  3580.0  3528.0  3544.0   145168    48380     0.0
2     2009-03-31  3538.0  3566.0  3531.0  3549.0    70592    44714     0.0
3     2009-04-01  3560.0  3561.0  3543.0  3547.0    28100    42076     0.0
4     2009-04-02  3545.0  3548.0  3456.0  3473.0   235446    68888     0.0
          ...     ...     ...     ...     ...      ...      ...     ...
3644  2024-03-28  3470.0  3504.0  3441.0  3481.0  2312398  1433991  3473.0
3645  2024-03-29  3475.0  3486.0  3405.0  3412.0  2015960  1299182  3448.0
3646  2024-04-01  3400.0  3456.0  3368.0  3456.0  1622273  1141786  3405.0
3647  2024-04-02  3459.0  3493.0  3431.0  3463.0  1217107  1026768  3463.0
3648  2024-04-03  3500.0  3540.0  3462.0  3463.0  1620584  1674662  3509.0
[3649 rows x 8 columns]
```

接口示例-合约

```python
import akshare as ak

futures_zh_daily_sina_df = ak.futures_zh_daily_sina(symbol="RB2410")
print(futures_zh_daily_sina_df)
```

数据示例-合约

```
           date    open    high     low   close   volume     hold  settle
0    2023-10-17  3600.0  3650.0  3600.0  3631.0     1140      893  3631.0
1    2023-10-18  3636.0  3636.0  3593.0  3602.0     1050     1642  3615.0
2    2023-10-19  3612.0  3633.0  3590.0  3623.0      818     2070  3608.0
3    2023-10-20  3630.0  3638.0  3580.0  3588.0     1554     3088  3606.0
4    2023-10-23  3580.0  3595.0  3555.0  3568.0     2058     4175  3570.0
..          ...     ...     ...     ...     ...      ...      ...     ...
110  2024-03-28  3487.0  3535.0  3461.0  3509.0   799059  1302305  3493.0
111  2024-03-29  3505.0  3507.0  3443.0  3450.0   837570  1488697  3475.0
112  2024-04-01  3439.0  3497.0  3408.0  3492.0  1404806  1569587  3445.0
113  2024-04-02  3498.0  3546.0  3482.0  3506.0  1694801  1651838  3512.0
114  2024-04-03  3500.0  3540.0  3462.0  3463.0  1620584  1674662  3509.0
[115 rows x 8 columns]
```

#### 内盘-历史行情数据-交易所

接口: get_futures_daily

目标地址: 各交易所网站

描述: 提供各交易所各品种的网站的历史行情数据, 其中 20040625, 20070604, 20081226, 20090119 原网页数据缺失

限量: 单次返回指定时间段指定交易所的所有期货品种历史数据

输入参数

| 名称         | 类型  | 描述                                                                      |
|------------|-----|-------------------------------------------------------------------------|
| start_date | str | start_date="20200701"                                                   |
| end_date   | str | end_date="20200716"                                                     |
| market     | str | market="DCE"; choice of {"CFFEX", "INE", "CZCE", "DCE", "SHFE", "GFEX"} |

输出参数

| 名称            | 类型    | 描述   |
|---------------|-------|------|
| symbol        | str   | 合约   |
| date          | str   | 交易日  |
| open          | float | 开盘价  |
| high          | float | 最高价  |
| low           | float | 最低价  |
| close         | str   | 收盘价  |
| volume        | str   | 成交量  |
| open_interest | str   | 持仓量  |
| turnover      | float | 成交额  |
| settle        | float | 结算价  |
| pre_settle    | float | 前结算价 |
| variety       | str   | 品种   |

接口示例

```python
import akshare as ak

get_futures_daily_df = ak.get_futures_daily(start_date="20200701", end_date="20200716", market="DCE")
print(get_futures_daily_df)
```

数据示例

```
     symbol      date     open  ...   settle pre_settle variety
0     A2007  20200701     6160  ...     6122       5643       A
1     A2009  20200701     4871  ...     4874       4839       A
2     A2011  20200701     4480  ...     4424       4411       A
3     A2101  20200701     4402  ...     4385       4395       A
4     A2103  20200701     4422  ...     4394       4412       A
     ...       ...      ...  ...      ...        ...     ...
2749   PG99  20200716  3899.35  ...  3881.69    3879.72      PG
2750    P99  20200716   5257.2  ...  5269.32    5212.02       P
2751    L99  20200716  7213.86  ...  7156.44    7220.27       L
2752    M99  20200716  2858.98  ...  2862.43    2855.63       M
2753   JM99  20200716  1193.58  ...  1195.49    1197.92      JM
```

#### 外盘-品种代码表

接口: futures_hq_subscribe_exchange_symbol

目标地址: https://finance.sina.com.cn/money/future/hf.html

描述: 新浪财经-外盘商品期货品种代码表数据

限量: 单次返回当前交易日的订阅的所有期货品种的品种代码表数据

输入参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| -   | -   | -   |

输出参数

| 名称     | 类型     | 描述  |
|--------|--------|-----|
| symbol | object | -   |
| code   | object | -   |

接口示例

```python
import akshare as ak

futures_hq_subscribe_exchange_symbol_df = ak.futures_hq_subscribe_exchange_symbol()
print(futures_hq_subscribe_exchange_symbol_df)
```

数据示例

```
      symbol  code
0   NYBOT-棉花    CT
1    LME镍3个月   NID
2    LME铅3个月   PBD
3    LME锡3个月   SND
4    LME锌3个月   ZSD
5    LME铝3个月   AHD
6    LME铜3个月   CAD
7    CBOT-黄豆     S
8    CBOT-小麦     W
9    CBOT-玉米     C
10  CBOT-黄豆油    BO
11  CBOT-黄豆粉    SM
12      日本橡胶   TRB
13    COMEX铜    HG
14  NYMEX天然气    NG
15   NYMEX原油    CL
16   COMEX白银    SI
17   COMEX黄金    GC
18   CME-瘦肉猪   LHC
19     布伦特原油   OIL
20       伦敦金   XAU
21       伦敦银   XAG
22      伦敦铂金   XPT
23      伦敦钯金   XPD
24       马棕油  FCPO
25     欧洲碳排放   EUA
```

#### 外盘-实时行情数据

接口: futures_foreign_commodity_realtime

目标地址: https://finance.sina.com.cn/money/future/hf.html

描述: 新浪财经-外盘商品期货数据

限量: 单次返回当前交易日的订阅的所有期货品种的数据

输入参数

| 名称     | 类型          | 描述                                                                     |
|--------|-------------|------------------------------------------------------------------------|
| symbol | list or str | 需要订阅的合约代码; 调用 **ak.futures_hq_subscribe_exchange_symbol()** 获取字段及代码对应表 |

输出参数

| 名称    | 类型      | 描述  |
|-------|---------|-----|
| 名称    | object  | -   |
| 最新价   | float64 | -   |
| 人民币报价 | float64 | -   |
| 涨跌额   | float64 | -   |
| 涨跌幅   | float64 | -   |
| 开盘价   | float64 | -   |
| 最高价   | float64 | -   |
| 最低价   | float64 | -   |
| 昨日结算价 | float64 | -   |
| 持仓量   | float64 | -   |
| 买价    | float64 | -   |
| 卖价    | float64 | -   |
| 行情时间  | object  | -   |
| 日期    | object  | -   |

接口示例-传入字符串

```python
import akshare as ak

futures_foreign_commodity_realtime_df = ak.futures_foreign_commodity_realtime(symbol='CT,NID')
print(futures_foreign_commodity_realtime_df)
```

数据示例-传入字符串

```
         名称      最新价          人民币报价  ...        卖价      行情时间          日期
0  NYBOT-棉花     81.1   12953.138327  ...     81.13  16:13:34  2024-05-24
1   LME镍3个月  20256.0  146748.643200  ...  20265.00  16:13:08  2024-05-24
[2 rows x 14 columns]
```

接口示例-传入列表

```python
import time
import akshare as ak

print("开始接收实时行情, 每 3 秒刷新一次")
subscribe_list = ak.futures_foreign_commodity_subscribe_exchange_symbol()  # 其中 subscribe_list 为列表
while True:
    time.sleep(3)
    futures_foreign_commodity_realtime_df = ak.futures_foreign_commodity_realtime(symbol=subscribe_list)
    print(futures_foreign_commodity_realtime_df)
```

数据示例-传入列表

```
          名称       最新价    人民币报价  ...         卖价      行情时间       日期
0     新加坡铁矿石    100.955  7.159729e+00  ...    101.000  05:05:00  2024-08-31
1        马棕油   3980.500  9.075850e+02  ...   3979.000  18:00:00  2024-08-30
2        日橡胶    374.790  2.658011e+04  ...    375.000  18:00:46  2024-08-30
3       美国原糖     19.424  1.377550e+00  ...     19.440  01:00:00  2024-08-31
4   CME比特币期货  61300.500  2.173716e+06  ...  61285.000  12:55:15  2024-08-23
5   NYBOT-棉花     69.975  1.094071e+04  ...     69.960  02:19:58  2024-08-31
6    LME镍3个月  16700.000  1.184364e+05  ...  16755.000  01:59:52  2024-08-31
7    LME铅3个月   2067.950  1.466590e+04  ...   2068.000  01:59:02  2024-08-31
8    LME锡3个月  32457.000  2.301850e+05  ...  32440.000  01:47:38  2024-08-31
9    LME锌3个月   2897.150  2.054659e+04  ...   2899.000  01:59:00  2024-08-31
10   LME铝3个月   2445.450  1.734313e+04  ...   2448.000  01:59:54  2024-08-31
11   LME铜3个月   9248.850  6.559284e+04  ...   9254.000  01:59:48  2024-08-31
12   CBOT-黄豆    999.875  2.605537e+03  ...    999.250  02:19:59  2024-08-31
13   CBOT-小麦    552.150  1.438827e+03  ...    552.250  02:19:56  2024-08-31
14   CBOT-玉米    401.525  1.121056e+03  ...    401.500  02:20:00  2024-08-31
15  CBOT-黄豆油     41.855  6.544098e+03  ...     41.900  02:20:00  2024-08-31
16  CBOT-黄豆粉    312.300  2.441852e+03  ...    312.600  02:20:00  2024-08-31
18    COMEX铜    421.755  6.594209e+04  ...    422.000  04:59:55  2024-08-31
19  NYMEX天然气      2.148  1.523362e+01  ...      2.146  04:59:58  2024-08-31
20   NYMEX原油     73.621  3.811477e+03  ...     73.660  04:59:59  2024-08-31
21   COMEX白银     29.229  2.072921e+02  ...     29.250  04:59:55  2024-08-31
22   COMEX黄金   2535.060  5.780135e+02  ...   2536.000  04:59:58  2024-08-31
23   CME-瘦肉猪     82.218  5.830901e+02  ...     82.250  02:04:59  2024-08-31
24     布伦特原油     77.015  3.987190e+03  ...     77.060  05:58:53  2024-08-31
25       伦敦金   2503.290  5.707696e+02  ...   2503.670  04:55:00  2024-08-31
26       伦敦银     28.830  6.573465e+00  ...     28.880  04:55:00  2024-08-31
27      伦敦铂金    930.960  2.122661e+02  ...    931.400  04:59:56  2024-08-31
28      伦敦钯金    961.400  2.192067e+02  ...    962.500  04:58:04  2024-08-31
29     欧洲碳排放     70.185  4.977520e+02  ...     70.260  00:00:00  2024-08-31
[29 rows x 14 columns]
```

#### 外盘-实时行情数据-东财

接口: futures_global_spot_em

目标地址: https://quote.eastmoney.com/center/gridlist.html#futures_global

描述: 东方财富网-行情中心-期货市场-国际期货-实时行情数据

限量: 单次返回所有期货品种的实时行情数据

输入参数

| 名称 | 类型 | 描述 |
|----|----|----|
| -  | -  | -  |

输出参数

| 名称  | 类型      | 描述      |
|-----|---------|---------|
| 序号  | int64   | -       |
| 代码  | object  | -       |
| 名称  | object  | -       |
| 最新价 | float64 | -       |
| 涨跌额 | float64 | -       |
| 涨跌幅 | float64 | 注意单位: % |
| 今开  | float64 | -       |
| 最高  | float64 | -       |
| 最低  | float64 | -       |
| 昨结  | float64 | -       |
| 成交量 | int64   | -       |
| 买盘  | int64   | -       |
| 卖盘  | int64   | -       |
| 持仓量 | int64   | -       |

接口示例

```python
import akshare as ak

futures_global_spot_em_df = ak.futures_global_spot_em()
print(futures_global_spot_em_df)
```

数据示例

```
     序号  代码           名称        最新价  ...   成交量  买盘  卖盘    持仓量
0      1   NG00Y          天然气      3.252  ...  15662  8269  7393  305016
1      2   NG25F      天然气2501      3.252  ...  15662  8269  7393  305016
2      3  JRU24Z      日橡胶2412    380.000  ...      3     3     0      69
3      4    LNKT        综合镍03  16100.000  ...      0     0     0       0
4      5  JRU25G      日橡胶2502    376.000  ...     11    11     0     495
..   ...     ...          ...        ...  ...    ...   ...   ...     ...
615  616    M25G      天然气2502    119.310  ...    815   495   320   47830
616  617  MPM25K      棕榈油2505   4570.000  ...    859   621   238   20731
617  618  MPM25X      棕榈油2511   4235.000  ...      3     3     0    3189
618  619   GC25Z  COMEX黄金2512   2756.200  ...     25     8    17    8537
619  620   SB26K     糖11号2605     18.470  ...   2742  1289  1453   29289
[620 rows x 14 columns]
```

#### 外盘-历史行情数据-东财

接口: futures_global_hist_em

目标地址: https://quote.eastmoney.com/globalfuture/HG25J.html

描述: 东方财富网-行情中心-期货市场-国际期货-历史行情数据

限量: 单次返回指定品种的历史数据

输入参数

| 名称     | 类型  | 描述                                                                        |
|--------|-----|---------------------------------------------------------------------------|
| symbol | str | symbol="HG00Y"; 品种代码；可以通过 ak.futures_global_spot_em() 来获取所有可获取历史行情数据的品种代码 |

输出参数

| 名称  | 类型      | 描述      |
|-----|---------|---------|
| 日期  | object  | -       |
| 代码  | object  | -       |
| 名称  | object  | -       |
| 开盘  | float64 | -       |
| 最新价 | float64 | -       |
| 最高  | float64 | -       |
| 最低  | float64 | -       |
| 总量  | int64   | -       |
| 涨幅  | float64 | 注意单位: % |
| 持仓  | object  | -       |
| 日增  | int64   | -       |

接口示例

```python
import akshare as ak

futures_global_hist_em_df = ak.futures_global_hist_em(symbol="HG00Y")
print(futures_global_hist_em_df)
```

数据示例

```
         日期     代码      名称     开盘  ...     总量    涨幅      持仓      日增
0     2011-07-15  HG00Y  COMEX铜  4.3740  ...      1  0.00       0           0
1     2011-07-21  HG00Y  COMEX铜  4.4330  ...      2  1.35       0           0
2     2011-07-29  HG00Y  COMEX铜  4.4580  ...    114  1.04       0           0
3     2011-08-03  HG00Y  COMEX铜  4.3600  ...    167 -3.42       0           0
4     2011-08-12  HG00Y  COMEX铜  4.0240  ...      6 -7.49       0           0
...          ...    ...     ...     ...  ...    ...   ...     ...         ...
3480  2025-02-27  HG00Y  COMEX铜  4.5835  ...  50367  0.17  122084        1306
3481  2025-02-28  HG00Y  COMEX铜  4.5955  ...  42115 -1.19  123553        1469
3482  2025-03-03  HG00Y  COMEX铜  4.5765  ...  48192  0.93  122627  4294966370
3483  2025-03-04  HG00Y  COMEX铜  4.5880  ...  47016 -0.56  122610  4294967279
3484  2025-03-05  HG00Y  COMEX铜  4.5800  ...  57782  5.25  123815        1205
[3485 rows x 11 columns]
```

#### 外盘-历史行情数据-新浪

接口: futures_foreign_hist

目标地址: https://finance.sina.com.cn/futuremarket/

描述: 新浪财经-期货外盘历史行情数据

限量: 单次返回指定品种的历史数据

输入参数

| 名称     | 类型  | 描述                                                                           |
|--------|-----|------------------------------------------------------------------------------|
| symbol | str | symbol="ZSD"; 外盘期货的 **symbol** 可以通过 **ak.hf_subscribe_exchange_symbol()** 获取 |

输出参数

| 名称     | 类型      | 描述  |
|--------|---------|-----|
| date   | object  | 交易日 |
| open   | float64 | 开盘价 |
| high   | float64 | 最高价 |
| low    | float64 | 最低价 |
| close  | float64 | 收盘价 |
| volume | int64   | 成交量 |

接口示例

```python
import akshare as ak

futures_foreign_hist_df = ak.futures_foreign_hist(symbol="ZSD")
print(futures_foreign_hist_df)
```

数据示例

```
           date     open    high      low   close  volume  position  s
0    2014-01-20  2082.00  2086.0  2068.75  2076.5    1159         0  0
1    2014-01-21  2077.75  2092.5  2061.25  2089.0    1318         0  0
2    2014-01-22  2088.75  2102.0  2077.50  2084.0    1754         0  0
3    2014-01-23  2083.75  2084.0  2041.25  2045.5    2639         0  0
4    2014-01-24  2043.75  2051.0  2019.25  2025.0    2264         0  0
         ...      ...     ...      ...     ...     ...       ... ..
2541 2024-01-15  2510.00  2615.0  2506.50  2555.0    9364         0  0
2542 2024-01-16  2555.00  2567.0  2531.00  2548.5    6719         0  0
2543 2024-01-17  2548.00  2550.0  2460.00  2466.0   11555         0  0
2544 2024-01-18  2474.00  2478.5  2443.00  2466.0   10442         0  0
2545 2024-01-19  2469.50  2485.0  2454.00  2456.5    7236         0  0
[2546 rows x 8 columns]
```

#### 外盘-合约详情

接口: futures_foreign_detail

目标地址: https://finance.sina.com.cn/futuremarket/

描述: 新浪财经-期货外盘期货合约详情

限量: 单次返回指定品种的合约详情数据

输入参数

| 名称     | 类型  | 描述                                                                      |
|--------|-----|-------------------------------------------------------------------------|
| symbol | str | symbol="ZSD"; 外盘期货的 **symbol** 可以通过 **hf_subscribe_exchange_symbol** 获取 |

输出参数

| 名称      | 类型  | 描述  |
|---------|-----|-----|
| 交易品种    | str | -   |
| 最小变动价位	 | str | -   |
| 交易时间	   | str | -   |
| 交易代码	   | str | -   |
| 交易单位	   | str | -   |
| 涨跌停板幅度	 | str | -   |
| 交割品级		  | str | -   |
| 上市交易所		 | str | -   |
| 报价单位		  | str | -   |
| 合约交割月份  | str | -   |
| 交割地点    | str | -   |
| 附加信息    | str | -   |

接口示例

```python
import akshare as ak
futures_foreign_detail_df = ak.futures_foreign_detail(symbol="ZSD")
print(futures_foreign_detail_df)
```

数据示例

```
        0                               1  ...       4                       5
0    交易品种                伦敦锌(CFD差价合约并非期货)  ...    报价单位                    美元/吨
1  最小变动价位       电话交易：0.5美元/吨 电子盘：0.25美元/吨  ...  合约交割月份  LME三个月期货合约是连续合约，每日都有交割
2    交易时间  LME Select北京时间（夏令时）08:00-02:00  ...    交割地点                     NaN
3    交易代码                             ZSD  ...    附加信息                     NaN
```

#### 新加坡交易所期货

接口: futures_settlement_price_sgx

目标地址: https://www.sgx.com/zh-hans/research-education/derivatives

描述: 新加坡交易所-衍生品-历史数据-历史结算价格; 数据于下个工作日新加坡时间下午 2 点起提供

限量: 单次获取指定交易日前一日的所有期货品种的结算价数据; 只能获取过去 60 个交易日内的数据; 由于国内网络限制, 请使用代理访问

输入参数

| 名称   | 类型  | 描述                   |
|------|-----|----------------------|
| date | str | date="20231107"; 交易日 |

输出参数

| 名称     | 类型      | 描述     |
|--------|---------|--------|
| DATE   | int64   | 日期     |
| COM    | object  | 品种代码   |
| COM_MM | int64   | 品种到期月份 |
| COM_YY | int64   | 品种年份   |
| OPEN   | float64 | 开盘价    |
| HIGH   | float64 | 最高价    |
| LOW    | float64 | 最低价    |
| CLOSE  | float64 | 收盘价    |
| SETTLE | float64 | 结算价    |
| VOLUME | int64   | 交易量    |
| OINT   | int64   | 未平仓合约  |
| SERIES | object  | 合约代码   |

接口示例

```python
import akshare as ak

futures_settlement_price_sgx_df = ak.futures_settlement_price_sgx(date="20231108")
print(futures_settlement_price_sgx_df)
```

数据示例

```
          DATE    COM  COM_MM  COM_YY  ...  SETTLE  VOLUME  OINT   SERIES
0     20231107  1MF        11    2023  ...  465.34       0     0   1MFX23
1     20231107  1MF        12    2023  ...  462.13       0     0   1MFZ23
2     20231107  1MF         1    2024  ...  457.05       0     0   1MFF24
3     20231107  1MF         2    2024  ...  453.97       0     0   1MFG24
4     20231107  1MF         3    2024  ...  452.39       0     0   1MFH24
        ...    ...     ...     ...  ...     ...     ...   ...      ...
3165  20231107  ZYES       12    2023  ...   16.95       0     0  ZYESZ23
3166  20231107  ZYES        1    2024  ...   17.05       0     0  ZYESF24
3167  20231107  ZZEE       11    2023  ...  263.45     584    90  ZZEEX23
3168  20231107  ZZEE       12    2023  ...  264.80       0     0  ZZEEZ23
3169  20231107  ZZEE        1    2024  ...  266.25       0     0  ZZEEF24
[3170 rows x 12 columns]
```

### 期货连续合约

接口: futures_main_sina

目标地址: https://vip.stock.finance.sina.com.cn/quotes_service/view/qihuohangqing.html#titlePos_0

描述: 新浪财经-期货-主力连续合约历史数据

限量: 单次返回单个期货品种的主力连续合约的日频历史数据

输入参数

| 名称         | 类型  | 描述                                                                            |
|------------|-----|-------------------------------------------------------------------------------|
| symbol     | str | symbol="IF0"; 请参考 **新浪连续合约品种一览表**, 也可通过 **ak.futures_display_main_sina()** 获取 |
| start_date | str | start_date="19900101";                                                        |
| end_date   | str | end_date="22220101";                                                          |

新浪连续合约品种一览表

| index | symbol | exchange | name        |
|------:|:-------|:---------|:------------|
|     0 | V0     | dce      | PVC连续       |
|     1 | P0     | dce      | 棕榈油连续       |
|     2 | B0     | dce      | 豆二连续        |
|     3 | M0     | dce      | 豆粕连续        |
|     4 | I0     | dce      | 铁矿石连续       |
|     5 | JD0    | dce      | 鸡蛋连续        |
|     6 | L0     | dce      | 塑料连续        |
|     7 | PP0    | dce      | 聚丙烯连续       |
|     8 | FB0    | dce      | 纤维板连续       |
|     9 | BB0    | dce      | 胶合板连续       |
|    10 | Y0     | dce      | 豆油连续        |
|    11 | C0     | dce      | 玉米连续        |
|    12 | A0     | dce      | 豆一连续        |
|    13 | J0     | dce      | 焦炭连续        |
|    14 | JM0    | dce      | 焦煤连续        |
|    15 | CS0    | dce      | 淀粉连续        |
|    16 | EG0    | dce      | 乙二醇连续       |
|    17 | RR0    | dce      | 粳米连续        |
|    18 | EB0    | dce      | 苯乙烯连续       |
|    19 | LH0    | dce      | 生猪连续        |
|    20 | TA0    | czce     | PTA连续       |
|    21 | OI0    | czce     | 菜油连续        |
|    22 | RS0    | czce     | 菜籽连续        |
|    23 | RM0    | czce     | 菜粕连续        |
|    24 | ZC0    | czce     | 动力煤连续       |
|    25 | WH0    | czce     | 强麦连续        |
|    26 | JR0    | czce     | 粳稻连续        |
|    27 | SR0    | czce     | 白糖连续        |
|    28 | CF0    | czce     | 棉花连续        |
|    29 | RI0    | czce     | 早籼稻连续       |
|    30 | MA0    | czce     | 甲醇连续        |
|    31 | FG0    | czce     | 玻璃连续        |
|    32 | LR0    | czce     | 晚籼稻连续       |
|    33 | SF0    | czce     | 硅铁连续        |
|    34 | SM0    | czce     | 锰硅连续        |
|    35 | CY0    | czce     | 棉纱连续        |
|    36 | AP0    | czce     | 苹果连续        |
|    37 | CJ0    | czce     | 红枣连续        |
|    38 | UR0    | czce     | 尿素连续        |
|    39 | SA0    | czce     | 纯碱连续        |
|    40 | PF0    | czce     | 短纤连续        |
|    41 | PK0    | czce     | 花生连续        |
|    42 | FU0    | shfe     | 燃料油连续       |
|    43 | SC0    | ine      | 上海原油连续      |
|    44 | AL0    | shfe     | 铝连续         |
|    45 | RU0    | shfe     | 天然橡胶连续      |
|    46 | ZN0    | shfe     | 沪锌连续        |
|    47 | CU0    | shfe     | 铜连续         |
|    48 | AU0    | shfe     | 黄金连续        |
|    49 | RB0    | shfe     | 螺纹钢连续       |
|    50 | WR0    | shfe     | 线材连续        |
|    51 | PB0    | shfe     | 铅连续         |
|    52 | AG0    | shfe     | 白银连续        |
|    53 | BU0    | shfe     | 沥青连续        |
|    54 | HC0    | shfe     | 热轧卷板连续      |
|    55 | SN0    | shfe     | 锡连续         |
|    56 | NI0    | shfe     | 镍连续         |
|    57 | SP0    | shfe     | 纸浆连续        |
|    58 | NR0    | ine      | 20号胶连续      |
|    59 | SS0    | shfe     | 不锈钢连续       |
|    60 | LU0    | ine      | 低硫燃料油连续     |
|    61 | BC0    | ine      | 国际铜连续       |
|    62 | IF0    | cffex    | 沪深300指数期货连续 |
|    63 | TF0    | cffex    | 5年期国债期货连续   |
|    64 | IH0    | cffex    | 上证50指数期货连续  |
|    65 | IC0    | cffex    | 中证500指数期货连续 |
|    66 | TS0    | cffex    | 2年期国债期货连续   |

输出参数

| 名称    | 类型     | 描述   |
|-------|--------|------|
| 日期    | object | -    |
| 开盘价   | int64  | -    |
| 最高价   | int64  | -    |
| 最低价   | int64  | -    |
| 收盘价   | int64  | -    |
| 成交量   | int64  | 注意单位 |
| 持仓量   | int64  | 注意单位 |
| 动态结算价 | int64  | -    |

接口示例-主力连续合约

```python
import akshare as ak

futures_main_sina_hist = ak.futures_main_sina(symbol="V0", start_date="20200101", end_date="20220101")
print(futures_main_sina_hist)
```

数据示例-主力连续合约

```
     日期        开盘价  最高价 最低价 收盘价  成交量  持仓量  动态结算价
0    2020-01-02  6520  6530  6485  6500    54491  230632   6500
1    2020-01-03  6500  6510  6480  6495    72391  229655   6495
2    2020-01-06  6495  6590  6480  6545   174761  237376   6535
3    2020-01-07  6540  6545  6495  6510    86013  230968   6515
4    2020-01-08  6515  6570  6510  6565   115493  235940   6550
..          ...   ...   ...   ...   ...      ...     ...    ...
481  2021-12-27  8500  8605  8233  8239  1162292  322968   8413
482  2021-12-28  8239  8510  8224  8483   930875  342271   8362
483  2021-12-29  8500  8520  8413  8484   797016  348914   8468
484  2021-12-30  8480  8503  8372  8478   924423  351493      0
485  2021-12-31  8492  8530  8276  8321   987714  320158   8384
```

接口示例-新浪主力连续合约品种一览表接口

```python
import akshare as ak

futures_display_main_sina_df = ak.futures_display_main_sina()
print(futures_display_main_sina_df)
```

数据示例-新浪主力连续合约品种一览表接口

```
   symbol    exchange       name
0      V0      dce        PVC连续
1      P0      dce        棕榈油连续
2      B0      dce         豆二连续
3      M0      dce         豆粕连续
4      I0      dce        铁矿石连续
..    ...      ...          ...
58    IF0    cffex  沪深300指数期货连续
59    TF0    cffex    5年期国债期货连续
60    IH0    cffex   上证50指数期货连续
61    IC0    cffex  中证500指数期货连续
62    TS0    cffex    2年期国债期货连续
```

### 期货合约详情

接口: futures_contract_detail

目标地址: https://finance.sina.com.cn/futures/quotes/V2101.shtml

描述: 新浪财经-期货-期货合约详情数据

限量: 单次返回指定 symbol 的合约详情数据

输入参数

| 名称     | 类型  | 描述                                                                              |
|--------|-----|---------------------------------------------------------------------------------|
| symbol | str | symbol='AP2101'; 请参考**新浪连续合约品种一览表**, 也可通过 **ak.futures_display_main_sina()** 获取 |

新浪连续合约品种一览表（更新于 20241226）

|    | symbol | exchange | name        |
|---:|:-------|:---------|:------------|
|  0 | V0     | dce      | PVC连续       |
|  1 | P0     | dce      | 棕榈油连续       |
|  2 | B0     | dce      | 豆二连续        |
|  3 | M0     | dce      | 豆粕连续        |
|  4 | I0     | dce      | 铁矿石连续       |
|  5 | JD0    | dce      | 鸡蛋连续        |
|  6 | L0     | dce      | 塑料连续        |
|  7 | PP0    | dce      | 聚丙烯连续       |
|  8 | FB0    | dce      | 纤维板连续       |
|  9 | BB0    | dce      | 胶合板连续       |
| 10 | Y0     | dce      | 豆油连续        |
| 11 | C0     | dce      | 玉米连续        |
| 12 | A0     | dce      | 豆一连续        |
| 13 | J0     | dce      | 焦炭连续        |
| 14 | JM0    | dce      | 焦煤连续        |
| 15 | CS0    | dce      | 淀粉连续        |
| 16 | EG0    | dce      | 乙二醇连续       |
| 17 | RR0    | dce      | 粳米连续        |
| 18 | EB0    | dce      | 苯乙烯连续       |
| 19 | PG0    | dce      | 液化石油气连续     |
| 20 | LH0    | dce      | 生猪连续        |
| 21 | TA0    | czce     | PTA连续       |
| 22 | OI0    | czce     | 菜油连续        |
| 23 | RS0    | czce     | 菜籽连续        |
| 24 | RM0    | czce     | 菜粕连续        |
| 25 | WH0    | czce     | 强麦连续        |
| 26 | JR0    | czce     | 粳稻连续        |
| 27 | SR0    | czce     | 白糖连续        |
| 28 | CF0    | czce     | 棉花连续        |
| 29 | RI0    | czce     | 早籼稻连续       |
| 30 | MA0    | czce     | 甲醇连续        |
| 31 | FG0    | czce     | 玻璃连续        |
| 32 | LR0    | czce     | 晚籼稻连续       |
| 33 | SF0    | czce     | 硅铁连续        |
| 34 | SM0    | czce     | 锰硅连续        |
| 35 | CY0    | czce     | 棉纱连续        |
| 36 | AP0    | czce     | 苹果连续        |
| 37 | CJ0    | czce     | 红枣连续        |
| 38 | UR0    | czce     | 尿素连续        |
| 39 | SA0    | czce     | 纯碱连续        |
| 40 | PF0    | czce     | 短纤连续        |
| 41 | PK0    | czce     | 花生连续        |
| 42 | SH0    | czce     | 烧碱连续        |
| 43 | PX0    | czce     | 对二甲苯连续      |
| 44 | FU0    | shfe     | 燃料油连续       |
| 45 | SC0    | ine      | 上海原油连续      |
| 46 | AL0    | shfe     | 铝连续         |
| 47 | RU0    | shfe     | 天然橡胶连续      |
| 48 | ZN0    | shfe     | 沪锌连续        |
| 49 | CU0    | shfe     | 铜连续         |
| 50 | AU0    | shfe     | 黄金连续        |
| 51 | RB0    | shfe     | 螺纹钢连续       |
| 52 | WR0    | shfe     | 线材连续        |
| 53 | PB0    | shfe     | 铅连续         |
| 54 | AG0    | shfe     | 白银连续        |
| 55 | BU0    | shfe     | 沥青连续        |
| 56 | HC0    | shfe     | 热轧卷板连续      |
| 57 | SN0    | shfe     | 锡连续         |
| 58 | NI0    | shfe     | 镍连续         |
| 59 | SP0    | shfe     | 纸浆连续        |
| 60 | NR0    | ine      | 20号胶连续      |
| 61 | SS0    | shfe     | 不锈钢连续       |
| 62 | LU0    | ine      | 低硫燃料油连续     |
| 63 | BC0    | ine      | 国际铜连续       |
| 64 | AO0    | shfe     | 氧化铝连续       |
| 65 | BR0    | shfe     | 丁二烯橡胶连续     |
| 66 | EC0    | ine      | 集运指数欧线期货连续  |
| 67 | IF0    | cffex    | 沪深300指数期货连续 |
| 68 | TF0    | cffex    | 5年期国债期货连续   |
| 69 | IH0    | cffex    | 上证50指数期货连续  |
| 70 | IC0    | cffex    | 中证500指数期货连续 |
| 71 | TS0    | cffex    | 2年期国债期货连续   |
| 72 | IM0    | cffex    | 中证连续指数期货连续  |
| 73 | SI0    | gfex     | 工业硅连续       |
| 74 | LC0    | gfex     | 碳酸锂连续       |
| 75 | PS0    | gfex     | 多晶硅连续       |

输出参数

| 名称    | 类型     | 描述       |
|-------|--------|----------|
| item  | object | 合约具体的项目  |
| value | object | 合约具体的项目值 |

接口示例

```python
import akshare as ak

futures_contract_detail_df = ak.futures_contract_detail(symbol='V2001')
print(futures_contract_detail_df)
```

数据示例

```
       item                                              value
0      交易品种                                               聚氯乙烯
1    最小变动价位                                               5元/吨
2      交易时间  上午 09:00-10:15 10:30-11:30 下午 13:30-15:00 夜间 2...
3      交割品级  质量标准符合《悬浮法通用型聚氯乙烯树脂（GB/T 5761-2006）》规定的SG5型一等品...
4      交割方式                                               实物交割
5      交易单位                                               5吨/手
6    涨跌停板幅度                                       上一交易日结算价的±4%
7     最后交易日                                        合约月份第10个交易日
8   最低交易保证金                                投机买卖20.0%，套保买卖20.0%
9      交易代码                                                  V
10     报价单位                                           元(人民币/吨)
11   合约交割月份                                            1---12月
12    最后交割日                                       最后交易日后第3个交易日
13    交易手续费                                  开平仓2元/手，短线开平仓1元/手
14    上市交易所                                            大连商品交易所
```

### 中证商品指数

#### 中证商品指数

接口: futures_index_ccidx

目标地址: http://www.ccidx.com/index.html

描述: 中证商品指数

限量: 单次返回指定 symbol 的指数日频率数据

输入参数

| 名称     | 类型  | 描述                                                        |
|--------|-----|-----------------------------------------------------------|
| symbol | str | symbol="中证商品期货指数"; choice of {"中证商品期货指数", "中证商品期货价格指数", } |

输出参数

| 名称   | 类型      | 描述 |
|------|---------|----|
| 日期   | object  | -  |
| 指数代码 | object  | -  |
| 收盘点位 | float64 | -  |
| 结算点位 | float64 | -  |
| 涨跌   | float64 | -  |
| 涨跌幅  | float64 | -  |

接口示例

```python
import akshare as ak

futures_index_ccidx_df = ak.futures_index_ccidx(symbol="中证商品期货指数")
print(futures_index_ccidx_df)
```

数据示例

```
     日期        指数代码      收盘点位  结算点位   涨跌   涨跌幅
0    2021-02-18  100001.CCI  1421.18  1414.30  45.82  3.35
1    2021-02-19  100001.CCI  1420.86  1419.17   4.87  0.34
2    2021-02-22  100001.CCI  1439.96  1437.49  18.32  1.29
3    2021-02-23  100001.CCI  1447.08  1445.75   8.26  0.57
4    2021-02-24  100001.CCI  1449.70  1443.34  -2.41 -0.17
..          ...         ...      ...      ...    ...   ...
960  2025-02-06  100001.CCI  1965.37  1958.64   3.38  0.17
961  2025-02-07  100001.CCI  1977.99  1972.23  13.59  0.69
962  2025-02-10  100001.CCI  1984.26  1979.65   7.42  0.38
963  2025-02-11  100001.CCI  1981.76  1985.65   6.00  0.30
964  2025-02-12  100001.CCI  1986.45  1981.82  -3.83 -0.19
[965 rows x 6 columns]
```

#### 中证商品指数-分时

接口: futures_index_min_ccidx

目标地址: http://www.ccidx.com/index.html

描述: 中证商品指数-分时数据

限量: 单次返回指定 symbol 的指数分时数据

输入参数

| 名称     | 类型  | 描述                                                                                                                      |
|--------|-----|-------------------------------------------------------------------------------------------------------------------------|
| symbol | str | symbol="中证监控油脂油料期货指数"; choice of {"中证商品期货指数", "中证商品期货价格指数", "中证监控油脂油料期货指数", "中证监控软商品期货指数",  "中证监控能化期货指数", "中证监控钢铁期货指数"} |

输出参数

| 名称       | 类型      | 描述  |
|----------|---------|-----|
| datetime | object  |     |
| value    | object  | 最新价 |

接口示例

```python
import akshare as ak

futures_index_min_ccidx_df = ak.futures_index_min_ccidx(symbol="中证商品期货指数")
print(futures_index_min_ccidx_df)
```

数据示例

```
             datetime      value
0    2022-12-27 21:00  1802.6834
1    2022-12-27 21:01  1801.8893
2    2022-12-27 21:02  1802.0698
3    2022-12-27 21:03  1802.5329
4    2022-12-27 21:04  1803.5531
..                ...        ...
554  2022-12-28 14:56  1798.3488
555  2022-12-28 14:57  1798.1081
556  2022-12-28 14:58  1797.4098
557  2022-12-28 14:59  1797.7608
558  2022-12-28 15:00  1797.7799
```

### 现货与股票

接口: futures_spot_stock

目标地址: https://data.eastmoney.com/ifdata/xhgp.html

描述: 东方财富网-数据中心-现货与股票

限量: 单次返回指定 indicator 的所有数据

输入参数

| 名称     | 类型  | 描述                                                                      |
|--------|-----|-------------------------------------------------------------------------|
| symbol | str | symbol="能源"; choice of {'能源', '化工', '塑料', '纺织', '有色', '钢铁', '建材', '农副'} |

输出参数

| 名称     | 类型      | 描述        |
|--------|---------|-----------|
| 商品名称   | object  | -         |
| 近5月    | float64 | 注意: 具体的日期 |
| 近4月    | float64 | 注意: 具体的日期 |
| 近3月    | float64 | 注意: 具体的日期 |
| 近2月    | float64 | 注意: 具体的日期 |
| 近1月    | float64 | 注意: 具体的日期 |
| 最新价    | float64 | -         |
| 近半年涨跌幅 | float64 | 注意单位: %   |
| 生产商    | object  | 注意: 字符串组成 |
| 下游用户   | object  | 注意: 字符串组成 |

接口示例

```python
import akshare as ak

futures_spot_stock_df = ak.futures_spot_stock(symbol="能源")
print(futures_spot_stock_df)
```

数据示例

```
  商品名称  ...                                               下游用户
0   甲醇  ...                                                  -
1  燃料油  ...                                                  -
2  炼焦煤  ...                              美锦能源, 长春燃气, 山西焦化, 宝泰隆
3   沥青  ...                                                  -
4  液化气  ...                                                  -
5  动力煤  ...                                                  -
6   焦炭  ...  沙钢股份, 中信特钢, 抚顺特钢, 三钢闽光, 首钢股份, 华菱钢铁, 鞍钢股份, 包钢股份...
[7 rows x 10 columns]
```

### COMEX 库存数据

接口: futures_comex_inventory

目标地址: https://data.eastmoney.com/pmetal/comex/by.html

描述: 东方财富网-数据中心-期货期权-COMEX 库存数据

限量: 单次返回指定 symbol 的所有历史数据

输入参数

| 名称     | 类型  | 描述                                  |
|--------|-----|-------------------------------------|
| symbol | str | symbol="黄金"; choice of {"黄金", "白银"} |

输出参数

| 名称                  | 类型      | 描述       |
|---------------------|---------|----------|
| 序号                  | int64   | -        |
| 日期                  | object  | -        |
| COMEX{symbol}库存量-吨  | float64 | 注意单位: 盎司 |
| COMEX{symbol}库存量-盎司 | float64 | 注意单位: 吨  |

接口示例

```python
import akshare as ak

futures_comex_inventory_df = ak.futures_comex_inventory(symbol="黄金")
print(futures_comex_inventory_df)
```

数据示例

```
     序号      日期  COMEX白银库存量-吨  COMEX白银库存量-盎司
0        1  2018-11-15    249.638588   8.026061e+06
1        2  2018-11-16    249.638588   8.026061e+06
2        3  2018-11-19    249.618620   8.025419e+06
3        4  2018-11-20    249.318222   8.015761e+06
4        5  2018-11-21    249.318222   8.015761e+06
    ...         ...           ...            ...
1244  1245  2023-11-08    618.567383   1.988739e+07
1245  1246  2023-11-09    618.564383   1.988729e+07
1246  1247  2023-11-10    618.462382   1.988401e+07
1247  1248  2023-11-13    618.461542   1.988399e+07
1248  1249  2023-11-14    618.461542   1.988399e+07
[1249 rows x 4 columns]
```

### 生猪信息

#### 核心数据

接口: futures_hog_core

目标地址: https://zhujia.zhuwang.com.cn

描述: 玄田数据-核心数据

限量: 单次返回指定 symbol 的所有历史数据

输入参数

| 名称     | 类型  | 描述                                            |
|--------|-----|-----------------------------------------------|
| symbol | str | symbol="外三元"; choice of {"外三元", "内三元", "土杂猪"} |

输出参数

| 名称    | 类型      | 描述 |
|-------|---------|----|
| date  | object  | -  |
| value | float64 | -  |

接口示例

```python
import akshare as ak

futures_hog_core_df = ak.futures_hog_core(symbol="外三元")
print(futures_hog_core_df)
```

数据示例

```
           date  value
0    2023-03-18  15.42
1    2023-03-19  15.46
2    2023-03-20  15.42
3    2023-03-21  15.44
4    2023-03-22  15.25
..          ...    ...
362  2024-03-14  14.58
363  2024-03-15  14.53
364  2024-03-16  14.54
365  2024-03-17  14.67
366  2024-03-18  14.71
[367 rows x 2 columns]
```

#### 成本维度

接口: futures_hog_cost

目标地址: https://zhujia.zhuwang.com.cn

描述: 玄田数据-成本维度

限量: 单次返回指定 symbol 的所有历史数据

输入参数

| 名称     | 类型  | 描述                                                    |
|--------|-----|-------------------------------------------------------|
| symbol | str | symbol="玉米"; choice of {"玉米", "豆粕", "二元母猪价格", "仔猪价格"} |

输出参数

| 名称    | 类型      | 描述 |
|-------|---------|----|
| date  | object  | -  |
| value | float64 | -  |

接口示例

```python
import akshare as ak

futures_hog_cost_df = ak.futures_hog_cost(symbol="玉米")
print(futures_hog_cost_df)
```

数据示例

```
           date  value
0    2023-03-18   2915
1    2023-03-19   2895
2    2023-03-20   2874
3    2023-03-21   2903
4    2023-03-22   2891
..          ...    ...
362  2024-03-14   2474
363  2024-03-15   2486
364  2024-03-16   2473
365  2024-03-17   2471
366  2024-03-18   2462
[367 rows x 2 columns]
```

#### 供应维度

接口: futures_hog_supply

目标地址: https://zhujia.zhuwang.com.cn

描述: 玄田数据-供应维度

限量: 单次返回指定 symbol 的所有历史数据

输入参数

| 名称     | 类型  | 描述                                                                                          |
|--------|-----|---------------------------------------------------------------------------------------------|
| symbol | str | symbol="玉米"; choice of {"猪肉批发价", "储备冻猪肉", "饲料原料数据", "白条肉", "生猪产能", "育肥猪", "肉类价格指数", "猪粮比价"} |

输出参数

| 名称    | 类型      | 描述 |
|-------|---------|----|
| date  | object  | -  |
| value | float64 | -  |

接口示例

```python
import akshare as ak

futures_hog_supply_df = ak.futures_hog_supply(symbol="猪肉批发价")
print(futures_hog_supply_df)
```

数据示例

```
          date  value
0   2023-12-17  20.14
1   2023-12-18  20.12
2   2023-12-19  20.24
3   2023-12-20  20.39
4   2023-12-21  20.54
..         ...    ...
85  2024-03-11  19.89
86  2024-03-12  19.96
87  2024-03-13  20.01
88  2024-03-14  20.12
89  2024-03-15  20.27
[90 rows x 2 columns]
```

### 生猪市场价格指数

接口: index_hog_spot_price

目标地址: https://hqb.nxin.com/pigindex/index.shtml

描述: 行情宝-生猪市场价格指数

限量: 单次返回所有数据

输入参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| -   | -   | -   |

输出参数

| 名称     | 类型      | 描述         |
|--------|---------|------------|
| 日期     | object  | -          |
| 指数     | float64 | -          |
| 4个月均线  | float64 | -          |
| 6个月均线  | float64 | -          |
| 12个月均线 | float64 | -          |
| 预售均价   | float64 | 注意单位: 元/公斤 |
| 成交均价   | float64 | 注意单位: 元/公斤 |
| 成交均重   | int64   | 注意单位: kg   |

接口示例

```python
import akshare as ak

index_hog_spot_price_df = ak.index_hog_spot_price()
print(index_hog_spot_price_df)
```

数据示例

```
       日期        指数   4个月均线   6个月均线  12个月均线  预售均价   成交均价  成交均重
0    2015-01-05   92.88     NaN     NaN     NaN   0.00  13.49     0
1    2015-01-12   92.82     NaN     NaN     NaN   0.00  13.48     0
2    2015-01-19   93.29     NaN     NaN     NaN   0.00  13.55     0
3    2015-01-26   93.48     NaN     NaN     NaN   0.00  13.57     0
4    2015-02-02   92.12     NaN     NaN     NaN   0.00  13.38     0
..          ...     ...     ...     ...     ...    ...    ...   ...
460  2024-02-05  107.08  100.72  105.82  103.19  16.06  15.55   116
461  2024-02-26   95.14   99.63  103.68  103.25  15.64  13.81   122
462  2024-03-04   96.85   99.31  102.08  103.02  15.60  14.06   120
463  2024-03-11   98.45   99.10  101.32  102.79  15.69  14.30   122
464  2024-03-18  100.62   99.07  100.69  102.65  15.76  14.61   125
[465 rows x 8 columns]
```

### 期货资讯

接口: futures_news_shmet

目标地址: https://www.shmet.com/newsFlash/newsFlash.html?searchKeyword=

描述: 上海金属网-快讯

限量: 指定 symbol 的数据

输入参数

| 名称     | 类型  | 描述                                                                                           |
|--------|-----|----------------------------------------------------------------------------------------------|
| symbol | str | symbol="全部"; choice of {"全部", "要闻", "VIP", "财经", "铜", "铝", "铅", "锌", "镍", "锡", "贵金属", "小金属"} |

输出参数

| 名称   | 类型     | 描述  |
|------|--------|-----|
| 发布时间 | object | -   |
| 内容   | object | -   |

接口示例

```python
import akshare as ak

futures_news_shmet_df = ak.futures_news_shmet(symbol="铜")
print(futures_news_shmet_df)
```

数据示例

```
                   发布时间                                                 内容
0   2024-02-05 14:02:44+08:00  【金诚信资源：Lonshi铜矿增储情况】金诚信近日接受机构调研时表示，公司在2022年4月发...
1   2024-02-05 14:02:44+08:00  【金诚信资源项目进展情况】金诚信近日接受机构调研时表示，公司两岔河磷矿南采区已投产，2023...
2   2024-02-05 14:02:44+08:00  【金诚信资源：雨季对Dikulushi铜矿和Lonshi铜矿的影响】金诚信近日接受机构调研时...
3   2024-02-05 14:02:44+08:00  【金诚信：整体来看，铜价存在长期持续震荡上行可能】金诚信近日接受机构调研时表示，从中长期来看...
4   2024-02-05 14:19:22+08:00  【江苏省雨雪天气或影响该地区铜杆厂生产销售】2月3日11时12分，江苏省气象台变更发布道路结...
..                        ...                                                ...
995 2024-03-21 11:39:32+08:00  【中科铜箔2023年铜箔年产量超过34500吨】2023年，湖北中科铜箔的铜箔年产量由原来的...
996 2024-03-21 11:39:32+08:00  【江西新越年产40万吨电解铜项目环评批准公示】根据建设项目环境影响评价审批程序的有关规定，经...
997 2024-03-21 11:39:32+08:00  【铜陵有色铜基新材料项目空分制氧项目1号主冷箱顺利封顶】3月16日上午8点18分，随着300...
998 2024-03-21 11:39:32+08:00  【电工合金：2023年净利润1.36亿元，同比增19.98%】近日，电工合金发布2023年年...
999 2024-03-21 12:10:40+08:00  【SHMET铜现货报价】上海金属网讯：截止11:30分，上海金属网1#电解铜报72700-7...
[1000 rows x 2 columns]
```

## [AKShare](https://github.com/akfamily/akshare) 公募基金数据

### 基金基本信息

接口: fund_name_em

目标地址: http://fund.eastmoney.com/fund.html

描述: 东方财富网-天天基金网-基金数据-所有基金的基本信息数据

限量: 单次返回当前时刻所有历史数据

输入参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| -   | -   | -   |

输出参数

| 名称   | 类型     | 描述  |
|------|--------|-----|
| 基金代码 | object | -   |
| 拼音缩写 | object | -   |
| 基金简称 | object | -   |
| 基金类型 | object | -   |
| 拼音全称 | object | -   |

接口示例

```python
import akshare as ak

fund_name_em_df = ak.fund_name_em()
print(fund_name_em_df)
```

数据示例

```
       基金代码      拼音缩写  ...  基金类型                              拼音全称
0      000001        HXCZHH  ...   混合型                  HUAXIACHENGZHANGHUNHE
1      000002        HXCZHH  ...   混合型                  HUAXIACHENGZHANGHUNHE
2      000003      ZHKZZZQA  ...   债券型           ZHONGHAIKEZHUANZHAIZHAIQUANA
3      000004      ZHKZZZQC  ...   债券型           ZHONGHAIKEZHUANZHAIZHAIQUANC
4      000005    JSZQXYDQZQ  ...  定开债券   JIASHIZENGQIANGXINYONGDINGQIZHAIQUAN
       ...           ...  ...   ...                                    ...
10223  952035     GTJAJDCHH  ...   混合型             GUOTAIJUNANJUNDECHENGHUNHE
10224  952099    GTJAJDXHHC  ...   混合型              GUOTAIJUNANJUNDEXINHUNHEC
10225  959991  XZJQLLXYSHHA  ...   混合型  XINGZHENGJINQILINLINGXIANYOUSHIHUNHEA
10226  959993  XZJQLLXYSHHC  ...   混合型  XINGZHENGJINQILINLINGXIANYOUSHIHUNHEC
10227  980003   TPYLGYGDCYZ  ...   债券型   TAIPINGYANGLIUGEYUEGUNDONGCHIYOUZHAI
```

### 基金基本信息-雪球

接口: fund_individual_basic_info_xq

目标地址: https://danjuanfunds.com/funding/000001

描述: 雪球基金-基金详情

限量: 单次返回单只基金基本信息

输入参数

| 名称      | 类型    | 描述                      |
|---------|-------|-------------------------|
| symbol  | str   | symbol="000001"; 基金代码   |
| timeout | float | timeout=None; 默认不设置超时参数 |

输出参数

| 名称    | 类型     | 描述 |
|-------|--------|----|
| item  | object | -  |
| value | object | -  |

接口示例

```python
import akshare as ak

fund_individual_basic_info_xq_df = ak.fund_individual_basic_info_xq(symbol="000001")
print(fund_individual_basic_info_xq_df)
```

数据示例

```
      item                                              value
0     基金代码                                             000001
1     基金名称                                             华夏成长混合
2     基金全称                                            华夏成长前收费
3     成立时间                                         2001-12-18
4     最新规模                                             27.30亿
5     基金公司                                         华夏基金管理有限公司
6     基金经理                                            王泽实 万方方
7     托管银行                                       中国建设银行股份有限公司
8     基金类型                                             混合型-偏股
9     评级机构                                               晨星评级
10    基金评级                                               一星基金
11    投资策略  在股票投资方面，本基金重点投资于预期利润或收入具有良好增长潜力的成长型上市公司发行的股票，从...
12    投资目标  本基金属成长型基金，主要通过投资于具有良好成长性的上市公司的股票，在保持基金资产安全性和流动...
13  业绩比较基准                                       本基金暂不设业绩比较基准
```

### 基金基本信息-指数型

接口: fund_info_index_em

目标地址: http://fund.eastmoney.com/trade/zs.html

描述: 东方财富网-天天基金网-基金数据-基金基本信息-指数型

限量: 单次返回当前时刻所有历史数据

输入参数

| 名称        | 类型  | 描述                                                                                    |
|-----------|-----|---------------------------------------------------------------------------------------|
| symbol    | str | symbol="全部"; choice of {"全部", "沪深指数", "行业主题", "大盘指数", "中盘指数", "小盘指数", "股票指数", "债券指数"} |
| indicator | str | indicator="全部"; choice of {"全部", "被动指数型", "增强指数型"}                                    |

输出参数

| 名称   | 类型      | 描述      |
|------|---------|---------|
| 基金代码 | object  | -       |
| 基金名称 | object  | -       |
| 单位净值 | float64 | -       |
| 日期   | object  | -       |
| 日增长率 | float64 | 注意单位: % |
| 近1周  | float64 | 注意单位: % |
| 近1月  | float64 | 注意单位: % |
| 近3月  | float64 | 注意单位: % |
| 近6月  | float64 | 注意单位: % |
| 近1年  | float64 | 注意单位: % |
| 近2年  | float64 | 注意单位: % |
| 近3年  | float64 | 注意单位: % |
| 今年来  | float64 | 注意单位: % |
| 成立来  | float64 | 注意单位: % |
| 手续费  | float64 | 注意单位: % |
| 起购金额 | object  | -       |
| 跟踪标的 | object  | -       |
| 跟踪方式 | object  | -       |

接口示例

```python
import akshare as ak

fund_info_index_em_df = ak.fund_info_index_em(symbol="沪深指数", indicator="增强指数型")
print(fund_info_index_em_df)
```

数据示例

```
     基金代码            基金名称         单位净值    日期  ...   手续费  起购金额  跟踪标的   跟踪方式
0    005313        万家中证1000指数增强A  1.3029  2022-07-26  ...  0.15   10元  沪深指数  增强指数型
1    005314        万家中证1000指数增强C  1.2988  2022-07-26  ...  0.00   10元  沪深指数  增强指数型
2    004194        招商中证1000指数增强A  1.5630  2022-07-26  ...  0.12   10元  沪深指数  增强指数型
3    004195        招商中证1000指数增强C  1.5469  2022-07-26  ...  0.00   10元  沪深指数  增强指数型
4    007178           浙商中华预期高股息A  1.1339  2022-07-26  ...  0.12   10元  沪深指数  增强指数型
..      ...                  ...     ...         ...  ...   ...   ...   ...    ...
275  015784      中信建投中证1000指数增强A  0.9946  2022-07-22  ...  0.15   10元  沪深指数  增强指数型
276  015785      中信建投中证1000指数增强C  0.9943  2022-07-22  ...  0.00   10元  沪深指数  增强指数型
277  015921  申万菱信国证2000指数增强型发起式A  0.9945  2022-07-22  ...  0.12   10元  沪深指数  增强指数型
278  015148        华安中证1000指数增强A  0.9999  2022-07-22  ...  0.12   10元  沪深指数  增强指数型
279  016134       嘉实沪深300指数研究增强C  0.9614  2022-07-26  ...  0.00   10元  沪深指数  增强指数型
```

### 基金申购状态

接口: fund_purchase_em

目标地址: http://fund.eastmoney.com/Fund_sgzt_bzdm.html#fcode,asc_1

描述: 东方财富网站-天天基金网-基金数据-基金申购状态

限量: 单次返回当前时刻所有历史数据

输入参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| -   | -   | -   |

输出参数

| 名称             | 类型      | 描述      |
|----------------|---------|---------|
| 序号             | object  | -       |
| 基金代码           | object  | -       |
| 基金简称           | object  | -       |
| 基金类型           | object  | -       |
| 最新净值/万份收益      | float64 | -       |
| 最新净值/万份收益-报告时间 | object  | -       |
| 申购状态           | object  | -       |
| 赎回状态           | object  | -       |
| 下一开放日          | object  | -       |
| 购买起点           | float64 | -       |
| 日累计限定金额        | float64 | -       |
| 手续费            | float64 | 注意单位: % |

接口示例

```python
import akshare as ak

fund_purchase_em_df = ak.fund_purchase_em()
print(fund_purchase_em_df)
```

数据示例

```
          序号    基金代码                基金简称  ... 购买起点       日累计限定金额   手续费
0          1  000001              华夏成长混合  ...   10  1.000000e+11  0.15
1          2  000003            中海可转债债券A  ...   10  1.000000e+10  0.08
2          3  000004            中海可转债债券C  ...   10  1.000000e+10  0.00
3          4  000005          嘉实增强信用定期债券  ...   10  1.000000e+11  0.08
4          5  000006         西部利得量化成长混合A  ...   10  2.000000e+04  0.15
      ...     ...                 ...  ...  ...           ...   ...
14955  14956  970118        东吴裕丰6个月持有债券C  ...   10  1.000000e+11  0.00
14956  14957  970119  兴证资管金麒麟恒睿致远一年持有混合A  ...    0  0.000000e+00  1.20
14957  14958  970120  兴证资管金麒麟恒睿致远一年持有混合B  ...    0  0.000000e+00   NaN
14958  14959  970121  兴证资管金麒麟恒睿致远一年持有混合C  ...    0  0.000000e+00  0.00
14959  14960  980003         太平洋六个月滚动持有债  ...  100  1.000000e+11  0.05
```

### 基金行情

#### ETF基金实时行情-东财

接口: fund_etf_spot_em

目标地址: https://quote.eastmoney.com/center/gridlist.html#fund_etf

描述: 东方财富-ETF 实时行情

限量: 单次返回所有数据

输入参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| -   | -   | -   |

输出参数

| 名称         | 类型      | 描述      |
|------------|---------|---------|
| 代码         | object  | -       |
| 名称         | object  | -       |
| 最新价        | float64 | -       |
| IOPV实时估值   | float64 | -       |
| 基金折价率      | float64 | 注意单位: % |
| 涨跌额        | float64 | -       |
| 涨跌幅        | float64 | 注意单位: % |
| 成交量        | float64 | -       |
| 成交额        | float64 | -       |
| 开盘价        | float64 | -       |
| 最高价        | float64 | -       |
| 最低价        | float64 | -       |
| 昨收         | float64 | -       |
| 换手率        | float64 | -       |
| 量比         | float64 | -       |
| 委比         | float64 | -       |
| 外盘         | float64 | -       |
| 内盘         | float64 | -       |
| 主力净流入-净额   | float64 | -       |
| 主力净流入-净占比  | float64 | -       |
| 超大单净流入-净额  | float64 | -       |
| 超大单净流入-净占比 | float64 | -       |
| 大单净流入-净额   | float64 | -       |
| 大单净流入-净占比  | float64 | -       |
| 中单净流入-净额   | float64 | -       |
| 中单净流入-净占比  | float64 | -       |
| 小单净流入-净额   | float64 | -       |
| 小单净流入-净占比  | float64 | -       |
| 现手         | float64 | -       |
| 买一         | float64 | -       |
| 卖一         | float64 | -       |
| 最新份额       | float64 | -       |
| 流通市值       | int64   | -       |
| 总市值        | int64   | -       |
| 数据日期       | object  | -       |
| 更新时间       | object  | -       |

接口示例

```python
import akshare as ak

fund_etf_spot_em_df = ak.fund_etf_spot_em()
print(fund_etf_spot_em_df)
```

数据示例

```
          代码    名称  ...       数据日期                  更新时间
0     520890  港股通红利低波ETF  ... 2024-12-26 2024-12-26 16:11:57+08:00
1     159331     红利港股ETF  ... 2024-12-26 2024-12-26 15:34:48+08:00
2     159333   港股央企红利ETF  ... 2024-12-26 2024-12-26 15:34:45+08:00
3     159583     通信设备ETF  ... 2024-12-26 2024-12-26 15:35:03+08:00
4     515880       通信ETF  ... 2024-12-26 2024-12-26 16:11:33+08:00
...      ...         ...  ...        ...                       ...
1005  561170     绿电50ETF  ... 2024-12-26 2024-12-26 16:11:52+08:00
1006  562550       绿电ETF  ... 2024-12-26 2024-12-26 16:11:39+08:00
1007  159301     公用事业ETF  ... 2024-12-26 2024-12-26 15:34:36+08:00
1008  159611       电力ETF  ... 2024-12-26 2024-12-26 15:34:12+08:00
1009  560580     电力ETF南方  ... 2024-12-26 2024-12-26 16:11:52+08:00
[1010 rows x 37 columns]
```

#### ETF基金实时行情-同花顺

接口: fund_etf_spot_ths

目标地址: https://fund.10jqka.com.cn/datacenter/jz/kfs/etf/

描述: 同花顺理财-基金数据-每日净值-ETF-实时行情

限量: 单次返回指定 date 的所有数据

输入参数

| 名称   | 类型  | 描述                   |
|------|-----|----------------------|
| date | str | date=""; 默认返回当前最新的数据 |

输出参数

| 名称       | 类型      | 描述      |
|----------|---------|---------|
| 序号       | int64   | -       |
| 基金代码     | object  | -       |
| 基金名称     | object  | -       |
| 当前-单位净值  | float64 | -       |
| 当前-累计净值  | float64 | -       |
| 前一日-单位净值 | float64 | -       |
| 前一日-累计净值 | float64 |         |
| 增长值      | float64 | -       |
| 增长率      | float64 | 注意单位: % |
| 赎回状态     | object  | -       |
| 申购状态     | object  | -       |
| 最新-交易日   | object  | -       |
| 最新-单位净值  | float64 | -       |
| 最新-累计净值  | float64 | -       |
| 基金类型     | object  | -       |
| 查询日期     | object  | -       |

接口示例

```python
import akshare as ak

fund_etf_spot_ths_df = ak.fund_etf_spot_ths(date="20240620")
print(fund_etf_spot_ths_df)
```

数据示例

```
      序号    基金代码         基金名称  ...  最新-累计净值  基金类型    查询日期
0      1  159691            高股息ETF港股  ...   1.2039   股票型  2024-06-20
1      2  588200        嘉实上证科创板芯片ETF  ...   1.0000   股票型  2024-06-20
2      3  588290        华安上证科创板芯片ETF  ...   0.9775   股票型  2024-06-20
3      4  588890        南方上证科创板芯片ETF  ...   1.1194   股票型  2024-06-20
4      5  513530                港股红利  ...   1.3692   股票型  2024-06-20
..   ...     ...                 ...  ...      ...   ...         ...
979  980  560360         万家中证软件服务ETF  ...   1.0000   股票型  2024-06-20
980  981  588500  易方达上证科创板100增强策略ETF  ...      NaN   股票型  2024-06-20
981  982  588680   广发上证科创板100增强策略ETF  ...      NaN   股票型  2024-06-20
982  983  588860             科创医药ETF  ...      NaN   股票型  2024-06-20
983  984  588990        博时上证科创板芯片ETF  ...      NaN   股票型  2024-06-20
[984 rows x 16 columns]
```

#### LOF基金实时行情-东财

接口: fund_lof_spot_em

目标地址: https://quote.eastmoney.com/center/gridlist.html#fund_lof

描述: 东方财富-LOF 实时行情

限量: 单次返回所有数据

输入参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| -   | -   | -   |

输出参数

| 名称   | 类型      | 描述      |
|------|---------|---------|
| 代码   | object  | -       |
| 名称   | object  | -       |
| 最新价  | float64 | -       |
| 涨跌额  | float64 | -       |
| 涨跌幅  | float64 | 注意单位: % |
| 成交量  | float64 | -       |
| 成交额  | float64 | -       |
| 开盘价  | float64 | -       |
| 最高价  | float64 | -       |
| 最低价  | float64 | -       |
| 昨收   | float64 | -       |
| 换手率  | float64 | -       |
| 流通市值 | int64   | -       |
| 总市值  | int64   | -       |

接口示例

```python
import akshare as ak

fund_lof_spot_em_df = ak.fund_lof_spot_em()
print(fund_lof_spot_em_df)
```

数据示例

```
      代码       名称    最新价    涨跌额  ...     昨收  换手率  流通市值        总市值
0    166009     中欧动力LOF  3.123  0.114  ...  3.009  0.64    6716805    6716805
1    161811  沪深300LOF银华  0.859  0.029  ...  0.830  0.04    7986026    7986026
2    160807  长盛沪深300LOF  1.454  0.033  ...  1.421  0.56    3979752    3979752
3    165520       有色LOF  1.515  0.034  ...  1.481  0.30   17217774   17217774
4    161217     国投资源LOF  1.238  0.027  ...  1.211  0.07    5947481    5947481
..      ...         ...    ...    ...  ...    ...   ...        ...        ...
146  501083     科创银华LOF  1.336 -0.024  ...  1.360  0.32  124253745  124253745
147  160512     博时卓越LOF  2.465 -0.045  ...  2.510  0.00   35852612   35852612
148  160621     鹏华丰和LOF  1.421 -0.029  ...  1.450  0.41    2367956    2367956
149  160215     国泰价值LOF  2.341 -0.053  ...  2.394  0.07    7602067    7602067
150  165515    信诚300LOF  1.048 -0.054  ...  1.102  0.07   37943012   37943012
[151 rows x 14 columns]
```

#### 基金实时行情-新浪

接口: fund_etf_category_sina

目标地址: http://vip.stock.finance.sina.com.cn/fund_center/index.html#jjhqetf

描述: 新浪财经-基金列表及行情数据

限量: 单次返回指定 symbol 基金的所有数据

输入参数

| 名称     | 类型  | 描述                                                    |
|--------|-----|-------------------------------------------------------|
| symbol | str | symbol="LOF基金"; choice of {"封闭式基金", "ETF基金", "LOF基金"} |

输出参数

| 名称  | 类型      | 描述      |
|-----|---------|---------|
| 代码  | object  | -       |
| 名称  | object  | -       |
| 最新价 | float64 | -       |
| 涨跌额 | float64 | -       |
| 涨跌幅 | float64 | 注意单位: % |
| 买入  | float64 | -       |
| 卖出  | float64 | -       |
| 昨收  | float64 | -       |
| 今开  | float64 | -       |
| 最高  | float64 | -       |
| 最低  | float64 | -       |
| 成交量 | int64   | 注意单位: 股 |
| 成交额 | int64   | 注意单位: 元 |

接口示例

```python
import akshare as ak

fund_etf_category_sina_df = ak.fund_etf_category_sina(symbol="封闭式基金")
print(fund_etf_category_sina_df)
```

数据示例

```
       代码      名称     最新价    涨跌额  ...  最高      最低    成交量     成交额
0   sz180801    首钢绿能  12.520  0.008  ...  12.530  12.475   839446  10510150
1   sz180701    绍兴原水   3.413  0.001  ...   3.425   3.385  2764883   9437963
2   sz180603     大悦城   3.398 -0.006  ...   3.420   3.350   425614   1447551
3   sz180602    中金印力   3.200 -0.005  ...   3.205   3.192  1477800   4729717
4   sz180601    华润商业   7.347  0.001  ...   7.388   7.347   521600   3834548
5   sz180502    蛇口租房   3.060  0.030  ...   3.062   3.010  1079728   3287126
6   sz180501    红土安居   2.825 -0.001  ...   2.829   2.815  1075226   3036275
7   sz180401    鹏华深能   6.018 -0.013  ...   6.053   6.010  1696744  10231735
8   sz180303  宝湾REIT   3.712 -0.035  ...   3.763   3.702   408619   1519206
9   sz180302     深国际   2.347 -0.005  ...   2.355   2.342   592300   1391501
10  sz180301  盐港REIT   1.995 -0.009  ...   2.004   1.995  3516392   7024407
11  sz180203    招商高速   6.950 -0.030  ...   6.979   6.908   104832    728293
12  sz180202    越秀高速   6.280 -0.037  ...   6.319   6.279   602846   3794019
13  sz180201    广州广河   9.296  0.041  ...   9.313   9.255   790604   7344115
14  sz180105  广开REIT   2.340 -0.013  ...   2.356   2.340   154500    362621
15  sz180103    和达高科   2.252 -0.013  ...   2.280   2.251  2045315   4616908
16  sz180102    合肥高新   1.850 -0.010  ...   1.863   1.846  4180592   7751961
17  sz180101    蛇口产园   1.992 -0.008  ...   2.004   1.990  1864202   3718024
18  sh508099     中关村   2.095 -0.017  ...   2.120   2.089  1662042   3487014
19  sh508098    京东仓储   2.609 -0.003  ...   2.624   2.608  3107261   8117486
20  sh508096    京能光伏  10.070  0.061  ...  10.070  10.009   916226   9187077
21  sh508089  特变REIT   4.905  0.034  ...   4.905   4.852   414200   2023350
22  sh508088  东久REIT   3.290 -0.030  ...   3.339   3.260  7251364  23935638
23  sh508086    河北高速   5.425 -0.007  ...   5.444   5.416   460352   2498931
24  sh508077    华润有巢   2.567 -0.023  ...   2.600   2.560  3447426   8891771
25  sh508069    南京交通   5.715 -0.005  ...   5.835   5.700   436500   2498995
26  sh508068  京保REIT   3.151  0.003  ...   3.155   3.138  3164947   9956753
27  sh508066  苏交REIT   6.088  0.093  ...   6.091   5.988  2878543  17329774
28  sh508058    厦门安居   3.237  0.015  ...   3.266   3.208  1717570   5547566
29  sh508056     普洛斯   3.304 -0.014  ...   3.330   3.296  3530115  11676209
30  sh508033  深高REIT   6.449  0.004  ...   6.462   6.449   121205    782249
31  sh508031    城投宽庭   3.388  0.015  ...   3.400   3.366   900154   3047776
32  sh508028    国家电投  10.054  0.001  ...  10.078  10.035  2473268  24877774
33  sh508027    东吴苏园   3.301 -0.032  ...   3.348   3.301  3575937  11922619
34  sh508026    电建清源   3.522 -0.032  ...   3.586   3.503  1333100   4715147
35  sh508022    津开产园   2.469  0.001  ...   2.477   2.436   175933    434868
36  sh508021  临港REIT   4.265 -0.094  ...   4.359   4.251  3999528  17192524
37  sh508019    湖北科投   2.255 -0.004  ...   2.274   2.240  3526346   7958065
38  sh508018  中交REIT   4.976 -0.014  ...   4.996   4.975  4906984  24480193
39  sh508017    金茂商业   2.592 -0.017  ...   2.615   2.576   363552    943842
40  sh508015  明阳REIT   7.694  0.080  ...   7.750   7.620   220000   1689001
41  sh508011    物美消费   2.721  0.011  ...   2.723   2.710  1004698   2731640
42  sh508009    安徽交控   6.901  0.003  ...   6.907   6.882  1251593   8633658
43  sh508008  铁建REIT   8.369 -0.002  ...   8.378   8.333  1158391   9686968
44  sh508007    山东高速   7.490 -0.055  ...   7.553   7.490  1484920  11176058
45  sh508006    首创水务   3.723  0.002  ...   3.744   3.700  3058890  11380469
46  sh508005    首创奥莱   2.695  0.009  ...   2.695   2.630   360381    967838
47  sh508003    中金联东   3.227 -0.005  ...   3.230   3.202    20226     65206
48  sh508002    百联消费   2.219 -0.006  ...   2.230   2.215   857272   1904408
49  sh508001    浙江杭徽   7.123 -0.021  ...   7.144   7.123  2369443  16895672
50  sh508000  张江REIT   2.526 -0.009  ...   2.531   2.519  1483719   3742364
[51 rows x 13 columns]
```

#### ETF基金分时行情-东财

接口: fund_etf_hist_min_em

目标地址: https://quote.eastmoney.com/sz159707.html

描述: 东方财富-ETF 分时行情; 该接口只能获取近期的分时数据，注意时间周期的设置

限量: 单次返回指定 ETF、频率、复权调整和时间区间的分时数据, 其中 1 分钟数据只返回近 5 个交易日数据且不复权

输入参数

| 名称         | 类型  | 描述                                                                                                  |
|------------|-----|-----------------------------------------------------------------------------------------------------|
| symbol     | str | symbol='513500'; ETF 代码可以在 **ak.fund_etf_spot_em()** 中获取                                            |
| start_date | str | start_date="1979-09-01 09:32:00"; 日期时间; 默认返回所有数据                                                    |
| end_date   | str | end_date="2222-01-01 09:32:00"; 日期时间; 默认返回所有数据                                                      |
| period     | str | period='5'; choice of {'1', '5', '15', '30', '60'}; 其中 1 分钟数据返回近 5 个交易日数据且不复权                       |
| adjust     | str | adjust=''; choice of {'', 'qfq', 'hfq'}; '': 不复权, 'qfq': 前复权, 'hfq': 后复权, 其中 1 分钟数据返回近 5 个交易日数据且不复权 |

输出参数-1分钟数据

| 名称  | 类型      | 描述  |
|-----|---------|-----|
| 时间  | object  | -   |
| 开盘  | float64 | -   |
| 收盘  | float64 | -   |
| 最高  | float64 | -   |
| 最低  | float64 | -   |
| 成交量 | float64 | -   |
| 成交额 | float64 | -   |
| 均价  | float64 | -   |

接口示例-1分钟数据

```python
import akshare as ak

fund_etf_hist_min_em_df = ak.fund_etf_hist_min_em(symbol="511220", period="1", adjust="", start_date="2024-03-20 09:30:00", end_date="2024-03-20 17:40:00")
print(fund_etf_hist_min_em_df)
```

数据示例-1分钟数据

```
              时间         开盘      收盘  ...   成交量   成交额       均价
0    2024-03-20 09:30:00  10.191  10.191  ...     0        0.0  10.1910
1    2024-03-20 09:31:00  10.191  10.192  ...   413   420929.0  10.1920
2    2024-03-20 09:32:00  10.192  10.191  ...    59    60127.0  10.1919
3    2024-03-20 09:33:00  10.191  10.191  ...   123   125360.0  10.1919
4    2024-03-20 09:34:00  10.191  10.192  ...    37    37711.0  10.1919
..                   ...     ...     ...  ...   ...        ...      ...
236  2024-03-20 14:56:00  10.190  10.190  ...     9     9171.0  10.1901
237  2024-03-20 14:57:00  10.190  10.190  ...     0        0.0  10.1901
238  2024-03-20 14:58:00  10.191  10.190  ...    58    59103.0  10.1901
239  2024-03-20 14:59:00  10.190  10.191  ...   109   111082.0  10.1901
240  2024-03-20 15:00:00  10.191  10.190  ...  3953  4028111.0  10.1901
[241 rows x 8 columns]
```

输出参数-其他

| 名称  | 类型      | 描述      |
|-----|---------|---------|
| 时间  | object  | -       |
| 开盘  | float64 | -       |
| 收盘  | float64 | -       |
| 最高  | float64 | -       |
| 最低  | float64 | -       |
| 涨跌幅 | float64 | 注意单位: % |
| 涨跌额 | float64 | -       |
| 成交量 | float64 | -       |
| 成交额 | float64 | -       |
| 振幅  | float64 | 注意单位: % |
| 换手率 | float64 | 注意单位: % |

接口示例-其他

```python
import akshare as ak

fund_etf_hist_min_em_df = ak.fund_etf_hist_min_em(symbol="513500", period="5", adjust="hfq", start_date="2023-12-11 09:32:00", end_date="2023-12-11 17:40:00")
print(fund_etf_hist_min_em_df)
```

数据示例-其他

```
         时间     开盘     收盘     最高  ...     成交量         成交额    振幅   换手率
0   2023-12-11 09:35:00  3.114  3.114  3.116  ...   49422   7694560.0  0.13  0.10
1   2023-12-11 09:40:00  3.112  3.116  3.116  ...   11297   1758943.0  0.13  0.02
2   2023-12-11 09:45:00  3.116  3.114  3.116  ...   10528   1639996.0  0.06  0.02
3   2023-12-11 09:50:00  3.116  3.114  3.116  ...   14457   2252082.0  0.06  0.03
4   2023-12-11 09:55:00  3.116  3.116  3.116  ...   31021   4832213.0  0.06  0.06
5   2023-12-11 10:00:00  3.116  3.116  3.116  ...   15096   2351804.0  0.06  0.03
6   2023-12-11 10:05:00  3.116  3.116  3.116  ...   28473   4436031.0  0.06  0.06
7   2023-12-11 10:10:00  3.116  3.116  3.116  ...   21966   3422245.0  0.06  0.04
8   2023-12-11 10:15:00  3.118  3.118  3.118  ...    4083    636297.0  0.06  0.01
9   2023-12-11 10:20:00  3.118  3.118  3.118  ...    3527    549824.0  0.06  0.01
10  2023-12-11 10:25:00  3.118  3.118  3.118  ...    4559    710690.0  0.06  0.01
11  2023-12-11 10:30:00  3.118  3.118  3.118  ...    9277   1445434.0  0.06  0.02
12  2023-12-11 10:35:00  3.118  3.116  3.118  ...   24972   3891409.0  0.06  0.05
13  2023-12-11 10:40:00  3.118  3.118  3.118  ...    2117    329945.0  0.06  0.00
14  2023-12-11 10:45:00  3.118  3.116  3.118  ...   10884   1696677.0  0.06  0.02
15  2023-12-11 10:50:00  3.118  3.116  3.118  ...    1601    249564.0  0.06  0.00
16  2023-12-11 10:55:00  3.118  3.116  3.118  ...    9538   1486933.0  0.06  0.02
17  2023-12-11 11:00:00  3.118  3.118  3.118  ...    4282    667478.0  0.06  0.01
18  2023-12-11 11:05:00  3.118  3.118  3.118  ...   11936   1860818.0  0.06  0.02
19  2023-12-11 11:10:00  3.118  3.116  3.118  ...    3570    556521.0  0.06  0.01
20  2023-12-11 11:15:00  3.118  3.118  3.118  ...    2789    434766.0  0.06  0.01
21  2023-12-11 11:20:00  3.118  3.118  3.118  ...    2315    360889.0  0.06  0.00
22  2023-12-11 11:25:00  3.116  3.118  3.118  ...   21950   3421895.0  0.06  0.04
23  2023-12-11 11:30:00  3.118  3.118  3.118  ...    3537    551374.0  0.06  0.01
24  2023-12-11 13:05:00  3.118  3.116  3.118  ...   21913   3414971.0  0.06  0.04
25  2023-12-11 13:10:00  3.118  3.118  3.118  ...   18369   2863406.0  0.06  0.04
26  2023-12-11 13:15:00  3.118  3.116  3.118  ...   54827   8546798.0  0.06  0.11
27  2023-12-11 13:20:00  3.118  3.118  3.118  ...    2570    400640.0  0.06  0.01
28  2023-12-11 13:25:00  3.118  3.118  3.118  ...    2601    405414.0  0.06  0.01
29  2023-12-11 13:30:00  3.118  3.118  3.118  ...    3033    472734.0  0.06  0.01
30  2023-12-11 13:35:00  3.118  3.116  3.118  ...   15563   2426052.0  0.06  0.03
31  2023-12-11 13:40:00  3.118  3.116  3.118  ...   13824   2155085.0  0.06  0.03
32  2023-12-11 13:45:00  3.118  3.118  3.118  ...   16696   2602092.0  0.06  0.03
33  2023-12-11 13:50:00  3.118  3.118  3.118  ...    2991    466255.0  0.06  0.01
34  2023-12-11 13:55:00  3.118  3.120  3.120  ...   37419   5833886.0  0.06  0.07
35  2023-12-11 14:00:00  3.120  3.122  3.122  ...   34011   5305927.0  0.13  0.07
36  2023-12-11 14:05:00  3.120  3.122  3.124  ...  100028  15612481.0  0.13  0.20
37  2023-12-11 14:10:00  3.124  3.124  3.124  ...    4448    694602.0  0.06  0.01
38  2023-12-11 14:15:00  3.124  3.120  3.124  ...   20700   3231425.0  0.13  0.04
39  2023-12-11 14:20:00  3.122  3.122  3.122  ...   28460   4440057.0  0.06  0.06
40  2023-12-11 14:25:00  3.120  3.120  3.122  ...   40567   6330234.0  0.06  0.08
41  2023-12-11 14:30:00  3.120  3.120  3.122  ...   38468   5999601.0  0.13  0.08
42  2023-12-11 14:35:00  3.118  3.118  3.120  ...   15520   2420175.0  0.06  0.03
43  2023-12-11 14:40:00  3.120  3.120  3.120  ...    7381   1151356.0  0.06  0.01
44  2023-12-11 14:45:00  3.120  3.120  3.120  ...   27514   4289666.0  0.06  0.05
45  2023-12-11 14:50:00  3.120  3.118  3.120  ...   16476   2568813.0  0.06  0.03
46  2023-12-11 14:55:00  3.116  3.118  3.118  ...   10180   1586741.0  0.06  0.02
47  2023-12-11 15:00:00  3.118  3.118  3.120  ...   32074   5000284.0  0.13  0.06
[48 rows x 11 columns]
```

#### LOF基金分时行情-东财

接口: fund_lof_hist_min_em

目标地址: https://quote.eastmoney.com/sz166009.html

描述: 东方财富-LOF 分时行情; 该接口只能获取近期的分时数据，注意时间周期的设置

限量: 单次返回指定 LOF、频率、复权调整和时间区间的分时数据, 其中 1 分钟数据只返回近 5 个交易日数据且不复权

输入参数

| 名称         | 类型  | 描述                                                                                                  |
|------------|-----|-----------------------------------------------------------------------------------------------------|
| symbol     | str | symbol='166009'; LOF 代码可以在 **ak.fund_lof_spot_em()** 中获取                                            |
| start_date | str | start_date="1979-09-01 09:32:00"; 日期时间; 默认返回所有数据                                                    |
| end_date   | str | end_date="2222-01-01 09:32:00"; 日期时间; 默认返回所有数据                                                      |
| period     | str | period='5'; choice of {'1', '5', '15', '30', '60'}; 其中 1 分钟数据返回近 5 个交易日数据且不复权                       |
| adjust     | str | adjust=''; choice of {'', 'qfq', 'hfq'}; '': 不复权, 'qfq': 前复权, 'hfq': 后复权, 其中 1 分钟数据返回近 5 个交易日数据且不复权 |

输出参数-1分钟数据

| 名称  | 类型      | 描述  |
|-----|---------|-----|
| 时间  | object  | -   |
| 开盘  | float64 | -   |
| 收盘  | float64 | -   |
| 最高  | float64 | -   |
| 最低  | float64 | -   |
| 成交量 | float64 | -   |
| 成交额 | float64 | -   |
| 均价  | float64 | -   |

接口示例-1分钟数据

```python
import akshare as ak

fund_lof_hist_min_em_df = ak.fund_lof_hist_min_em(symbol="166009", period="1", adjust="", start_date="2024-03-20 09:30:00", end_date="2024-03-20 14:40:00")
print(fund_lof_hist_min_em_df)
```

数据示例-1分钟数据

```
                 时间     开盘    收盘    最高   最低  成交量 成交额  均价
0    2024-03-20 09:30:00  2.627  2.627  2.627  2.627    0  0.0  2.627
1    2024-03-20 09:31:00  2.627  2.627  2.627  2.627    0  0.0  2.627
2    2024-03-20 09:32:00  2.627  2.627  2.627  2.627    0  0.0  2.627
3    2024-03-20 09:33:00  2.627  2.627  2.627  2.627    0  0.0  2.627
4    2024-03-20 09:34:00  2.627  2.627  2.627  2.627    0  0.0  2.627
..                   ...    ...    ...    ...    ...  ...  ...    ...
216  2024-03-20 14:36:00  2.563  2.563  2.563  2.563    0  0.0  2.563
217  2024-03-20 14:37:00  2.563  2.563  2.563  2.563    0  0.0  2.563
218  2024-03-20 14:38:00  2.563  2.563  2.563  2.563    0  0.0  2.563
219  2024-03-20 14:39:00  2.563  2.563  2.563  2.563    0  0.0  2.563
220  2024-03-20 14:40:00  2.563  2.563  2.563  2.563    0  0.0  2.563
[221 rows x 8 columns]
```

输出参数-其他

| 名称  | 类型      | 描述      |
|-----|---------|---------|
| 时间  | object  | -       |
| 开盘  | float64 | -       |
| 收盘  | float64 | -       |
| 最高  | float64 | -       |
| 最低  | float64 | -       |
| 涨跌幅 | float64 | 注意单位: % |
| 涨跌额 | float64 | -       |
| 成交量 | float64 | -       |
| 成交额 | float64 | -       |
| 振幅  | float64 | 注意单位: % |
| 换手率 | float64 | 注意单位: % |

接口示例-其他

```python
import akshare as ak

fund_lof_hist_min_em_df = ak.fund_lof_hist_min_em(symbol="166009", period="5", adjust="hfq", start_date="2023-07-01 09:32:00", end_date="2023-07-04 14:40:00")
print(fund_lof_hist_min_em_df)
```

数据示例-其他

```
         时间     开盘     收盘     最高  ...  成交量      成交额    振幅   换手率
0   2023-07-03 09:35:00  3.996  3.996  3.996  ...    2    590.0  0.00  0.01
1   2023-07-03 09:40:00  3.996  3.996  3.996  ...    0      0.0  0.00  0.00
2   2023-07-03 09:45:00  3.996  3.996  3.996  ...    0      0.0  0.00  0.00
3   2023-07-03 09:50:00  3.996  4.271  4.271  ...    1    322.0  6.88  0.00
4   2023-07-03 09:55:00  4.271  4.060  4.271  ...    9   2715.0  4.94  0.04
5   2023-07-03 10:00:00  4.060  4.062  4.062  ...    1    302.0  0.05  0.00
6   2023-07-03 10:05:00  4.062  4.060  4.062  ...   91  27463.0  0.05  0.42
7   2023-07-03 10:10:00  4.060  4.060  4.060  ...    0      0.0  0.00  0.00
8   2023-07-03 10:15:00  4.060  4.060  4.060  ...    0      0.0  0.00  0.00
9   2023-07-03 10:20:00  4.060  4.060  4.060  ...    0      0.0  0.00  0.00
10  2023-07-03 10:25:00  4.060  4.060  4.060  ...    0      0.0  0.00  0.00
11  2023-07-03 10:30:00  4.060  4.060  4.060  ...    0      0.0  0.00  0.00
12  2023-07-03 10:35:00  4.060  4.060  4.060  ...    0      0.0  0.00  0.00
13  2023-07-03 10:40:00  4.060  4.060  4.060  ...    0      0.0  0.00  0.00
14  2023-07-03 10:45:00  4.060  4.060  4.060  ...    9   2716.0  0.00  0.04
15  2023-07-03 10:50:00  4.060  4.060  4.060  ...    0      0.0  0.00  0.00
16  2023-07-03 10:55:00  4.060  4.060  4.060  ...    0      0.0  0.00  0.00
17  2023-07-03 11:00:00  4.060  4.060  4.060  ...    0      0.0  0.00  0.00
18  2023-07-03 11:05:00  4.060  4.060  4.060  ...    0      0.0  0.00  0.00
19  2023-07-03 11:10:00  4.060  4.242  4.242  ...    1    320.0  4.48  0.00
20  2023-07-03 11:15:00  4.242  4.242  4.242  ...    0      0.0  0.00  0.00
21  2023-07-03 11:20:00  4.242  4.067  4.242  ...   22   6655.0  4.13  0.10
22  2023-07-03 11:25:00  4.067  4.067  4.067  ...    0      0.0  0.00  0.00
23  2023-07-03 11:30:00  4.067  4.067  4.067  ...    0      0.0  0.00  0.00
24  2023-07-03 13:05:00  4.067  4.067  4.067  ...    0      0.0  0.00  0.00
25  2023-07-03 13:10:00  4.067  4.067  4.067  ...    0      0.0  0.00  0.00
26  2023-07-03 13:15:00  4.067  4.067  4.067  ...    0      0.0  0.00  0.00
27  2023-07-03 13:20:00  4.067  4.067  4.067  ...    0      0.0  0.00  0.00
28  2023-07-03 13:25:00  4.067  4.067  4.067  ...    0      0.0  0.00  0.00
29  2023-07-03 13:30:00  4.067  4.067  4.067  ...    0      0.0  0.00  0.00
30  2023-07-03 13:35:00  4.067  4.067  4.067  ...    0      0.0  0.00  0.00
31  2023-07-03 13:40:00  4.067  4.067  4.067  ...    0      0.0  0.00  0.00
32  2023-07-03 13:45:00  4.067  4.067  4.067  ...    0      0.0  0.00  0.00
33  2023-07-03 13:50:00  4.067  4.067  4.067  ...    0      0.0  0.00  0.00
34  2023-07-03 13:55:00  4.067  4.067  4.067  ...    0      0.0  0.00  0.00
35  2023-07-03 14:00:00  4.067  4.067  4.067  ...    0      0.0  0.00  0.00
36  2023-07-03 14:05:00  4.067  4.067  4.067  ...    0      0.0  0.00  0.00
37  2023-07-03 14:10:00  4.067  4.067  4.067  ...    0      0.0  0.00  0.00
38  2023-07-03 14:15:00  4.067  4.067  4.067  ...    0      0.0  0.00  0.00
39  2023-07-03 14:20:00  4.067  4.067  4.067  ...    0      0.0  0.00  0.00
40  2023-07-03 14:25:00  4.067  4.067  4.067  ...    0      0.0  0.00  0.00
41  2023-07-03 14:30:00  4.067  4.067  4.067  ...    0      0.0  0.00  0.00
42  2023-07-03 14:35:00  4.067  4.067  4.067  ...    0      0.0  0.00  0.00
43  2023-07-03 14:40:00  4.067  4.067  4.067  ...    0      0.0  0.00  0.00
44  2023-07-03 14:45:00  4.067  4.067  4.067  ...    0      0.0  0.00  0.00
45  2023-07-03 14:50:00  4.067  4.067  4.067  ...    0      0.0  0.00  0.00
46  2023-07-03 14:55:00  4.067  4.067  4.067  ...    0      0.0  0.00  0.00
47  2023-07-03 15:00:00  4.067  4.165  4.165  ...    1    312.0  2.41  0.00
[48 rows x 11 columns]
```

#### ETF基金历史行情-东财

接口: fund_etf_hist_em

目标地址: http://quote.eastmoney.com/sz159707.html

描述: 东方财富-ETF 行情; 历史数据按日频率更新, 当日收盘价请在收盘后获取

限量: 单次返回指定 ETF、指定周期和指定日期间的历史行情日频率数据

输入参数

| 名称         | 类型  | 描述                                                              |
|------------|-----|-----------------------------------------------------------------|
| symbol     | str | symbol='159707'; ETF 代码可以在 **ak.fund_etf_spot_em()** 中获取或查看东财主页 |
| period     | str | period='daily'; choice of {'daily', 'weekly', 'monthly'}        |
| start_date | str | start_date='20000101'; 开始查询的日期                                  |
| end_date   | str | end_date='20230104'; 结束查询的日期                                    |
| adjust     | str | 默认返回不复权的数据; qfq: 返回前复权后的数据; hfq: 返回后复权后的数据                      |

**数据复权**

1.为何要复权：由于股票存在配股、分拆、合并和发放股息等事件，会导致股价出现较大的缺口。
若使用不复权的价格处理数据、计算各种指标，将会导致它们失去连续性，且使用不复权价格计算收益也会出现错误。
为了保证数据连贯性，常通过前复权和后复权对价格序列进行调整。

2.前复权：保持当前价格不变，将历史价格进行增减，从而使股价连续。
前复权用来看盘非常方便，能一眼看出股价的历史走势，叠加各种技术指标也比较顺畅，是各种行情软件默认的复权方式。
这种方法虽然很常见，但也有两个缺陷需要注意。

2.1 为了保证当前价格不变，每次股票除权除息，均需要重新调整历史价格，因此其历史价格是时变的。
这会导致在不同时点看到的历史前复权价可能出现差异。

2.2 对于有持续分红的公司来说，前复权价可能出现负值。

3.后复权：保证历史价格不变，在每次股票权益事件发生后，调整当前的股票价格。
后复权价格和真实股票价格可能差别较大，不适合用来看盘。
其优点在于，可以被看作投资者的长期财富增长曲线，反映投资者的真实收益率情况。

4.在量化投资研究中普遍采用后复权数据。

输出参数

| 名称  | 类型      | 描述  |
|-----|---------|-----|
| 日期  | object  | -   |
| 开盘  | float64 | -   |
| 收盘  | float64 | -   |
| 最高  | float64 | -   |
| 最低  | float64 | -   |
| 成交量 | int64   | -   |
| 成交额 | float64 | -   |
| 振幅  | float64 | -   |
| 涨跌幅 | float64 | -   |
| 涨跌额 | float64 | -   |
| 换手率 | float64 | -   |

接口示例-不复权

```python
import akshare as ak

fund_etf_hist_em_df = ak.fund_etf_hist_em(symbol="513500", period="daily", start_date="20000101", end_date="20230201", adjust="")
print(fund_etf_hist_em_df)
```

数据示例-不复权

```
       日期     开盘     收盘     最高  ...    振幅   涨跌幅    涨跌额   换手率
0     2014-01-15  0.994  0.986  0.996  ...  0.00  0.00  0.000  1.88
1     2014-01-16  0.988  0.991  0.994  ...  0.61  0.51  0.005  0.56
2     2014-01-17  0.993  0.987  0.993  ...  0.81 -0.40 -0.004  0.24
3     2014-01-20  0.983  0.982  0.988  ...  0.61 -0.51 -0.005  0.20
4     2014-01-21  0.982  0.984  0.996  ...  2.65  0.20  0.002  0.31
          ...    ...    ...    ...  ...   ...   ...    ...   ...
2179  2022-12-28  1.260  1.260  1.262  ...  0.31 -1.10 -0.014  2.73
2180  2022-12-29  1.245  1.244  1.248  ...  0.48 -1.27 -0.016  2.91
2181  2022-12-30  1.261  1.255  1.263  ...  0.64  0.88  0.011  2.28
2182  2023-01-03  1.250  1.250  1.252  ...  0.48 -0.40 -0.005  4.49
2183  2023-01-04  1.247  1.245  1.249  ...  0.40 -0.40 -0.005  1.81
```

接口示例-前复权

```python
import akshare as ak

fund_etf_hist_em_df = ak.fund_etf_hist_em(symbol="513500", period="daily", start_date="20000101", end_date="20230201", adjust="qfq")
print(fund_etf_hist_em_df)
```

数据示例-前复权

```
      日期     开盘     收盘     最高  ...    振幅   涨跌幅    涨跌额   换手率
0     2014-01-15  0.497  0.493  0.498  ...  0.00  0.00  0.000  1.88
1     2014-01-16  0.494  0.496  0.497  ...  0.61  0.61  0.003  0.56
2     2014-01-17  0.497  0.494  0.497  ...  0.81 -0.40 -0.002  0.24
3     2014-01-20  0.492  0.491  0.494  ...  0.61 -0.61 -0.003  0.20
4     2014-01-21  0.491  0.492  0.498  ...  2.65  0.20  0.001  0.31
          ...    ...    ...    ...  ...   ...   ...    ...   ...
2179  2022-12-28  1.260  1.260  1.262  ...  0.31 -1.10 -0.014  2.73
2180  2022-12-29  1.245  1.244  1.248  ...  0.48 -1.27 -0.016  2.91
2181  2022-12-30  1.261  1.255  1.263  ...  0.64  0.88  0.011  2.28
2182  2023-01-03  1.250  1.250  1.252  ...  0.48 -0.40 -0.005  4.49
2183  2023-01-04  1.247  1.245  1.249  ...  0.40 -0.40 -0.005  1.85
```

接口示例-后复权

```python
import akshare as ak

fund_etf_hist_em_df = ak.fund_etf_hist_em(symbol="513500", period="daily", start_date="20000101", end_date="20230201", adjust="hfq")
print(fund_etf_hist_em_df)
```

数据示例-后复权

```
       日期     开盘     收盘     最高  ...    振幅   涨跌幅    涨跌额   换手率
0     2014-01-15  0.994  0.986  0.996  ...  0.00  0.00  0.000  1.88
1     2014-01-16  0.988  0.991  0.994  ...  0.61  0.51  0.005  0.56
2     2014-01-17  0.993  0.987  0.993  ...  0.81 -0.40 -0.004  0.24
3     2014-01-20  0.983  0.982  0.988  ...  0.61 -0.51 -0.005  0.20
4     2014-01-21  0.982  0.984  0.996  ...  2.65  0.20  0.002  0.31
          ...    ...    ...    ...  ...   ...   ...    ...   ...
2179  2022-12-28  2.520  2.520  2.524  ...  0.31 -1.10 -0.028  2.73
2180  2022-12-29  2.490  2.488  2.496  ...  0.48 -1.27 -0.032  2.91
2181  2022-12-30  2.522  2.510  2.526  ...  0.64  0.88  0.022  2.28
2182  2023-01-03  2.500  2.500  2.504  ...  0.48 -0.40 -0.010  4.49
2183  2023-01-04  2.494  2.490  2.498  ...  0.40 -0.40 -0.010  1.86
```

#### LOF基金历史行情-东财

接口: fund_lof_hist_em

目标地址: https://quote.eastmoney.com/sz166009.html

描述: 东方财富-LOF 行情; 历史数据按日频率更新, 当日收盘价请在收盘后获取

限量: 单次返回指定 LOF、指定周期和指定日期间的历史行情日频率数据

输入参数

| 名称         | 类型  | 描述                                                       |
|------------|-----|----------------------------------------------------------|
| symbol     | str | symbol='166009'; LOF 代码可以在 **ak.fund_lof_spot_em()** 中获取 |
| period     | str | period='daily'; choice of {'daily', 'weekly', 'monthly'} |
| start_date | str | start_date='20000101'; 开始查询的日期                           |
| end_date   | str | end_date='20230104'; 结束查询的日期                             |
| adjust     | str | 默认返回不复权的数据; qfq: 返回前复权后的数据; hfq: 返回后复权后的数据               |

**数据复权**

1.为何要复权：由于股票存在配股、分拆、合并和发放股息等事件，会导致股价出现较大的缺口。
若使用不复权的价格处理数据、计算各种指标，将会导致它们失去连续性，且使用不复权价格计算收益也会出现错误。
为了保证数据连贯性，常通过前复权和后复权对价格序列进行调整。

2.前复权：保持当前价格不变，将历史价格进行增减，从而使股价连续。
前复权用来看盘非常方便，能一眼看出股价的历史走势，叠加各种技术指标也比较顺畅，是各种行情软件默认的复权方式。
这种方法虽然很常见，但也有两个缺陷需要注意。

2.1 为了保证当前价格不变，每次股票除权除息，均需要重新调整历史价格，因此其历史价格是时变的。
这会导致在不同时点看到的历史前复权价可能出现差异。

2.2 对于有持续分红的公司来说，前复权价可能出现负值。

3.后复权：保证历史价格不变，在每次股票权益事件发生后，调整当前的股票价格。
后复权价格和真实股票价格可能差别较大，不适合用来看盘。
其优点在于，可以被看作投资者的长期财富增长曲线，反映投资者的真实收益率情况。

4.在量化投资研究中普遍采用后复权数据。

输出参数

| 名称  | 类型      | 描述  |
|-----|---------|-----|
| 日期  | object  | -   |
| 开盘  | float64 | -   |
| 收盘  | float64 | -   |
| 最高  | float64 | -   |
| 最低  | float64 | -   |
| 成交量 | int64   | -   |
| 成交额 | float64 | -   |
| 振幅  | float64 | -   |
| 涨跌幅 | float64 | -   |
| 涨跌额 | float64 | -   |
| 换手率 | float64 | -   |

接口示例-不复权

```python
import akshare as ak

fund_lof_hist_em_df = ak.fund_lof_hist_em(symbol="166009", period="daily", start_date="20000101", end_date="20230703", adjust="")
print(fund_lof_hist_em_df)
```

数据示例-不复权

```
      日期     开盘  收盘     最高  ...    振幅   涨跌幅    涨跌额     换手率
0     2011-04-29  0.966  0.970  0.971  ...  0.00  0.00  0.000  541.96
1     2011-05-03  0.973  0.980  0.980  ...  0.82  1.03  0.010   41.61
2     2011-05-04  0.976  0.971  0.980  ...  1.12 -0.92 -0.009   48.42
3     2011-05-05  0.966  0.970  0.971  ...  0.51 -0.10 -0.001   24.30
4     2011-05-06  0.960  0.973  0.975  ...  1.55  0.31  0.003   21.23
          ...    ...    ...    ...  ...   ...   ...    ...     ...
2515  2023-06-27  3.083  2.975  3.083  ...  4.07 -4.62 -0.144    0.27
2516  2023-06-28  3.054  2.967  3.054  ...  3.36 -0.27 -0.008    0.54
2517  2023-06-29  2.951  2.995  2.995  ...  1.48  0.94  0.028    0.57
2518  2023-06-30  3.010  3.009  3.156  ...  4.97  0.47  0.014    0.16
2519  2023-07-03  2.954  3.123  3.229  ...  9.14  3.79  0.114    0.64
[2520 rows x 11 columns]
```

接口示例-前复权

```python
import akshare as ak

fund_lof_hist_em_df = ak.fund_lof_hist_em(symbol="166009", period="daily", start_date="20000101", end_date="20230703", adjust="qfq")
print(fund_lof_hist_em_df)
```

数据示例-前复权

```
     日期     开盘     收盘     最高  ...     振幅    涨跌幅    涨跌额     换手率
0     2011-04-29 -0.076 -0.072 -0.071  ...   0.00   0.00  0.000  541.96
1     2011-05-03 -0.069 -0.062 -0.062  ... -11.11  13.89  0.010   41.61
2     2011-05-04 -0.066 -0.071 -0.062  ... -17.74 -14.52 -0.009   48.42
3     2011-05-05 -0.076 -0.072 -0.071  ...  -7.04  -1.41 -0.001   24.30
4     2011-05-06 -0.082 -0.069 -0.067  ... -20.83   4.17  0.003   21.23
          ...    ...    ...    ...  ...    ...    ...    ...     ...
2515  2023-06-27  3.083  2.975  3.083  ...   4.07  -4.62 -0.144    0.27
2516  2023-06-28  3.054  2.967  3.054  ...   3.36  -0.27 -0.008    0.54
2517  2023-06-29  2.951  2.995  2.995  ...   1.48   0.94  0.028    0.57
2518  2023-06-30  3.010  3.009  3.156  ...   4.97   0.47  0.014    0.16
2519  2023-07-03  2.954  3.123  3.229  ...   9.14   3.79  0.114    0.64
[2520 rows x 11 columns]
```

接口示例-后复权

```python
import akshare as ak

fund_lof_hist_em_df = ak.fund_lof_hist_em(symbol="166009", period="daily", start_date="20000101", end_date="20230703", adjust="hfq")
print(fund_lof_hist_em_df)
```

数据示例-后复权

```
      日期     开盘     收盘     最高  ...    振幅   涨跌幅    涨跌额     换手率
0     2011-04-29  0.966  0.970  0.971  ...  0.00  0.00  0.000  541.96
1     2011-05-03  0.973  0.980  0.980  ...  0.82  1.03  0.010   41.61
2     2011-05-04  0.976  0.971  0.980  ...  1.12 -0.92 -0.009   48.42
3     2011-05-05  0.966  0.970  0.971  ...  0.51 -0.10 -0.001   24.30
4     2011-05-06  0.960  0.973  0.975  ...  1.55  0.31  0.003   21.23
          ...    ...    ...    ...  ...   ...   ...    ...     ...
2515  2023-06-27  4.125  4.017  4.125  ...  3.05 -3.46 -0.144    0.27
2516  2023-06-28  4.096  4.009  4.096  ...  2.49 -0.20 -0.008    0.54
2517  2023-06-29  3.993  4.037  4.037  ...  1.10  0.70  0.028    0.57
2518  2023-06-30  4.052  4.051  4.198  ...  3.69  0.35  0.014    0.16
2519  2023-07-03  3.996  4.165  4.271  ...  6.79  2.81  0.114    0.64
[2520 rows x 11 columns]
```

#### 基金历史行情-新浪

接口: fund_etf_hist_sina

目标地址: http://vip.stock.finance.sina.com.cn/fund_center/index.html#jjhqetf

描述: 新浪财经-基金行情的日频率行情数据

限量: 单次返回指定基金的所有数据

输入参数

| 名称     | 类型  | 描述                                                                                                      |
|--------|-----|---------------------------------------------------------------------------------------------------------|
| symbol | str | symbol="sh510050"; 基金列表可以通过 **ak.fund_etf_category_sina(symbol="LOF基金")** 可选参数为: 封闭式基金, ETF基金, LOF基金 查询 |

输出参数

| 名称     | 类型      | 描述      |
|--------|---------|---------|
| date   | object  | -       |
| open   | float64 | -       |
| high   | float64 | -       |
| low    | float64 | -       |
| close  | float64 | -       |
| volume | int64   | 注意单位: 手 |

接口示例

```python
import akshare as ak

fund_etf_hist_sina_df = ak.fund_etf_hist_sina(symbol="sh510050")
print(fund_etf_hist_sina_df)
```

数据示例

```
            date   open   high    low  close      volume
0     2005-02-23  0.881  0.882  0.866  0.876  1269742542
1     2005-02-24  0.876  0.876  0.868  0.876   451614223
2     2005-02-25  0.877  0.887  0.875  0.880   506460695
3     2005-02-28  0.878  0.879  0.870  0.872   187965193
4     2005-03-01  0.870  0.873  0.865  0.867   208094456
...          ...    ...    ...    ...    ...         ...
4796  2024-11-15  2.808  2.821  2.777  2.778  1394418910
4797  2024-11-18  2.789  2.835  2.776  2.790  1199619816
4798  2024-11-19  2.786  2.793  2.743  2.778   919468916
4799  2024-11-20  2.772  2.794  2.764  2.784   854979945
4800  2024-11-21  2.780  2.795  2.772  2.791   738982704
[4801 rows x 6 columns]
```

### 基金净值

#### 开放式基金-实时数据

接口: fund_open_fund_daily_em

目标地址: http://fund.eastmoney.com/fund.html#os_0;isall_0;ft_;pt_1

描述: 东方财富网-天天基金网-基金数据, 此接口在每个交易日 **16:00-23:00** 更新当日的最新开放式基金净值数据

限量: 单次返回当前时刻所有历史数据

输入参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| -   | -   | -   |

输出参数

| 名称        | 类型    | 描述      |
|-----------|-------|---------|
| 基金代码      | str   | -       |
| 基金简称      | str   | -       |
| 单位净值      | float | 随时间变动   |
| 累计净值      | float | 随时间变动   |
| 前交易日-单位净值 | float | 随时间变动   |
| 前交易日-累计净值 | float | 随时间变动   |
| 日增长值      | float | -       |
| 日增长率      | float | -       |
| 申购状态      | str   | -       |
| 赎回状态      | str   | -       |
| 手续费       | str   | 注意单位: % |

接口示例

```python
import akshare as ak

fund_open_fund_daily_em_df = ak.fund_open_fund_daily_em()
print(fund_open_fund_daily_em_df)
```

数据示例

```
      基金代码             基金简称 2020-12-28-单位净值  ...  申购状态  赎回状态    手续费
0      010407        安信中债1-3年政策性金融债C          1.0906  ...  开放申购  开放赎回  0.00%
1      161725             招商中证白酒指数分级          1.3869  ...   限大额  开放赎回  0.10%
2      160632                  鹏华酒分级          1.0360  ...  开放申购  开放赎回  0.12%
3      004987             诺德新享灵活配置混合          2.2665  ...  开放申购  开放赎回  0.15%
4      008135               华宸未来价值先锋          1.4346  ...  开放申购  开放赎回  0.15%
       ...                    ...             ...  ...   ...   ...    ...
10005  009209       长信稳利一年持有期混合(FOF)                  ...  开放申购   封闭期  0.80%
10006  873002  广发资管全球精选一年持有期债券(QDII)                  ...   封闭期   封闭期
10007  007401       浦银颐和稳健养老一年(FOF)A                  ...  开放申购  开放赎回  0.08%
10008  010672        鹏华创新未来18个月封闭混合B                  ...   封闭期  开放赎回
10009  010650       易方达创新未来18个月封闭混合B                  ...   封闭期  开放赎回
```

#### 开放式基金-历史数据

接口: fund_open_fund_info_em

目标地址: http://fund.eastmoney.com/pingzhongdata/710001.js

描述: 东方财富网-天天基金网-基金数据-具体基金信息

限量: 单次返回当前时刻所有历史数据, 在查询基金数据的时候注意基金前后端问题

输入参数

| 名称        | 类型  | 描述                                                                                             |
|-----------|-----|------------------------------------------------------------------------------------------------|
| symbol    | str | symbol="710001"; 需要基金代码, 可以通过调用 **ak.fund_open_fund_daily_em()** 获取                            |
| indicator | str | indicator="单位净值走势";  参见 **fund_open_fund_info_em** 参数一览表                                       |
| period    | str | period="成立来"; 该参数只对 `累计收益率走势` 有效, choice of {"1月", "3月", "6月", "1年", "3年", "5年", "今年来", "成立来"} |

fund_open_fund_info_em 参数一览表

| 参数名称    | 备注  |
|---------|-----|
| 单位净值走势  | -   |
| 累计净值走势  | -   |
| 累计收益率走势 | -   |
| 同类排名走势  | -   |
| 同类排名百分比 | -   |
| 分红送配详情  | -   |
| 拆分详情    | -   |

输出参数-单位净值走势

| 名称   | 类型      | 描述      |
|------|---------|---------|
| 净值日期 | object  | -       |
| 单位净值 | float64 | -       |
| 日增长率 | float64 | 注意单位: % |

接口示例-单位净值走势

```python
import akshare as ak

fund_open_fund_info_em_df = ak.fund_open_fund_info_em(symbol="710001", indicator="单位净值走势")
print(fund_open_fund_info_em_df)
```

数据示例-单位净值走势

```
       净值日期    单位净值  日增长率
0     2011-09-21  1.0000  0.00
1     2011-09-23  1.0000  0.00
2     2011-09-30  1.0001  0.01
3     2011-10-14  1.0005  0.04
4     2011-10-21  1.0007  0.02
          ...     ...   ...
2973  2023-12-25  2.6137  0.28
2974  2023-12-26  2.5804 -1.27
2975  2023-12-27  2.6219  1.61
2976  2023-12-28  2.6322  0.39
2977  2023-12-29  2.6590  1.02
[2978 rows x 3 columns]
```

输出参数-累计净值走势

| 名称   | 类型      | 描述  |
|------|---------|-----|
| 净值日期 | object  | -   |
| 累计净值 | float64 | -   |

接口示例-累计净值走势

```python
import akshare as ak

fund_open_fund_info_em_df = ak.fund_open_fund_info_em(symbol="710001", indicator="累计净值走势")
print(fund_open_fund_info_em_df)
```

数据示例-累计净值走势

```
       净值日期    累计净值
0     2011-09-21  1.0000
1     2011-09-23  1.0000
2     2011-09-30  1.0001
3     2011-10-14  1.0005
4     2011-10-21  1.0007
          ...     ...
2973  2023-12-25  2.6137
2974  2023-12-26  2.5804
2975  2023-12-27  2.6219
2976  2023-12-28  2.6322
2977  2023-12-29  2.6590
[2978 rows x 2 columns]
```

输出参数-累计收益率走势

| 名称    | 类型      | 描述      |
|-------|---------|---------|
| 日期    | object  | -       |
| 累计收益率 | float64 | 注意单位: % |

接口示例-累计收益率走势

```python
import akshare as ak

fund_open_fund_info_em_df = ak.fund_open_fund_info_em(symbol="710001", indicator="累计收益率走势", period="成立来")
print(fund_open_fund_info_em_df)
```

数据示例-累计收益率走势

```
         日期   累计收益率
0    2011-09-21    0.00
1    2011-11-09    3.52
2    2011-11-29    1.92
3    2011-12-19   -3.17
4    2012-01-09   -5.69
..          ...     ...
209  2023-10-19  165.84
210  2023-11-08  173.36
211  2023-11-28  165.36
212  2023-12-18  164.23
213  2023-12-29  165.90
[214 rows x 2 columns]
```

输出参数-同类排名走势

| 名称            | 类型      | 描述  |
|---------------|---------|-----|
| 报告日期          | object  | -   |
| 同类型排名-每日近三月排名 | float64 | -   |
| 总排名-每日近三月排名   | float64 | -   |

接口示例-同类排名走势

```python
import akshare as ak

fund_open_fund_info_em_df = ak.fund_open_fund_info_em(symbol="710001", indicator="同类排名走势")
print(fund_open_fund_info_em_df)
```

数据示例-同类排名走势

```
     报告日期  同类型排名-每日近三月排名  总排名-每日近三月排名
0     2013-01-04             39          353
1     2013-01-07             49          353
2     2013-01-08             72          353
3     2013-01-09             83          353
4     2013-01-10             90          353
          ...            ...          ...
2670  2023-12-25           1969         3970
2671  2023-12-26           2415         3978
2672  2023-12-27           2072         3983
2673  2023-12-28           2389         3983
2674  2023-12-29           2404         4036
[2675 rows x 3 columns]
```

输出参数-同类排名百分比

| 名称                 | 类型      | 描述  |
|--------------------|---------|-----|
| 报告日期               | object  | -   |
| 同类型排名-每日近3月收益排名百分比 | float64 | -   |

接口示例-同类排名百分比

```python
import akshare as ak

fund_open_fund_info_em_df = ak.fund_open_fund_info_em(symbol="710001", indicator="同类排名百分比")
print(fund_open_fund_info_em_df)
```

数据示例-同类排名百分比

```
     报告日期  同类型排名-每日近3月收益排名百分比
0     2013-01-04               88.95
1     2013-01-07               86.12
2     2013-01-08               79.60
3     2013-01-09               76.49
4     2013-01-10               74.50
          ...                 ...
2670  2023-12-25               50.40
2671  2023-12-26               39.29
2672  2023-12-27               47.98
2673  2023-12-28               40.02
2674  2023-12-29               40.44
[2675 rows x 2 columns]
```

输出参数-分红送配详情

| 名称    | 类型     | 描述  |
|-------|--------|-----|
| 年份    | object | -   |
| 权益登记日 | object | -   |
| 除息日   | object | -   |
| 每份分红  | object | -   |
| 分红发放日 | object | -   |

接口示例-分红送配详情

```python
import akshare as ak

fund_open_fund_info_em_df = ak.fund_open_fund_info_em(symbol="161606", indicator="分红送配详情")
print(fund_open_fund_info_em_df)
```

数据示例-分红送配详情

```
  年份       权益登记日    除息日          每份分红       分红发放日
0  2023年  2023-01-16  2023-01-16  每份派现金0.0050元  2023-01-18
1  2022年  2022-01-19  2022-01-19  每份派现金0.0050元  2022-01-21
2  2021年  2021-01-19  2021-01-19  每份派现金0.0050元  2021-01-21
3  2007年  2007-10-25  2007-10-25  每份派现金0.7700元  2007-10-29
4  2007年  2007-03-12  2007-03-12  每份派现金1.1100元  2007-03-14
5  2006年  2006-05-30  2006-05-30  每份派现金0.0600元  2006-06-01
```

输出参数-拆分详情

| 名称     | 类型     | 描述  |
|--------|--------|-----|
| 年份     | object | -   |
| 拆分折算日  | object | -   |
| 拆分类型   | object | -   |
| 拆分折算比例 | object | -   |

接口示例-拆分详情

```python
import akshare as ak

fund_open_fund_info_em_df = ak.fund_open_fund_info_em(symbol="161606", indicator="拆分详情")
print(fund_open_fund_info_em_df)
```

数据示例-拆分详情

```
    年份   拆分折算日  拆分类型    拆分折算比例
0  2020年  2020-12-15  份额折算  1:1.0054
1  2020年  2020-08-26  份额折算  1:1.4982
2  2019年  2019-12-16  份额折算  1:1.0107
3  2019年  2019-07-02  份额折算  1:1.5569
4  2018年  2018-12-17  份额折算  1:1.0255
5  2017年  2017-12-15  份额折算  1:1.0045
6  2017年  2017-09-26  份额折算  1:1.4942
7  2016年  2016-12-15  份额折算  1:1.0230
8  2015年  2015-12-15  份额折算  1:1.0180
```

#### 货币型基金-实时数据

接口: fund_money_fund_daily_em

目标地址: http://fund.eastmoney.com/HBJJ_pjsyl.html

描述: 东方财富网-天天基金网-基金数据-货币型基金收益, 此接口数据每个交易日 **16:00～23:00**

限量: 单次返回当前时刻所有历史数据

输入参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| -   | -   | -   |

输出参数

| 名称          | 类型    | 描述  |
|-------------|-------|-----|
| 基金代码        | str   | -   |
| 基金简称        | str   | -   |
| 当前交易日-万份收益  | float | -   |
| 当前交易日-7日年化% | float | -   |
| 当前交易日-单位净值  | float | -   |
| 前一交易日-万份收益  | float | -   |
| 前一交易日-7日年化% | float | -   |
| 前一交易日-单位净值  | float | -   |
| 日涨幅         | str   | -   |
| 成立日期        | str   | -   |
| 基金经理        | str   | -   |
| 手续费         | str   | -   |
| 可购全部        | str   | -   |

接口示例

```python
import akshare as ak

fund_money_fund_daily_em_df = ak.fund_money_fund_daily_em()
print(fund_money_fund_daily_em_df)
```

数据示例

```
       基金代码        基金简称 当前交易日-万份收益 当前交易日-7日年化%  ...        成立日期  基金经理  手续费 可购全部
2    004186     江信增利货币B     0.4461     4.1720%  ...  2017-08-03    杨淳  0费率   购买
3    002884  华润元大现金通货币B     0.8919     3.9870%  ...  2016-07-27  李仆 等  0费率   购买
4    004185     江信增利货币A     0.4065     3.9620%  ...  2017-08-03    杨淳  0费率   购买
5    002883  华润元大现金通货币A     0.8585     3.8460%  ...  2016-07-27  李仆 等  0费率   购买
6    740602       长安货币B     0.4641     3.7260%  ...  2013-01-25    孟楠  0费率   购买
..      ...         ...        ...         ...  ...         ...   ...  ...  ...
448  519513    万家日日薪货币R        ---         ---  ...  2013-01-15    郅元  0费率   购买
449  005202    兴业稳天盈货币B        ---         ---  ...  2017-09-11  丁进 等  0费率   购买
450  000908   农银红利日结货币B        ---         ---  ...  2014-12-19  许娅 等  0费率   购买
451  000907   农银红利日结货币A        ---         ---  ...  2014-12-19  许娅 等  0费率   购买
452  519507       万家货币B        ---         ---  ...  2013-08-15    郅元  0费率   购买
```

#### 货币型基金-历史数据

接口: fund_money_fund_info_em

目标地址: https://fundf10.eastmoney.com/jjjz_000009.html

描述: 东方财富网-天天基金网-基金数据-货币型基金-历史净值

限量: 单次返回当前时刻所有历史数据

输入参数

| 名称     | 类型  | 描述                                                                   |
|--------|-----|----------------------------------------------------------------------|
| symbol | str | symbol="000009"; 需要基金代码, 可以通过调用 **ak.fund_money_fund_daily_em()** 获取 |

输出参数

| 名称      | 类型      | 描述 |
|---------|---------|----|
| 净值日期    | object  | -  |
| 每万份收益   | float64 | -  |
| 7日年化收益率 | float64 | -  |
| 申购状态    | object  | -  |
| 赎回状态    | object  | -  |

接口示例

```python
import akshare as ak

fund_money_fund_info_em_df = ak.fund_money_fund_info_em(symbol="000009")
print(fund_money_fund_info_em_df)
```

数据示例

```
      净值日期    每万份收益  7日年化收益率  申购状态  赎回状态
0     2013-03-25  21.2775    3.485   封闭期   封闭期
1     2013-03-26   0.9561    3.482  开放申购  开放赎回
2     2013-03-27   0.3729    3.175  开放申购  开放赎回
3     2013-03-28   0.7068    3.042  开放申购  开放赎回
4     2013-03-29   0.6744    2.892  开放申购  开放赎回
...          ...      ...      ...   ...   ...
4187  2025-02-27   0.3724    1.371  暂停申购  开放赎回
4188  2025-02-28   0.3789    1.374  暂停申购  开放赎回
4189  2025-03-01   0.3790    1.377  暂停申购  开放赎回
4190  2025-03-02   0.3790    1.381  暂停申购  开放赎回
4191  2025-03-03   0.4081    1.408  暂停申购  开放赎回
[4192 rows x 5 columns]
```

#### 理财型基金-实时数据

接口: fund_financial_fund_daily_em

目标地址: http://fund.eastmoney.com/lcjj.html#1_1__0__ljjz,desc_1_os1

描述: 东方财富网-天天基金网-基金数据-理财型基金-实时数据, 此接口数据每个交易日 **16:00～23:00** 更新

限量: 该接口由于目标网站未更新数据，暂时不能返回数据

输入参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| -   | -   | -   |

输出参数

| 名称          | 类型    | 描述  |
|-------------|-------|-----|
| 序号          | int   | -   |
| 基金代码        | str   | -   |
| 基金简称        | str   | -   |
| 上一期年化收益率    | float | -   |
| 当前交易日-万份收益  | float | -   |
| 当前交易日-7日年华  | float | -   |
| 前一个交易日-万份收益 | float | -   |
| 前一个交易日-7日年华 | float | -   |
| 封闭期         | float | -   |
| 申购状态        | str   | -   |

接口示例

```python
import akshare as ak

fund_financial_fund_daily_em_df = ak.fund_financial_fund_daily_em()
print(fund_financial_fund_daily_em_df)
```

数据示例

```
   序号 基金代码   基金简称  上一期年化收益率  ... 前一个交易日-万份收益 前一个交易日-7日年华  封闭期 申购状态
0   1  000134     信诚理财28日盈A   2.1010  ...      0.4548      1.8150  28天  限大额
1   2  090021      大成月添利债券A   4.7470  ...      0.5243      1.8540  1个月  限大额
2   3  110051   易方达月月利理财债券B   2.4040  ...      0.6606      2.2450  1个月  限大额
3   4  070035    嘉实理财宝7天债券A   1.8310  ...      0.4871      1.8310   7天  限大额
4   5  660016     农银7天理财债券A   1.6780  ...      0.4567      1.6780   7天  限大额
5   6  000791   银华双月定期理财债券A           ...      0.6134      2.2390  2个月  限大额
6   7  001057    华夏理财30天债券A   3.0650  ...      0.6477      2.2020  1个月  限大额
7   8  110050   易方达月月利理财债券A   2.1140  ...      0.5813      1.9560  1个月  限大额
8   9  090023  大成月月盈短期理财债券A  22.2030  ...      0.4640      1.7510  1个月  限大额
9  10  000322    农银14天理财债券A           ...      0.4592      1.6080  14天  限大额
```

#### 理财型基金-历史数据

接口: fund_financial_fund_info_em

目标地址: http://fundf10.eastmoney.com/jjjz_000791.html

描述: 东方财富网站-天天基金网-基金数据-理财型基金收益-历史净值明细

限量: 单次返回当前时刻所有历史数据

输入参数

| 名称     | 类型  | 描述                                                                     |
|--------|-----|------------------------------------------------------------------------|
| symbol | str | symbol="000134"; 基金代码, 可以通过调用 **ak.fund_financial_fund_daily_em()** 获取 |

输出参数

| 名称   | 类型      | 描述      |
|------|---------|---------|
| 净值日期 | object  | -       |
| 单位净值 | float64 | -       |
| 累计净值 | float64 | -       |
| 日增长率 | float64 | -       |
| 申购状态 | object  | -       |
| 赎回状态 | object  | -       |
| 分红送配 | object  | 注意单位: % |

接口示例

```python
import akshare as ak

fund_financial_fund_info_em_df = ak.fund_financial_fund_info_em(symbol="000134")
print(fund_financial_fund_info_em_df)
```

数据示例

```
     净值日期    单位净值    累计净值  日增长率    申购状态  赎回状态 分红送配
0    2020-06-23  0.9982  0.9982   NaN    暂停申购  开放赎回
1    2020-06-24  0.9987  0.9987  0.05    暂停申购  开放赎回
2    2020-06-29  0.9994  0.9994  0.07    暂停申购  开放赎回
3    2020-06-30  0.9995  0.9995  0.01    暂停申购  开放赎回
4    2020-07-01  0.9998  0.9998  0.03    暂停申购  开放赎回
..          ...     ...     ...   ...     ...   ...  ...
663  2023-03-16  1.0291  1.0951  0.02  限制大额申购  开放赎回
664  2023-03-17  1.0294  1.0954  0.03  限制大额申购  开放赎回
665  2023-03-20  1.0302  1.0962  0.08  限制大额申购  开放赎回
666  2023-03-21  1.0303  1.0963  0.01  限制大额申购  开放赎回
667  2023-03-22  1.0303  1.0963  0.00  限制大额申购  开放赎回
```

#### 分级基金-实时数据

接口: fund_graded_fund_daily_em

目标地址: http://fund.eastmoney.com/fjjj.html#1_1__0__zdf,desc_1

描述: 东方财富网-天天基金网-基金数据-分级基金-实时数据, 此接口数据每个交易日 **16:00～23:00**

限量: 单次返回当前时刻所有历史数据

输入参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| -   | -   | -   |

输出参数

| 名称        | 类型    | 描述      |
|-----------|-------|---------|
| 基金代码      | str   | -       |
| 基金简称      | str   | -       |
| 单位净值      | float | -       |
| 累计净值      | float | -       |
| 前交易日-单位净值 | float | -       |
| 前交易日-累计净值 | float | -       |
| 日增长值      | float | -       |
| 日增长率      | float | 注意单位: % |
| 市价        | str   | -       |
| 折价率       | str   | -       |
| 手续费       | str   | -       |

接口示例

```python
import akshare as ak

fund_graded_fund_daily_em_df = ak.fund_graded_fund_daily_em()
print(fund_graded_fund_daily_em_df)
```

数据示例

```
       基金代码            基金简称    单位净值    累计净值  ...   日增长率      市价     折价率    手续费
0    150232     申万菱信申万电子分级B  0.5598          ...  15.85  0.9080  -62.20
1    150174  信诚中证TMT产业主题分级B  0.6580  2.0740  ...  10.77    0.76  -15.50
2    150312   信诚中证智能家居指数分级B  0.7430  0.0960  ...  10.73  0.7570   -1.88
3    150310   信诚中证信息安全指数分级B  0.9090  0.1040  ...   9.92  1.0790  -18.70
4    150151  信诚中证800有色指数分级B    0.63  1.1180  ...   6.42  0.7110  -12.86
..      ...             ...     ...     ...  ...    ...     ...     ...    ...
341  150252   招商中证煤炭等权指数分级B                  ...         0.5120
342  150324   工银中证环保产业指数分级B                  ...           0.74
343  150197   国泰国证有色金属行业分级B                  ...           0.44
344  150294   南方中证高铁产业指数分级B                  ...         0.4350
345  161825       银华中证800分级                  ...                         1.20%
```

#### 分级基金-历史数据

接口: fund_graded_fund_info_em

目标地址: http://fundf10.eastmoney.com/jjjz_004186.html

描述: 东方财富网站-天天基金网-基金数据-分级基金-历史数据

限量: 单次返回当前时刻所有历史数据

输入参数

| 名称     | 类型  | 描述                                                                    |
|--------|-----|-----------------------------------------------------------------------|
| symbol | str | symbol="150232"; 需要基金代码, 可以通过调用 **ak.fund_graded_fund_daily_em()** 获取 |

输出参数

| 名称   | 类型    | 描述                                                                       |
|------|-------|--------------------------------------------------------------------------|
| 净值日期 | str   | -                                                                        |
| 单位净值 | float | -                                                                        |
| 累计净值 | float | -                                                                        |
| 日增长率 | float | 注意单位: %; 日增长率为空原因如下: 1. 非交易日净值不参与日增长率计算(灰色数据行). 2. 上一交易日净值未披露, 日增长率无法计算. |
| 申购状态 | str   | -                                                                        |
| 赎回状态 | str   | -                                                                        |

接口示例

```python
import akshare as ak

fund_graded_fund_info_em_df = ak.fund_graded_fund_info_em(symbol="150232")
print(fund_graded_fund_info_em_df)
```

数据示例

```
      净值日期     单位净值 累计净值 日增长率 申购状态 赎回状态
0     2015-05-14  1.0000  1.0000   NaN  封闭期  封闭期
1     2015-05-15  0.9997  0.9997 -0.03  封闭期  封闭期
2     2015-05-18  0.9993  0.9993 -0.04  封闭期  封闭期
3     2015-05-19  0.9991  0.9991 -0.02  封闭期  封闭期
4     2015-05-20  0.9988  0.9988 -0.03  封闭期  封闭期
...          ...     ...     ...   ...  ...  ...
1338  2020-11-03  0.9749     NaN  1.72  封闭期  封闭期
1339  2020-11-04  0.9830     NaN  0.83  封闭期  封闭期
1340  2020-11-05  1.0235     NaN  4.12  封闭期  封闭期
1341  2020-11-06  1.0453     NaN  2.13  封闭期  封闭期
1342  2020-11-09  1.1120     NaN  6.38  封闭期  封闭期
[1343 rows x 6 columns]
```

#### 场内交易基金-实时数据

接口: fund_etf_fund_daily_em

目标地址: http://fund.eastmoney.com/cnjy_dwjz.html

描述: 东方财富网站-天天基金网-基金数据-场内交易基金-实时数据, 此接口数据每个交易日 **16:00～23:00**

限量: 单次返回当前时刻所有数据

输入参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| -   | -   | -   |

输出参数

| 名称          | 类型      | 描述            |
|-------------|---------|---------------|
| 基金代码        | object  | -             |
| 基金简称        | object  | -             |
| 类型          | float64 | -             |
| 当前交易日-单位净值  | float64 | 会返回具体的日期值作为字段 |
| 当前交易日-累计净值  | float64 | 会返回具体的日期值作为字段 |
| 前一个交易日-单位净值 | float64 | 会返回具体的日期值作为字段 |
| 前一个交易日-累计净值 | float64 | 会返回具体的日期值作为字段 |
| 增长值         | float64 | -             |
| 增长率         | object  | -             |
| 市价          | object  | -             |
| 折价率         | object  | -             |

接口示例

```python
import akshare as ak

fund_etf_fund_daily_em_df = ak.fund_etf_fund_daily_em()
print(fund_etf_fund_daily_em_df)
```

数据示例

```
       基金代码             基金简称      类型  ...     增长率      市价     折价率
0    159909   招商深证TMT50ETF行情  ETF-场内  ...   0.12%  7.0200   0.14%
1    159991     招商创业板大盘ETF行情  ETF-场内  ...   0.64%  1.4840   0.02%
2    150270    招商中证白酒指数分级B行情    分级杠杆  ...   2.61%  1.4650  -1.16%
3    150262    华宝中证医疗指数分级B行情    分级杠杆  ...   0.81%  1.3930   2.64%
4    150250    招商中证银行指数分级B行情    分级杠杆  ...  -1.06%  1.3380  -0.62%
..      ...              ...     ...  ...     ...     ...     ...
474  510860      兴业上证50ETF行情  ETF-场内  ...     ---     ---     ---
475  150175    银华恒生国企指数分级A行情    固定收益  ...     ---     ---     ---
476  150205    鹏华中证国防指数分级A行情    固定收益  ...     ---     ---     ---
477  150321    富国中证煤炭指数分级A行情    固定收益  ...     ---     ---     ---
478  515790  华泰柏瑞中证光伏产业ETF行情  ETF-场内  ...     ---     ---     ---
```

#### 场内交易基金-历史数据

接口: fund_etf_fund_info_em

目标地址: http://fundf10.eastmoney.com/jjjz_004186.html

描述: 东方财富网站-天天基金网-基金数据-场内交易基金-历史净值数据

限量: 单次返回当前时刻所有历史数据

输入参数

| 名称         | 类型  | 描述                                                             |
|------------|-----|----------------------------------------------------------------|
| fund       | str | fund="511280"; 基金代码, 可以通过调用 **ak.fund_etf_fund_daily_em()** 获取 |
| start_date | str | start_date="20000101"; 开始时间                                    |
| end_date   | str | end_date="20500101"; 结束时间                                      |

输出参数

| 名称   | 类型      | 描述      |
|------|---------|---------|
| 净值日期 | object  | -       |
| 单位净值 | float64 | -       |
| 累计净值 | float64 | -       |
| 日增长率 | float64 | 注意单位: % |
| 申购状态 | object  | -       |
| 赎回状态 | object  | -       |

接口示例

```python
import akshare as ak

fund_etf_fund_info_em_df = ak.fund_etf_fund_info_em(fund="511280", start_date="20000101", end_date="20500101")
print(fund_etf_fund_info_em_df)
```

数据示例

```
     净值日期      单位净值    累计净值  日增长率  申购状态  赎回状态
0    2018-05-03    1.0000  1.0000   NaN   封闭期   封闭期
1    2018-05-04    1.0003  1.0003  0.03   封闭期   封闭期
2    2018-05-11    1.0006  1.0006   NaN   封闭期   封闭期
3    2018-05-14  100.0804  1.0008  0.02   封闭期   封闭期
4    2018-05-18  100.1054  1.0011   NaN   封闭期   封闭期
..          ...       ...     ...   ...   ...   ...
779  2021-07-28  110.8370  1.1084 -0.02  场内买入  场内卖出
780  2021-07-29  110.8154  1.1082 -0.02  场内买入  场内卖出
781  2021-07-30  110.7640  1.1076 -0.05  场内买入  场内卖出
782  2021-08-02  110.6097  1.1061 -0.14  场内买入  场内卖出
783  2021-08-03  113.8015  1.1380  2.89  暂停申购  暂停赎回
[784 rows x 6 columns]
```

#### 香港基金-历史数据

接口: fund_hk_fund_hist_em

目标地址: http://overseas.1234567.com.cn/f10/FundJz/968092#FHPS

描述: 东方财富网站-天天基金网-基金数据-香港基金-历史净值明细

限量: 单次返回指定 code 和 symbol 所有历史数据

输入参数

| 名称     | 类型  | 描述                                                            |
|--------|-----|---------------------------------------------------------------|
| code   | str | code="1002200683"; 香港基金代码, 可以通过调用 **ak.fund_em_hk_rank()** 获取 |
| symbol | str | symbol="历史净值明细"; choice of {"历史净值明细", "分红送配详情"}               |

输出参数-历史净值明细

| 名称   | 类型      | 描述      |
|------|---------|---------|
| 净值日期 | object  | -       |
| 单位净值 | float64 | -       |
| 日增长值 | float64 | -       |
| 日增长率 | float64 | 注意单位: % |
| 单位   | object  | -       |

接口示例-历史净值明细

```python
import akshare as ak

fund_hk_fund_hist_em_df = ak.fund_hk_fund_hist_em(code='1002200683', symbol="历史净值明细")
print(fund_hk_fund_hist_em_df)
```

数据示例-历史净值明细

```
     净值日期     单位净值    日增长值    日增长率 单位
0    2021-01-19  10.0056 -0.0583 -0.5793  元
1    2021-01-18  10.0639  0.0620  0.6199  元
2    2021-01-15  10.0019 -0.0074 -0.0739  元
3    2021-01-14  10.0093  0.0444  0.4456  元
4    2021-01-13   9.9649 -0.0026 -0.0261  元
..          ...      ...     ...     ... ..
165  2020-05-14  10.0774  0.0214  0.2128  元
166  2020-05-13  10.0560  0.0210  0.2093  元
167  2020-05-12  10.0350  0.0350  0.3500  元
168  2020-05-11  10.0000  0.0000  0.0000  元
169  2020-05-08  10.0000                  元
```

输出参数-分红送配详情

| 名称    | 类型      | 描述  |
|-------|---------|-----|
| 年份    | object  | -   |
| 权益登记日 | object  | -   |
| 除息日   | object  | -   |
| 分红发放日 | object  | -   |
| 分红金额  | float64 | -   |
| 单位    | object  | -   |

接口示例-分红送配详情

```python
import akshare as ak

fund_hk_fund_hist_em_df = ak.fund_hk_fund_hist_em(code='1002200683', symbol="分红送配详情")
print(fund_hk_fund_hist_em_df)
```

数据示例-分红送配详情

```
     年份权益登记日         除息日    分红发放日    分红金额 单位
0  2020              2020-12-31              0.0522  元
1  2020  2020-11-30  2020-11-30  2020-12-14  0.0552  元
2  2020  2020-10-30  2020-10-30  2020-11-13  0.0573  元
3  2020  2020-09-30  2020-09-30  2020-10-16  0.0527  元
4  2020  2020-08-31  2020-08-31  2020-09-14  0.0545  元
5  2020  2020-07-31  2020-07-31  2020-08-14  0.0593  元
6  2020  2020-06-30  2020-06-30  2020-07-15  0.0669  元
```

### 分红送配

#### 基金累计分红

接口: fund_etf_dividend_sina

目标地址: https://finance.sina.com.cn/fund/quotes/510050/bc.shtml

描述: 新浪财经-基金-ETF 基金-累计分红

限量: 单次返回所有历史数据

输入参数

| 名称     | 类型  | 描述                |
|--------|-----|-------------------|
| symbol | str | symbol="sh510050" |

输出参数

| 名称   | 类型      | 描述    |
|------|---------|-------|
| 日期   | object  | 除权除息日 |
| 累计分红 | float64 | -     |

接口示例

```python
import akshare as ak

fund_etf_dividend_sina_df = fund_etf_dividend_sina(symbol="sh510050")
print(fund_etf_dividend_sina_df)
```

数据示例

```
           日期   累计分红
0  2006-05-19  0.024
1  2006-11-16  0.061
2  2008-11-19  0.121
3  2010-11-16  0.147
4  2012-05-16  0.158
5  2012-11-13  0.195
6  2013-11-15  0.248
7  2014-11-17  0.291
8  2016-11-29  0.344
9  2017-11-28  0.398
10 2018-12-03  0.447
11 2019-12-02  0.494
12 2020-11-30  0.545
13 2021-11-29  0.586
14 2022-12-01  0.623
15 2023-11-27  0.662
16 2024-12-02  0.717
```

#### 基金分红

接口: fund_fh_em

目标地址: http://fund.eastmoney.com/data/fundfenhong.html

描述: 天天基金网-基金数据-分红送配-基金分红

限量: 单次返回所有历史数据

输入参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| -   | -   | -   |

输出参数

| 名称    | 类型      | 描述        |
|-------|---------|-----------|
| 序号    | int64   | -         |
| 基金代码  | object  | -         |
| 基金简称  | object  | -         |
| 权益登记日 | object  | -         |
| 除息日期  | object  | -         |
| 分红    | float64 | 注意单位: 元/份 |
| 分红发放日 | object  | -         |

接口示例

```python
import akshare as ak

fund_fh_em_df = ak.fund_fh_em()
print(fund_fh_em_df)
```

数据示例

```
          序号   基金代码       基金简称       权益登记日        除息日期    分红    分红发放日
0          1  007286  中邮纯债裕利三个月定开债  2021-11-22  2021-11-22  0.0530  2021-11-24
1          2  000799   民生加银家盈半年定期宝  2021-11-22  2021-11-22  0.0142  2021-11-23
2          3  090002       大成债券A/B  2021-11-22  2021-11-22  0.0110  2021-11-23
3          4  092002         大成债券C  2021-11-22  2021-11-22  0.0110  2021-11-23
4          5  002868        鹏华丰茂债券  2021-11-22  2021-11-22  0.0350  2021-11-24
      ...     ...           ...         ...         ...     ...         ...
24620  24621  500008          基金兴华  1999-04-05  1999-04-06  0.0220  1999-04-02
24621  24622  184688          基金开元  1999-04-05  1999-04-06  0.0300  1999-04-02
24622  24623  500003          基金安信  1999-04-05  1999-04-06  0.0420  1999-04-02
24623  24624  500006          基金裕阳  1999-04-05  1999-04-06  0.0210  1999-04-02
24624  24625  500001          基金金泰  1999-04-05  1999-04-06  0.0490  1999-04-06
```

#### 基金拆分

接口: fund_cf_em

目标地址: http://fund.eastmoney.com/data/fundchaifen.html

描述: 天天基金网-基金数据-分红送配-基金拆分

限量: 单次返回所有历史数据

输入参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| -   | -   | -   |

输出参数

| 名称    | 类型      | 描述       |
|-------|---------|----------|
| 序号    | int64   | -        |
| 基金代码  | object  | -        |
| 基金简称  | object  | -        |
| 拆分折算日 | object  | -        |
| 拆分类型  | object  | -        |
| 拆分折算  | float64 | 注意单位: 每份 |

接口示例

```python
import akshare as ak

fund_cf_em_df = ak.fund_cf_em()
print(fund_cf_em_df)
```

数据示例

```
        序号   基金代码       基金简称       拆分折算日  拆分类型      拆分折算
0        1  516090     易方达中证新能源ETF  2021-11-26  份额分拆  2.000000
1        2  516070  易方达中证内地低碳经济ETF  2021-11-19  份额分拆  2.000000
2        3  000465   景顺长城鑫月薪定期支付债券  2021-11-18  份额折算       NaN
3        4  512560      易方达中证军工ETF  2021-11-12  份额分拆  2.000000
4        5  159863     鹏华中证光伏产业ETF  2021-11-05  份额分拆  1.500000
    ...     ...             ...         ...   ...       ...
3949  3950  500028            基金兴业  2006-09-29  份额折算  1.160000
3950  3951  100020        富国天益价值混合  2006-07-17  份额折算  1.606483
3951  3952  510180      华安上证180ETF  2006-05-10  份额折算  0.372683
3952  3953  159901     易方达深证100ETF  2006-04-14  份额折算  0.949483
3953  3954  510050       华夏上证50ETF  2005-02-04  份额折算  1.183841
```

#### 基金分红排行

接口: fund_fh_rank_em

目标地址: http://fund.eastmoney.com/data/fundleijifenhong.html

描述: 天天基金网-基金数据-分红送配-基金分红排行

限量: 单次返回所有历史数据

输入参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| -   | -   | -   |

输出参数

| 名称   | 类型      | 描述        |
|------|---------|-----------|
| 序号   | int64   | -         |
| 基金代码 | object  | -         |
| 基金简称 | object  | -         |
| 累计分红 | float64 | 注意单位: 元/份 |
| 累计次数 | int64   | -         |
| 成立日期 | object  | -         |

接口示例

```python
import akshare as ak

fund_fh_rank_em_df = ak.fund_fh_rank_em()
print(fund_fh_rank_em_df)
```

数据示例

```
        序号  基金代码            基金简称      累计分红  累计次数   成立日期
0        1  511220      海富通上证城投债ETF  32.760000    27  2014-11-13
1        2  184801      鹏华前海万科REITS  32.528400     5  2015-07-06
2        3  511880            银华日利A  27.060000     9  2013-04-01
3        4  003816            银华日利B  25.794000     6  2016-11-23
4        5  511270     海富通上证10年期ETF   9.770000    12  2018-10-12
    ...     ...              ...        ...   ...         ...
4616  4617  009678  浙商智多益稳健一年持有期混合C   0.001000     1  2021-01-29
4617  4618  010539     浙商智多金稳健一年持有A   0.001000     1  2021-06-24
4618  4619  006792     鹏华港美互联股票美元现汇   0.000607     1  2019-04-15
4619  4620  159937          博时黄金ETF   0.000200     1  2014-08-13
4620  4621  008139        鑫元一年中高等级债   0.000100     1  2020-03-18
```

### 基金排行

#### 开放式基金排行

接口: fund_open_fund_rank_em

目标地址: https://fund.eastmoney.com/data/fundranking.html

描述: 东方财富网-数据中心-开放式基金排行

限量: 单次返回当前时刻所有数据

输入参数

| 名称     | 类型  | 描述                                                                       |
|--------|-----|--------------------------------------------------------------------------|
| symbol | str | symbol="全部"; choice of {"全部", "股票型", "混合型", "债券型", "指数型", "QDII", "FOF"} |

输出参数

| 名称   | 类型      | 描述      |
|------|---------|---------|
| 序号   | int64   | -       |
| 基金代码 | object  | -       |
| 基金简称 | object  | -       |
| 日期   | object  | -       |
| 单位净值 | float64 | -       |
| 累计净值 | float64 | -       |
| 日增长率 | float64 | 注意单位: % |
| 近1周  | float64 | 注意单位: % |
| 近1月  | float64 | 注意单位: % |
| 近3月  | float64 | 注意单位: % |
| 近6月  | float64 | 注意单位: % |
| 近1年  | float64 | 注意单位: % |
| 近2年  | float64 | 注意单位: % |
| 近3年  | float64 | 注意单位: % |
| 今年来  | float64 | 注意单位: % |
| 成立来  | float64 | 注意单位: % |
| 自定义  | float64 | 注意单位: % |
| 手续费  | object  | -       |

接口示例

```python
import akshare as ak

fund_open_fund_rank_em_df = ak.fund_open_fund_rank_em(symbol="全部")
print(fund_open_fund_rank_em_df)
```

数据示例

```
      序号    基金代码                    基金简称  ...       成立来      自定义    手续费
0          1  003384                  金鹰添盈纯债债券A  ...  146.67  138.619600  0.08%
1          2  012623                  金鹰添盈纯债债券C  ...  106.92  133.400700  0.00%
2          3  008729                    同泰恒利纯债C  ...  141.60  125.063500  0.00%
3          4  008728                    同泰恒利纯债A  ...  139.84  122.035400  0.08%
4          5  017091  景顺长城纳斯达克科技ETF联接(QDII)A人民币  ...   62.36         NaN  0.12%
      ...     ...                        ...  ...     ...         ...    ...
15005  15006  019983                  泓德智选启元混合C  ...  -14.00  -14.000000  0.00%
15006  15007  020693                  长城健康消费混合C  ...    5.15    5.148741  0.00%
15007  15008  019936  华安恒生互联网科技业ETF发起式联接(QDII)A  ...    1.39    1.390000  0.05%
15008  15009  020880                  天弘齐享债券发起D  ...    0.21    0.207645  0.06%
15009  15010  020738               安信宝利债券(LOF)C  ...    0.72    0.716648  0.00%
[15010 rows x 18 columns]
```

#### 场内交易基金排行榜

接口: fund_exchange_rank_em

目标地址: https://fund.eastmoney.com/data/fbsfundranking.html

描述: 东方财富网-数据中心-场内交易基金排行榜

限量: 单次返回当前时刻所有数据, 每个交易日 17 点后更新

输入参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| -   | -   | -   |

输出参数

| 名称   | 类型      | 描述      |
|------|---------|---------|
| 序号   | int64   | -       |
| 基金代码 | object  | -       |
| 基金简称 | object  | -       |
| 类型   | object  | -       |
| 日期   | object  | -       |
| 单位净值 | float64 | -       |
| 累计净值 | float64 | -       |
| 近1周  | float64 | 注意单位: % |
| 近1月  | float64 | 注意单位: % |
| 近3月  | float64 | 注意单位: % |
| 近6月  | float64 | 注意单位: % |
| 近1年  | float64 | 注意单位: % |
| 近2年  | float64 | 注意单位: % |
| 近3年  | float64 | 注意单位: % |
| 今年来  | float64 | 注意单位: % |
| 成立来  | float64 | 注意单位: % |
| 成立日期 | object  | -       |

接口示例

```python
import akshare as ak

fund_exchange_rank_em_df = ak.fund_exchange_rank_em()
print(fund_exchange_rank_em_df)
```

数据示例

```
      序号    基金代码                基金简称  ...    今年来     成立来        成立日期
0      1  513300  华夏纳斯达克100ETF(QDII)  ...   8.31   62.36  2020-10-22
1      2  159632  华安纳斯达克100ETF(QDII)  ...   6.52   46.67  2022-07-21
2      3  513100        国泰纳斯达克100ETF  ...   7.14  545.00  2013-04-25
3      4  159941        广发纳斯达克100ETF  ...   7.07  288.28  2015-06-10
4      5  513520        华夏野村日经225ETF  ...  11.69   42.39  2019-06-12
..   ...     ...                 ...  ...    ...     ...         ...
875  876  159573       华夏创业板中盘200ETF  ... -15.70  -15.30  2023-12-15
876  877  159577      汇添富MSCI美国50ETF  ...    NaN    0.13  2024-02-05
877  878  159549     天弘中证红利低波动100ETF  ...   3.48    2.38  2023-11-23
878  879  159562    华夏中证沪深港黄金产业股票ETF  ...    NaN    3.62  2024-01-11
879  880  560300        汇添富中证电信主题ETF  ...   4.35    2.64  2023-12-05
[880 rows x 17 columns]
```

#### 货币型基金排行

接口: fund_money_rank_em

目标地址: https://fund.eastmoney.com/data/hbxfundranking.html

描述: 东方财富网-数据中心-货币型基金排行

限量: 单次返回当前时刻所有数据, 每个交易日 17 点后更新, 货币基金的单位净值均为 1.0000 元，最新一年期定存利率: 1.50%

输入参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| -   | -   | -   |

输出参数

| 名称       | 类型      | 描述      |
|----------|---------|---------|
| 序号       | int64   | -       |
| 基金代码     | object  | -       |
| 基金简称     | object  | -       |
| 日期       | object  | -       |
| 万份收益     | float64 | 注意单位: % |
| 年化收益率7日  | float64 | 注意单位: % |
| 年化收益率14日 | float64 | 注意单位: % |
| 年化收益率28日 | float64 | 注意单位: % |
| 近1月      | float64 | 注意单位: % |
| 近3月      | float64 | 注意单位: % |
| 近6月      | float64 | 注意单位: % |
| 近1年      | float64 | 注意单位: % |
| 近2年      | float64 | 注意单位: % |
| 近3年      | float64 | 注意单位: % |
| 近5年      | float64 | 注意单位: % |
| 今年来      | float64 | 注意单位: % |
| 成立来      | float64 | 注意单位: % |
| 手续费      | object  | -       |

接口示例

```python
import akshare as ak

fund_money_rank_em_df = ak.fund_money_rank_em()
print(fund_money_rank_em_df)
```

数据示例

```
      序号  基金代码   基金简称          日期  ...    近5年   今年来    成立来    手续费
0      1  004121    兴银现金添利A  2024-02-28  ...   9.83  0.39  18.94  0.00%
1      2  002234  泰信天天收益货币B  2024-02-28  ...  11.96  0.39  22.74  0.00%
2      3  740602      长安货币B  2024-02-28  ...  11.56  0.37  40.86  0.00%
3      4  015972   中航航行宝货币B  2024-02-28  ...    NaN  0.39   3.78  0.00%
4      5  001821   兴全天添益货币B  2024-02-28  ...  13.18  0.39  27.46  0.00%
..   ...     ...        ...         ...  ...    ...   ...    ...    ...
539  540  019040      国联日盈C  2024-02-28  ...    NaN  0.44   1.29  0.00%
540  541  019145      长盛货币E  2024-02-28  ...    NaN  0.37   1.14  0.00%
541  542  019769  华商现金增利货币E  2024-02-28  ...    NaN  0.34   0.74  0.00%
542  543  019967    工银如意货币C  2024-02-28  ...    NaN  0.38   0.72  0.00%
543  544  020097      中加货币E  2024-02-28  ...    NaN  0.35   0.52  0.00%
[544 rows x 18 columns]
```

#### 理财基金排行

接口: fund_lcx_rank_em

目标地址: https://fund.eastmoney.com/data/lcxfundranking.html#t;c0;r;sSYL_Z;ddesc;pn50;f;os1;

描述: 东方财富网-数据中心-理财基金排行, 每个交易日17点后更新, 货币基金的单位净值均为 1.0000 元，最新一年期定存利率: 1.50%

限量: 由于目标网站没有数据，该接口暂时未能返回数据

输入参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| -   | -   | -   |

输出参数

| 名称       | 类型      | 描述      |
|----------|---------|---------|
| 序号       | int64   | -       |
| 基金代码     | object  | -       |
| 基金简称     | object  | -       |
| 日期       | object  | -       |
| 万份收益     | float64 | -       |
| 年化收益率7日  | float64 | 注意单位: % |
| 年化收益率14日 | float64 | 注意单位: % |
| 年化收益率28日 | float64 | 注意单位: % |
| 近1周      | float64 | 注意单位: % |
| 近1月      | float64 | 注意单位: % |
| 近3月      | float64 | 注意单位: % |
| 近6月      | float64 | 注意单位: % |
| 今年来      | float64 | 注意单位: % |
| 成立来      | float64 | 注意单位: % |
| 可购买      | float64 | 可购买     |
| 手续费      | object  | -       |

接口示例

```python
import akshare as ak

fund_lcx_rank_em_df = ak.fund_lcx_rank_em()
print(fund_lcx_rank_em_df)
```

数据示例

```
   序号    基金代码     基金简称          日期    万份收益  ...   近6月   今年来      成立来 可购买    手续费
0   1  531014  建信双周理财B  2021-01-17   0.783  ...  1.12  0.14  36.6531   1  0.00%
1   2  530014  建信双周理财A  2021-01-17  0.7035  ...  0.98  0.12   33.374   1  0.00%
```

#### 香港基金排行

接口: fund_hk_rank_em

目标地址: https://overseas.1234567.com.cn/FundList

描述: 东方财富网-数据中心-基金排行-香港基金排行

限量: 单次返回当前时刻所有数据

输入参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| -   | -   | -   |

输出参数

| 名称     | 类型      | 描述                       |
|--------|---------|--------------------------|
| 序号     | int64   | -                        |
| 基金代码   | object  | -                        |
| 基金简称   | object  | -                        |
| 币种     | object  | -                        |
| 日期     | object  | -                        |
| 单位净值   | float64 | -                        |
| 日增长率   | float64 | 注意单位: %                  |
| 近1周    | float64 | 注意单位: %                  |
| 近1月    | float64 | 注意单位: %                  |
| 近3月    | float64 | 注意单位: %                  |
| 近6月    | float64 | 注意单位: %                  |
| 近1年    | float64 | 注意单位: %                  |
| 近2年    | float64 | 注意单位: %                  |
| 近3年    | float64 | 注意单位: %                  |
| 今年来    | float64 | 注意单位: %                  |
| 成立来    | float64 | 注意单位: %                  |
| 可购买    | object  | -                        |
| 香港基金代码 | object  | 用于查询历史净值数据, 通过该字段查询相关的数据 |

接口示例

```python
import akshare as ak

fund_hk_rank_em_df = ak.fund_hk_rank_em()
print(fund_hk_rank_em_df)
```

数据示例

```
      序号    基金代码                       基金简称  ...      成立来   可购买      香港基金代码
0      1  968077             东方汇理创新动力股票基金分派  ...   2.5000  不可购买  1002185388
1      2  968062                 摩根太平洋科技人民币  ...  20.7000   可购买  1002075743
2      3  968063                  摩根太平洋科技美元  ...  16.9000  不可购买  1002075744
3      4  968075                百达策略收益累积CNY  ...  19.6950   可购买  1002185380
4      5  968061               摩根太平洋科技人民币对冲  ...  16.9000   可购买  1002075741
..   ...     ...                        ...  ...      ...   ...         ...
107  108  968031              中银香港全天候香港股票基金  ... -46.5613  不可购买  1001402919
108  109  968006                     行健宏扬中国  ... -17.9400   可购买  1000595430
109  110  968058  博时-安本标准精选新兴市场债券基金PRC类别A美元  ...      NaN  不可购买  1002059492
110  111  968060  博时-安本标准精选新兴市场债券基金PRC类别I美元  ...      NaN  不可购买  1002059495
111  112  968153                 南方东英精选美元债券  ...   1.6000  不可购买  1003656354
[112 rows x 18 columns]
```

### 基金业绩-雪球

接口: fund_individual_achievement_xq

目标地址: https://danjuanfunds.com/rn/funding/:code/RankInfo?symbol=000001&fd_type=2&btn_pos=1

描述: 雪球基金-基金详情-基金业绩-详情

限量: 单次返回单只基金业绩详情

输入参数

| 名称      | 类型    | 描述                      |
|---------|-------|-------------------------|
| symbol  | str   | symbol="000001"; 基金代码   |
| timeout | float | timeout=None; 默认不设置超时参数 |

输出参数

| 名称       | 类型      | 描述      |
|----------|---------|---------|
| 业绩类型     | object  | -       |
| 周期       | object  | -       |
| 本产品区间收益  | float64 | 注意单位: % |
| 本产品最大回撒  | float64 | 注意单位: % |
| 周期收益同类排名 | object  | -       |

接口示例

```python
import akshare as ak

fund_individual_achievement_xq_df = ak.fund_individual_achievement_xq(symbol="000001")
print(fund_individual_achievement_xq_df)
```

数据示例

```
    业绩类型  周期    本产品区间收益 本产品最大回撒 周期收益同类排名
0   年度业绩  成立以来  399.458300    54.55   128/7671
1   年度业绩  今年以来   -0.768251    26.58  4175/7674
2   年度业绩  2023  -21.990000    26.58  1631/1843
3   年度业绩  2022  -17.040000    27.87   872/1740
4   年度业绩  2021   -7.400000    21.63  1505/1625
5   年度业绩  2020   27.660000    14.39  1023/1549
6   年度业绩  2019   25.970000    11.81   854/1471
7   年度业绩  2018  -19.050000    23.65   761/1278
8   年度业绩  2017   17.110000     6.94   224/1068
9   年度业绩  2016  -22.720000    24.67    609/681
10  年度业绩  2015   25.860000    40.81    192/298
11  年度业绩  2014   15.270000    10.66    134/204
12  年度业绩  2013   15.710000     9.96     53/156
13  年度业绩  2012    7.250000    12.67     39/132
14  年度业绩  2011  -24.450000    29.07     81/114
15  年度业绩  2010    3.560000    16.23     61/103
16  年度业绩  2009   67.370000    21.29      38/87
17  年度业绩  2008  -44.020000    53.97      18/73
18  年度业绩  2007  130.730000    15.58      13/60
19  年度业绩  2006  118.050000    12.46      20/45
20  年度业绩  2005   -5.330000    18.35      32/33
21  年度业绩  2004    3.910000    18.06       5/16
22  年度业绩  2003   13.090000    12.68        7/7
23  年度业绩  2002   -3.090000    11.93        1/1
24  阶段业绩   近1月   -4.791167      NaN  6390/7643
25  阶段业绩   近3月  -11.731204    13.78  7244/7564
26  阶段业绩   近6月  -17.377404    19.72  5985/7313
27  阶段业绩   近1年  -22.592303    26.58  5891/6832
28  阶段业绩   近3年  -40.505159    48.55  3301/3783
29  阶段业绩   近5年   -3.250474    48.55  2300/2414
```

### 净值估算

接口: fund_value_estimation_em

目标地址: http://fund.eastmoney.com/fundguzhi.html

描述: 东方财富网-数据中心-净值估算

限量: 单次返回当前交易日指定 symbol 的所有数据

输入参数

| 名称     | 类型  | 描述                                                                                                    |
|--------|-----|-------------------------------------------------------------------------------------------------------|
| symbol | str | symbol='全部'; 默认返回所有数据; choice of {'全部', '股票型', '混合型', '债券型', '指数型', 'QDII', 'ETF联接', 'LOF', '场内交易基金'} |

输出参数

| 名称             | 类型    | 描述  |
|----------------|-------|-----|
| 序号             | str   | -   |
| 基金代码           | str   | -   |
| 基金名称           | str   | -   |
| 交易日-估算数据-估算值   | float | -   |
| 交易日-估算数据-估算增长率 | str   | -   |
| 交易日-公布数据-单位净值  | float | -   |
| 交易日-公布数据-日增长率  | str   | -   |
| 估算偏差           | str   | -   |
| 交易日-单位净值       | str   | -   |

接口示例

```python
import akshare as ak

fund_value_estimation_em_df = ak.fund_value_estimation_em(symbol="混合型")
print(fund_value_estimation_em_df)
```

数据示例

```
        序号    基金代码  ...    估算偏差 2021-02-18-单位净值
0        1  164403  ...   0.06%          1.6720
1        2  180020  ...  -0.34%          1.9700
2        3  161810  ...  -0.47%          3.3300
3        4  009394  ...  -1.04%          1.0705
4        5  001763  ...   1.37%          1.5990
    ...     ...  ...     ...             ...
4846  4847  010895  ...     ---             ---
4847  4848  010875  ...     ---             ---
4848  4849  010718  ...     ---             ---
4849  4850  010717  ...     ---             ---
4850  4851  011059  ...     ---             ---
```

### 基金数据分析

接口: fund_individual_analysis_xq

目标地址: https://danjuanfunds.com/funding/000001

描述: 雪球基金-基金详情-数据分析

限量: 返回单只基金历史表现分析数据

输入参数

| 名称      | 类型    | 描述                      |
|---------|-------|-------------------------|
| symbol  | str   | symbol="000001"; 基金代码   |
| timeout | float | timeout=None; 默认不设置超时参数 |

输出参数

| 名称       | 类型      | 描述     |
|----------|---------|--------|
| 周期       | object  | -      |
| 较同类风险收益比 | int64   | 注意单位：% |
| 较同类抗风险波动 | int64   | 注意单位：% |
| 年化波动率    | float64 | 注意单位：% |
| 年化夏普比率   | float64 | -      |
| 最大回撤     | float64 | 注意单位：% |

接口示例

```python
import akshare as ak

fund_individual_analysis_xq_df = ak.fund_individual_analysis_xq(symbol="000001")
print(fund_individual_analysis_xq_df)
```

数据示例

```
   周期  较同类风险收益比 较同类抗风险波动  年化波动率  年化夏普比率   最大回撤
0  近1年         3        61  12.72   -1.89  26.58
1  近3年         9        56  18.66   -0.93  48.55
2  近5年         2        57  19.04   -0.11  48.55
```

### 基金盈利概率

接口: fund_individual_profit_probability_xq

目标地址: https://danjuanfunds.com/funding/000001

描述: 雪球基金-基金详情-盈利概率；历史任意时点买入，持有满X时间，盈利概率，以及平均收益

限量: 单次返回单只基金历史任意时点买入，持有满 X 时间，盈利概率，以及平均收益

输入参数

| 名称      | 类型    | 描述                      |
|---------|-------|-------------------------|
| symbol  | str   | symbol="000001"; 基金代码   |
| timeout | float | timeout=None; 默认不设置超时参数 |

输出参数

| 名称   | 类型     | 描述     |
|------|--------|--------|
| 持有时长 | object | -      |
| 盈利概率 | object | 注意单位：% |
| 平均收益 | object | 注意单位：% |

接口示例

```python
import akshare as ak

fund_individual_profit_probability_xq_df = ak.fund_individual_profit_probability_xq(symbol="000001")
print(fund_individual_profit_probability_xq_df)
```

数据示例

```
  持有时长  盈利概率 平均收益
0  满6个月    53   5.97
1   满1年    59  14.23
2   满2年    66  32.34
3   满3年    76  51.16
```

### 基金持仓资产比例

接口: fund_individual_detail_hold_xq

目标地址: https://danjuanfunds.com/rn/fund-detail/archive?id=103&code=000001

描述: 雪球基金-基金详情-基金持仓-详情

限量: 单次返回单只基金指定日期的持仓大类资产比例

输入参数

| 名称      | 类型    | 描述                      |
|---------|-------|-------------------------|
| symbol  | str   | symbol="000001"; 基金代码   |
| date    | str   | date="20231231"; 季度日期   |
| timeout | float | timeout=None; 默认不设置超时参数 |

输出参数

| 名称   | 类型      | 描述     |
|------|---------|--------|
| 资产类型 | object  | -      |
| 仓位占比 | float64 | 注意单位：% |

接口示例

```python
import akshare as ak

fund_individual_detail_hold_xq_df = ak.fund_individual_detail_hold_xq(symbol="002804", date="20231231")
print(fund_individual_detail_hold_xq_df)
```

数据示例

```
  资产类型   仓位占比
0   股票  51.95
1   现金  19.51
2   其他  29.09
```

### 基金交易费率

接口: fund_fee_em

目标地址: https://fundf10.eastmoney.com/jjfl_015641.html

描述: 天天基金-基金档案-购买信息

限量: 单次返回指定 symbol 的 indicator 数据

输入参数

| 名称        | 类型  | 描述                                                                                       |
|-----------|-----|------------------------------------------------------------------------------------------|
| symbol    | str | symbol="015641"; 基金代码                                                                    |
| indicator | str | indicator="申购费率"; choice of {"交易状态", "申购与赎回金额", "交易确认日", "运作费用", "认购费率", "申购费率", "赎回费率"} |

输出参数

| 名称    | 类型      | 描述 |
|-------|---------|----|
| 费用类型  | object  | -  |
| 条件或名称 | object  | -  |
| 费用    | float64 | -  |

接口示例

```python
import akshare as ak

fund_fee_em_df = fund_fee_em(symbol="015641", indicator="认购费率")
print(fund_fee_em_df)
```

数据示例

```
                适用金额 适用期限      原费率 天天基金优惠费率
0            小于100万元  ---    1.20%    0.12%
1  大于等于100万元，小于200万元  ---    0.80%    0.08%
2  大于等于200万元，小于500万元  ---    0.50%    0.05%
3          大于等于500万元  ---  每笔1000元  每笔1000元
```

### 基金交易规则

接口: fund_individual_detail_info_xq

目标地址: https://danjuanfunds.com/djapi/fund/detail/675091

描述: 雪球基金-基金详情-基金交易规则

限量: 单次返回单只基金基金交易规则

输入参数

| 名称      | 类型    | 描述                      |
|---------|-------|-------------------------|
| symbol  | str   | symbol="000001"; 基金代码   |
| timeout | float | timeout=None; 默认不设置超时参数 |

输出参数

| 名称    | 类型      | 描述 |
|-------|---------|----|
| 费用类型  | object  | -  |
| 条件或名称 | object  | -  |
| 费用    | float64 | -  |

接口示例

```python
import akshare as ak

fund_individual_detail_info_xq_df = ak.fund_individual_detail_info_xq(symbol="000001")
print(fund_individual_detail_info_xq_df)
```

数据示例

```
   费用类型                 条件或名称      费用
0  买入规则      0.0万<买入金额<100.0万     1.5
1  买入规则   100.0万<=买入金额<500.0万     1.2
2  买入规则  500.0万<=买入金额<1000.0万     0.8
3  买入规则         1000.0万<=买入金额  1000.0
4  卖出规则        0.0天<持有期限<7.0天     1.5
5  卖出规则            7.0天<=持有期限     0.5
6  其他费用                 基金管理费     1.2
7  其他费用                 基金托管费     0.2
```

### 基金持仓

接口: fund_portfolio_hold_em

目标地址: https://fundf10.eastmoney.com/ccmx_000001.html

描述: 天天基金网-基金档案-投资组合-基金持仓

限量: 单次返回指定 symbol 和 date 的所有持仓数据

输入参数

| 名称     | 类型  | 描述                                                       |
|--------|-----|----------------------------------------------------------|
| symbol | str | symbol="000001"; 基金代码, 可以通过调用 **ak.fund_name_em()** 接口获取 |
| date   | str | date="2024"; 指定年份                                        |

输出参数

| 名称    | 类型      | 描述       |
|-------|---------|----------|
| 序号    | int64   | -        |
| 股票代码  | object  | -        |
| 股票名称  | object  | -        |
| 占净值比例 | float64 | 注意单位: %  |
| 持股数   | float64 | 注意单位: 万股 |
| 持仓市值  | float64 | 注意单位: 万元 |
| 季度    | object  | -        |

接口示例

```python
import akshare as ak

fund_portfolio_hold_em_df = ak.fund_portfolio_hold_em(symbol="000001", date="2024")
print(fund_portfolio_hold_em_df)
```

数据示例

```
   序号 股票代码  股票名称  占净值比例 持股数 持仓市值              季度
0   1  002025   航天电器   3.46  209.92  7947.67  2024年1季度股票投资明细
1   2  600862   中航高科   3.24  380.43  7441.16  2024年1季度股票投资明细
2   3  600941   中国移动   2.86   62.11  6568.75  2024年1季度股票投资明细
3   4  300395    菲利华   2.80  216.80  6417.42  2024年1季度股票投资明细
4   5  300034   钢研高纳   2.69  403.16  6168.33  2024年1季度股票投资明细
5   6  002371   北方华创   2.67   20.07  6134.03  2024年1季度股票投资明细
6   7  002475   立讯精密   2.30  179.77  5287.04  2024年1季度股票投资明细
7   8  600276   恒瑞医药   2.22  111.06  5105.35  2024年1季度股票投资明细
8   9  600522   中天科技   1.99  325.78  4570.69  2024年1季度股票投资明细
9  10  000100  TCL科技   1.82  893.37  4172.03  2024年1季度股票投资明细
```

### 债券持仓

接口: fund_portfolio_bond_hold_em

目标地址: https://fundf10.eastmoney.com/ccmx_000001.html

描述: 天天基金网-基金档案-投资组合-债券持仓

限量: 单次返回指定 symbol 和 date 的所有持仓数据

输入参数

| 名称     | 类型  | 描述                                                       |
|--------|-----|----------------------------------------------------------|
| symbol | str | symbol="000001"; 基金代码, 可以通过调用 **ak.fund_name_em()** 接口获取 |
| date   | str | date="2023"; 指定年份                                        |

输出参数

| 名称    | 类型      | 描述       |
|-------|---------|----------|
| 序号    | int64   | -        |
| 债券代码  | object  | -        |
| 债券名称  | object  | -        |
| 占净值比例 | float64 | 注意单位: %  |
| 持仓市值  | float64 | 注意单位: 万元 |
| 季度    | object  | -        |

接口示例

```python
import akshare as ak

fund_portfolio_bond_hold_em_df = ak.fund_portfolio_bond_hold_em(symbol="000001", date="2023")
print(fund_portfolio_bond_hold_em_df)
```

数据示例

```
    序号       债券代码         债券名称  占净值比例      持仓市值              季度
0    1     230304       23进出04   4.05  11047.16  2023年3季度债券投资明细
1    2  101564021  15华能集MTN002   3.77  10301.03  2023年3季度债券投资明细
2    3  101901385  19中石油MTN006   3.45   9405.76  2023年3季度债券投资明细
3    4     220216       22国开16   2.97   8119.40  2023年3季度债券投资明细
4    5     220411       22农发11   1.86   5088.53  2023年3季度债券投资明细
5    6     113648         巨星转债   0.06    175.04  2023年3季度债券投资明细
6    7     113563         柳药转债   0.04    104.69  2023年3季度债券投资明细
7    8     123117         健帆转债   0.02     61.57  2023年3季度债券投资明细
8    9     113633         科沃转债   0.02     66.63  2023年3季度债券投资明细
9   10     127073         天赐转债   0.02     55.47  2023年3季度债券投资明细
10  11     123119         康泰转2   0.02     50.93  2023年3季度债券投资明细
11  12     113661        福22转债   0.01     40.55  2023年3季度债券投资明细
12  13     113061         拓普转债   0.01     22.23  2023年3季度债券投资明细
13  14     128134         鸿路转债   0.01     35.97  2023年3季度债券投资明细
14  15     123114         三角转债   0.01     31.16  2023年3季度债券投资明细
15  16     118019         金盘转债   0.00      2.39  2023年3季度债券投资明细
16  17     118031        天23转债   0.00      0.21  2023年3季度债券投资明细
17  18     123107         温氏转债   0.00      0.02  2023年3季度债券投资明细
18  19     220014     22附息国债14   3.83  11197.77  2023年2季度债券投资明细
19  20  101564021  15华能集MTN002   3.50  10239.61  2023年2季度债券投资明细
20  21  101901385  19中石油MTN006   3.20   9351.59  2023年2季度债券投资明细
21  22     220216       22国开16   2.77   8084.53  2023年2季度债券投资明细
22  23     200207       20国开07   2.11   6168.95  2023年2季度债券投资明细
23  24     123114         三角转债   0.15    445.84  2023年2季度债券投资明细
24  25     113648         巨星转债   0.06    184.35  2023年2季度债券投资明细
25  26     113563         柳药转债   0.04    108.85  2023年2季度债券投资明细
26  27     113633         科沃转债   0.02     67.83  2023年2季度债券投资明细
27  28     123119         康泰转2   0.02     47.93  2023年2季度债券投资明细
28  29     123117         健帆转债   0.02     60.68  2023年2季度债券投资明细
29  30     127073         天赐转债   0.02     62.25  2023年2季度债券投资明细
30  31     113061         拓普转债   0.01     22.68  2023年2季度债券投资明细
31  32     113661        福22转债   0.01     43.46  2023年2季度债券投资明细
32  33     128134         鸿路转债   0.01     36.04  2023年2季度债券投资明细
33  34     118019         金盘转债   0.00      2.30  2023年2季度债券投资明细
34  35     123107         温氏转债   0.00      0.03  2023年2季度债券投资明细
35  36     220014     22附息国债14   3.80  11139.61  2023年1季度债券投资明细
36  37  101564021  15华能集MTN002   3.57  10476.87  2023年1季度债券投资明细
37  38  101901385  19中石油MTN006   3.15   9245.03  2023年1季度债券投资明细
38  39     220216       22国开16   2.74   8027.62  2023年1季度债券投资明细
39  40     200207       20国开07   2.09   6133.08  2023年1季度债券投资明细
40  41     123114         三角转债   0.15    444.77  2023年1季度债券投资明细
41  42     113648         巨星转债   0.06    176.94  2023年1季度债券投资明细
42  43     113563         柳药转债   0.04    107.36  2023年1季度债券投资明细
43  44     127073         天赐转债   0.02     61.26  2023年1季度债券投资明细
44  45     123117         健帆转债   0.02     67.79  2023年1季度债券投资明细
45  46     113633         科沃转债   0.02     67.44  2023年1季度债券投资明细
46  47     123119         康泰转2   0.02     51.18  2023年1季度债券投资明细
47  48     113061         拓普转债   0.01     22.16  2023年1季度债券投资明细
48  49     128134         鸿路转债   0.01     37.60  2023年1季度债券投资明细
49  50     118019         金盘转债   0.00      2.54  2023年1季度债券投资明细
50  51     123107         温氏转债   0.00      0.03  2023年1季度债券投资明细
```

### 行业配置

接口: fund_portfolio_industry_allocation_em

目标地址: https://fundf10.eastmoney.com/hytz_000001.html

描述: 天天基金网-基金档案-投资组合-行业配置

限量: 单次返回指定 symbol 和 date 的所有持仓数据

输入参数

| 名称     | 类型  | 描述                                                       |
|--------|-----|----------------------------------------------------------|
| symbol | str | symbol="000001"; 基金代码, 可以通过调用 **ak.fund_name_em()** 接口获取 |
| date   | str | date="2023"; 指定年份                                        |

输出参数

| 名称    | 类型      | 描述       |
|-------|---------|----------|
| 序号    | int64   | -        |
| 行业类别  | object  | -        |
| 占净值比例 | float64 | 注意单位: %  |
| 市值    | float64 | 注意单位: 万元 |
| 截止时间  | object  | -        |

接口示例

```python
import akshare as ak

fund_portfolio_industry_allocation_em_df = ak.fund_portfolio_industry_allocation_em(symbol="000001", date="2023")
print(fund_portfolio_industry_allocation_em_df)
```

数据示例

```
    序号              行业类别  占净值比例             市值        截止时间
0    1               制造业  69.53  189787.963063  2023-09-30
1    2        科学研究和技术服务业   1.61    4388.935702  2023-09-30
2    3            批发和零售业   1.43    3896.705281  2023-09-30
3    4               金融业   1.17    3195.408100  2023-09-30
4    5               采矿业   1.17    3194.758900  2023-09-30
5    6              房地产业   0.96    2629.990410  2023-09-30
6    7          租赁和商务服务业   0.62    1688.184585  2023-09-30
7    8   信息传输、软件和信息技术服务业   0.52    1423.173173  2023-09-30
8    9               建筑业   0.21     571.626784  2023-09-30
9   10           卫生和社会工作   0.20     538.414546  2023-09-30
10  11     水利、环境和公共设施管理业   0.00       4.560998  2023-09-30
11  12  电力、热力、燃气及水生产和供应业   0.00       3.356397  2023-09-30
12  13               制造业  68.81  201157.775395  2023-06-30
13  14            批发和零售业   1.41    4129.958752  2023-06-30
14  15              房地产业   1.39    4067.173378  2023-06-30
15  16        科学研究和技术服务业   1.26    3684.969783  2023-06-30
16  17               采矿业   0.93    2716.791300  2023-06-30
17  18   信息传输、软件和信息技术服务业   0.78    2268.536719  2023-06-30
18  19               金融业   0.67    1962.987600  2023-06-30
19  20          租赁和商务服务业   0.35    1021.826600  2023-06-30
20  21               建筑业   0.34     982.386764  2023-06-30
21  22           卫生和社会工作   0.33     972.647166  2023-06-30
22  23  电力、热力、燃气及水生产和供应业   0.18     525.067947  2023-06-30
23  24     水利、环境和公共设施管理业   0.00       2.256657  2023-06-30
24  25               制造业  65.89  193354.962582  2023-03-31
25  26        科学研究和技术服务业   3.92   11490.739335  2023-03-31
26  27          租赁和商务服务业   1.90    5584.193500  2023-03-31
27  28   信息传输、软件和信息技术服务业   1.45    4256.190176  2023-03-31
28  29               金融业   1.35    3952.021810  2023-03-31
29  30            批发和零售业   0.90    2635.429240  2023-03-31
30  31              房地产业   0.86    2519.415738  2023-03-31
31  32               建筑业   0.24     708.283312  2023-03-31
32  33       交通运输、仓储和邮政业   0.24     699.449400  2023-03-31
33  34         文化、体育和娱乐业   0.23     669.061400  2023-03-31
34  35           卫生和社会工作   0.20     596.362379  2023-03-31
35  36  电力、热力、燃气及水生产和供应业   0.01      36.160320  2023-03-31
36  37     水利、环境和公共设施管理业   0.00      10.346959  2023-03-31
```

### 重大变动

接口: fund_portfolio_change_em

目标地址: https://fundf10.eastmoney.com/ccbd_000001.html

描述: 天天基金网-基金档案-投资组合-重大变动

限量: 单次返回指定 symbol、indicator 和 date 的所有重大变动数据

输入参数

| 名称        | 类型  | 描述                                                       |
|-----------|-----|----------------------------------------------------------|
| symbol    | str | symbol="003567"; 基金代码, 可以通过调用 **ak.fund_name_em()** 接口获取 |
| indicator | str | indicator="累计买入"; choice of {"累计买入", "累计卖出"}             |
| date      | str | date="2023"; 指定年份                                        |

输出参数

| 名称          | 类型      | 描述       |
|-------------|---------|----------|
| 序号          | int64   | -        |
| 股票代码        | object  | -        |
| 股票名称        | object  | -        |
| 本期累计买入金额    | float64 | 注意单位: 万元 |
| 占期初基金资产净值比例 | float64 | 注意单位: %  |
| 季度          | object  | -        |

接口示例

```python
import akshare as ak

fund_portfolio_change_em_df = ak.fund_portfolio_change_em(symbol="003567", indicator="累计买入", date="2023")
print(fund_portfolio_change_em_df)
```

数据示例

```
    序号    股票代码  股票名称  本期累计买入金额  占期初基金资产净值比例    季度
0    1  000063  中兴通讯  45959.42         4.19  2023年2季度累计买入股票明细
1    2  002006  精工科技  42754.23         3.90  2023年2季度累计买入股票明细
2    3  603533  掌阅科技  33449.27         3.05  2023年2季度累计买入股票明细
3    4  688036  传音控股  28652.88         2.61  2023年2季度累计买入股票明细
4    5  688258  卓易信息  28106.85         2.56  2023年2季度累计买入股票明细
5    6  300476  胜宏科技  26217.00         2.39  2023年2季度累计买入股票明细
6    7  300166  东方国信  23755.48         2.17  2023年2季度累计买入股票明细
7    8  300212   易华录  23142.50         2.11  2023年2季度累计买入股票明细
8    9  002291  遥望科技  23005.45         2.10  2023年2季度累计买入股票明细
9   10  688111  金山办公  23023.65         2.10  2023年2季度累计买入股票明细
10  11  300383  光环新网  19910.32         1.82  2023年2季度累计买入股票明细
11  12  300101  振芯科技  19625.59         1.79  2023年2季度累计买入股票明细
12  13  688596  正帆科技  19312.14         1.76  2023年2季度累计买入股票明细
13  14  002865  钧达股份  18848.16         1.72  2023年2季度累计买入股票明细
14  15  605377  华旺科技  18064.00         1.65  2023年2季度累计买入股票明细
15  16  605589  圣泉集团  18005.08         1.64  2023年2季度累计买入股票明细
16  17  603806   福斯特  17598.39         1.61  2023年2季度累计买入股票明细
17  18  300613   富瀚微  17566.87         1.60  2023年2季度累计买入股票明细
18  19  002919  名臣健康  16740.63         1.53  2023年2季度累计买入股票明细
19  20  600641  万业企业  16500.35         1.51  2023年2季度累计买入股票明细
```

### 基金评级

#### 基金评级总汇

接口: fund_rating_all

目标地址: https://fund.eastmoney.com/data/fundrating.html

描述: 天天基金网-基金评级-基金评级总汇

限量: 单次返回所有基金评级数据

输入参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| -   | -   | -   |

输出参数

| 名称     | 类型      | 描述  |
|--------|---------|-----|
| 代码     | object  | -   |
| 简称     | object  | -   |
| 基金经理   | object  | -   |
| 基金公司   | object  | -   |
| 5星评级家数 | int64   | -   |
| 上海证券   | float64 | -   |
| 招商证券   | float64 | -   |
| 济安金信   | float64 | -   |
| 手续费    | float64 | -   |
| 类型     | object  | -   |

接口示例

```python
import akshare as ak

fund_rating_all_df = ak.fund_rating_all()
print(fund_rating_all_df)
```

数据示例

```
          代码             简称     基金经理  基金公司  ...  招商证券  济安金信     手续费       类型
0     270007       广发大盘成长混合       李巍    广发  ...   4.0   4.0  0.0015   混合型-灵活
1     270009        广发增强债券C    张芊,方抗    广发  ...   3.0   3.0  0.0000  债券型-混合债
2     270010  广发沪深300ETF联接A      霍华明    广发  ...   4.0   4.0  0.0012   指数型-股票
3     270014          广发货币B      温秀娟    广发  ...   NaN   5.0  0.0000      货币型
4     270025      广发行业领先混合A       程琨    广发  ...   3.0   4.0  0.0015   混合型-偏股
      ...            ...      ...   ...  ...   ...   ...     ...      ...
7418  000351   国富恒丰一年持有期债券A       王莉    国海  ...   3.0   3.0  0.0007   债券型-长债
7419  540009     汇丰晋信消费红利股票  范坤祥,费馨涵  汇丰晋信  ...   3.0   3.0  0.0015      股票型
7420  519726      交银稳固收益债券A      周珊珊    交银  ...   4.0   3.0  0.0008  债券型-混合债
7421  004276    浦银安和回报定开混合A       赵楠    浦银  ...   3.0   3.0  0.0012   混合型-偏债
7422  000075    华夏恒生ETF联接现汇       徐猛    华夏  ...   NaN   2.0  0.0000     QDII
[7423 rows x 10 columns]
```

#### 上海证券评级

接口: fund_rating_sh

目标地址: https://fund.eastmoney.com/data/fundrating_3.html

描述: 天天基金网-基金评级-上海证券评级

限量: 单次返回指定交易日的所有基金评级数据

输入参数

| 名称   | 类型  | 描述                                                                        |
|------|-----|---------------------------------------------------------------------------|
| date | str | date='20230630'; https://fund.eastmoney.com/data/fundrating_3.html 获取查询日期 |

输出参数

| 名称         | 类型      | 描述      |
|------------|---------|---------|
| 代码         | object  | -       |
| 简称         | object  | -       |
| 基金经理       | object  | -       |
| 基金公司       | object  | -       |
| 3年期评级-3年评级 | int64   | -       |
| 3年期评级-较上期  | float64 | -       |
| 5年期评级-5年评级 | float64 | -       |
| 5年期评级-较上期  | float64 | -       |
| 单位净值       | float64 | -       |
| 日期         | object  | -       |
| 日增长率       | float64 | 注意单位: % |
| 近1年涨幅      | float64 | 注意单位: % |
| 近3年涨幅      | float64 | 注意单位: % |
| 近5年涨幅      | float64 | 注意单位: % |
| 手续费        | object  | -       |
| 类型         | object  | -       |

接口示例

```python
import akshare as ak

fund_rating_sh_df = ak.fund_rating_sh(date='20230630')
print(fund_rating_sh_df)
```

数据示例

```
      代码           简称     基金经理    基金公司  ...  近3年涨幅  近5年涨幅    手续费  类型
0     000004     中海可转债债券C       章俊    中海基金  ...  -8.00  24.14  0.00%
1     000003     中海可转债债券A       章俊    中海基金  ...  -6.77  25.94  0.08%
2     000014      华夏聚利债券A      何家琪    华夏基金  ...  27.31  61.22  0.06%
3     000015      华夏纯债债券A      柳万军    华夏基金  ...  10.16  22.21  0.08%
4     000046     工银产业债债券B      何秀红  工银瑞信基金  ...   8.79  26.21  0.00%
      ...          ...      ...     ...  ...    ...    ...    ...  ..
4632  540009   汇丰晋信消费红利股票  范坤祥,费馨涵  汇丰晋信基金  ...  -5.45  39.22  0.15%
4633  540010   汇丰晋信科技先锋股票       陈平  汇丰晋信基金  ... -30.95  31.16  0.15%
4634  960000    汇丰晋信大盘股票H      闵良超  汇丰晋信基金  ...    NaN    NaN
4635  671030   西部利得事件驱动股票      张昌平  西部利得基金  ...  35.24    NaN  0.15%
4636  673090  西部利得个股精选股票A      童国林  西部利得基金  ...   3.18  50.24  0.12%
[4637 rows x 16 columns]
```

#### 招商证券评级

接口: fund_rating_zs

目标地址: http://fund.eastmoney.com/data/fundrating_2.html

描述: 天天基金网-基金评级-招商证券评级

限量: 单次返回指定交易日的所有基金评级数据

输入参数

| 名称   | 类型  | 描述                                                                        |
|------|-----|---------------------------------------------------------------------------|
| date | str | date='20230331'; https://fund.eastmoney.com/data/fundrating_2.html 获取查询日期 |

输出参数

| 名称         | 类型      | 描述      |
|------------|---------|---------|
| 代码         | object  | -       |
| 简称         | object  | -       |
| 基金经理       | object  | -       |
| 基金公司       | object  | -       |
| 3年期评级-3年评级 | int64   | -       |
| 3年期评级-较上期  | float64 | -       |
| 单位净值       | float64 | -       |
| 日期         | object  | -       |
| 日增长率       | float64 | 注意单位: % |
| 近1年涨幅      | float64 | 注意单位: % |
| 近3年涨幅      | float64 | 注意单位: % |
| 近5年涨幅      | float64 | 注意单位: % |
| 手续费        | object  | -       |

接口示例

```python
import akshare as ak

fund_rating_zs_df = ak.fund_rating_zs(date='20230331')
print(fund_rating_zs_df)
```

数据示例

```
      代码               简称     基金经理  基金公司  ...  近1年涨幅  近3年涨幅   近5年涨幅    手续费
0     005161        华商上游产业股票A       童立  华商基金  ...  -1.82  84.44  153.39  0.15%
1     005660        嘉实资源精选股票A      苏文杰  嘉实基金  ...  -3.08  76.04     NaN  0.15%
2     007130         中庚小盘价值股票      丘栋荣  中庚基金  ...   4.65  94.52     NaN  1.50%
3     320020         诺安策略精选股票       李迪  诺安基金  ...   1.63  44.76  102.39  0.15%
4     007538         永赢沪深300A       万纯  永赢基金  ...  -9.77   4.63     NaN  0.10%
      ...              ...      ...   ...  ...    ...    ...     ...    ...
3319  162714  广发深证100指数(LOF)A      陆志明  广发基金  ... -17.19 -10.96   30.65  0.12%
3320  070009         嘉实超短债债券C      李金灿  嘉实基金  ...   2.12   6.91   13.97  0.00%
3321  000227         华安年年红债券A      石雨欣  华安基金  ...   1.45   6.83   27.18  0.06%
3322  002864        广发安泽短债债券A      刘志辉  广发基金  ...   2.70   8.07   16.11  0.04%
3323  000239        华安年年盈定开债A  朱才敏,魏媛媛  华安基金  ...   0.50   8.18   32.63  0.06%
[3324 rows x 13 columns]
```

#### 济安金信评级

接口: fund_rating_ja

目标地址: https://fund.eastmoney.com/data/fundrating_4.html

描述: 天天基金网-基金评级-济安金信评级

限量: 单次返回指定交易日的所有基金评级数据

输入参数

| 名称   | 类型  | 描述                                                                        |
|------|-----|---------------------------------------------------------------------------|
| date | str | date='20200930'; https://fund.eastmoney.com/data/fundrating_4.html 获取查询日期 |

输出参数

| 名称         | 类型      | 描述      |
|------------|---------|---------|
| 代码         | object  | -       |
| 简称         | object  | -       |
| 基金经理       | object  | -       |
| 基金公司       | object  | -       |
| 3年期评级-3年评级 | int64   | -       |
| 3年期评级-较上期  | float64 | -       |
| 单位净值       | float64 | -       |
| 日期         | object  | -       |
| 日增长率       | float64 | 注意单位: % |
| 近1年涨幅      | float64 | 注意单位: % |
| 近3年涨幅      | float64 | 注意单位: % |
| 近5年涨幅      | float64 | 注意单位: % |
| 手续费        | object  | -       |
| 类型         | object  | -       |

接口示例

```python
import akshare as ak

fund_rating_ja_df = ak.fund_rating_ja(date='20200930')
print(fund_rating_ja_df)
```

数据示例

```
          代码           简称     基金经理    基金公司  ...  近3年涨幅  近5年涨幅    手续费   类型
0     000005   嘉实增强信用定期债券       轩璇    嘉实基金  ...  10.34  25.83  0.08%  纯债型
1     000010   易方达天天理财货币B  石大怿,刘朝阳   易方达基金  ...   6.83  13.06  0.00%  货币型
2     000006  西部利得量化成长混合A      盛丰衍  西部利得基金  ...  46.57    NaN  0.15%  混合型
3     000011    华夏大盘精选混合A      陈伟彦    华夏基金  ...  12.92  48.80  0.15%  混合型
4     000009   易方达天天理财货币A  石大怿,刘朝阳   易方达基金  ...   6.06  11.71  0.00%  货币型
      ...          ...      ...     ...  ...    ...    ...    ...  ...
7718  690005   民生加银内需增长混合      柳世庆  民生加银基金  ...  -6.02  66.62  0.15%  混合型
7719  690010  民生加银现金增利货币A   张玓,郑雅洁  民生加银基金  ...   5.49  10.74  0.00%  货币型
7720  690009   民生加银红利回报混合   王亮,邓凯成  民生加银基金  ...  -0.87  37.61  0.15%  混合型
7721  690210  民生加银现金增利货币B   张玓,郑雅洁  民生加银基金  ...   6.25  12.09  0.00%  货币型
7722  690202    民生增强收益债券C      谢志华  民生加银基金  ...   0.41  24.72  0.00%  二级债
[7723 rows x 14 columns]
```

### 基金经理

接口: fund_manager_em

目标地址: https://fund.eastmoney.com/manager/default.html

描述: 天天基金网-基金数据-基金经理大全

限量: 单次返回所有基金经理数据

输入参数

| 名称 | 类型 | 描述 |
|----|----|----|
| -  | -  | -  |

输出参数

| 名称        | 类型      | 描述       |
|-----------|---------|----------|
| 序号        | int64   | -        |
| 姓名        | object  | -        |
| 所属公司      | object  | -        |
| 现任基金      | object  | -        |
| 累计从业时间    | int64   | 注意单位: 天  |
| 现任基金资产总规模 | float64 | 注意单位: 亿元 |
| 现任基金最佳回报  | float64 | 注意单位: %  |

接口示例

```python
import akshare as ak

fund_manager_em_df = ak.fund_manager_em()
print(fund_manager_em_df)
```

数据示例

```
       序号   姓名    所属公司             现任基金  累计从业时间  现任基金资产总规模  现任基金最佳回报
0         1   安安    中金公司          中金恒瑞债券A     704      34.61      6.19
1         1   安安    中金公司    中金安心回报灵活配置混合A     704      34.61      6.19
2         1   安安    中金公司    中金安心回报灵活配置混合C     704      34.61      6.19
3         1   安安    中金公司          中金恒瑞债券C     704      34.61      6.19
4         2  艾邦妮    华夏基金           华夏红利混合     431      54.88    -12.57
     ...  ...     ...              ...     ...        ...       ...
25460  3589  祝祯哲    富国基金  富国利享回报12个月持有混合A     216      46.98     -1.67
25461  3589  祝祯哲    富国基金  富国利享回报12个月持有混合C     216      46.98     -1.67
25462  3589  祝祯哲    富国基金         富国可转换债券E     216      46.98     -1.67
25463  3589  祝祯哲    富国基金           富国可转债A     216      46.98     -1.67
25464  3590  周宗舟  汇丰晋信基金         汇丰晋信创新先锋     176       0.68     -1.74
[25465 rows x 7 columns]
```

### 新发基金

接口: fund_new_found_em

目标地址: https://fund.eastmoney.com/data/xinfound.html

描述: 天天基金网-基金数据-新发基金-新成立基金

限量: 单次返回所有新发基金数据

输入参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| -   | -   | -   |

输出参数

| 名称    | 类型      | 描述       |
|-------|---------|----------|
| 基金代码  | object  | -        |
| 基金简称  | object  | -        |
| 发行公司  | object  | -        |
| 基金类型  | object  | -        |
| 集中认购期 | object  | -        |
| 募集份额  | float64 | 注意单位: 亿份 |
| 成立日期  | object  | -        |
| 成立来涨幅 | float64 | 注意单位: %  |
| 基金经理  | object  | -        |
| 申购状态  | object  | -        |
| 优惠费率  | float64 | 注意单位: %  |

接口示例

```python
import akshare as ak

fund_new_found_em_df = ak.fund_new_found_em()
print(fund_new_found_em_df)
```

数据示例

```
        基金代码                      基金简称    发行公司  ...   基金经理  申购状态  优惠费率
0     019879              万家周期驱动股票发起式A    万家基金  ...     叶勇  开放申购  0.15
1     019873                     长城短债E    长城基金  ...    邹德立   限大额  0.00
2     019872                     长城短债D    长城基金  ...    邹德立   限大额  0.03
3     019880              万家周期驱动股票发起式C    万家基金  ...     叶勇  开放申购  0.00
4     019671  广发中证香港创新药ETF发起式联接(QDII)C    广发基金  ...     刘杰  开放申购  0.00
      ...                       ...     ...  ...    ...   ...   ...
7310  008411                  博时富信纯债债券    博时基金  ...     陈黎   限大额  0.08
7311  008135                  华宸未来价值先锋  华宸未来基金  ...    张翼翔  开放申购  0.15
7312  007729     招商普盛全球配置(QDII-FOF)人民币    招商基金  ...    范刚强  开放申购  0.15
7313  008055            汇添富中债7-10年国开债C   汇添富基金  ...  何旻,李伟  开放申购  0.00
7314  008054            汇添富中债7-10年国开债A   汇添富基金  ...  何旻,李伟  开放申购  0.05
[7315 rows x 11 columns]
```

### 基金规模

#### 开放式基金

接口: fund_scale_open_sina

目标地址: https://vip.stock.finance.sina.com.cn/fund_center/index.html#jjhqetf

描述: 基金数据中心-基金规模-开放式基金

限量: 单次返回指定 symbol 的基金规模数据

输入参数

| 名称     | 类型  | 描述                                                                       |
|--------|-----|--------------------------------------------------------------------------|
| symbol | str | symbol="股票型基金"; choice of {"股票型基金", "混合型基金", "债券型基金", "货币型基金", "QDII基金"} |

输出参数

| 名称    | 类型      | 描述       |
|-------|---------|----------|
| 序号    | int64   | -        |
| 基金代码  | object  | -        |
| 基金简称  | object  | -        |
| 单位净值  | float64 | 注意单位: 元  |
| 总募集规模 | float64 | 注意单位: 万份 |
| 最近总份额 | float64 | 注意单位: 份  |
| 成立日期  | object  | -        |
| 基金经理  | object  | -        |
| 更新日期  | object  | -        |

接口示例

```python
import akshare as ak

fund_scale_open_sina_df = ak.fund_scale_open_sina(symbol='股票型基金')
print(fund_scale_open_sina_df)
```

数据示例

```
        序号    基金代码               基金简称  ...        成立日期     基金经理        更新日期
0        1  510300       华泰柏瑞沪深300ETF  ...  2012-05-04       柳军  2023-11-06
1        2  009341          易方达均衡成长股票  ...  2020-05-22       陈皓  2023-11-06
2        3  512960        博时央企结构调整ETF  ...  2018-10-19      赵云阳  2023-11-06
3        4  000051      华夏沪深300ETF联接A  ...  2009-07-10  张弘弢、赵宗庭  2023-11-06
4        5  001409           工银互联网加股票  ...  2015-06-05       单文  2023-11-06
    ...     ...                ...  ...         ...      ...         ...
3677  3678  015676  鹏华中证移动互联网指数(LOF)C  ...  2022-05-10      罗英宇  2023-11-06
3678  3679  160222      国泰国证食品饮料行业指数A  ...  2021-01-01       梁杏  2023-11-06
3679  3680  160221      国泰国证有色金属行业指数A  ...  2021-01-01      吴中昊  2023-11-06
3680  3681  160218       国泰国证房地产行业指数A  ...  2021-01-01      吴中昊  2023-11-06
3681  3682  960000          汇丰晋信大盘股票H  ...  2015-12-29      闵良超  2023-11-06
[3682 rows x 9 columns]
```

#### 封闭式基金

接口: fund_scale_close_sina

目标地址: https://vip.stock.finance.sina.com.cn/fund_center/index.html#jjhqetf

描述: 基金数据中心-基金规模-封闭式基金

限量: 单次返回所有封闭式基金的基金规模数据

输入参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| -   | -   | -   |

输出参数

| 名称    | 类型      | 描述       |
|-------|---------|----------|
| 序号    | int64   | -        |
| 基金代码  | object  | -        |
| 基金简称  | object  | -        |
| 单位净值  | float64 | 注意单位: 元  |
| 总募集规模 | float64 | 注意单位: 万份 |
| 最近总份额 | float64 | 注意单位: 份  |
| 成立日期  | object  | -        |
| 基金经理  | object  | -        |
| 更新日期  | object  | -        |

接口示例

```python
import akshare as ak

fund_scale_close_sina_df = ak.fund_scale_close_sina()
print(fund_scale_close_sina_df)
```

数据示例

```
      序号    基金代码            基金简称  ...        成立日期  基金经理        更新日期
0      1  017800     招商恒鑫30个月封闭债  ...  2023-02-22   李家辉  2023-10-16
1      2  001369    兴业稳固收益两年理财债券  ...  2015-06-10   唐丁祥  2023-11-06
2      3  017798  易方达恒固18个月封闭债券A  ...  2023-03-06   纪玲云  2023-10-16
3      4  017792      工银泰丰一年封闭债券  ...  2023-03-21   张略钊         NaT
4      5  163418    兴全合兴混合(LOF)A  ...  2021-01-12    陈宇  2023-11-06
..   ...     ...             ...  ...         ...   ...         ...
135  136  165809       东吴中证可转债指数  ...  2014-05-09    邵笛         NaN
136  137  519059      海富通可转债优选债券  ...  2014-05-06    何谦         NaN
137  138  000631        中银聚利分级债券  ...  2014-06-05    陈玮         NaN
138  139  005863    华夏鼎禄三个月定开债券C  ...  2018-10-11    孙蕾         NaN
139  140  006394  招商添德3个月定开债发起式C  ...  2018-09-12   黄晓婷         NaN
[140 rows x 9 columns]
```

#### 分级子基金

接口: fund_scale_structured_sina

目标地址: https://vip.stock.finance.sina.com.cn/fund_center/index.html#jjgmfjall

描述: 基金数据中心-基金规模-分级子基金

限量: 单次返回所有分级子基金的基金规模数据

输入参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| -   | -   | -   |

输出参数

| 名称    | 类型      | 描述       |
|-------|---------|----------|
| 序号    | int64   | -        |
| 基金代码  | object  | -        |
| 基金简称  | object  | -        |
| 单位净值  | float64 | 注意单位: 元  |
| 总募集规模 | float64 | 注意单位: 万份 |
| 最近总份额 | float64 | 注意单位: 份  |
| 成立日期  | object  | -        |
| 基金经理  | object  | -        |
| 更新日期  | object  | -        |

接口示例

```python
import akshare as ak

fund_scale_structured_sina_df = ak.fund_scale_structured_sina()
print(fund_scale_structured_sina_df)
```

数据示例

```
      序号    基金代码             基金简称  ...        成立日期  基金经理        更新日期
0      1  150006            长盛同庆A  ...  2009-05-12    王宁         NaN
1      2  150007            长盛同庆B  ...  2009-05-12    王宁         NaN
2      3  150099         长盛同庆800B  ...  2012-05-12    王超         NaN
3      4  150098         长盛同庆800A  ...  2012-05-12    王超         NaN
4      5  150259       易方达重组指数分级A  ...  2015-06-03   刘树荣         NaN
..   ...     ...              ...  ...         ...   ...         ...
397  398  000910          鑫元合丰纯债C  ...  2016-12-27    陈浩  2023-11-09
398  399  000501     华富恒富18个月定开债C  ...  2017-11-22   姚姣姣         NaT
399  400  000502     华富恒富18个月定开债A  ...  2017-11-22   姚姣姣         NaT
400  401  000623      华富恒财定期开放债券C  ...  2018-08-07   倪莉莎         NaT
401  402  163118  申万菱信中证申万医药生物指数A  ...  2021-01-01   廖裕舟  2023-11-09
[402 rows x 9 columns]
```

### 基金公司规模

#### 基金规模详情

接口: fund_aum_em

目标地址: https://fund.eastmoney.com/Company/lsgm.html

描述: 天天基金网-基金数据-基金规模

限量: 单次返回所有基金规模数据

输入参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| -   | -   | -   |

输出参数

| 名称     | 类型      | 描述       |
|--------|---------|----------|
| 序号     | int64   | -        |
| 基金公司   | object  | -        |
| 成立时间   | object  | -        |
| 全部管理规模 | float64 | 注意单位: 亿元 |
| 全部基金数  | int64   | -        |
| 全部经理数  | int64   | -        |
| 更新日期   | object  | -        |

接口示例

```python
import akshare as ak

fund_aum_em_df = ak.fund_aum_em()
print(fund_aum_em_df)
```

数据示例

```
      序号          基金公司        成立时间    全部管理规模  全部基金数  全部经理数   更新日期
0      1   易方达基金管理有限公司  2001-04-17  16599.27    632     81  11-08
1      2    广发基金管理有限公司  2003-08-05  12701.86    660     82  11-10
2      3    华夏基金管理有限公司  1998-04-09  12379.85    709    113  11-10
3      4  南方基金管理股份有限公司  1998-03-06  10966.40    607     84  10-24
4      5    天弘基金管理有限公司  2004-11-08  10486.77    334     46  11-01
..   ...           ...         ...       ...    ...    ...    ...
200  201    国都证券股份有限公司  2001-12-28      0.41      2      2  09-30
201  202    天风证券股份有限公司  2000-03-29       NaN      0      0  12-31
202  203    国新证券股份有限公司  2007-09-07       NaN      0      0  03-31
203  204    安信证券股份有限公司  2006-08-22       NaN      0      0  03-31
204  205    东兴证券股份有限公司  2008-05-28       NaN      0      0  03-31
[205 rows x 7 columns]
```

#### 基金规模走势

接口: fund_aum_trend_em

目标地址: http://fund.eastmoney.com/Company/default.html

描述: 天天基金网-基金数据-市场全部基金规模走势

限量: 单次返回所有市场全部基金规模走势数据

输入参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| -   | -   | -   |

输出参数

| 名称    | 类型      | 描述  |
|-------|---------|-----|
| date  | object  | -   |
| value | float64 | -   |

接口示例

```python
import akshare as ak

fund_aum_trend_em_df = ak.fund_aum_trend_em()
print(fund_aum_trend_em_df)
```

数据示例

```
          date         value
0   2016-06-30  7.795280e+12
1   2016-09-30  8.572922e+12
2   2016-12-31  8.966215e+12
3   2017-03-31  8.964740e+12
4   2017-06-30  9.887788e+12
5   2017-09-30  1.085338e+13
6   2017-12-31  1.141607e+13
7   2018-03-31  1.205598e+13
8   2018-06-30  1.256286e+13
9   2018-09-30  1.307637e+13
10  2018-12-31  1.285114e+13
11  2019-03-31  1.349216e+13
12  2019-06-30  1.321250e+13
13  2019-09-30  1.332581e+13
14  2019-12-31  1.463123e+13
15  2020-03-31  1.624671e+13
16  2020-06-30  1.649092e+13
17  2020-09-30  1.717224e+13
18  2020-12-31  1.953767e+13
19  2021-03-31  2.116346e+13
20  2021-06-28  2.233053e+13
```

#### 基金公司历年管理规模

接口: fund_aum_hist_em

目标地址: http://fund.eastmoney.com/Company/lsgm.html

描述: 天天基金网-基金数据-基金公司历年管理规模排行列表

限量: 单次返回所有基金公司历年管理规模排行列表数据

输入参数

| 名称   | 类型  | 描述                      |
|------|-----|-------------------------|
| year | str | year="2023"; 从 2001 年开始 |

输出参数

| 名称   | 类型      | 描述  |
|------|---------|-----|
| 序号   | int64   | -   |
| 基金公司 | object  | -   |
| 总规模  | float64 | -   |
| 股票型  | float64 | -   |
| 混合型  | float64 | -   |
| 债券型  | float64 | -   |
| 指数型  | float64 | -   |
| QDII | float64 | -   |
| 货币型  | float64 | -   |

接口示例

```python
import akshare as ak

fund_aum_hist_em_df = ak.fund_aum_hist_em(year="2023")
print(fund_aum_hist_em_df)
```

数据示例

```
      序号       基金公司       总规模      股票型  ...    债券型    指数型    QDII      货币型
0      1   易方达基金管理有限公司  16514.20  2704.67  ...  3221.98  2343.89  763.82  6802.29
1      2    广发基金管理有限公司  12633.03  1124.83  ...  2749.18  1179.12  394.54  6046.73
2      3    华夏基金管理有限公司  12238.81  3551.49  ...  1924.34  3430.87  785.84  4458.91
3      4  南方基金管理股份有限公司  10853.73  1266.44  ...  2198.48  1453.78   83.07  5791.66
4      5    天弘基金管理有限公司  10479.03   817.96  ...  1908.11   862.39  153.29  7405.90
..   ...           ...       ...      ...  ...      ...      ...     ...      ...
194  195    瑞达基金管理有限公司      1.05      NaN  ...      NaN      NaN     NaN      NaN
195  196    华创证券有限责任公司      1.03      NaN  ...     1.03      NaN     NaN      NaN
196  197  明亚基金管理有限责任公司      1.00     0.47  ...      NaN     0.47     NaN      NaN
197  198    华鑫证券有限责任公司      0.72      NaN  ...     0.72      NaN     NaN      NaN
198  199    国都证券股份有限公司      0.41      NaN  ...      NaN      NaN     NaN      NaN
[199 rows x 9 columns]
```

### REITs

#### REITs-实时行情

接口: reits_realtime_em

目标地址: http://quote.eastmoney.com/center/gridlist.html#fund_reits_all

描述: 东方财富网-行情中心-REITs-沪深 REITs-实时行情

限量: 单次返回所有 REITs 的实时行情数据

输入参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| -   | -   | -   |

输出参数

| 名称  | 类型      | 描述      |
|-----|---------|---------|
| 序号  | int64   | -       |
| 代码  | object  | -       |
| 名称  | object  | -       |
| 最新价 | float64 | -       |
| 涨跌额 | float64 | -       |
| 涨跌幅 | float64 | 注意单位: % |
| 成交量 | int64   | -       |
| 成交额 | float64 | -       |
| 开盘价 | float64 | -       |
| 最高价 | float64 | -       |
| 最低价 | float64 | -       |
| 昨收  | float64 | -       |

接口示例

```python
import akshare as ak

reits_realtime_em_df = ak.reits_realtime_em()
print(reits_realtime_em_df)
```

数据示例

```
    序号  代码               名称    最新价  ...    开盘价    最高价  最低价     昨收
0    1  508026   嘉实中国电建清洁能源REIT  4.180  ...  4.110  4.199  4.110  4.150
1    2  508009       中金安徽交控REIT  7.749  ...  7.722  7.781  7.687  7.722
2    3  180106    广发成都高投产业园REIT  4.064  ...  4.053  4.100  4.000  4.050
3    4  180401       鹏华深圳能源REIT  6.361  ...  6.348  6.379  6.344  6.344
4    5  508008       国金中国铁建REIT  9.460  ...  9.434  9.486  9.425  9.435
..  ..     ...              ...    ...  ...    ...    ...    ...    ...
58  59  180102       华夏合肥高新REIT  2.196  ...  2.218  2.245  2.172  2.245
59  60  508021  国泰君安临港创新产业园REIT  4.415  ...  4.515  4.526  4.388  4.515
60  61  508066       华泰江苏交控REIT  6.524  ...  6.650  6.730  6.521  6.679
61  62  180302        华夏深国际REIT  2.829  ...  2.888  2.988  2.822  2.928
62  63  508007       中金山东高速REIT  8.184  ...  8.432  8.432  8.180  8.472
[63 rows x 12 columns]
```

#### REITs-历史行情

接口: reits_hist_em

目标地址: https://quote.eastmoney.com/sh508097.html

描述: 东方财富网-行情中心-REITs-沪深 REITs-历史行情

限量: 单次返回指定 symbol 的历史行情数据

输入参数

| 名称     | 类型  | 描述                        |
|--------|-----|---------------------------|
| symbol | str | symbol="508097"; REITs 代码 |

输出参数

| 名称  | 类型      | 描述      |
|-----|---------|---------|
| 日期  | object  | -       |
| 今开  | float64 | -       |
| 最高  | float64 | -       |
| 最低  | float64 | -       |
| 最新价 | float64 | -       |
| 成交量 | int64   | -       |
| 成交额 | float64 | -       |
| 振幅  | float64 | 注意单位: % |
| 换手  | float64 | 注意单位: % |

接口示例

```python
import akshare as ak

reits_hist_em_df = ak.reits_hist_em(symbol="508097")
print(reits_hist_em_df)
```

数据示例

```
      日期       今开    最高   最低   最新价    成交量 成交额    振幅    换手
0   2024-12-03  2.599  2.599  2.567  2.595  26355  6792450.0  1.25  0.66
1   2024-12-04  2.595  2.600  2.557  2.600   5391  1397092.0  1.66  0.13
2   2024-12-05  2.600  2.600  2.572  2.592   1481   383988.0  1.08  0.04
3   2024-12-06  2.592  2.703  2.592  2.694   3165   829019.0  4.28  0.08
4   2024-12-09  2.717  2.800  2.717  2.769   7622  2099354.0  3.08  0.19
..         ...    ...    ...    ...    ...    ...        ...   ...   ...
71  2025-03-21  3.311  3.348  3.293  3.347   2408   801004.0  1.66  0.06
72  2025-03-24  3.346  3.420  3.346  3.408   6742  2289801.0  2.21  0.17
73  2025-03-25  3.408  3.479  3.408  3.448   4742  1631309.0  2.08  0.12
74  2025-03-26  3.432  3.520  3.419  3.515   3782  1314263.0  2.93  0.09
75  2025-03-27  3.498  3.600  3.498  3.570   3915  1389384.0  2.90  0.10
[76 rows x 9 columns]
```

### 基金报告

#### 基金重仓股

接口: fund_report_stock_cninfo

目标地址: https://webapi.cninfo.com.cn/#/thematicStatistics

描述: 巨潮资讯-数据中心-专题统计-基金报表-基金重仓股

限量: 返回指定 date 的所有数据; date 从 2017 年开始

输入参数

| 名称   | 类型  | 描述                                                                                       |
|------|-----|------------------------------------------------------------------------------------------|
| date | str | date="20210630"; choice of {"XXXX0331", "XXXX0630", "XXXX0930", "XXXX1231"}, 其中 XXXX 为年份 |

输出参数

| 名称     | 类型     | 描述  |
|--------|--------|-----|
| 序号     | int64  | -   |
| 股票代码   | object | -   |
| 股票简称   | object | -   |
| 报告期    | object | -   |
| 基金覆盖家数 | int64  | -   |
| 持股总数   | object | -   |
| 持股总市值  | object | -   |

接口示例

```python
import akshare as ak

fund_report_stock_cninfo_df = ak.fund_report_stock_cninfo(date="20210630")
print(fund_report_stock_cninfo_df)
```

数据示例

```
       序号  股票代码  股票简称   报告期  基金覆盖家数   持股总数  持股总市值
0        1  605011  杭州热电  2021-06-30    3722  1928625   1712.62
1        2  603171  税友股份  2021-06-30    3653  2289995   4396.79
2        3  001208  华菱线缆  2021-06-30    3593  6218772   4807.11
3        4  605287  德才股份  2021-06-30    3461  1200847   3789.87
4        5  601528  瑞丰银行  2021-06-30    3324  7284126  11341.38
...    ...     ...   ...         ...     ...      ...       ...
3951  3952  836239  长虹能源  2021-06-30       1   273070   1791.34
3952  3953  836675  秉扬科技  2021-06-30       1   477163    660.87
3953  3954  836826  盖世食品  2021-06-30       1  1731830   1087.59
3954  3955  837344  三元基因  2021-06-30       1   274142    610.79
3955  3956  839167  同享科技  2021-06-30       1   315074    667.96
```

#### 基金行业配置

接口: fund_report_industry_allocation_cninfo

目标地址: https://webapi.cninfo.com.cn/#/thematicStatistics

描述: 巨潮资讯-数据中心-专题统计-基金报表-基金行业配置

限量: 返回指定 date 的所有数据; date 从 2017 年开始

输入参数

| 名称   | 类型  | 描述                                                                                       |
|------|-----|------------------------------------------------------------------------------------------|
| date | str | date="20210630"; choice of {"XXXX0331", "XXXX0630", "XXXX0930", "XXXX1231"}, 其中 XXXX 为年份 |

输出参数

| 名称      | 类型      | 描述       |
|---------|---------|----------|
| 行业编码    | object  | -        |
| 证监会行业名称 | object  | -        |
| 报告期     | object  | -        |
| 基金覆盖家数  | int64   | 注意单位: 只  |
| 行业规模    | float64 | 注意单位: 亿元 |
| 占净资产比例  | float64 | 注意单位: %  |

接口示例

```python
import akshare as ak

fund_report_industry_allocation_cninfo_df = ak.fund_report_industry_allocation_cninfo(date="20210630")
print(fund_report_industry_allocation_cninfo_df)
```

数据示例

```
   行业编码           证监会行业名称         报告期  基金覆盖家数      行业规模  占净资产比例
0     A          农、林、牧、渔业  2021-06-30    3117    225.63    0.10
1     B               采矿业  2021-06-30    2182    807.04    0.35
2     C               制造业  2021-06-30    4897  37945.44   16.66
3     D  电力、热力、燃气及水生产和供应业  2021-06-30    4044    350.24    0.15
4     E               建筑业  2021-06-30    3931    236.84    0.10
5     F            批发和零售业  2021-06-30    3802    352.99    0.15
6     G       交通运输、仓储和邮政业  2021-06-30    2150    739.33    0.32
7     H            住宿和餐饮业  2021-06-30     513    145.43    0.06
8     I   信息传输、软件和信息技术服务业  2021-06-30    4472   2501.85    1.10
9     J               金融业  2021-06-30    4348   4539.41    1.99
10    K              房地产业  2021-06-30    2062    669.58    0.29
11    L          租赁和商务服务业  2021-06-30    2410   1035.95    0.45
12    M        科学研究和技术服务业  2021-06-30    3052   1595.45    0.70
13    N     水利、环境和公共设施管理业  2021-06-30    3486    124.66    0.05
14    O     居民服务、修理和其他服务业  2021-06-30       1      0.00    0.00
15    P                教育  2021-06-30     737     77.00    0.03
16    Q           卫生和社会工作  2021-06-30    1877   1487.55    0.65
17    R         文化、体育和娱乐业  2021-06-30    1386    375.28    0.16
18    S                综合  2021-06-30     278     60.31    0.03
```

#### 基金资产配置

接口: fund_report_asset_allocation_cninfo

目标地址: https://webapi.cninfo.com.cn/#/thematicStatistics

描述: 巨潮资讯-数据中心-专题统计-基金报表-基金资产配置

限量: 返回所有基金资产配置数据

输入参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| -   | -   | -   |

输出参数

| 名称            | 类型     | 描述       |
|---------------|--------|----------|
| 报告期           | object | -        |
| 基金覆盖家数        | object | 注意单位: 只  |
| 股票权益类占净资产比例   | object | 注意单位: %  |
| 债券固定收益类占净资产比例 | object | 注意单位: %  |
| 现金货币类占净资产比例   | object | 注意单位: %  |
| 基金市场净资产规模     | object | 注意单位: 亿元 |

接口示例

```python
import akshare as ak

fund_report_asset_allocation_cninfo_df = ak.fund_report_asset_allocation_cninfo()
print(fund_report_asset_allocation_cninfo_df)
```

数据示例

```
      报告期  基金覆盖家数  股票权益类占净资产比例  债券固定收益类占净资产比例  现金货币类占净资产比例  基金市场净资产规模
0   2024-06-30   11600        18.72          58.23        19.68  303791.15
1   2024-03-31   11346        20.37          58.42        20.17  286070.29
2   2023-12-31   11012        21.36          58.07        20.63  270156.52
3   2023-09-30   10776        22.22          57.36        17.84  270492.99
4   2023-06-30   10539        22.58          56.70        19.31  272807.40
..         ...     ...          ...            ...          ...        ...
63  2008-09-30     392        57.63          30.48        10.80   18272.96
64  2008-06-30     358        62.90          19.38        10.67   20680.07
65  2008-03-31     342        68.70          14.88        13.48   25757.00
66  2007-12-31     342        74.32          10.17        10.85   32918.04
67  2007-09-30     327        77.00           9.25        12.32   29924.06
[68 rows x 6 columns]
```

### 规模份额

#### 规模变动

接口: fund_scale_change_em

目标地址: https://fund.eastmoney.com/data/gmbdlist.html

描述: 天天基金网-基金数据-规模份额-规模变动

限量: 返回所有规模变动数据

输入参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| -   | -   | -   |

输出参数

| 名称    | 类型      | 描述       |
|-------|---------|----------|
| 序号    | int64   | -        |
| 截止日期  | object  | -        |
| 基金家数  | int64   | -        |
| 期间申购  | float64 | 注意单位: 亿份 |
| 期间赎回  | float64 | 注意单位: 亿份 |
| 期末总份额 | float64 | 注意单位: 亿份 |
| 期末净资产 | float64 | 注意单位: 亿份 |

接口示例

```python
import akshare as ak

fund_scale_change_em_df = ak.fund_scale_change_em()
print(fund_scale_change_em_df)
```

数据示例

```
      序号  截止日期   基金家数  期间申购  期间赎回      期末总份额   期末净资产
0      1  2023-09-30  18788  358045.47  360752.19  264069.33  273412.57
1      2  2023-06-30  18132  340624.75  330338.84  264410.61  275641.99
2      3  2023-03-31  17531  325891.23  324651.27  251161.43  264545.81
3      4  2022-12-31  16880  307176.62  317969.28  246613.07  258026.93
4      5  2022-09-30  16180  313441.70  310953.24  253704.92  264088.21
..   ...         ...    ...        ...        ...        ...        ...
97    98  1999-06-30     12        NaN        NaN     260.00     360.04
98    99  1999-03-31      6        NaN        NaN     120.00     128.21
99   100  1998-12-31      5        NaN        NaN     100.00     107.42
100  101  1998-09-30      5        NaN        NaN     100.00     101.79
101  102  1998-06-30      4        NaN        NaN      80.00      82.72
[102 rows x 7 columns]
```

#### 持有人结构

接口: fund_hold_structure_em

目标地址: https://fund.eastmoney.com/data/cyrjglist.html

描述: 天天基金网-基金数据-规模份额-持有人结构

限量: 返回所有持有人结构数据

输入参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| -   | -   | -   |

输出参数

| 名称     | 类型      | 描述       |
|--------|---------|----------|
| 序号     | int64   | -        |
| 截止日期   | object  | -        |
| 基金家数   | int64   | -        |
| 机构持有比列 | float64 | 注意单位: %  |
| 个人持有比列 | float64 | 注意单位: %  |
| 内部持有比列 | float64 | 注意单位: %  |
| 总份额    | float64 | 注意单位: 亿份 |

接口示例

```python
import akshare as ak

fund_hold_structure_em_df = ak.fund_hold_structure_em()
print(fund_hold_structure_em_df)
```

数据示例

```
    序号  截止日期  基金家数 机构持有比列 个人持有比列 内部持有比列  总份额
0    1  2023-06-30  17981   45.77   54.12    1.14  263786.82
1    2  2022-12-31  16726   46.74   53.23    1.12  245886.54
2    3  2022-06-30  15373   48.43   51.67    0.92  245830.19
3    4  2021-12-31  13551   47.37   52.84    0.81  223047.85
4    5  2021-06-30  11800   46.79   53.68    0.65  203021.86
5    6  2020-12-31  10613   48.55   51.80    0.60  177103.29
6    7  2020-06-30   9618   50.46   49.83    0.58  161524.18
7    8  2019-12-31   8602   49.35   50.73    0.48  148597.63
8    9  2019-06-30   7816   48.47   51.78    0.45  147281.28
9   10  2018-12-31   7121   48.11   52.39    0.34  142042.26
10  11  2018-06-30   7021   44.02   56.00    0.28  140090.11
11  12  2017-12-31   6591   48.18   51.91    0.24  122782.24
12  13  2017-06-30   6120   49.24   50.77    0.27  118044.49
13  14  2016-12-31   4921   53.77   46.92    0.31  109800.64
14  15  2016-06-30   4179   53.98   46.01    0.26  117137.30
15  16  2015-12-31   4193   47.42   52.57    0.40  122292.88
16  17  2015-06-30   3339   38.34   61.55    0.55   91637.69
17  18  2014-12-31   2500   28.30   72.02    0.64   61490.76
18  19  2014-06-30   2227   27.46   72.38    0.91   57789.93
19  20  2013-12-31   1961   26.91   74.45    1.33   41896.32
20  21  2013-06-30   1688   25.10   78.03    1.41   34968.03
21  22  2012-12-31   1391   27.56   72.49    1.47   30319.83
22  23  2012-06-30   1169   23.78   75.82    2.24   27154.95
23  24  2011-12-31   1017   25.04   77.67    1.99   25755.88
24  25  2011-06-30    876   20.19   79.76    1.35   23881.04
25  26  2010-12-31    750   21.34   80.29    1.36   22991.23
26  27  2010-06-30    664   16.62   86.15    0.99   23082.30
27  28  2009-12-31    600   20.27   82.84    0.36   23112.19
28  29  2009-06-30    522   14.58   88.84    0.03   21839.02
29  30  2008-12-31    455   15.70   86.93    0.02   24780.24
30  31  2008-06-30    391   12.40   92.53    0.01   21690.83
31  32  2007-12-31    367   11.12   93.65    0.01   21479.03
32  33  2007-06-30    340   15.46   91.26    0.35   11237.33
33  34  2006-12-31    300   37.44   81.51    0.00    4024.31
34  35  2006-06-30    249   60.80   65.54    0.00    2759.32
35  36  2005-12-31    211   55.68   60.64    0.00    3904.09
36  37  2005-06-30    184   56.04   66.87    0.00    3521.60
37  38  2004-12-31    153   55.29   79.25    0.00    2365.37
38  39  2004-06-30    127   66.97   79.44    0.00    1759.85
```

### 基金仓位

#### 股票型基金仓位

接口: fund_stock_position_lg

目标地址: https://legulegu.com/stockdata/fund-position/pos-stock

描述: 乐咕乐股-基金仓位-股票型基金仓位

限量: 返回所有历史数据

输入参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| -   | -   | -   |

输出参数

| 名称       | 类型      | 描述               |
|----------|---------|------------------|
| date     | object  | -                |
| close    | float64 | 注意单位: 沪深 300 收盘价 |
| position | float64 | 注意单位: 持仓比例       |

接口示例

```python
import akshare as ak

fund_stock_position_lg_df = ak.fund_stock_position_lg()
print(fund_stock_position_lg_df)
```

数据示例

```
           date    close  position
0    2017-12-04  4018.86     89.25
1    2017-12-08  4003.38     90.57
2    2017-12-15  3980.86     90.43
3    2017-12-22  4054.60     90.27
4    2017-12-29  4030.85     90.34
..          ...      ...       ...
256  2022-11-25  3775.78     87.73
257  2022-12-02  3870.95     89.01
258  2022-12-09  3998.24     89.58
259  2022-12-16  3954.23     90.10
260  2022-12-23  3828.22     89.67
```

#### 平衡混合型基金仓位

接口: fund_balance_position_lg

目标地址: https://legulegu.com/stockdata/fund-position/pos-pingheng

描述: 乐咕乐股-基金仓位-平衡混合型基金仓位

限量: 返回所有历史数据

输入参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| -   | -   | -   |

输出参数

| 名称       | 类型      | 描述               |
|----------|---------|------------------|
| date     | object  | -                |
| close    | float64 | 注意单位: 沪深 300 收盘价 |
| position | float64 | 注意单位: 持仓比例       |

接口示例

```python
import akshare as ak

fund_balance_position_lg_df = ak.fund_balance_position_lg()
print(fund_balance_position_lg_df)
```

数据示例

```
           date    close  position
0    2017-12-04  4018.86     68.03
1    2017-12-08  4003.38     70.94
2    2017-12-15  3980.86     71.28
3    2017-12-22  4054.60     71.88
4    2017-12-29  4030.85     72.29
..          ...      ...       ...
256  2022-11-25  3775.78     65.26
257  2022-12-02  3870.95     66.41
258  2022-12-09  3998.24     67.51
259  2022-12-16  3954.23     68.88
260  2022-12-23  3828.22     68.28
```

#### 灵活配置型基金仓位

接口: fund_linghuo_position_lg

目标地址: https://legulegu.com/stockdata/fund-position/pos-linghuo

描述: 乐咕乐股-基金仓位-灵活配置型基金仓位

限量: 返回所有历史数据

输入参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| -   | -   | -   |

输出参数

| 名称       | 类型      | 描述               |
|----------|---------|------------------|
| date     | object  | -                |
| close    | float64 | 注意单位: 沪深 300 收盘价 |
| position | float64 | 注意单位: 持仓比例       |

接口示例

```python
import akshare as ak

fund_linghuo_position_lg_df = ak.fund_linghuo_position_lg()
print(fund_linghuo_position_lg_df)
```

数据示例

```
           date    close  position
0    2017-12-04  4018.86     51.44
1    2017-12-08  4003.38     52.48
2    2017-12-15  3980.86     52.59
3    2017-12-22  4054.60     52.70
4    2017-12-29  4030.85     53.18
..          ...      ...       ...
256  2022-11-25  3775.78     69.86
257  2022-12-02  3870.95     71.01
258  2022-12-09  3998.24     71.95
259  2022-12-16  3954.23     72.73
260  2022-12-23  3828.22     72.55
```

### 基金公告

#### 人事公告

接口: fund_announcement_personnel_em

目标地址: http://fundf10.eastmoney.com/jjgg_000001_4.html

描述: 东方财富网站-天天基金网-基金档案-基金公告-人事调整

限量: 返回所有历史数据

输入参数

| 名称     | 类型  | 描述                                 |
|--------|-----|------------------------------------|
| symbol | str | 基金代码，可以通过调用 ak.fund_name_em() 接口获取 |

输出参数

| 名称   | 类型     | 描述                     |
|------|--------|------------------------|
| 基金代码 | object | 基金代码                   |
| 公告标题 | object | -                      |
| 基金名称 | object | 基金名称                   |
| 公告日期 | object | 公告的发布日期                |
| 报告ID | object | 获取报告详情的依据; 拼接后可以获取公告地址 |

接口示例

```python
import akshare as ak

fund_announcement_personnel_em_df = ak.fund_announcement_personnel_em(symbol="000001")
print(fund_announcement_personnel_em_df)
```

数据示例

```
      基金代码                               公告标题    基金名称        公告日期                  报告ID
0   000001    华夏基金管理有限公司关于调整华夏成长证券投资基金基金经理的公告  华夏成长混合  2013-06-29  AN201306290003484248
1   000001    华夏基金管理有限公司关于增聘华夏成长证券投资基金基金经理的公告  华夏成长混合  2014-03-18  AN201403180005270024
2   000001    华夏基金管理有限公司关于调整华夏成长证券投资基金基金经理的公告  华夏成长混合  2014-06-21  AN201406210006119867
3   000001    华夏基金管理有限公司关于调整华夏成长证券投资基金基金经理的公告  华夏成长混合  2015-01-09  AN201501090008098479
4   000001  华夏基金管理有限公司关于调整华夏成长证券投资公告基金基金经理的公告  华夏成长混合  2015-11-21  AN201511210011484696
5   000001    华夏基金管理有限公司关于调整华夏成长证券投资基金基金经理的公告  华夏成长混合  2017-01-14  AN201701140268312342
6   000001    华夏基金管理有限公司关于调整华夏成长证券投资基金基金经理的公告  华夏成长混合  2017-02-25  AN201702250364410226
7   000001    华夏基金管理有限公司关于调整华夏成长证券投资基金基金经理的公告  华夏成长混合  2017-03-30  AN201703300451418174
8   000001    华夏基金管理有限公司关于调整华夏成长证券投资基金基金经理的公告  华夏成长混合  2021-02-24  AN202102241464811736
9   000001    华夏基金管理有限公司关于增聘华夏成长证券投资基金基金经理的公告  华夏成长混合  2021-09-18  AN202109181516930266
10  000001    华夏基金管理有限公司关于调整华夏成长证券投资基金基金经理的公告  华夏成长混合  2022-04-13  AN202204131558964253
11  000001    华夏基金管理有限公司关于调整华夏成长证券投资基金基金经理的公告  华夏成长混合  2022-10-29  AN202210291579611414
```

## [AKShare](https://github.com/akfamily/akshare) 外汇数据

### 实时行情数据

接口: forex_spot_em

目标地址: https://quote.eastmoney.com/center/gridlist.html#forex_all

描述: 东方财富网-行情中心-外汇市场-所有汇率-实时行情数据

限量: 单次返回所有实时行情数据

输入参数

| 名称 | 类型 | 描述 |
|----|----|----|
| -  | -  | -  |

输出参数

| 名称  | 类型      | 描述 |
|-----|---------|----|
| 序号  | int64   | -  |
| 代码  | object  | -  |
| 名称  | object  | -  |
| 最新价 | float64 | -  |
| 涨跌额 | float64 | -  |
| 涨跌幅 | float64 | -  |
| 今开  | float64 | -  |
| 最高  | float64 | -  |
| 最低  | float64 | -  |
| 昨收  | float64 | -  |

接口示例

```python
import akshare as ak

forex_spot_em_df = ak.forex_spot_em()
print(forex_spot_em_df)
```

数据示例

```
      序号       代码     名称      最新价  ...       今开       最高       最低       昨收
0      1  EURCNYC     欧元人民币中间价   7.7573  ...   7.7573   7.7573   7.7573   7.6452
1      2  NZDCNYC     纽元人民币中间价   4.1284  ...   4.1284   4.1284   4.1284   4.0877
2      3  CNYRUBC     人民币卢布中间价  12.4840  ...  12.4840  12.4840  12.4840  12.3640
3      4   JPYZAR   100日元兑南非兰特  12.3175  ...  12.4045  12.4247  12.2472  12.2892
4      5  AUDCNYC     澳元人民币中间价   4.5589  ...   4.5589   4.5589   4.5589   4.5181
..   ...      ...          ...      ...  ...      ...      ...      ...      ...
185  186  CNYDKKC   人民币丹麦克朗中间价   0.9622  ...   0.9622   0.9622   0.9622   0.9766
186  187  CNYNOKC   人民币挪威克朗中间价   1.5177  ...   1.5177   1.5177   1.5177   1.5412
187  188  CNYHUFC  人民币匈牙利福林中间价  51.3327  ...  51.3327  51.3327  51.3327  52.1618
188  189  CNYPLNC  人民币波兰兹罗提中间价   0.5353  ...   0.5353   0.5353   0.5353   0.5450
189  190  CNYSEKC   人民币瑞典克朗中间价   1.4174  ...   1.4174   1.4174   1.4174   1.4464
[190 rows x 10 columns]
```

### 历史行情数据

接口: forex_hist_em

目标地址: https://quote.eastmoney.com/cnyrate/EURCNYC.html

描述: 东方财富网-行情中心-外汇市场-所有汇率-历史行情数据

限量: 单次返回指定 symbol 的历史行情数据

输入参数

| 名称     | 类型  | 描述                                                                |
|--------|-----|-------------------------------------------------------------------|
| symbol | str | symbol="USDCNH"; 品种代码；可以通过 ak.forex_spot_em() 来获取所有可获取历史行情数据的品种代码 |

输出参数

| 名称  | 类型      | 描述 |
|-----|---------|----|
| 序号  | int64   | -  |
| 代码  | object  | -  |
| 名称  | object  | -  |
| 最新价 | float64 | -  |
| 涨跌额 | float64 | -  |
| 涨跌幅 | float64 | -  |
| 今开  | float64 | -  |
| 最高  | float64 | -  |
| 最低  | float64 | -  |
| 昨收  | float64 | -  |

接口示例

```python
import akshare as ak

forex_hist_em_df = ak.forex_hist_em(symbol="USDCNH")
print(forex_hist_em_df)
```

数据示例

```
         日期      代码        名称      今开     最新价      最高      最低    振幅
0     2010-08-23  USDCNH  美元兑离岸人民币  6.7200  6.7250  6.7250  6.7200  0.00
1     2010-08-24  USDCNH  美元兑离岸人民币  6.7230  6.7265  6.7265  6.7230  0.05
2     2010-08-25  USDCNH  美元兑离岸人民币  6.7325  6.7325  6.7325  6.7325  0.00
3     2010-08-26  USDCNH  美元兑离岸人民币  6.7500  6.7450  6.7500  6.7450  0.07
4     2010-08-27  USDCNH  美元兑离岸人民币  6.7530  6.7530  6.7530  6.7530  0.00
...          ...     ...       ...     ...     ...     ...     ...   ...
3776  2025-02-28  USDCNH  美元兑离岸人民币  7.3007  7.2954  7.3019  7.2844  0.24
3777  2025-03-03  USDCNH  美元兑离岸人民币  7.2915  7.3027  7.3069  7.2848  0.30
3778  2025-03-04  USDCNH  美元兑离岸人民币  7.3028  7.2543  7.3069  7.2488  0.80
3779  2025-03-05  USDCNH  美元兑离岸人民币  7.2532  7.2362  7.2769  7.2343  0.59
3780  2025-03-06  USDCNH  美元兑离岸人民币  7.2363  7.2484  7.2485  7.2354  0.18
[3781 rows x 8 columns]
```

### 人民币牌价数据

接口: currency_boc_sina

目标地址: https://biz.finance.sina.com.cn/forex/forex.php?startdate=2012-01-01&enddate=2021-06-14&money_code=EUR&type=0

描述: 新浪财经-中行人民币牌价历史数据

限量: 单次返回指定日期的所有历史数据

输入参数

| 名称         | 类型  | 描述                                                                                                                                                   |
|------------|-----|------------------------------------------------------------------------------------------------------------------------------------------------------|
| symbol     | str | symbol="美元"; choice of {'美元', '英镑', '欧元', '澳门元', '泰国铢', '菲律宾比索', '港币', '瑞士法郎', '新加坡元', '瑞典克朗', '丹麦克朗', '挪威克朗', '日元', '加拿大元', '澳大利亚元', '新西兰元', '韩国元'} |
| start_date | str | start_date="20230304"; 开始日期和结束日期之间的间隔要超过 6 个月                                                                                                        |
| end_date   | str | end_date="20231110"; 开始日期和结束日期之间的间隔要超过 6 个月                                                                                                          |

输出参数

| 名称        | 类型      | 描述      |
|-----------|---------|---------|
| 日期        | object  | -       |
| 中行汇买价     | float64 | 注意单位: 元 |
| 中行钞买价     | float64 | 注意单位: 元 |
| 中行钞卖价/汇卖价 | float64 | 注意单位: 元 |
| 央行中间价     | float64 | 注意单位: 元 |

接口示例

```python
import akshare as ak

currency_boc_sina_df = ak.currency_boc_sina(symbol="美元", start_date="20230304", end_date="20231110")
print(currency_boc_sina_df)
```

数据示例

```
    日期   中行汇买价   中行钞买价  中行钞卖价/汇卖价   央行中间价
0    2023-03-06  691.80  686.17     694.73  689.51
1    2023-03-07  694.49  688.84     697.44  691.56
2    2023-03-08  693.49  687.85     696.43  695.25
3    2023-03-09  695.04  689.39     697.99  696.66
4    2023-03-10  689.70  684.09     692.62  696.55
..          ...     ...     ...        ...     ...
175  2023-11-06  725.83  725.67     728.73  717.80
176  2023-11-07  726.73  726.57     729.63  717.76
177  2023-11-08  726.73  726.57     729.63  717.73
178  2023-11-09  727.28  727.12     730.18  717.72
179  2023-11-10  727.93  727.77     730.83  717.71
[180 rows x 5 columns]
```

### 人民币汇率中间价

接口: currency_boc_safe

目标地址: https://www.safe.gov.cn/safe/rmbhlzjj/index.html

描述: 外汇管理局-人民币汇率中间价

限量: 单次返回所有历史数据

输入参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| -   | -   | -   |

输出参数

| 名称   | 类型      | 描述  |
|------|---------|-----|
| 日期   | object  | -   |
| 美元   | float64 | -   |
| 欧元   | float64 | -   |
| 日元   | float64 | -   |
| 港元   | float64 | -   |
| 英镑   | float64 | -   |
| 林吉特  | float64 | -   |
| 卢布   | float64 | -   |
| 澳元   | float64 | -   |
| 加元   | float64 | -   |
| 新西兰元 | float64 | -   |
| 新加坡元 | float64 | -   |
| 瑞士法郎 | float64 | -   |
| 兰特   | float64 | -   |
| 韩元   | float64 | -   |
| 迪拉姆  | float64 | -   |
| 里亚尔  | float64 | -   |
| 福林   | float64 | -   |
| 兹罗提  | float64 | -   |
| 丹麦克朗 | float64 | -   |
| 瑞典克朗 | float64 | -   |
| 挪威克朗 | float64 | -   |
| 里拉   | float64 | -   |
| 比索   | float64 | -   |
| 泰铢   | float64 | -   |

P.S. 人民币对马来西亚林吉特、俄罗斯卢布、南非兰特、韩元、阿联酋迪拉姆、沙特里亚尔、匈牙利福林、波兰兹罗提、丹麦克朗、瑞典克朗、挪威克朗、土耳其里拉、墨西哥比索、泰铢汇率中间价采取间接标价法，即100人民币折合多少外币。人民币对其它10种货币汇率中间价仍采取直接标价法，即100外币折合多少人民币。

接口示例

```python
import akshare as ak

currency_boc_safe_df = ak.currency_boc_safe()
print(currency_boc_safe_df)
```

数据示例

```
       日期        美元      欧元   日元  ...    挪威克朗     里拉      比索    泰铢
0     1994-01-01  870.00     NaN  7.7800  ...     NaN      NaN     NaN     NaN
1     1994-01-03  870.00     NaN  7.7800  ...     NaN      NaN     NaN     NaN
2     1994-01-04  870.00     NaN  7.7196  ...     NaN      NaN     NaN     NaN
3     1994-01-05  870.00     NaN  7.7196  ...     NaN      NaN     NaN     NaN
4     1994-01-06  870.00     NaN  7.7196  ...     NaN      NaN     NaN     NaN
          ...     ...     ...     ...  ...     ...      ...     ...     ...
7318  2023-09-11  721.48  781.40  4.9710  ...  145.79  368.808  242.47  484.82
7319  2023-09-12  719.86  779.52  4.9573  ...  146.60  371.740  239.65  487.87
7320  2023-09-13  718.94  779.19  4.9356  ...  147.20  372.274  238.83  489.51
7321  2023-09-14  718.74  776.11  4.9174  ...  148.01  372.886  238.35  492.54
7322  2023-09-15  717.86  768.65  4.9064  ...  148.78  373.622  237.86  493.03
[7323 rows x 25 columns]
```

### 人民币外汇即期报价

接口: fx_spot_quote

目标地址: http://www.chinamoney.com.cn/chinese/mkdatapfx/

描述: 人民币外汇即期报价

限量: 单次返回实时行情数据

输入参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| -   | -   | -   |

输出参数

人民币外汇即期报价

| 名称  | 类型      | 描述  |
|-----|---------|-----|
| 货币对 | object  |     |
| 买报价 | float64 |     |
| 卖报价 | float64 |     |

**注：本行情为询价报价行情(美元为ODM), 实时更新**

接口示例
```python
import akshare as ak

fx_spot_quote_df = ak.fx_spot_quote()
print(fx_spot_quote_df)
```

数据示例

```
      货币对        买报价        卖报价
0      USD/CNY    6.68500    6.68540
1      EUR/CNY    7.08170    7.08260
2   100JPY/CNY    4.92400    4.92480
3      HKD/CNY    0.85184    0.85196
4      GBP/CNY    8.20610    8.20690
5      AUD/CNY    4.65300    4.65310
6      NZD/CNY    4.21240    4.21320
7      SGD/CNY    4.82670    4.82680
8      CHF/CNY    7.00390    7.00450
9      CAD/CNY    5.21290    5.21360
10     CNY/MYR    0.65590    0.65750
11     CNY/RUB    7.93950    7.98320
12     CNY/ZAR    2.37330    2.37360
13     CNY/KRW  192.14000  192.20000
14     CNY/AED    0.54935    0.54948
15     CNY/SAR    0.56142    0.56147
16     CNY/HUF   56.53140   56.57020
17     CNY/PLN    0.66319    0.66344
18     CNY/DKK    1.05070    1.05070
19     CNY/SEK    1.50220    1.50240
20     CNY/NOK    1.45980    1.46000
21     CNY/TRY    2.48949    2.48980
22     CNY/MXN    2.97690    2.97870
23     CNY/THB    5.24800    5.25000
```

### 人民币外汇远掉报价

接口: fx_swap_quote

目标地址: http://www.chinamoney.com.cn/chinese/mkdatapfx/

描述: 人民币外汇远掉报价

限量: 单次返回实时行情数据

输入参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| -   | -   | -   |

输出参数

人民币外汇远掉报价

| 名称  | 类型     | 描述                  |
|-----|--------|---------------------|
| 货币对 | object | e.g., "USD/CNY"     |
| 1周  | object | e.g., "11.50/12.00" |
| 1月  | object |                     |
| 3月  | object |                     |
| 6月  | object |                     |
| 9月  | object |                     |
| 1年  | object |                     |

**注：本行情为询价报价行情(美元为ODM), 实时更新**

接口示例

```python
import akshare as ak

fx_swap_quote_df = ak.fx_swap_quote()
print(fx_swap_quote_df)
```

数据示例

```
           货币对        1周  ...                 9月                 1年
0      USD/CNY      5.00/5.00  ...    -235.00/-233.00    -347.00/-345.00
1      EUR/CNY    39.17/39.28  ...    1241.70/1242.30    1513.74/1514.19
2   100JPY/CNY    25.93/26.33  ...    1156.41/1158.04    1604.66/1604.74
3      HKD/CNY      2.35/2.55  ...          6.88/7.10        -4.88/-4.41
4      GBP/CNY    15.36/16.28  ...      221.38/222.74      188.33/191.70
5      AUD/CNY      9.47/9.87  ...    -128.45/-127.09    -254.98/-248.56
6      NZD/CNY      1.36/1.76  ...    -311.14/-299.26    -435.15/-420.15
7      SGD/CNY      3.31/3.39  ...        44.03/44.60        72.00/84.97
8      CHF/CNY    35.23/36.21  ...    1346.89/1347.41    1702.55/1706.77
9      CAD/CNY      7.86/7.96  ...    -124.21/-122.50    -193.11/-190.60
10     CNY/MYR     -1.43/0.35  ...       -22.75/-9.30       -21.12/-5.39
11     CNY/RUB  627.40/627.80  ...  30785.79/32165.82  35730.95/36968.82
12     CNY/ZAR    12.36/12.37  ...      719.25/726.67     992.29/1007.07
13     CNY/KRW    -4.63/-0.56  ...     -106.97/-93.21    -125.42/-117.52
14     CNY/AED    -0.60/-0.50  ...        18.31/18.63        27.90/28.35
15     CNY/SAR        ---/---  ...        22.03/22.54        32.68/33.00
16     CNY/HUF  370.56/423.79  ...  26568.04/26596.38  36664.72/36718.42
17     CNY/PLN      5.37/5.39  ...      264.19/264.92      359.67/361.01
18     CNY/DKK    -5.70/-5.49  ...    -186.20/-186.08    -229.34/-226.54
19     CNY/SEK    -5.91/-5.90  ...    -132.23/-131.85    -137.94/-137.43
20     CNY/NOK    -2.67/-2.64  ...      -43.57/-42.56      -35.65/-33.93
21     CNY/TRY    53.89/61.57  ...    8321.13/8339.39  11516.20/11550.77
22     CNY/MXN    -2.84/-2.80  ...      105.61/105.73    2350.95/2393.33
23     CNY/THB  -68.44/-43.00  ...    -514.60/-436.28    -675.05/-534.00
```

### 外币对即期报价

接口: fx_pair_quote

目标地址: http://www.chinamoney.com.cn/chinese/mkdatapfx/

描述: 外币对即期报价

限量: 单次返回当前时点最近更新的即时数据

输入参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| -   | -   | -   |

输出参数

| 名称  | 类型      | 描述              |
|-----|---------|-----------------|
| 货币对 | object  | e.g., "AUD/USD" |
| 买报价 | float64 | e.g., "0.68460" |
| 卖报价 | float64 | e.g., "0.68461" |

**注：本行情为询价报价行情(美元为ODM), 实时更新**

接口示例

```python
import akshare as ak

fx_pair_quote_df = ak.fx_pair_quote()
print(fx_pair_quote_df)
```

数据示例

```
        货币对        买报价        卖报价
0   AUD/USD    0.69594    0.69600
1   EUR/JPY  143.80300  143.81500
2   EUR/USD    1.05929    1.05935
3   GBP/USD    1.22733    1.22739
4   USD/CAD    1.28238    1.28247
5   USD/CHF    0.95410    0.95417
6   USD/HKD    7.84744    7.84755
7   USD/JPY  135.75500  135.76000
8   USD/SGD    1.38510    1.38518
9   NZD/USD    0.63003    0.63012
10  EUR/GBP    0.86308    0.86308
```

### 指定币种的所有货币对

接口: currency_pair_map

目标地址: https://cn.investing.com/currencies/cny-jmd

描述: 指定币种的所有能够获取到的货币对信息，历史数据可以调用 **ak.currency_history()** 获取

限量: 单次返回指定币种的所有能获取数据的货币对

输入参数

| 名称     | 类型  | 描述                                                                                       |
|--------|-----|------------------------------------------------------------------------------------------|
| symbol | str | symbol="人民币"; 此处提供中文的币种名称, 可以访问[网页](https://cn.investing.com/currencies/cny-jmd) 的页面下方查看 |

输出参数

| 名称   | 类型      | 描述      |
|------|---------|---------|
| name | object  | 货币对中文简称 |
| code | float64 | 货币对代码   |

接口示例

```python
import akshare as ak

currency_pair_map_df = ak.currency_pair_map(symbol="人民币")
print(currency_pair_map_df)
```

数据示例

```
         name     code
0     人民币-丹麦克朗  cny-dkk
1     丹麦克朗-人民币  dkk-cny
2     人民币-瑞士法郎  cny-chf
3     瑞士法郎-人民币  chf-cny
4     人民币-捷克克朗  cny-czk
..         ...      ...
85   人民币-澳大利亚元  cny-aud
86   澳大利亚元-人民币  aud-cny
87    人民币-新西兰元  cny-nzd
88    新西兰元-人民币  nzd-cny
89  人民币-巴拿马巴波亚  cny-pab
[90 rows x 2 columns]
```

### 货币对-投机情绪报告

接口: macro_fx_sentiment

目标地址: https://datacenter.jin10.com/reportType/dc_ssi_trends

描述: 货币对-投机情绪报告

限量: 单次返回指定日期所有品种的数据(所指定的日期必须在当前交易日之前的30个交易日内)

输入参数

| 名称         | 类型  | 描述                                                |
|------------|-----|---------------------------------------------------|
| start_date | str | start_date="2020-04-07"; 所指定的日期必须在当前交易日之前的30个交易日内 |
| end_date   | str | end_date="2020-04-07"; 与 start_date 一致            |

输出参数

| 名称     | 类型      | 描述     |
|--------|---------|--------|
| date   | object  | 间隔10分钟 |
| AUDJPY | float64 | -      |
| AUDUSD | float64 | -      |
| EURAUD | float64 | -      |
| EURJPY | float64 | -      |
| EURUSD | float64 | -      |
| GBPJPY | float64 | -      |
| GBPUSD | float64 | -      |
| NZDUSD | float64 | -      |
| USDCAD | float64 | -      |
| USDCHF | float64 | -      |
| USDJPY | float64 | -      |
| USDX   | float64 | -      |
| XAUUSD | float64 | -      |

接口示例

```python
import akshare as ak
from datetime import datetime

test_date = datetime.now().date().isoformat().replace("-", "")
macro_fx_sentiment_df = ak.macro_fx_sentiment(start_date=test_date, end_date=test_date)
print(macro_fx_sentiment_df)
```

数据示例

```
                 date  AUDJPY  AUDUSD  EURAUD  ...  USDCHF  USDJPY   USDX  XAUUSD
0    2022-10-11 00:00   52.45   72.53   44.73  ...   39.59   33.90  37.00   72.38
1    2022-10-11 00:10   52.46   72.47   44.85  ...   39.10   33.78  36.49   72.43
2    2022-10-11 00:20   52.48   72.23   45.37  ...   39.10   33.88  36.48   72.75
3    2022-10-11 00:30   52.38   72.34   44.71  ...   38.90   33.73  36.34   72.83
4    2022-10-11 00:40   52.31   72.48   44.44  ...   38.80   33.61  36.23   72.82
..                ...     ...     ...     ...  ...     ...     ...    ...     ...
962  2022-10-17 19:30   54.86   68.14   36.53  ...   42.80   30.48  39.77   60.45
963  2022-10-17 19:40   54.43   67.81   37.01  ...   43.54   30.83  39.82   60.73
964  2022-10-17 19:50   54.39   68.46   36.92  ...   43.38   30.75  39.72   60.77
965  2022-10-17 20:00   54.10   68.05   38.01  ...   44.05   30.88  39.49   61.36
966  2022-10-17 20:10   55.51   67.39   36.80  ...   42.82   30.95  39.78   59.70
```

### 外汇行情报价

接口: fx_quote_baidu

目标地址: https://gushitong.baidu.com/top/foreign-common-%E5%B8%B8%E7%94%A8

描述: 百度股市通-外汇-行情榜单

限量: 单次返回指定 symbol 当前时点的行情报价

输入参数

| 名称     | 类型  | 描述                                   |
|--------|-----|--------------------------------------|
| symbol | str | symbol="人民币"; choice of {"人民币", 美元"} |

输出参数

| 名称  | 类型      | 描述  |
|-----|---------|-----|
| 代码  | object  | -   |
| 名称  | object  | -   |
| 最新价 | float64 | -   |
| 涨跌额 | float64 | -   |
| 涨跌幅 | float64 | -   |

接口示例

```python
import akshare as ak

fx_quote_baidu_df = ak.fx_quote_baidu(symbol="人民币")
print(fx_quote_baidu_df)
```

数据示例

```
         代码           名称      最新价     涨跌额     涨跌幅
0    CNYETH  人民币Ethereum   0.0001  0.0000  0.0455
1    CNYBTC       人民币比特币   0.0000  0.0000  0.0453
2    CNYSCR     人民币塞舌尔卢比   2.0303  0.0799  0.0410
3    CNYHTG      人民币海地古德  22.0779  0.3143  0.0144
4    CNYTOP      人民币汤加潘加   0.3446  0.0046  0.0135
..      ...          ...      ...     ...     ...
138  CNYXPT      人民币珀价盎司   0.0001 -0.0000 -0.0165
139  CNYLKR    人民币斯里兰卡卢比  49.1505 -0.8839 -0.0177
140  CNYBDT     人民币孟加拉塔卡  15.1800 -0.2900 -0.0187
141  CNYPKR    人民币巴基斯坦卢比  39.9572 -0.8003 -0.0196
142  CNYGHS      人民币加纳塞地   1.8169 -0.0413 -0.0222
```

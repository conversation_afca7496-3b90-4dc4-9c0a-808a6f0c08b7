## [AKShare](https://github.com/akfamily/akshare) 指数数据

### A股股票指数

#### 实时行情数据-东财

接口: stock_zh_index_spot_em

目标地址: https://quote.eastmoney.com/center/gridlist.html#index_sz

描述: 东方财富网-行情中心-沪深京指数

限量: 单次返回所有指数的实时行情数据

输入参数

| 名称     | 类型  | 描述                                                                         |
|--------|-----|----------------------------------------------------------------------------|
| symbol | str | symbol="上证系列指数"；choice of {"沪深重要指数", "上证系列指数", "深证系列指数", "指数成份", "中证系列指数"} |

输出参数

| 名称  | 类型      | 描述      |
|-----|---------|---------|
| 序号  | int64   | -       |
| 代码  | object  | -       |
| 名称  | object  | -       |
| 最新价 | float64 | -       |
| 涨跌额 | float64 | -       |
| 涨跌幅 | float64 | 注意单位: % |
| 成交量 | float64 | -       |
| 成交额 | float64 | -       |
| 振幅  | float64 | 注意单位: % |
| 最高  | float64 | -       |
| 最低  | float64 | -       |
| 今开  | float64 | -       |
| 昨收  | float64 | -       |
| 量比  | float64 | -       |

接口示例

```python
import akshare as ak

stock_zh_index_spot_em_df = ak.stock_zh_index_spot_em(symbol="上证系列指数")
print(stock_zh_index_spot_em_df)
```

数据示例

```
      序号  代码     名称    最新价   涨跌幅   涨跌额  ... 振幅  最高  最低 今开 昨收  量比
0      1  000116  信用100   183.69  0.01  0.02  ...  0.0 NaN NaN NaN   183.67 NaN
1      2  000101   5年信用   233.04  0.01  0.02  ...  0.0 NaN NaN NaN   233.02 NaN
2      3  000022   沪公司债   234.63  0.01  0.02  ...  0.0 NaN NaN NaN   234.61 NaN
3      4  000061  沪企债30   169.79  0.01  0.01  ...  0.0 NaN NaN NaN   169.78 NaN
4      5  000012   国债指数   206.03  0.00  0.01  ...  0.0 NaN NaN NaN   206.02 NaN
..   ...     ...    ...      ...   ...   ...  ...  ...  ..  ..  ..      ...  ..
174  175  000005   商业指数  2351.11  0.00  0.00  ...  0.0 NaN NaN NaN  2351.11 NaN
175  176  000004   工业指数  2703.99  0.00  0.00  ...  0.0 NaN NaN NaN  2703.99 NaN
176  177  000003   Ｂ股指数   234.19  0.00  0.00  ...  0.0 NaN NaN NaN   234.19 NaN
177  178  000002   Ａ股指数  3111.03  0.00  0.00  ...  0.0 NaN NaN NaN  3111.03 NaN
178  179  000001   上证指数  2967.25  0.00  0.00  ...  0.0 NaN NaN NaN  2967.25 NaN
[179 rows x 14 columns]
```

#### 实时行情数据-新浪

接口: stock_zh_index_spot_sina

目标地址: https://vip.stock.finance.sina.com.cn/mkt/#hs_s

描述: 新浪财经-中国股票指数数据

限量: 单次返回所有指数的实时行情数据

输入参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| -   | -   | -   |

输出参数

| 名称  | 类型      | 描述      |
|-----|---------|---------|
| 代码  | object  | -       |
| 名称  | object  | -       |
| 最新价 | float64 | -       |
| 涨跌额 | float64 | -       |
| 涨跌幅 | float64 | 注意单位: % |
| 昨收  | float64 | -       |
| 今开  | float64 | -       |
| 最高  | float64 | -       |
| 最低  | float64 | -       |
| 成交量 | float64 | 注意单位: 手 |
| 成交额 | float64 | 注意单位: 元 |

接口示例

```python
import akshare as ak

stock_zh_index_spot_sina_df = ak.stock_zh_index_spot_sina()
print(stock_zh_index_spot_sina_df)
```

数据示例

```
     代码      名称      最新价  ...       最低        成交量           成交额
0    sh000001  上证指数  2967.2472  ...  2953.2901   285455945  321018391310
1    sh000002  Ａ股指数  3111.0281  ...  3096.3842   285134140  320788878153
2    sh000003  Ｂ股指数   234.1943  ...   232.4912      297879     160673330
3    sh000004  工业指数  2703.9886  ...  2690.2040   168709808  228294788620
4    sh000005  商业指数  2351.1093  ...  2339.6375    23082870   23470835715
..        ...   ...        ...  ...        ...         ...           ...
552  sz980015  疫苗生科  8560.3130  ...  8533.3370   421452161   12430783309
553  sz980016  公卫健康  6844.2740  ...  6824.8810   *********   16723915499
554  sz980028  龙头家电  9330.1080  ...  9296.8940   *********    7181802948
555  sz980030  消费电子  4393.0420  ...  4372.5400  1645434624   24770906620
556  sz980032  新能电池  8862.8690  ...  8791.8960   *********   13001077515
[557 rows x 11 columns]
```

#### 历史行情数据

##### 历史行情数据-新浪

接口: stock_zh_index_daily

目标地址: https://finance.sina.com.cn/realstock/company/sz399552/nc.shtml(示例)

描述: 股票指数的历史数据按日频率更新

限量: 单次返回指定 symbol 的所有历史行情数据

输入参数

| 名称     | 类型  | 描述                |
|--------|-----|-------------------|
| symbol | str | symbol="sz399552" |

输出参数-历史行情数据

| 名称     | 类型      | 描述                    |
|--------|---------|-----------------------|
| date   | object  | 新浪的数据开始时间, 不是该指数的上市时间 |
| open   | float64 | -                     |
| high   | float64 | -                     |
| low    | float64 | -                     |
| close  | float64 | -                     |
| volume | int64   | -                     |

接口示例-历史行情数据

```python
import akshare as ak

stock_zh_index_daily_df = ak.stock_zh_index_daily(symbol="sz399552")
print(stock_zh_index_daily_df)
```

数据示例-历史行情数据

```
            date      open      high       low     close       volume
0     2005-01-04   996.682   996.682   984.795   986.927    232376203
1     2005-01-05   986.570  1008.855   985.677  1003.633    348610113
2     2005-01-06  1003.490  1003.490   990.792   994.595    293390559
3     2005-01-07   993.434  1009.000   990.446   997.606    339162698
4     2005-01-10   996.928  1006.457   993.123  1006.337    294940533
          ...       ...       ...       ...       ...          ...
4566  2023-10-23  5400.543  5400.794  5269.828  5290.334  10308312600
4567  2023-10-24  5311.669  5363.195  5290.505  5352.012  11474572900
4568  2023-10-25  5386.425  5423.014  5375.999  5375.999  11492124400
4569  2023-10-26  5351.163  5404.992  5337.493  5398.934  10248504400
4570  2023-10-27  5387.274  5514.308  5380.941  5492.027  11254280400
[4571 rows x 6 columns]
```

##### 历史行情数据-腾讯

接口: stock_zh_index_daily_tx

目标地址: https://gu.qq.com/sh000919/zs

描述: 股票指数(或者股票)历史行情数据

限量: 单次返回具体某个股票指数(或者股票)的所有历史行情数据

输入参数-历史行情数据

| 名称     | 类型  | 描述                |
|--------|-----|-------------------|
| symbol | str | symbol="sh000919" |

输出参数-历史行情数据

| 名称     | 类型      | 描述                  |
|--------|---------|---------------------|
| date   | object  | 腾讯的数据开始时间, 不是证券上市时间 |
| open   | float64 | -                   |
| close  | float64 | -                   |
| high   | float64 | -                   |
| low    | float64 | -                   |
| amount | float64 | 注意单位: 手             |

接口示例

```python
import akshare as ak

stock_zh_index_daily_tx_df = ak.stock_zh_index_daily_tx(symbol="sh000919")
print(stock_zh_index_daily_tx_df)
```

数据示例

```
            date     open    close     high      low       amount
0     2005-01-04   993.11   978.14   993.11   976.82   4235501.99
1     2005-01-05   976.94   981.50   985.52   972.80   3366738.80
2     2005-01-06   982.37   968.90   982.37   966.52   3137876.84
3     2005-01-07   968.77   967.72   978.27   963.10   3340483.54
4     2005-01-10   967.81   977.77   978.15   963.31   2680018.64
          ...      ...      ...      ...      ...          ...
4566  2023-10-23  4169.32  4140.72  4177.63  4115.03  51476987.00
4567  2023-10-24  4146.42  4148.72  4157.80  4119.85  63088387.00
4568  2023-10-25  4185.97  4158.66  4190.35  4149.74  70715384.00
4569  2023-10-26  4139.06  4174.96  4177.47  4134.28  54267034.00
4570  2023-10-27  4159.83  4179.02  4194.93  4146.62  60862014.00
[4571 rows x 6 columns]
```

##### 历史行情数据-东方财富

接口: stock_zh_index_daily_em

目标地址: http://quote.eastmoney.com/center/hszs.html

描述: 东方财富股票指数数据, 历史数据按日频率更新

限量: 单次返回具体指数的所有历史行情数据

输入参数

| 名称         | 类型  | 描述                                                             |
|------------|-----|----------------------------------------------------------------|
| symbol     | str | symbol="sz399552"; 支持 sz: 深交所, sh: 上交所, csi: 中证指数 + id(000905) |
| start_date | str | start_date="19900101"                                          |
| end_date   | str | end_date="20500101"                                            |

输出参数

| 名称     | 类型      | 描述                    |
|--------|---------|-----------------------|
| date   | object  | 东方财富的数据开始时间, 不是证券上市时间 |
| open   | float64 | -                     |
| close  | float64 | -                     |
| high   | float64 | -                     |
| low    | float64 | -                     |
| volume | int64   | -                     |
| amount | float64 | -                     |

接口示例

```python
import akshare as ak

stock_zh_index_daily_em_df = ak.stock_zh_index_daily_em(symbol="sz399812")
print(stock_zh_index_daily_em_df)
```

数据示例

```
            date     open    close     high      low    volume        amount
0     2005-01-04   996.03   989.56   996.03   986.46    675733  4.986503e+08
1     2005-01-05   989.87  1008.59  1011.29   989.46   1037894  9.068431e+08
2     2005-01-06  1008.88  1002.81  1008.88   999.76    779152  5.631133e+08
3     2005-01-07  1002.10  1004.06  1015.61   999.56    898377  7.554397e+08
4     2005-01-10  1002.63  1014.12  1014.12  1000.90    651187  5.609582e+08
          ...      ...      ...      ...      ...       ...           ...
4566  2023-10-23  5659.09  5590.27  5666.15  5563.09   7956295  1.752549e+10
4567  2023-10-24  5608.75  5692.22  5700.26  5590.94   8032521  1.902381e+10
4568  2023-10-25  5735.01  5713.71  5751.73  5713.65   8597481  2.057249e+10
4569  2023-10-26  5694.04  5749.56  5755.59  5684.16   8636096  2.021819e+10
4570  2023-10-27  5747.77  5952.02  5969.61  5741.26  11493696  3.220613e+10
[4571 rows x 7 columns]
```

##### 历史行情数据-通用

接口: index_zh_a_hist

目标地址: http://quote.eastmoney.com/center/hszs.html

描述: 东方财富网-中国股票指数-行情数据

限量: 单次返回具体指数指定 period 从 start_date 到 end_date 的之间的近期数据

输入参数

| 名称         | 类型  | 描述                                                       |
|------------|-----|----------------------------------------------------------|
| symbol     | str | symbol="399282"; 指数代码，此处不用市场标识                           |
| period     | str | period="daily"; choice of {'daily', 'weekly', 'monthly'} |
| start_date | str | start_date="19700101"; 开始日期                              |
| end_date   | str | end_date="22220101"; 结束时间                                |

输出参数

| 名称  | 类型      | 描述      |
|-----|---------|---------|
| 日期  | object  | 交易日     |
| 开盘  | float64 | 开盘价     |
| 收盘  | float64 | 收盘价     |
| 最高  | float64 | 最高价     |
| 最低  | float64 | 最低价     |
| 成交量 | int32   | 注意单位: 手 |
| 成交额 | float64 | 注意单位: 元 |
| 振幅  | float64 | 注意单位: % |
| 涨跌幅 | float64 | 注意单位: % |
| 涨跌额 | float64 | 注意单位: 元 |
| 换手率 | float64 | 注意单位: % |

接口示例

```python
import akshare as ak

index_zh_a_hist_df = ak.index_zh_a_hist(symbol="000016", period="daily", start_date="19700101", end_date="22220101")
print(index_zh_a_hist_df)
```

数据示例

```
       日期       开盘       收盘       最高  ...   振幅   涨跌幅 涨跌额   换手率
0     2004-01-02   997.00  1011.35  1021.57  ...  0.00  0.00   0.00  0.00
1     2004-01-05  1008.28  1060.80  1060.90  ...  5.20  4.89  49.45  0.00
2     2004-01-06  1059.14  1075.66  1086.69  ...  2.60  1.40  14.86  0.00
3     2004-01-07  1075.56  1086.30  1095.84  ...  2.31  0.99  10.64  0.00
4     2004-01-08  1087.68  1102.66  1108.29  ...  2.37  1.51  16.36  0.00
          ...      ...      ...      ...  ...   ...   ...    ...   ...
4400  2022-02-15  3101.36  3119.93  3126.42  ...  0.95  0.51  15.93  0.24
4401  2022-02-16  3134.88  3136.08  3150.86  ...  0.70  0.52  16.15  0.20
4402  2022-02-17  3138.77  3139.18  3156.87  ...  0.79  0.10   3.10  0.20
4403  2022-02-18  3126.28  3162.82  3162.82  ...  1.30  0.75  23.64  0.19
4404  2022-02-21  3155.32  3144.86  3155.88  ...  0.86 -0.57 -17.96  0.21
```

#### 分时行情数据

接口: index_zh_a_hist_min_em

目标地址: https://quote.eastmoney.com/center/hszs.html

描述: 东方财富网-指数数据-分时行情

限量: 单次返回具体指数指定 period 从 start_date 到 end_date 的之间的近期数据，该接口不能返回所有历史数据

输入参数

| 名称         | 类型  | 描述                                                                                |
|------------|-----|-----------------------------------------------------------------------------------|
| symbol     | str | symbol="399006"; 指数代码，此处不用市场标识                                                    |
| period     | str | period="1"; choice of {'1', '5', '15', '30', '60'}, 其中 1 分钟数据只能返回当前的, 其余只能返回近期的数据 |
| start_date | str | start_date="1979-09-01 09:32:00"; 开始日期时间                                          |
| end_date   | str | end_date="2222-01-01 09:32:00"; 结束时间时间                                            |

输出参数

| 名称  | 类型      | 描述      |
|-----|---------|---------|
| 时间  | object  | 交易日     |
| 开盘  | float64 | 开盘价     |
| 收盘  | float64 | 收盘价     |
| 最高  | float64 | 最高价     |
| 最低  | float64 | 最低价     |
| 成交量 | int64   | 注意单位: 手 |
| 成交额 | float64 | 注意单位: 元 |
| 均价  | float64 | -       |

接口示例

```python
import akshare as ak

index_zh_a_hist_min_em_df = ak.index_zh_a_hist_min_em(symbol="000001", period="1", start_date="2023-12-11 09:30:00", end_date="2023-12-11 19:00:00")
print(index_zh_a_hist_min_em_df)
```

数据示例

```
           时间       开盘       收盘  ...       成交量           成交额       均价
0    2023-12-11 09:30:00  2956.29  2956.29  ...   3198631  3.394971e+09  2957.681
1    2023-12-11 09:31:00  2955.35  2937.70  ...  12531907  1.227149e+10  2945.626
2    2023-12-11 09:32:00  2938.36  2935.57  ...   8823405  8.810568e+09  2940.907
3    2023-12-11 09:33:00  2935.72  2932.00  ...   7235184  7.282885e+09  2940.683
4    2023-12-11 09:34:00  2932.48  2933.37  ...   5309002  5.608316e+09  2945.141
..                   ...      ...      ...  ...       ...           ...       ...
236  2023-12-11 14:56:00  2990.23  2989.94  ...   2032283  2.301672e+09  2999.493
237  2023-12-11 14:57:00  2989.94  2990.58  ...   2234140  2.480918e+09  3000.265
238  2023-12-11 14:58:00  2990.73  2990.63  ...    128204  1.482932e+08  3000.532
239  2023-12-11 14:59:00  2990.63  2990.63  ...         0  0.000000e+00  3000.532
240  2023-12-11 15:00:00  2990.63  2991.44  ...   3969436  4.319616e+09  3001.557
[241 rows x 8 columns]
```

### 港股股票指数

#### 实时行情数据-新浪

接口: stock_hk_index_spot_sina

目标地址: https://vip.stock.finance.sina.com.cn/mkt/#zs_hk

描述: 新浪财经-行情中心-港股指数

限量: 单次返回所有数据

输入参数

| 名称 | 类型 | 描述 |
|----|----|----|
| -  | -  | -  |

输出参数

| 名称  | 类型      | 描述      |
|-----|---------|---------|
| 代码  | object  | -       |
| 名称  | object  | -       |
| 最新价 | float64 | -       |
| 涨跌额 | float64 | -       |
| 涨跌幅 | float64 | 注意单位: % |
| 昨收  | float64 | -       |
| 今开  | float64 | -       |
| 最高  | float64 | -       |
| 最低  | float64 | -       |

接口示例

```python
import akshare as ak

stock_hk_index_spot_sina_df = ak.stock_hk_index_spot_sina()
print(stock_hk_index_spot_sina_df)
```

数据示例

```
          代码               名称        最新价  ...         今开         最高         最低
0     CES100     中华港股通精选100指数   3648.330  ...   3702.688   3712.628   3647.700
1     CES120          中华120指数   5025.017  ...   5086.739   5107.486   5024.949
2     CES280          中华280指数   5665.616  ...   5717.851   5731.186   5662.476
3     CES300       中华沪深港300指数   3795.410  ...   3839.315   3850.479   3795.114
4     CESA80          中华A80指数   6451.657  ...   6513.025   6534.666   6439.589
5     CESG10          中华博彩业指数   3255.044  ...   3259.564   3282.658   3239.144
6     CESHKM         中华香港内地指数   4542.094  ...   4610.777   4632.182   4542.094
7      CSCMC         中证内地消费指数  10182.399  ...  10292.709  10347.834  10165.551
8    CSHK100        中证香港100指数   1470.354  ...   1492.664   1497.654   1470.354
9    CSHKDIV       中证香港红利港币指数   2666.181  ...   2696.106   2699.463   2665.965
10    CSHKLC  中证香港上市可交易内地消费指数    972.576  ...    986.668    991.827    972.160
11   CSHKLRE  中证香港上市可交易内地地産指数    261.217  ...    271.532    272.671    260.913
12   CSHKMCS     中证香港中盘精选港币指数   1895.666  ...   1919.041   1921.188   1895.014
13    CSHKME      中证香港内地股港元指数   1968.482  ...   1999.449   2008.286   1968.482
14    CSHKPE     中证香港内地民营企业指数   1065.490  ...   1083.291   1089.320   1065.315
15    CSHKSE     中证香港内地国有企业指数    577.901  ...    586.195    586.576    577.901
16    CSI300          沪深300指数   3475.839  ...   3505.750   3516.699   3470.061
17   CSRHK50  中证锐联香港基本面50港币指数   1507.089  ...   1527.417   1529.470   1506.434
18       GEM        标普香港创业板指数     15.920  ...     16.150     16.170     15.890
19       HKL        标普香港大型股指数  24092.189  ...  24486.980  24596.560  24092.190
20     HSCCI       恒生香港中资企业指数   3461.570  ...   3526.840   3526.840   3457.380
21     HSCEI         恒生中国企业指数   5879.580  ...   5972.800   5997.400   5879.580
22       HSI             恒生指数  16721.689  ...  16978.700  17031.310  16721.690
23     HSMBI       恒生中国内地银行指数   2538.970  ...   2572.330   2585.650   2538.570
24    HSMOGI   恒生中国内地石油及天然气指数   2115.530  ...   2112.430   2125.260   2099.070
25     HSMPI       恒生中国内地地产指数   1183.130  ...   1224.630   1231.290   1180.770
26    HSTECH           恒生科技指数   3474.470  ...   3523.710   3549.310   3472.430
27    SSE180          上证180指数   7619.146  ...   7682.757   7698.929   7607.821
28  SSE180GV      上证180公司治理指数    898.229  ...    905.148    906.478    896.845
29    SSE380          上证380指数   5125.197  ...   5147.366   5170.676   5118.144
30     SSE50           上证50指数   2374.047  ...   2397.284   2402.643   2370.277
31   SSECEQT       上证大宗商品股票指数   2760.349  ...   2768.583   2779.282   2746.892
32   SSECOMP           上证综合指数   3019.474  ...   3035.577   3043.560   3014.830
33    SSEDIV           上证红利指数   3171.188  ...   3171.996   3189.183   3157.513
34   SSEITOP         上证龙头企业指数   2952.552  ...   2970.302   2976.126   2948.095
35   SSEMCAP           上证中盘指数   3469.023  ...   3490.601   3498.174   3464.152
36   SSEMEGA         上证超级大盘指数   1864.488  ...   1882.455   1887.538   1860.408
37      VHSI           恒指波幅指数     22.400  ...     22.460     22.620     21.870
[38 rows x 9 columns]
```

#### 历史行情数据-新浪

接口: stock_hk_index_daily_sina

目标地址: https://stock.finance.sina.com.cn/hkstock/quotes/CES100.html

描述: 新浪财经-港股指数-历史行情数据

限量: 单次返回指定 symbol 的所有数据

输入参数

| 名称     | 类型  | 描述              |
|--------|-----|-----------------|
| symbol | str | symbol="CES100" |

输出参数

| 名称     | 类型      | 描述      |
|--------|---------|---------|
| date   | object  | -       |
| open   | object  | -       |
| close  | float64 | -       |
| high   | float64 | -       |
| low    | float64 | 注意单位: % |
| volume | float64 | -       |

接口示例

```python
import akshare as ak

stock_hk_index_daily_sina_df = ak.stock_hk_index_daily_sina(symbol="CES100")
print(stock_hk_index_daily_sina_df)
```

数据示例

```
            date      open      high       low     close      volume
0     2014-12-15  4354.014  4362.220  4331.394  4355.359  1087163017
1     2014-12-16  4334.295  4347.367  4275.529  4280.410  1303011243
2     2014-12-17  4278.770  4280.350  4223.895  4227.100  2002919200
3     2014-12-18  4281.007  4296.184  4264.265  4282.028  1547580777
4     2014-12-19  4347.982  4354.488  4321.534  4331.438  1515374212
          ...       ...       ...       ...       ...         ...
2293  2024-04-08  3645.559  3688.903  3624.238  3654.013  2067842312
2294  2024-04-09  3678.340  3719.132  3677.429  3686.826  1588737425
2295  2024-04-10  3706.046  3752.432  3701.602  3741.842  1947224622
2296  2024-04-11  3678.382  3736.533  3676.288  3725.992  1829890643
2297  2024-04-12  3702.688  3712.628  3647.700  3648.330  2097570788
[2298 rows x 6 columns]
```

#### 实时行情数据-东财

接口: stock_hk_index_spot_em

目标地址: https://quote.eastmoney.com/center/gridlist.html#hk_index

描述: 东方财富网-行情中心-港股-指数实时行情

限量: 单次返回所有数据

输入参数

| 名称 | 类型 | 描述 |
|----|----|----|
| -  | -  | -  |

输出参数

| 名称   | 类型      | 描述       |
|------|---------|----------|
| 序号   | int64   | -        |
| 内部编号 | int64   | -        |
| 代码   | object  | -        |
| 名称   | object  | -        |
| 最新价  | float64 | -        |
| 涨跌额  | float64 | -        |
| 涨跌幅  | float64 | 注意单位: %  |
| 今开   | float64 | -        |
| 最高   | float64 | -        |
| 最低   | float64 | -        |
| 昨收   | float64 | -        |
| 成交量  | float64 | -        |
| 成交额  | float64 | 注意单位: 港元 |

接口示例

```python
import akshare as ak

stock_hk_index_spot_em_df = ak.stock_hk_index_spot_em()
print(stock_hk_index_spot_em_df)
```

数据示例

```
      序号  内部编号        代码  ...       昨收          成交量           成交额
0      1   124      HSSH  ...  3193.38          0.0  3.222019e+09
1      2   124  HSTECF2S  ...  3012.59          0.0           NaN
2      3   124    HST2SI  ...   669.27          0.0           NaN
3      4   124    HSCATI  ...  2948.24          0.0  8.038308e+09
4      5   124    HSCIEN  ...  9125.68  398107136.0  1.974318e+09
..   ...   ...       ...  ...      ...          ...           ...
354  355   124    HSCASI  ...  2444.56          0.0  1.127230e+10
355  356   124    HSSCID  ...  2102.39          0.0  3.358686e+09
356  357   124    HSSCPB  ...  1477.63          0.0  3.409686e+09
357  358   124     HSIDI  ...  1879.83          0.0  3.344741e+09
358  359   125    CESHKB  ...  5000.55          0.0  2.768274e+09
[359 rows x 13 columns]
```

#### 历史行情数据-东财

接口: stock_hk_index_daily_em

目标地址: https://quote.eastmoney.com/gb/zsHSTECF2L.html

描述: 东方财富网-港股-股票指数数据

限量: 单次返回指定 symbol 的所有数据

输入参数

| 名称     | 类型  | 描述                                                     |
|--------|-----|--------------------------------------------------------|
| symbol | str | symbol="HSTECF2L"; 可以通过 ak.stock_hk_index_spot_em() 获取 |

输出参数

| 名称     | 类型      | 描述  |
|--------|---------|-----|
| date   | object  | -   |
| open   | float64 | -   |
| high   | float64 | -   |
| low    | float64 | -   |
| latest | float64 | 最新价 |

接口示例

```python
import akshare as ak

stock_zh_index_daily_em_df = ak.stock_hk_index_daily_em(symbol="HSTECF2L")
print(stock_zh_index_daily_em_df)
```

数据示例

```
           date     open     high      low   latest
0    2021-04-12  3004.54  3031.66  2864.62  2873.91
1    2021-04-13  2885.20  2931.77  2781.46  2799.10
2    2021-04-14  2907.05  2970.43  2887.55  2948.15
3    2021-04-15  2914.56  2931.71  2785.23  2871.69
4    2021-04-16  2844.18  2960.53  2840.66  2957.73
..          ...      ...      ...      ...      ...
675  2024-01-08   313.48   317.70   295.35   296.93
676  2024-01-09   300.03   307.01   291.85   292.02
677  2024-01-10   288.51   296.62   284.45   289.18
678  2024-01-11   288.87   306.52   288.03   300.63
679  2024-01-12   296.03   305.46   291.92   294.83
[680 rows x 5 columns]
```

### 美股股票指数

#### 指数行情

接口: index_us_stock_sina

目标地址: https://stock.finance.sina.com.cn/usstock/quotes/.IXIC.html

描述: 新浪财经-美股指数行情

输入参数

| 名称     | 类型  | 描述                                                         |
|--------|-----|------------------------------------------------------------|
| symbol | str | symbol=".INX"; choice of {".IXIC", ".DJI", ".INX", ".NDX"} |

输出参数

| 名称     | 类型      | 描述 |
|--------|---------|----|
| date   | object  | -  |
| open   | float64 | -  |
| high   | float64 | -  |
| low    | float64 | -  |
| close  | float64 | -  |
| volume | int64   | -  |
| amount | int64   | -  |

接口示例

```python
import akshare as ak

index_us_stock_sina_df = ak.index_us_stock_sina(symbol=".INX")
print(index_us_stock_sina_df)
```

数据示例

```
          date       open       high  ...      close      volume         amount
0     2004-01-02  1111.9200  1118.8500  ...  1108.4800  1153200000  1281300000000
1     2004-01-05  1108.4800  1122.2200  ...  1122.2200  1578200064  1760250000000
2     2004-01-06  1122.2200  1124.4600  ...  1123.6700  1494499968  1677120000000
3     2004-01-07  1123.6700  1126.3300  ...  1126.3300  1704899968  1914940000000
4     2004-01-08  1126.3300  1131.9200  ...  1131.9200  1868400000  2108990000000
          ...        ...        ...  ...        ...         ...            ...
4951  2023-08-30  4500.3398  4521.6499  ...  4514.8701  1827645178              0
4952  2023-08-31  4517.0098  4532.2598  ...  4507.6602  2352361081              0
4953  2023-09-01  4530.6001  4541.2500  ...  4515.7700  1958155311              0
4954  2023-09-05  4510.0601  4514.2900  ...  4496.8301  2128341430              0
4955  2023-09-06  4490.3501  4490.3501  ...  4465.4800  2122160141              0
[4956 rows x 7 columns]
```

### 全球指数

#### 全球指数-实时行情数据

接口: index_global_spot_em

目标地址: https://quote.eastmoney.com/center/gridlist.html#global_qtzs

描述: 东方财富网-行情中心-全球指数-实时行情数据

输入参数

| 名称 | 类型 | 描述 |
|----|----|----|
| -  | -  | -  |

输出参数

| 名称     | 类型      | 描述          |
|--------|---------|-------------|
| 序号     | object  | -           |
| 代码     | object  | -           |
| 名称     | object  | -           |
| 最新价    | float64 | -           |
| 涨跌额    | float64 | -           |
| 涨跌幅    | float64 | -           |
| 开盘价    | float64 | -           |
| 最高价    | float64 | -           |
| 最低价    | float64 | -           |
| 昨收价    | float64 | -           |
| 振幅     | float64 | -           |
| 最新行情时间 | object  | 注意是指数所在地的时间 |

接口示例

```python
import akshare as ak

index_global_spot_em_df = ak.index_global_spot_em()
print(index_global_spot_em_df)
```

数据示例

```
    序号     代码          名称  ...        昨收价    振幅          最新行情时间
0    1       BDI   波罗的海BDI指数  ...    1228.00  0.00  2025-03-06 12:56:29
1    2       PSI      菲律宾马尼拉  ...    6219.96  1.32  2025-03-07 14:50:43
2    3     PSI20    葡萄牙PSI20  ...    6690.48  1.25  2025-03-07 18:05:45
3    4       RTS      俄罗斯RTS  ...    1128.13  1.02  2025-03-07 18:20:38
4    5       SET       泰国SET  ...    1189.55  1.27  2025-03-07 17:29:50
5    6    KSE100     巴基斯坦卡拉奇  ...  113713.17  0.40  2025-03-07 15:30:00
6    7   VNINDEX       越南胡志明  ...    1318.22  0.67  2025-03-07 15:57:49
7    8     ICEXI      冰岛ICEX  ...    2273.38  0.52  2025-03-07 18:20:16
8    9     HSCCI        红筹指数  ...    3901.21  2.20  2025-03-07 16:09:00
9   10      JKSE     印尼雅加达综合  ...    6617.85  1.59  2025-03-07 16:50:00
10  11     OSEBX     挪威OSEBX  ...    1476.13  0.63  2025-03-07 18:20:47
11  12       MXX    墨西哥BOLSA  ...   52709.40  1.86  2025-03-07 04:59:59
12  13       WIG       波兰WIG  ...   93444.96  1.07  2025-03-07 18:05:00
13  14      BVSP   巴西BOVESPA  ...  123230.18  1.16  2025-03-07 04:30:00
14  15        PX       布拉格指数  ...    2020.44  0.52  2025-03-07 18:20:09
15  16       ASE     希腊雅典ASE  ...    1625.54  0.74  2025-03-07 18:05:31
16  17    SENSEX  印度孟买SENSEX  ...   74340.09  0.72  2025-03-07 17:59:58
17  18    CSEALL     斯里兰卡科伦坡  ...   16123.10  0.46  2025-03-07 17:00:00
18  19       AEX       荷兰AEX  ...     910.72  0.36  2025-03-07 18:05:45
19  20       STI   富时新加坡海峡时报  ...    3917.06  0.80  2025-03-07 17:20:00
20  21       CRB   路透CRB商品指数  ...     301.47  0.13  2025-03-07 01:00:01
21  22       BFX      比利时BFX  ...    4463.37  0.46  2025-03-07 18:05:45
22  23      NZ50       新西兰50  ...   12428.84  0.99  2025-03-07 11:45:34
23  24    000001        上证指数  ...    3381.10  0.80  2025-03-07 16:11:40
24  25       AXX     富时AIM全股  ...     692.46  0.54  2025-03-07 18:20:00
25  26     HSCEI        国企指数  ...    8938.09  2.75  2025-03-07 16:09:00
26  27    000300       沪深300  ...    3956.24  0.81  2025-03-07 16:11:58
27  28       MIB    富时意大利MIB  ...   38779.67  0.87  2025-03-07 18:20:54
28  29      SSMI       瑞士SMI  ...   13029.39  0.82  2025-03-07 18:05:49
29  30    399005       中小100  ...    6757.01  1.23  2025-03-07 15:34:42
30  31      IBEX   西班牙IBEX35  ...   13234.20  0.60  2025-03-07 18:05:49
31  32      KS11     韩国KOSPI  ...    2576.16  1.32  2025-03-07 14:29:59
32  33    399001        深证成指  ...   10898.75  1.23  2025-03-07 15:34:09
33  34      FTSE     英国富时100  ...    8682.84  0.64  2025-03-07 18:20:53
34  35  KOSPI200  韩国KOSPI200  ...     340.31  1.38  2025-03-07 14:29:59
35  36       UDI        美元指数  ...     104.20  0.67  2025-03-07 18:20:50
36  37       HSI        恒生指数  ...   24369.71  2.48  2025-03-07 16:09:00
37  38       HEX      芬兰赫尔辛基  ...   10592.07  0.78  2025-03-07 18:20:17
38  39      ISEQ       爱尔兰综合  ...   11194.44  1.15  2025-03-07 18:05:45
39  40      TWII        台湾加权  ...   22715.43  0.90  2025-03-07 13:30:00
40  41    OMXSPI    瑞典OMXSPI  ...    2733.02  1.07  2025-03-07 18:20:56
41  42      SX5E     欧洲斯托克50  ...    5520.47  0.87  2025-03-07 18:05:45
42  43      KLSE  富时马来西亚KLCI  ...    1558.91  0.85  2025-03-07 17:05:00
43  44       ATX      奥地利ATX  ...    4329.90  1.44  2025-03-07 18:05:48
44  45      FCHI     法国CAC40  ...    8197.67  1.03  2025-03-07 18:05:45
45  46       MCX     英国富时250  ...   20159.07  1.05  2025-03-07 18:20:53
46  47      DJIA         道琼斯  ...   43006.59  1.37  2025-03-07 04:59:44
47  48    OMXC20   OMX哥本哈根20  ...    2103.36  0.78  2025-03-07 18:20:53
48  49       TSX  加拿大S&P/TSX  ...   24870.82  1.41  2025-03-07 04:59:46
49  50     GDAXI     德国DAX30  ...   23419.48  0.94  2025-03-07 18:05:56
50  51    399006        创业板指  ...    2234.69  1.38  2025-03-07 15:34:36
51  52      AORD     澳大利亚普通股  ...    8326.40  1.80  2025-03-07 12:59:44
52  53       SPX       标普500  ...    5842.63  1.72  2025-03-07 04:59:44
53  54      AS51   澳大利亚标普200  ...    8094.70  1.83  2025-03-07 12:59:44
54  55      N225       日经225  ...   37704.93  1.08  2025-03-07 14:30:00
55  56       NDX        纳斯达克  ...   18552.73  2.47  2025-03-07 05:00:00
[56 rows x 12 columns]
```

#### 全球指数-历史行情数据-东财

接口: index_global_hist_em

目标地址: https://quote.eastmoney.com/gb/zsUDI.html

描述: 东方财富网-行情中心-全球指数-历史行情数据

输入参数

| 名称     | 类型  | 描述                                               |
|--------|-----|--------------------------------------------------|
| symbol | str | symbol="美元指数"; 可以通过 ak.index_global_spot_em() 获取 |

输出参数

| 名称  | 类型      | 描述      |
|-----|---------|---------|
| 日期  | object  | -       |
| 代码  | object  | -       |
| 名称  | object  | -       |
| 今开  | float64 | -       |
| 最新价 | float64 | -       |
| 最高  | float64 | -       |
| 最低  | float64 | -       |
| 振幅  | float64 | 主要单位: % |

接口示例

```python
import akshare as ak

index_global_hist_em_df = ak.index_global_hist_em(symbol="美元指数")
print(index_global_hist_em_df)
```

数据示例

```
          日期   代码    名称     今开     最新价   最高     最低    振幅
0      1986-01-09  UDI  美元指数  124.18  123.21  124.71  122.91  0.00
1      1986-01-10  UDI  美元指数  123.98  124.16  124.27  123.46  0.66
2      1986-01-13  UDI  美元指数  124.82  124.47  125.21  124.23  0.79
3      1986-01-14  UDI  美元指数  124.75  124.67  124.91  124.38  0.43
4      1986-01-15  UDI  美元指数  124.64  124.30  124.64  124.07  0.46
...           ...  ...   ...     ...     ...     ...     ...   ...
10027  2025-03-03  UDI  美元指数  107.38  106.54  107.41  106.42  0.92
10028  2025-03-04  UDI  美元指数  106.55  105.55  106.66  105.52  1.07
10029  2025-03-05  UDI  美元指数  105.56  104.32  105.78  104.26  1.44
10030  2025-03-06  UDI  美元指数  104.34  104.20  104.39  103.74  0.62
10031  2025-03-07  UDI  美元指数  104.23  103.63  104.25  103.55  0.67
[10032 rows x 8 columns]
```

#### 全球指数-历史行情数据-新浪

接口: index_global_hist_sina

目标地址: https://finance.sina.com.cn/stock/globalindex/quotes/UKX

描述: 新浪财经-行情中心-环球市场-历史行情

限量：单次返回最近的 1000 条数据

输入参数

| 名称     | 类型  | 描述                                                    |
|--------|-----|-------------------------------------------------------|
| symbol | str | symbol="瑞士股票指数"; 可以通过 ak.index_global_name_table() 获取 |

输出参数

| 名称     | 类型      | 描述 |
|--------|---------|----|
| date   | object  | -  |
| open   | object  | -  |
| high   | object  | -  |
| low    | float64 | -  |
| close  | float64 | -  |
| volume | float64 | -  |

接口示例

```python
import akshare as ak

index_global_hist_sina_df = ak.index_global_hist_sina(symbol="瑞士股票指数")
print(index_global_hist_sina_df)
```

数据示例

```
           date      open      high       low     close    volume
0    2021-03-22  10931.56  11048.58  10928.34  11048.58  51627000
1    2021-03-23  11030.89  11118.33  11016.21  11098.66  48422000
2    2021-03-24  11048.59  11106.94  11033.37  11063.87  43812000
3    2021-03-25  11026.65  11102.78  11017.32  11098.81  52086000
4    2021-03-26  11113.90  11143.86  11091.63  11116.81  46264000
..          ...       ...       ...       ...       ...       ...
995  2025-03-03  13032.86  13199.05  12956.84  13166.68  30859832
996  2025-03-04  13099.49  13146.92  12991.81  13006.74  38551012
997  2025-03-05  13081.21  13161.90  13066.22  13112.75  38270072
998  2025-03-06  13090.73  13105.02  12928.22  13056.91  36533732
999  2025-03-07  12959.80  12965.30  12888.30  12904.70   4468362
[1000 rows x 6 columns]
```

### 中国股票指数成份

#### 最新成份

接口: index_stock_cons

目标地址: http://vip.stock.finance.sina.com.cn/corp/view/vII_NewestComponent.php?page=1&indexid=399639

描述: 指定指数的最新成份股票信息, 注意该接口返回的数据有部分是重复会导致数据缺失, 可以调用 **ak.index_stock_cons_sina()** 获取主流指数数据, 或调用**ak.index_stock_cons_csindex()**获取中证指数网提供的成分数据

输入参数

| 名称     | 类型  | 描述                                                   |
|--------|-----|------------------------------------------------------|
| symbol | str | symbol="000300", 获取沪深 300 最新成份股, 指数代码见 **股票指数信息一览表** |

股票指数信息一览表(可以在 AKShare 中通过如下代码获取本表)

```python
import akshare as ak

index_stock_info_df = ak.index_stock_info()
print(index_stock_info_df)
```

| index\_code | display\_name      | publish\_date |
|-------------|--------------------|---------------|
| 000001      | 上证指数               | 1991/7/15     |
| 000002      | A股指数               | 1992/2/21     |
| 000003      | B股指数               | 1992/2/21     |
| 000004      | 工业指数               | 1993/5/3      |
| 000005      | 商业指数               | 1993/5/3      |
| 000006      | 地产指数               | 1993/5/3      |
| 000007      | 公用指数               | 1993/5/3      |
| 000008      | 综合指数               | 1993/5/3      |
| 000009      | 上证380              | 2010/11/29    |
| 000010      | 上证180              | 2002/7/1      |
| 000011      | 基金指数               | 2000/6/9      |
| 000012      | 国债指数               | 2003/1/2      |
| 000013      | 上证企业债指数            | 2003/6/9      |
| 000015      | 红利指数               | 2005/1/4      |
| 000016      | 上证50               | 2004/1/2      |
| 000017      | 新综指                | 2006/1/4      |
| 000018      | 180金融              | 2007/12/10    |
| 000019      | 治理指数               | 2008/1/2      |
| 000020      | 中型综指               | 2008/5/12     |
| 000021      | 180治理              | 2008/9/10     |
| 000022      | 上证公司债指数            | 2008/11/19    |
| 000025      | 180基建              | 2008/12/15    |
| 000026      | 180资源              | 2008/12/15    |
| 000027      | 180运输              | 2008/12/15    |
| 000028      | 180成长              | 2009/1/9      |
| 000029      | 180价值              | 2009/1/9      |
| 000030      | 180R成长             | 2009/1/9      |
| 000031      | 180R价值             | 2009/1/9      |
| 000032      | 上证能源               | 2009/1/9      |
| 000033      | 上证材料               | 2009/1/9      |
| 000034      | 上证工业               | 2009/1/9      |
| 000035      | 上证可选               | 2009/1/9      |
| 000036      | 上证消费               | 2009/1/9      |
| 000037      | 上证医药               | 2009/1/9      |
| 000038      | 上证金融               | 2009/1/9      |
| 000039      | 上证信息               | 2009/1/9      |
| 000040      | 上证电信               | 2009/1/9      |
| 000041      | 上证公用               | 2009/1/9      |
| 000042      | 上证央企               | 2009/3/30     |
| 000043      | 超大盘                | 2009/4/23     |
| 000044      | 上证中盘               | 2009/7/3      |
| 000045      | 上证小盘               | 2009/7/3      |
| 000046      | 上证中小               | 2009/7/3      |
| 000047      | 上证全指               | 2009/7/3      |
| 000048      | 责任指数               | 2009/8/5      |
| 000049      | 上证民企               | 2009/8/25     |
| 000050      | 50等权               | 2011/1/4      |
| 000051      | 180等权              | 2011/5/24     |
| 000052      | 50基本               | 2012/1/9      |
| 000053      | 180基本              | 2012/1/9      |
| 000054      | 上证海外               | 2010/1/4      |
| 000055      | 上证地企               | 2010/1/4      |
| 000056      | 上证国企               | 2010/1/4      |
| 000057      | 全指成长               | 2010/1/4      |
| 000058      | 全指价值               | 2010/1/4      |
| 000059      | 全R成长               | 2010/1/4      |
| 000060      | 全R价值               | 2010/1/4      |
| 000061      | 沪企债30              | 2010/1/4      |
| 000062      | 上证沪企               | 2010/1/4      |
| 000063      | 上证周期               | 2010/2/3      |
| 000064      | 非周期                | 2010/2/3      |
| 000065      | 上证龙头               | 2010/2/9      |
| 000066      | 上证商品               | 2010/4/30     |
| 000067      | 上证新兴               | 2010/4/30     |
| 000068      | 上证资源               | 2010/5/28     |
| 000069      | 消费80               | 2010/5/28     |
| 000070      | 能源等权               | 2010/8/18     |
| 000071      | 材料等权               | 2010/8/18     |
| 000072      | 工业等权               | 2010/8/18     |
| 000073      | 可选等权               | 2010/8/18     |
| 000074      | 消费等权               | 2010/8/18     |
| 000075      | 医药等权               | 2010/8/18     |
| 000076      | 金融等权               | 2010/8/18     |
| 000077      | 信息等权               | 2010/8/18     |
| 000078      | 电信等权               | 2010/8/18     |
| 000079      | 公用等权               | 2010/8/18     |
| 000090      | 上证流通               | 2010/12/2     |
| 000091      | 沪财中小               | 2011/4/6      |
| 000092      | 资源50               | 2011/5/10     |
| 000093      | 180分层              | 2011/5/24     |
| 000094      | 上证上游               | 2011/5/10     |
| 000095      | 上证中游               | 2011/5/10     |
| 000096      | 上证下游               | 2011/5/10     |
| 000097      | 高端装备               | 2011/5/24     |
| 000098      | 上证F200             | 2011/7/1      |
| 000099      | 上证F300             | 2011/7/1      |
| 000100      | 上证F500             | 2011/7/1      |
| 000101      | 5年信用               | 2013/4/1      |
| 000102      | 沪投资品               | 2011/6/13     |
| 000103      | 沪消费品               | 2011/6/13     |
| 000104      | 380能源              | 2011/6/13     |
| 000105      | 380材料              | 2011/6/13     |
| 000106      | 380工业              | 2011/6/13     |
| 000107      | 380可选              | 2011/6/13     |
| 000108      | 380消费              | 2011/6/13     |
| 000109      | 380医药              | 2011/6/13     |
| 000110      | 380金融              | 2011/6/13     |
| 000111      | 380信息              | 2011/6/13     |
| 000112      | 380电信              | 2011/6/13     |
| 000113      | 380公用              | 2011/6/13     |
| 000114      | 持续产业               | 2011/8/22     |
| 000115      | 380等权              | 2011/8/12     |
| 000116      | 信用100              | 2013/2/26     |
| 000117      | 380成长              | 2011/10/18    |
| 000118      | 380价值              | 2011/10/18    |
| 000119      | 380R成长             | 2011/10/18    |
| 000120      | 380R价值             | 2011/10/18    |
| 000121      | 医药主题               | 2011/10/18    |
| 000122      | 农业主题               | 2011/10/18    |
| 000123      | 180动态              | 2013/2/8      |
| 000125      | 180稳定              | 2013/2/8      |
| 000126      | 消费50               | 2011/12/9     |
| 000128      | 380基本              | 2012/1/9      |
| 000129      | 180波动              | 2012/1/9      |
| 000130      | 380波动              | 2012/1/9      |
| 000131      | 上证高新               | 2012/2/10     |
| 000132      | 上证100              | 2012/4/20     |
| 000133      | 上证150              | 2012/4/20     |
| 000134      | 上证银行               | 2012/5/29     |
| 000135      | 180高贝              | 2012/8/6      |
| 000136      | 180低贝              | 2012/8/6      |
| 000137      | 380高贝              | 2012/8/6      |
| 000138      | 380低贝              | 2012/8/6      |
| 000139      | 上证转债               | 2012/9/12     |
| 000141      | 380动态              | 2013/2/8      |
| 000142      | 380稳定              | 2013/2/8      |
| 000145      | 优势资源               | 2012/5/29     |
| 000146      | 优势制造               | 2012/5/29     |
| 000147      | 优势消费               | 2012/5/29     |
| 000148      | 消费领先               | 2012/5/29     |
| 000149      | 180红利              | 2012/7/20     |
| 000150      | 380红利              | 2012/7/20     |
| 000151      | 上国红利               | 2012/7/20     |
| 000152      | 上央红利               | 2012/7/20     |
| 000153      | 上民红利               | 2012/7/20     |
| 000155      | 市值百强               | 2012/7/20     |
| 000158      | 上证环保               | 2012/9/25     |
| 000159      | 上证沪股通指数            | 2014/11/17    |
| 000160      | 上证一带一路主题指数         | 2015/6/24     |
| 000161      | 上证中国制造2025主题指数     | 2015/10/29    |
| 000162      | 上证互联网\+主题指数        | 2015/10/29    |
| 000171      | 新兴成指               | 2017/1/25     |
| 000188      | 中国波指               | 2016/11/28    |
| 000300      | 沪深300              | 2005/4/8      |
| 000801      | 资源80               | 2011/11/8     |
| 000802      | 500沪市              | 2011/11/8     |
| 000803      | 300波动              | 2012/1/9      |
| 000804      | 500波动              | 2012/1/9      |
| 000805      | A股资源               | 2012/1/6      |
| 000806      | 消费服务               | 2012/2/10     |
| 000807      | 食品饮料               | 2012/2/17     |
| 000808      | 医药生物               | 2012/2/17     |
| 000809      | 细分农业               | 2012/4/11     |
| 000810      | 细分能源               | 2012/4/11     |
| 000811      | 细分有色               | 2012/4/11     |
| 000812      | 细分机械               | 2012/4/11     |
| 000813      | 细分化工               | 2012/4/11     |
| 000814      | 细分医药               | 2012/4/11     |
| 000815      | 细分食品               | 2012/4/11     |
| 000816      | 细分地产               | 2012/4/11     |
| 000817      | 兴证海峡               | 2011/9/27     |
| 000818      | 细分金融               | 2012/4/11     |
| 000819      | 有色金属               | 2012/5/9      |
| 000820      | 煤炭指数               | 2012/5/9      |
| 000821      | 300红利              | 2012/7/20     |
| 000822      | 500红利              | 2012/7/20     |
| 000823      | 中证800有色金属指数        | 2012/12/21    |
| 000824      | 国企红利               | 2012/7/20     |
| 000825      | 央企红利               | 2012/7/20     |
| 000826      | 民企红利               | 2012/7/20     |
| 000827      | 中证环保               | 2012/9/25     |
| 000828      | 300高贝              | 2012/8/6      |
| 000829      | 300低贝              | 2012/8/6      |
| 000830      | 500高贝              | 2012/8/6      |
| 000831      | 500低贝              | 2012/8/6      |
| 000832      | 中证转债               | 2012/9/12     |
| 000833      | 中高企债               | 2012/9/10     |
| 000838      | 创业价值               | 2012/9/26     |
| 000839      | 浙企综指               | 2012/11/9     |
| 000840      | 浙江民企               | 2012/11/9     |
| 000841      | 800医药              | 2012/12/21    |
| 000842      | 800等权              | 2012/12/21    |
| 000843      | 300动态              | 2013/1/7      |
| 000844      | 300稳定              | 2013/1/7      |
| 000846      | ESG100             | 2012/10/16    |
| 000847      | 中证腾安价值100指数        | 2013/11/18    |
| 000849      | 沪深300非银行金融指数       | 2012/12/21    |
| 000850      | 沪深300有色金属指数        | 2012/12/21    |
| 000851      | 中证百度百发策略100指数      | 2014/7/30     |
| 000852      | 中证1000指数           | 2014/10/17    |
| 000853      | 中证申万一带一路主题投资指数     | 2015/4/23     |
| 000854      | 500原料              | 2013/11/6     |
| 000855      | 央视财经500指数          | 2014/11/3     |
| 000856      | 500工业              | 2013/11/6     |
| 000857      | 500医药              | 2013/11/6     |
| 000858      | 500信息              | 2013/11/6     |
| 000891      | 中国战略新兴产业综合指数       | 2017/1/25     |
| 000901      | 小康指数               | 2010/4/1      |
| 000902      | 中证流通               | 2006/2/27     |
| 000903      | 中证100              | 2006/5/29     |
| 000904      | 中证200              | 2007/1/15     |
| 000905      | 中证500              | 2007/1/15     |
| 000906      | 中证800              | 2007/1/15     |
| 000907      | 中证700              | 2007/1/15     |
| 000908      | 300能源              | 2007/7/2      |
| 000909      | 300材料              | 2007/7/2      |
| 000910      | 300工业              | 2007/7/2      |
| 000911      | 300可选              | 2007/7/2      |
| 000912      | 300消费              | 2007/7/2      |
| 000913      | 300医药              | 2007/7/2      |
| 000914      | 300金融              | 2007/7/2      |
| 000915      | 300信息              | 2007/7/2      |
| 000916      | 300电信              | 2007/7/2      |
| 000917      | 300公用              | 2007/7/2      |
| 000918      | 300成长              | 2008/1/21     |
| 000919      | 300价值              | 2008/1/21     |
| 000920      | 300R成长             | 2008/1/21     |
| 000921      | 300R价值             | 2008/1/21     |
| 000922      | 中证红利               | 2008/5/26     |
| 000923      | 公司债                | 2008/11/19    |
| 000925      | 基本面50              | 2009/2/26     |
| 000926      | 中证央企               | 2009/3/30     |
| 000927      | 央企100              | 2009/3/30     |
| 000928      | 中证能源               | 2009/7/3      |
| 000929      | 中证材料               | 2009/7/3      |
| 000930      | 中证工业               | 2009/7/3      |
| 000931      | 中证可选               | 2009/7/3      |
| 000932      | 中证消费               | 2009/7/3      |
| 000933      | 中证医药               | 2009/7/3      |
| 000934      | 中证金融               | 2009/7/3      |
| 000935      | 中证信息               | 2009/7/3      |
| 000936      | 中证电信               | 2009/7/3      |
| 000937      | 中证公用               | 2009/7/3      |
| 000938      | 中证民企               | 2009/8/25     |
| 000939      | 民企200              | 2009/8/25     |
| 000940      | 财富大盘               | 2009/9/25     |
| 000941      | 新能源                | 2009/10/28    |
| 000942      | 内地消费               | 2009/10/28    |
| 000943      | 内地基建               | 2009/10/28    |
| 000944      | 内地资源               | 2009/10/28    |
| 000945      | 内地运输               | 2009/10/28    |
| 000946      | 内地金融               | 2009/10/28    |
| 000947      | 内地银行               | 2009/10/28    |
| 000948      | 内地地产               | 2009/10/28    |
| 000949      | 内地农业               | 2009/10/28    |
| 000950      | 300基建              | 2009/10/28    |
| 000951      | 300银行              | 2009/10/28    |
| 000952      | 300地产              | 2009/10/28    |
| 000953      | 中证地企               | 2010/2/9      |
| 000954      | 地企100              | 2010/2/9      |
| 000955      | 中证国企               | 2010/2/9      |
| 000956      | 国企200              | 2010/2/9      |
| 000957      | 300运输              | 2009/6/16     |
| 000958      | 创业成长               | 2010/3/24     |
| 000959      | 银河99               | 2010/2/9      |
| 000960      | 中证龙头               | 2010/2/9      |
| 000961      | 中证上游               | 2010/4/16     |
| 000962      | 中证中游               | 2010/4/16     |
| 000963      | 中证下游               | 2010/4/16     |
| 000964      | 中证新兴               | 2010/4/30     |
| 000965      | 基本200              | 2010/6/2      |
| 000966      | 基本400              | 2010/6/2      |
| 000967      | 基本600              | 2010/6/2      |
| 000968      | 300周期              | 2010/5/28     |
| 000969      | 300非周              | 2010/5/28     |
| 000970      | ESG40              | 2010/9/17     |
| 000971      | 等权90               | 2010/12/2     |
| 000972      | 300沪市              | 2010/12/2     |
| 000973      | 技术领先               | 2011/1/4      |
| 000974      | 中证800金融指数          | 2013/5/9      |
| 000975      | 钱江30               | 2014/1/2      |
| 000976      | 新华金牛               | 2014/2/18     |
| 000977      | 内地低碳               | 2011/1/21     |
| 000978      | 医药100              | 2011/3/18     |
| 000979      | 大宗商品               | 2011/8/22     |
| 000980      | 中证超大               | 2011/5/10     |
| 000981      | 300分层              | 2011/6/13     |
| 000982      | 500等权              | 2011/6/13     |
| 000983      | 智能资产               | 2011/6/17     |
| 000984      | 300等权              | 2011/8/2      |
| 000985      | 中证全指               | 2011/8/2      |
| 000986      | 全指能源               | 2011/8/2      |
| 000987      | 全指材料               | 2011/8/2      |
| 000988      | 全指工业               | 2011/8/2      |
| 000989      | 全指可选               | 2011/8/2      |
| 000990      | 全指消费               | 2011/8/2      |
| 000991      | 全指医药               | 2011/8/2      |
| 000992      | 全指金融               | 2011/8/2      |
| 000993      | 全指信息               | 2011/8/2      |
| 000994      | 全指电信               | 2011/8/2      |
| 000995      | 全指公用               | 2011/8/2      |
| 000996      | 领先行业               | 2011/8/2      |
| 000997      | 大消费                | 2011/11/11    |
| 000998      | 中证TMT              | 2011/11/8     |
| 000999      | 中证两岸三地500指数        | 2010/1/18     |
| 399001      | 深证成指               | 1995/1/23     |
| 399002      | 深成指R               | 1995/1/23     |
| 399003      | 成份B指               | 1995/1/23     |
| 399004      | 深证100R             | 2003/1/2      |
| 399005      | 中小板指               | 2006/1/24     |
| 399006      | 创业板指               | 2010/6/1      |
| 399007      | 深证300              | 2009/11/4     |
| 399008      | 中小300              | 2010/3/22     |
| 399009      | 深证200              | 2011/9/1      |
| 399010      | 深证700              | 2011/9/1      |
| 399011      | 深证1000             | 2011/9/1      |
| 399012      | 创业300              | 2013/1/7      |
| 399013      | 深市精选               | 2014/5/30     |
| 399015      | 深证中小创新指数           | 2015/3/24     |
| 399016      | 深证创新指数             | 2016/12/1     |
| 399017      | 中小板创新指数            | 2016/12/1     |
| 399018      | 创业板创新指数            | 2016/12/1     |
| 399050      | 创新引擎               | 2018/7/31     |
| 399100      | 新指数                | 2006/2/16     |
| 399101      | 中小板综               | 2005/12/1     |
| 399102      | 创业板综合指数            | 2010/8/20     |
| 399103      | 乐富指数               | 2011/12/2     |
| 399106      | 深证综指               | 1991/4/4      |
| 399107      | 深证A指               | 1992/10/4     |
| 399108      | 深证B指               | 1992/10/6     |
| 399231      | 农林指数               | 2013/3/4      |
| 399232      | 采矿指数               | 2013/3/4      |
| 399233      | 制造指数               | 2013/3/4      |
| 399234      | 水电指数               | 2013/3/4      |
| 399235      | 建筑指数               | 2013/3/4      |
| 399236      | 批零指数               | 2013/3/4      |
| 399237      | 运输指数               | 2013/3/4      |
| 399238      | 餐饮指数               | 2013/3/4      |
| 399239      | IT指数               | 2013/3/4      |
| 399240      | 金融指数               | 2013/3/4      |
| 399241      | 地产指数               | 2013/3/4      |
| 399242      | 商务指数               | 2013/3/4      |
| 399243      | 科研指数               | 2013/3/4      |
| 399244      | 公共指数               | 2013/3/4      |
| 399248      | 文化指数               | 2013/3/4      |
| 399249      | 综企指数               | 2013/3/4      |
| 399286      | 区块链50              | 2019/12/23    |
| 399290      | 深转交债               | 2019/6/6      |
| 399291      | 创精选88              | 2019/11/6     |
| 399292      | 民企发展               | 2019/3/5      |
| 399293      | 创业大盘               | 2019/4/18     |
| 399294      | 中小创Q               | 2019/6/6      |
| 399295      | 创业蓝筹               | 2019/1/23     |
| 399296      | 创成长                | 2019/1/23     |
| 399298      | 深证中高等级信用债指数        | 2014/9/25     |
| 399299      | 深证中低等级信用债指数        | 2014/9/25     |
| 399300      | 沪深300              | 2005/4/8      |
| 399301      | 深信用债               | 2013/1/11     |
| 399302      | 深公司债               | 2013/1/11     |
| 399303      | 国证2000             | 2014/3/28     |
| 399305      | 基金指数               | 2000/7/3      |
| 399306      | 深证ETF              | 2011/12/2     |
| 399307      | 深证转债               | 2014/8/27     |
| 399310      | 国证50               | 2005/9/5      |
| 399311      | 国证1000             | 2005/2/3      |
| 399312      | 国证300              | 2005/2/3      |
| 399313      | 巨潮100              | 2005/2/3      |
| 399314      | 巨潮大盘               | 2005/2/3      |
| 399315      | 巨潮中盘               | 2005/2/3      |
| 399316      | 巨潮小盘               | 2005/2/3      |
| 399317      | 国证A指               | 2005/2/3      |
| 399318      | 巨潮B股指数             | 2005/2/3      |
| 399319      | 资源优势               | 2005/1/4      |
| 399320      | 国证服务               | 2005/1/4      |
| 399321      | 国证红利               | 2005/1/4      |
| 399322      | 国证治理               | 2005/12/12    |
| 399324      | 深证红利               | 2006/1/24     |
| 399326      | 成长40               | 2006/1/24     |
| 399328      | 深证治理               | 2006/1/24     |
| 399330      | 深证100              | 2006/1/24     |
| 399332      | 深证创新\(旧\)          | 2006/2/27     |
| 399333      | 中小板R               | 2006/12/27    |
| 399335      | 深证央企               | 2009/8/3      |
| 399337      | 深证民营               | 2009/8/3      |
| 399339      | 深证科技               | 2009/8/3      |
| 399341      | 深证责任               | 2009/8/3      |
| 399344      | 深证300R             | 2009/11/4     |
| 399346      | 深证成长               | 2009/11/4     |
| 399348      | 深证价值               | 2009/11/4     |
| 399350      | 皖江30               | 2012/12/18    |
| 399351      | 深报指数               | 2004/9/1      |
| 399352      | 深报综指               | 2005/2/3      |
| 399353      | 国证物流               | 2005/3/1      |
| 399354      | 分析师指数              | 2006/11/25    |
| 399355      | 长三角                | 2007/7/2      |
| 399356      | 珠三角                | 2007/7/2      |
| 399357      | 环渤海                | 2007/7/2      |
| 399358      | 泰达指数               | 2008/1/2      |
| 399359      | 国证基建               | 2009/8/3      |
| 399360      | 国证装备               | 2009/8/3      |
| 399361      | 国证商业               | 2009/8/3      |
| 399362      | 国证民营               | 2009/8/3      |
| 399363      | 计算机指               | 2009/8/3      |
| 399364      | 中金消费               | 2009/8/3      |
| 399365      | 国证农业               | 2009/11/4     |
| 399366      | 国证大宗               | 2009/11/4     |
| 399367      | 巨潮地产               | 2009/11/4     |
| 399368      | 国证军工               | 2009/11/4     |
| 399369      | CBN\-兴全            | 2009/11/4     |
| 399370      | 国证成长               | 2010/1/4      |
| 399371      | 国证价值               | 2010/1/4      |
| 399372      | 大盘成长               | 2010/1/4      |
| 399373      | 大盘价值               | 2010/1/4      |
| 399374      | 中盘成长               | 2010/1/4      |
| 399375      | 中盘价值               | 2010/1/4      |
| 399376      | 小盘成长               | 2010/1/4      |
| 399377      | 小盘价值               | 2010/1/4      |
| 399378      | 南方低碳               | 2010/9/20     |
| 399379      | 国证基金               | 2011/12/2     |
| 399380      | 国证ETF              | 2011/12/2     |
| 399381      | 1000能源             | 2011/12/2     |
| 399382      | 1000材料             | 2011/12/2     |
| 399383      | 1000工业             | 2011/12/2     |
| 399384      | 1000可选             | 2011/12/2     |
| 399385      | 1000消费             | 2011/12/2     |
| 399386      | 1000医药             | 2011/12/2     |
| 399387      | 1000金融             | 2011/12/2     |
| 399388      | 1000信息             | 2011/12/2     |
| 399389      | 国证通信               | 2011/12/2     |
| 399390      | 1000公用             | 2011/12/2     |
| 399391      | 投资时钟               | 2012/3/26     |
| 399392      | 国证新兴               | 2012/3/28     |
| 399393      | 国证地产               | 2012/8/20     |
| 399394      | 国证医药               | 2012/10/29    |
| 399395      | 国证有色               | 2012/10/29    |
| 399396      | 国证食品               | 2012/10/29    |
| 399397      | OCT文化              | 2012/11/9     |
| 399398      | 绩效指数               | 2012/11/19    |
| 399399      | 中经GDP              | 2012/11/23    |
| 399400      | 大中盘                | 2013/3/20     |
| 399401      | 中小盘                | 2013/3/20     |
| 399402      | 周期100              | 2013/10/28    |
| 399403      | 防御100              | 2013/10/28    |
| 399404      | 大盘低波               | 2013/12/5     |
| 399405      | 大盘高贝               | 2013/12/5     |
| 399406      | 中盘低波               | 2013/12/5     |
| 399407      | 中盘高贝               | 2013/12/5     |
| 399408      | 小盘低波               | 2013/12/5     |
| 399409      | 小盘高贝               | 2013/12/5     |
| 399410      | 苏州率先               | 2013/12/12    |
| 399411      | 红利100              | 2014/4/10     |
| 399412      | 国证新能               | 2014/5/16     |
| 399413      | 国证转债               | 2014/8/27     |
| 399415      | I100               | 2014/9/12     |
| 399416      | I300               | 2014/9/12     |
| 399417      | 国证新能源汽车指数          | 2014/9/24     |
| 399418      | 国证国家安全指数           | 2014/11/21    |
| 399419      | 国证高铁指数             | 2014/12/10    |
| 399420      | 国证保险证券指数           | 2014/12/10    |
| 399423      | 中关村50指数            | 2015/2/5      |
| 399427      | 国证德高行专利领先指数        | 2015/2/17     |
| 399428      | 国证定向增发指数           | 2015/2/17     |
| 399429      | 新丝路指数              | 2015/1/8      |
| 399431      | 国证银行行业指数           | 2014/12/30    |
| 399432      | 国证汽车与汽车零配件行业指数     | 2014/12/30    |
| 399433      | 国证交通运输行业指数         | 2014/12/30    |
| 399434      | 国证传媒行业指数           | 2014/12/30    |
| 399435      | 国证农牧渔产品行业指数        | 2014/12/30    |
| 399436      | 国证煤炭行业指数           | 2014/12/30    |
| 399437      | 国证证券行业指数           | 2014/12/30    |
| 399438      | 国证电力公用事业行业指数       | 2014/12/30    |
| 399439      | 国证石油天然气行业指数        | 2014/12/30    |
| 399440      | 国证黑色金属行业指数         | 2014/12/30    |
| 399441      | 国证生物医药指数           | 2015/1/20     |
| 399481      | 企债指数               | 2003/1/2      |
| 399550      | 央视50               | 2012/6/6      |
| 399551      | 央视创新               | 2013/6/6      |
| 399552      | 央视成长               | 2013/6/6      |
| 399553      | 央视回报               | 2013/6/6      |
| 399554      | 央视治理               | 2013/6/6      |
| 399555      | 央视责任               | 2013/6/6      |
| 399556      | 央视生态               | 2014/6/6      |
| 399557      | 央视文化               | 2014/6/6      |
| 399602      | 中小成长               | 2010/5/24     |
| 399604      | 中小价值               | 2010/5/24     |
| 399606      | 创业板R               | 2010/6/1      |
| 399608      | 科技100              | 2010/10/18    |
| 399610      | TMT50              | 2010/11/8     |
| 399611      | 中创100R             | 2011/2/28     |
| 399612      | 中创100              | 2011/2/28     |
| 399613      | 深证能源               | 2011/6/15     |
| 399614      | 深证材料               | 2011/6/15     |
| 399615      | 深证工业               | 2011/6/15     |
| 399616      | 深证可选               | 2011/6/15     |
| 399617      | 深证消费               | 2011/6/15     |
| 399618      | 深证医药               | 2011/6/15     |
| 399619      | 深证金融               | 2011/6/15     |
| 399620      | 深证信息               | 2011/6/15     |
| 399621      | 深证电信               | 2011/6/15     |
| 399622      | 深证公用               | 2011/6/15     |
| 399623      | 中小基础               | 2011/7/25     |
| 399624      | 中创400              | 2011/8/15     |
| 399625      | 中创500              | 2011/8/15     |
| 399626      | 中创成长               | 2011/8/15     |
| 399627      | 中创价值               | 2011/8/15     |
| 399628      | 700成长              | 2011/9/1      |
| 399629      | 700价值              | 2011/9/1      |
| 399630      | 1000成长             | 2011/9/1      |
| 399631      | 1000价值             | 2011/9/1      |
| 399632      | 深100EW             | 2011/10/28    |
| 399633      | 深300EW             | 2011/10/28    |
| 399634      | 中小板EW              | 2011/10/28    |
| 399635      | 创业板EW              | 2011/10/28    |
| 399636      | 深证装备               | 2011/11/15    |
| 399637      | 深证地产               | 2011/11/15    |
| 399638      | 深证环保               | 2011/11/15    |
| 399639      | 深证大宗               | 2011/11/15    |
| 399640      | 创业基础               | 2012/1/16     |
| 399641      | 深证新兴               | 2012/2/1      |
| 399642      | 中小新兴               | 2012/2/1      |
| 399643      | 创业新兴               | 2012/2/1      |
| 399644      | 深证时钟               | 2012/3/26     |
| 399645      | 100低波              | 2012/6/12     |
| 399646      | 深消费50              | 2012/8/6      |
| 399647      | 深医药50              | 2012/8/6      |
| 399648      | 深证GDP              | 2012/8/8      |
| 399649      | 中小红利               | 2012/8/20     |
| 399650      | 中小治理               | 2012/8/20     |
| 399651      | 中小责任               | 2012/8/20     |
| 399652      | 中创高新               | 2012/9/17     |
| 399653      | 深证龙头               | 2012/9/25     |
| 399654      | 深证文化               | 2012/11/9     |
| 399655      | 深证绩效               | 2012/11/19    |
| 399656      | 100绩效              | 2012/11/19    |
| 399657      | 300绩效              | 2012/11/19    |
| 399658      | 中小绩效               | 2012/11/19    |
| 399659      | 深成指EW              | 2012/11/23    |
| 399660      | 中创EW               | 2012/11/23    |
| 399661      | 深证低波               | 2012/12/20    |
| 399662      | 深证高贝               | 2012/12/20    |
| 399663      | 中小低波               | 2012/12/20    |
| 399664      | 中小高贝               | 2012/12/20    |
| 399665      | 中创低波               | 2012/12/20    |
| 399666      | 中创高贝               | 2012/12/20    |
| 399667      | 创业板G               | 2013/1/7      |
| 399668      | 创业板V               | 2013/1/7      |
| 399669      | 深证农业               | 2013/6/24     |
| 399670      | 深周期50              | 2013/10/28    |
| 399671      | 深防御50              | 2013/10/28    |
| 399672      | 深红利50              | 2014/4/10     |
| 399673      | 创业板50              | 2014/6/18     |
| 399674      | 深A医药卫生指数           | 2015/6/8      |
| 399675      | 深A软件与互联网指数         | 2015/6/8      |
| 399676      | 深A医药卫生等权指数         | 2015/6/8      |
| 399677      | 深A软件与互联网等权指数       | 2015/6/8      |
| 399678      | 深证次新股指数            | 2015/6/18     |
| 399679      | 深证200指数            | 2015/6/18     |
| 399680      | 深成能源行业指数           | 2015/8/31     |
| 399681      | 深成原材料行业指数          | 2015/8/31     |
| 399682      | 深成工业行业指数           | 2015/8/31     |
| 399683      | 深成可选消费行业指数         | 2015/8/31     |
| 399684      | 深成主要消费行业指数         | 2015/8/31     |
| 399685      | 深成医药卫生行业指数         | 2015/8/31     |
| 399686      | 深成金融地产行业指数         | 2015/8/31     |
| 399687      | 深成信息技术行业指数         | 2015/8/31     |
| 399688      | 深成电信业务行业指数         | 2015/8/31     |
| 399689      | 深成公用事业行业指数         | 2015/8/31     |
| 399690      | 深证中小板专利领先指数        | 2016/5/23     |
| 399691      | 深证创业板专利领先指数        | 2016/5/23     |
| 399692      | 创业300低波动率指数        | 2016/12/20    |
| 399693      | 安防产业               | 2016/6/20     |
| 399694      | 创业高贝               | 2016/12/20    |
| 399695      | 深证节能环保指数           | 2017/4/28     |
| 399696      | 深证创投               | 2016/10/21    |
| 399697      | 中关村60              | 2017/1/10     |
| 399698      | 深证优势成长             | 2017/6/15     |
| 399699      | 金融科技               | 2017/6/9      |
| 399701      | 深证F60              | 2010/5/10     |
| 399702      | 深证F120             | 2010/5/10     |
| 399703      | 深证F200             | 2010/5/10     |
| 399704      | 深证上游               | 2011/10/18    |
| 399705      | 深证中游               | 2011/10/18    |
| 399706      | 深证下游               | 2011/10/18    |
| 399707      | 中证申万证券行业指数         | 2015/4/16     |
| 399802      | 500深市              | 2012/9/5      |
| 399803      | 中证工业4\.0指数         | 2015/3/10     |
| 399804      | 中证体育产业指数           | 2015/2/9      |
| 399805      | 中证互联网金融指数          | 2015/2/10     |
| 399806      | 中证环境治理指数           | 2014/7/21     |
| 399807      | 中证高铁产业指数           | 2015/1/20     |
| 399808      | 中证新能源指数            | 2015/2/10     |
| 399809      | 中证方正富邦保险主题指数       | 2015/2/13     |
| 399810      | 中证申万传媒行业投资指数       | 2015/8/3      |
| 399811      | 中证申万电子行业投资指数       | 2015/8/3      |
| 399812      | 中证养老产业指数           | 2014/6/6      |
| 399813      | 中证国防安全指数           | 2015/1/29     |
| 399814      | 中证大农业指数            | 2014/7/22     |
| 399817      | 中证阿拉善生态主题100指数     | 2015/10/21    |
| 399901      | 中证南方小康产业指数         | 2010/4/1      |
| 399902      | 中证流通指数             | 2006/2/27     |
| 399903      | 中证100指数            | 2006/5/29     |
| 399904      | 中证中盘200指数          | 2007/1/15     |
| 399905      | 中证500              | 2007/1/15     |
| 399906      | 中证800指数            | 2007/1/15     |
| 399907      | 中证中小盘700指数         | 2007/1/15     |
| 399908      | 沪深300能源指数          | 2007/7/2      |
| 399909      | 沪深300原材料指数         | 2007/7/2      |
| 399910      | 沪深300工业指数          | 2007/7/2      |
| 399911      | 沪深300可选消费指数        | 2007/7/2      |
| 399912      | 沪深300主要消费指数        | 2007/7/2      |
| 399913      | 沪深300医药卫生指数        | 2007/7/2      |
| 399914      | 沪深300金融地产指数        | 2007/7/2      |
| 399915      | 沪深300信息技术指数        | 2007/7/2      |
| 399916      | 沪深300电信业务指数        | 2007/7/2      |
| 399917      | 沪深300公用事业指数        | 2007/7/2      |
| 399918      | 沪深300成长指数          | 2008/1/21     |
| 399919      | 沪深300价值指数          | 2008/1/21     |
| 399920      | 沪深300相对成长指数        | 2008/1/21     |
| 399922      | 中证红利指数             | 2008/5/26     |
| 399923      | 公司债                | 2008/11/19    |
| 399925      | 中证锐联基本面50指数        | 2009/2/26     |
| 399926      | 中证中央企业综合指数         | 2009/3/30     |
| 399927      | 中证中央企业100指数        | 2009/3/30     |
| 399928      | 中证能源指数             | 2009/7/3      |
| 399929      | 中证原材料指数            | 2009/7/3      |
| 399930      | 中证工业指数             | 2009/7/3      |
| 399931      | 中证可选消费指数           | 2009/7/3      |
| 399932      | 中证主要消费指数           | 2009/7/3      |
| 399933      | 中证医药卫生指数           | 2009/7/3      |
| 399934      | 中证金融地产指数           | 2009/7/3      |
| 399935      | 中证信息技术指数           | 2009/7/3      |
| 399936      | 中证电信业务指数           | 2009/7/3      |
| 399937      | 中证公用事业指数           | 2009/7/3      |
| 399938      | 中证民营企业综合指数         | 2009/8/25     |
| 399939      | 中证民营企业200指数        | 2009/8/25     |
| 399940      | 中证财富大盘指数           | 2009/9/25     |
| 399941      | 中证内地新能源主题指数        | 2009/10/28    |
| 399942      | 中证内地消费主题指数         | 2009/10/28    |
| 399943      | 中证内地基建主题指数         | 2009/10/28    |
| 399944      | 中证内地资源主题指数         | 2009/10/28    |
| 399945      | 中证内地运输主题指数         | 2009/10/28    |
| 399946      | 中证内地金融主题指数         | 2009/10/28    |
| 399947      | 中证内地银行主题指数         | 2009/10/28    |
| 399948      | 中证内地地产主题指数         | 2009/10/28    |
| 399949      | 中证内地农业主题指数         | 2009/10/28    |
| 399950      | 沪深300基建主题指数        | 2009/10/28    |
| 399951      | 沪深300银行指数          | 2009/10/28    |
| 399952      | 沪深300地产指数          | 2009/10/28    |
| 399953      | 中证地方国有企业综合指数       | 2010/2/9      |
| 399954      | 中证地方国有企业100指数      | 2010/2/9      |
| 399955      | 中证国有企业综合指数         | 2010/2/9      |
| 399956      | 中证国有企业200指数        | 2010/2/9      |
| 399957      | 沪深300运输指数          | 2009/6/16     |
| 399958      | 中证创业成长指数           | 2010/3/24     |
| 399959      | 军工指数               | 2011/8/30     |
| 399960      | 中证龙头企业指数           | 2010/2/9      |
| 399961      | 中证上游资源产业指数         | 2010/4/16     |
| 399962      | 中证中游制造产业指数         | 2010/4/16     |
| 399963      | 中证下游消费与服务产业指数      | 2010/4/16     |
| 399964      | 中证新兴产业指数           | 2010/4/30     |
| 399965      | 800地产              | 2014/4/4      |
| 399966      | 800非银              | 2014/4/4      |
| 399967      | 中证军工               | 2013/12/26    |
| 399968      | 沪深300周期行业指数        | 2010/5/28     |
| 399969      | 沪深300非周期行业指数       | 2010/5/28     |
| 399970      | 中证移动互联网指数          | 2014/5/5      |
| 399971      | 中证传媒指数             | 2014/4/15     |
| 399972      | 300深市              | 2012/6/21     |
| 399973      | 中证国防指数             | 2014/4/15     |
| 399974      | 中证国有企业改革指数         | 2014/8/7      |
| 399975      | 中证全指证券公司指数\(四级行业\) | 2013/7/15     |
| 399976      | 中证新能源汽车指数          | 2014/11/28    |
| 399977      | 中证内地低碳经济主题指数       | 2011/1/21     |
| 399978      | 中证医药100指数          | 2011/3/18     |
| 399979      | 中证大宗商品股票指数         | 2011/8/22     |
| 399980      | 中证超级大盘指数           | 2011/5/10     |
| 399981      | 沪深300行业分层等权重指数     | 2011/6/13     |
| 399982      | 中证500等权重指数         | 2011/6/13     |
| 399983      | 沪深300地产等权重指数       | 2013/11/22    |
| 399984      | 沪深300等权重指数         | 2011/8/2      |
| 399985      | 中证全指指数             | 2011/8/2      |
| 399986      | 中证银行指数             | 2013/7/15     |
| 399987      | 中证酒指数              | 2014/12/10    |
| 399989      | 中证医疗指数             | 2014/10/31    |
| 399990      | 中证煤炭等权指数           | 2015/1/21     |
| 399991      | 中证一带一路主题指数         | 2015/2/16     |
| 399992      | 中证万得并购重组指数         | 2015/5/8      |
| 399993      | 中证万得生物科技指数         | 2015/5/8      |
| 399994      | 中证信息安全主题指数         | 2015/3/12     |
| 399995      | 中证基建工程指数           | 2015/3/12     |
| 399996      | 中证智能家居指数           | 2014/9/17     |
| 399997      | 中证白酒指数             | 2015/1/21     |
| 399998      | 中证煤炭指数             | 2015/2/13     |

输出参数

| 名称   | 类型     | 描述      |
|------|--------|---------|
| 品种代码 | object | 股票代码    |
| 品种名称 | object | 股票名称    |
| 纳入日期 | object | 成份股纳入日期 |

接口示例

```python
import akshare as ak

index_stock_cons_df = ak.index_stock_cons(symbol="000300")
print(index_stock_cons_df)
```

数据示例

```
       品种代码  品种名称     纳入日期
0    601916  浙商银行  2023-12-11
1    301269  华大九天  2023-12-11
2    688256   寒武纪  2023-12-11
3    600515  海南机场  2023-12-11
4    000999  华润三九  2023-12-11
..      ...   ...         ...
295  000157  中联重科  2005-04-08
296  000069  华侨城A  2005-04-08
297  000063  中兴通讯  2005-04-08
298  000001  深发展A  2005-04-08
299  000002   万科A  2005-04-08
[300 rows x 3 columns]
```

输出参数-按市场归类

| 名称   | 类型     | 描述      |
|------|--------|---------|
| 品种代码 | object | 股票代码    |
| 品种名称 | object | 股票名称    |
| 纳入日期 | object | 成份股纳入日期 |

接口示例-按市场归类

```python
import akshare as ak

index_stock_cons_df = ak.index_stock_cons(symbol="000300")  # 主要调用 ak.stock_a_code_to_symbol() 来进行转换
index_stock_cons_df['symbol'] = index_stock_cons_df['品种代码'].apply(ak.stock_a_code_to_symbol)
print(index_stock_cons_df)
```

数据示例-按市场归类

```
      品种代码  品种名称  纳入日期    symbol
0   000688  国城矿业  2020-12-14  sz000688
1   002409  雅克科技  2020-12-14  sz002409
2   002683  宏大爆破  2020-12-14  sz002683
3   002709  天赐材料  2020-12-14  sz002709
4   002064  华峰氨纶  2020-06-15  sz002064
5   002458  益生股份  2020-06-15  sz002458
6   002812  恩捷股份  2019-12-16  sz002812
7   002128  露天煤业  2019-12-16  sz002128
8   002080  中材科技  2019-12-16  sz002080
9   000708  中信特钢  2019-12-16  sz000708
10  002157  正邦科技  2019-06-17  sz002157
11  000723  美锦能源  2019-06-17  sz000723
12  000629  攀钢钒钛  2019-06-17  sz000629
13  000930  中粮生化  2019-01-02  sz000930
14  000860  顺鑫农业  2019-01-02  sz000860
15  002110  三钢闽光  2018-07-02  sz002110
16  002078  太阳纸业  2018-07-02  sz002078
17  000703  恒逸石化  2018-07-02  sz000703
18  000830  鲁西化工  2018-01-02  sz000830
19  300618  寒锐钴业  2018-01-02  sz300618
20  002408  齐翔腾达  2017-07-03  sz002408
21  300498  温氏股份  2017-01-03  sz300498
22  002299  圣农发展  2016-07-01  sz002299
23  000959  首钢股份  2016-07-01  sz000959
24  000807  云铝股份  2016-07-01  sz000807
25  002221  东华能源  2016-01-04  sz002221
26  000898  鞍钢股份  2014-07-01  sz000898
27  002340   格林美  2014-01-02  sz002340
28  002311  海大集团  2014-01-02  sz002311
29  002385   大北农  2012-07-02  sz002385
30  000876   新希望  2012-01-04  sz000876
31  000998  隆平高科  2011-11-15  sz000998
32  000983  西山煤电  2011-11-15  sz000983
33  000960  锡业股份  2011-11-15  sz000960
34  000878  云南铜业  2011-11-15  sz000878
35  000825  太钢不锈  2011-11-15  sz000825
36  000778  新兴铸管  2011-11-15  sz000778
37  000709  河北钢铁  2011-11-15  sz000709
38  000630  铜陵有色  2011-11-15  sz000630
39  000060  中金岭南  2011-11-15  sz000060
[40 rows x 4 columns]
```

#### 中证指数成份股

接口: index_stock_cons_csindex

目标地址: http://www.csindex.com.cn/zh-CN/indices/index-detail/000300

描述: 中证指数网站-成份股目录

输入参数

| 名称     | 类型  | 描述                    |
|--------|-----|-----------------------|
| symbol | str | symbol="000300"; 指数代码 |

输出参数

| 名称      | 类型     | 描述  |
|---------|--------|-----|
| 日期      | object | -   |
| 指数代码    | object | -   |
| 指数名称    | object | -   |
| 指数英文名称  | object | -   |
| 成分券代码   | object | -   |
| 成分券名称   | object | -   |
| 成分券英文名称 | object | -   |
| 交易所     | object | -   |
| 交易所英文名称 | object | -   |

示例代码

```python
import akshare as ak

index_stock_cons_csindex_df = ak.index_stock_cons_csindex(symbol="000300")
print(index_stock_cons_csindex_df)
```

数据示例

```
             日期    指数代码  ...      交易所                  交易所英文名称
0    2024-01-04  000300  ...  深圳证券交易所  Shenzhen Stock Exchange
1    2024-01-04  000300  ...  深圳证券交易所  Shenzhen Stock Exchange
2    2024-01-04  000300  ...  深圳证券交易所  Shenzhen Stock Exchange
3    2024-01-04  000300  ...  深圳证券交易所  Shenzhen Stock Exchange
4    2024-01-04  000300  ...  深圳证券交易所  Shenzhen Stock Exchange
..          ...     ...  ...      ...                      ...
295  2024-01-04  000300  ...  上海证券交易所  Shanghai Stock Exchange
296  2024-01-04  000300  ...  上海证券交易所  Shanghai Stock Exchange
297  2024-01-04  000300  ...  上海证券交易所  Shanghai Stock Exchange
298  2024-01-04  000300  ...  上海证券交易所  Shanghai Stock Exchange
299  2024-01-04  000300  ...  上海证券交易所  Shanghai Stock Exchange
[300 rows x 9 columns]
```

#### 中证指数成份股权重

接口: index_stock_cons_weight_csindex

目标地址: http://www.csindex.com.cn/zh-CN/indices/index-detail/000300

描述: 中证指数网站-成份股权重

输入参数

| 名称     | 类型  | 描述                    |
|--------|-----|-----------------------|
| symbol | str | symbol="000300"; 指数代码 |

输出参数

| 名称      | 类型      | 描述      |
|---------|---------|---------|
| 日期      | object  | -       |
| 指数代码    | object  | -       |
| 指数名称    | object  | -       |
| 指数英文名称  | object  | -       |
| 成分券代码   | object  | -       |
| 成分券名称   | object  | -       |
| 成分券英文名称 | object  | -       |
| 交易所     | object  | -       |
| 交易所英文名称 | object  | -       |
| 权重      | float64 | 注意单位: % |

示例代码

```python
import akshare as ak

index_stock_cons_weight_csindex_df = ak.index_stock_cons_weight_csindex(symbol="000300")
print(index_stock_cons_weight_csindex_df)
```

数据示例

```
     日期    指数代码   指数名称  ...     交易所                交易所英文名称     权重
0    2023-12-29  000300  沪深300  ...  深圳证券交易所  Shenzhen Stock Exchange  0.524
1    2023-12-29  000300  沪深300  ...  深圳证券交易所  Shenzhen Stock Exchange  0.410
2    2023-12-29  000300  沪深300  ...  深圳证券交易所  Shenzhen Stock Exchange  0.486
3    2023-12-29  000300  沪深300  ...  深圳证券交易所  Shenzhen Stock Exchange  0.088
4    2023-12-29  000300  沪深300  ...  深圳证券交易所  Shenzhen Stock Exchange  0.465
..          ...     ...    ...  ...      ...                      ...    ...
295  2023-12-29  000300  沪深300  ...  上海证券交易所  Shanghai Stock Exchange  0.074
296  2023-12-29  000300  沪深300  ...  上海证券交易所  Shanghai Stock Exchange  0.136
297  2023-12-29  000300  沪深300  ...  上海证券交易所  Shanghai Stock Exchange  0.063
298  2023-12-29  000300  沪深300  ...  上海证券交易所  Shanghai Stock Exchange  0.178
299  2023-12-29  000300  沪深300  ...  上海证券交易所  Shanghai Stock Exchange  0.602
[300 rows x 10 columns]
```

### 国证指数

#### 全部指数

接口: index_all_cni

目标地址: http://www.cnindex.com.cn/zh_indices/sese/index.html?act_menu=1&index_type=-1

描述: 国证指数-最近交易日的所有指数的代码和基本信息

输入参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| -   | -   | -   |

输出参数

| 名称     | 类型      | 描述                               |
|--------|---------|----------------------------------|
| 指数代码   | object  | -                                |
| 指数简称   | object  | -                                |
| 样本数    | int64   | -                                |
| 收盘点位   | float64 | -                                |
| 涨跌幅    | float64 | -                                |
| PE滚动   | float64 | -                                |
| 成交量    | float64 | 注意单位: 债券指数成交量单位为亿张，非债券指数成交量单位为万手 |
| 成交额    | float64 | 注意单位: 亿元                         |
| 总市值    | float64 | 注意单位: 亿元                         |
| 自由流通市值 | float64 | 注意单位: 亿元                         |

接口示例

```python
import akshare as ak

index_all_cni_df = ak.index_all_cni()
print(index_all_cni_df)
```

数据示例

```
          指数代码          指数简称  样本数  ...          成交额            总市值        自由流通市值
0       399001          深证成指  500  ...  1551.817177  182200.555052  93688.587978
1       399002          深成指R  500  ...  1551.817177  182200.555052  93688.587978
2       399003          成份Ｂ指   10  ...     0.545615     345.946714    289.787042
3       399004        深证100R  100  ...   725.052558  102390.468955  51849.808034
4       399005         中小100  100  ...   425.914666   52479.098750  26772.330931
        ...           ...  ...  ...          ...            ...           ...
1257  CNB20014  高等级非贴标绿债（全价）    0  ...   109.463922            NaN           NaN
1258  CNB20015   高等级贴标绿债（全价）    0  ...   193.434154            NaN           NaN
1259      CNYR       人民币实际指数    0  ...     0.000000            NaN           NaN
1260      CNYX         人民币指数    0  ...     0.000000            NaN           NaN
1261   RETHKDG    中华房地产信托基金R   30  ...     2.311884    1617.705097    221.909288
[1262 rows x 10 columns]
```

#### 指数行情

接口: index_hist_cni

目标地址: http://www.cnindex.com.cn/module/index-detail.html?act_menu=1&indexCode=399001

描述: 国证指数-具体指数的日频率行情数据

输入参数

| 名称         | 类型  | 描述                                             |
|------------|-----|------------------------------------------------|
| symbol     | str | symbol="399005"; 从 ak.index_all_cni() 接口获取指数代码 |
| start_date | str | start_date="20230114"                          |
| end_date   | str | end_date="20240114"                            |

输出参数

| 名称  | 类型      | 描述       |
|-----|---------|----------|
| 日期  | object  | -        |
| 开盘价 | float64 | -        |
| 最高价 | float64 | -        |
| 最低价 | float64 | -        |
| 收盘价 | float64 | -        |
| 涨跌幅 | float64 | -        |
| 成交量 | float64 | 注意单位: 万手 |
| 成交额 | float64 | 注意单位: 亿元 |

接口示例

```python
import akshare as ak

index_hist_cni_df = ak.index_hist_cni(symbol="399005", start_date="20230114", end_date="20240114")
print(index_hist_cni_df)
```

数据示例

```
       日期      开盘价     最高价   最低价    收盘价   涨跌幅    成交量     成交额
0    2023-01-16  7646.53  7791.17  7622.78  7726.87  0.0111  3333.08  764.47
1    2023-01-17  7739.01  7763.82  7708.61  7747.54  0.0027  2451.54  553.48
2    2023-01-18  7754.17  7788.91  7742.23  7752.89  0.0007  2135.29  491.04
3    2023-01-19  7752.13  7821.15  7720.00  7821.15  0.0088  2316.82  551.81
4    2023-01-20  7831.02  7872.72  7812.26  7858.43  0.0048  2388.04  591.12
..          ...      ...      ...      ...      ...     ...      ...     ...
237  2024-01-08  5724.03  5740.62  5618.65  5618.65 -0.0209  2380.41  393.86
238  2024-01-09  5629.52  5683.72  5599.87  5641.58  0.0041  2480.49  427.98
239  2024-01-10  5619.17  5670.40  5562.28  5594.86 -0.0083  2180.10  402.80
240  2024-01-11  5588.35  5720.80  5575.51  5691.66  0.0173  2619.25  512.84
241  2024-01-12  5675.48  5709.83  5646.72  5646.90 -0.0079  2454.19  433.13
[242 rows x 8 columns]
```

#### 指数样本详情

接口: index_detail_cni

目标地址: http://www.cnindex.com.cn/module/index-detail.html?act_menu=1&indexCode=399001

描述: 国证指数-指数样本详情数据

输入参数

| 名称     | 类型  | 描述                                                 |
|--------|-----|----------------------------------------------------|
| symbol | str | symbol='399001'; 从 **ak.index_all_cni()** 接口获取指数代码 |
| date   | str | date='202404'; 指定年月                                |

输出参数

| 名称     | 类型      | 描述       |
|--------|---------|----------|
| 日期     | object  | -        |
| 样本代码   | object  | -        |
| 样本简称   | object  | -        |
| 所属行业   | object  | -        |
| 自由流通市值 | float64 | 注意单位: 亿元 |
| 总市值    | float64 | 注意单位: 亿元 |
| 权重     | float64 | 注意单位: %  |

接口示例

```python
import akshare as ak

index_detail_cni_df = ak.index_detail_cni(symbol='399001', date='202404')
print(index_detail_cni_df)
```

数据示例

```
          日期    样本代码  样本简称  所属行业   总市值   权重
0    2024-04-30  300750   宁德时代    工业  8912.46  4.97
1    2024-04-30  000333   美的集团  可选消费  4867.11  3.30
2    2024-04-30  000858  五 粮 液  主要消费  5839.10  2.68
3    2024-04-30  000651   格力电器  可选消费  2371.95  1.83
4    2024-04-30  002594    比亚迪  可选消费  3961.72  1.82
..          ...     ...    ...   ...      ...   ...
495  2024-04-30  002901   大博医疗  医药卫生   132.98  0.02
496  2024-04-30  001308   康冠科技  信息技术   172.77  0.02
497  2024-04-30  003039   顺控发展  公用事业    86.14  0.02
498  2024-04-30  001322   箭牌家居  可选消费    87.99  0.01
499  2024-04-30  301177   迪阿股份  可选消费    97.12  0.01
[500 rows x 6 columns]
```

#### 历史样本

接口: index_detail_hist_cni

目标地址: http://www.cnindex.com.cn/module/index-detail.html?act_menu=1&indexCode=399001

描述: 国证指数-历史样本数据

输入参数

| 名称     | 类型  | 描述                                                           |
|--------|-----|--------------------------------------------------------------|
| symbol | str | symbol='399005'; 从 **ak.index_all_cni()** 接口获取指数代码           |
| date   | str | date='202201', 默认空返回所有数据; date="202201", 则返回 2022 年 1 月的全部数据 |

输出参数

| 名称     | 类型      | 描述       |
|--------|---------|----------|
| 日期     | object  | -        |
| 样本代码   | object  | -        |
| 样本简称   | object  | -        |
| 所属行业   | object  | -        |
| 自由流通市值 | float64 | 注意单位: 亿元 |
| 总市值    | float64 | 注意单位: 亿元 |
| 权重     | float64 | 注意单位: %  |

接口示例

```python
import akshare as ak

index_detail_hist_cni_df = ak.index_detail_hist_cni(symbol='399005', date='202201')
print(index_detail_hist_cni_df)
```

数据示例

```
            日期    样本代码  样本简称  所属行业   自由流通市值      总市值    权重
0   2022-01-28  002475  立讯精密  信息技术  2019.85  3302.14  5.58
1   2022-01-28  002594   比亚迪  可选消费  1840.93  4112.21  5.09
2   2022-01-28  002415  海康威视  信息技术  1752.41  4430.77  4.84
3   2022-01-28  002142  宁波银行    金融  1332.60  2581.34  3.68
4   2022-01-28  002714  牧原股份  主要消费  1241.05  2872.72  3.43
..         ...     ...   ...   ...      ...      ...   ...
95  2022-01-28  003022  联泓新科   原材料    66.33   375.83  0.18
96  2022-01-28  003035  南网能源    工业    52.73   263.64  0.15
97  2022-01-28  002563  森马服饰  可选消费    48.03   188.05  0.13
98  2022-01-28  002945  华林证券    金融    37.72   377.19  0.10
99  2022-01-28  002901  大博医疗  医药卫生    19.99   195.24  0.06
[100 rows x 7 columns]
```

#### 历史调样

接口: index_detail_hist_adjust_cni

目标地址: http://www.cnindex.com.cn/module/index-detail.html?act_menu=1&indexCode=399001

描述: 国证指数-样本详情-历史调样

输入参数

| 名称     | 类型  | 描述                                                 |
|--------|-----|----------------------------------------------------|
| symbol | str | symbol='399005'; 从 **ak.index_all_cni()** 接口获取指数代码 |

输出参数

| 名称   | 类型     | 描述  |
|------|--------|-----|
| 开始日期 | object | -   |
| 结束日期 | object | -   |
| 样本代码 | object | -   |
| 样本简称 | object | -   |
| 所属行业 | object | -   |
| 调整类型 | object | -   |

接口示例

```python
import akshare as ak

index_detail_hist_adjust_cni_df = ak.index_detail_hist_adjust_cni(symbol='399005')
print(index_detail_hist_adjust_cni_df)
```

数据示例

```
      开始日期        结束日期  样本代码  样本简称 所属行业 调整类型
0     2024-06-17  2024-12-13  002001  新 和 成  医药卫生  OLD
1     2024-06-17  2024-12-13  002007   华兰生物  医药卫生  OLD
2     2024-06-17  2024-12-13  002008   大族激光  信息技术  OLD
3     2024-06-17  2024-12-13  002025   航天电器  信息技术  OLD
4     2024-06-17  2024-12-13  002027   分众传媒  可选消费  OLD
...          ...         ...     ...    ...   ...  ...
1185  2019-12-16  2020-06-12  002926   华西证券    金融  OLD
1186  2019-12-16  2020-06-12  002938   鹏鼎控股  信息技术  OLD
1187  2019-12-16  2020-06-12  002939   长城证券    金融  OLD
1188  2019-12-16  2020-06-12  002958   青农商行    金融    +
1189  2019-12-16  2020-06-12  003816   中国广核  公用事业  OLD
[1190 rows x 6 columns]
```

### 期权波动率指数

#### 50ETF 期权波动率指数

接口: index_option_50etf_qvix

目标地址: http://1.optbbs.com/s/vix.shtml?50ETF

描述: 50ETF 期权波动率指数 QVIX; 又称中国版的恐慌指数

限量: 单次返回所有数据

输入参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| -   | -   | -   |

输出参数

| 名称    | 类型      | 描述  |
|-------|---------|-----|
| date  | object  | -   |
| open  | float64 | -   |
| high  | float64 | -   |
| low   | float64 | -   |
| close | float64 | -   |

接口示例

```python
import akshare as ak

index_option_50etf_qvix_df = ak.index_option_50etf_qvix()
print(index_option_50etf_qvix_df)
```

数据示例

```
            date       open       high        low  close
0     2015-02-09  28.800000  28.756878  27.829555  28.63
1     2015-02-10  28.630000  29.435024  28.499700  28.75
2     2015-02-11  26.470710  26.470710  26.176691  26.44
3     2015-02-12  26.389094  26.389094  25.773367  25.80
4     2015-02-13  24.286958  24.286958  22.810473  23.41
          ...        ...        ...        ...    ...
2108  2023-10-13  15.290000  15.780000  15.180000  15.61
2109  2023-10-16  16.270000  16.720000  16.060000  16.41
2110  2023-10-17  16.470000  16.580000  15.800000  15.80
2111  2023-10-18  15.810000  16.110000  15.450000  15.70
2112  2023-10-19  16.060000  18.280000  16.060000  17.95
[2113 rows x 5 columns]
```

#### 50ETF 期权波动率指数-分时

接口: index_option_50etf_min_qvix

目标地址: http://1.optbbs.com/s/vix.shtml?50ETF

描述: 50ETF 期权波动率指数-分时

限量: 单次返回最近交易日的分时数据

输入参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| -   | -   | -   |

输出参数

| 名称    | 类型      | 描述  |
|-------|---------|-----|
| time  | object  | -   |
| qvix  | float64 | -   |

接口示例

```python
import akshare as ak

index_option_50etf_min_qvix_df = ak.index_option_50etf_min_qvix()
print(index_option_50etf_min_qvix_df)
```

数据示例

```
         time   qvix
0     9:30:00  16.06
1     9:31:11  16.52
2     9:32:11  16.52
3     9:33:11  16.70
4     9:34:11  16.64
..        ...    ...
234  14:53:19  18.14
235  14:54:19  18.06
236  14:55:19  17.99
237  14:56:19  17.95
238  15:00:00    NaN
[239 rows x 2 columns]
```

#### 300ETF 期权波动率指数

接口: index_option_300etf_qvix

目标地址: https://1.optbbs.com/s/vix.shtml?300ETF

描述: 300ETF 期权波动率指数 QVIX

限量: 单次返回所有数据

输入参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| -   | -   | -   |

输出参数

| 名称    | 类型      | 描述  |
|-------|---------|-----|
| date  | object  | -   |
| open  | float64 | -   |
| high  | float64 | -   |
| low   | float64 | -   |
| close | float64 | -   |

接口示例

```python
import akshare as ak

index_option_300etf_qvix_df = ak.index_option_300etf_qvix()
print(index_option_300etf_qvix_df)
```

数据示例

```
            date   open   high    low  close
0     2015-02-09    NaN    NaN    NaN    NaN
1     2015-02-10    NaN    NaN    NaN    NaN
2     2015-02-11    NaN    NaN    NaN    NaN
3     2015-02-12    NaN    NaN    NaN    NaN
4     2015-02-13    NaN    NaN    NaN    NaN
          ...    ...    ...    ...    ...
2108  2023-10-13  15.18  15.70  15.00  15.46
2109  2023-10-16  16.06  17.04  15.89  16.67
2110  2023-10-17  16.73  16.96  16.18  16.19
2111  2023-10-18  16.42  16.42  15.92  16.25
2112  2023-10-19  16.59  18.46  16.59  18.06
[2113 rows x 5 columns]
```

#### 300ETF 期权波动率指数-分时

接口: index_option_300etf_min_qvix

目标地址: https://1.optbbs.com/s/vix.shtml?300ETF

描述: 300ETF 期权波动率指数-分时

限量: 单次返回最近交易日的分时数据

输入参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| -   | -   | -   |

输出参数

| 名称    | 类型      | 描述  |
|-------|---------|-----|
| time  | object  | -   |
| qvix  | float64 | -   |

接口示例

```python
import akshare as ak

index_option_300etf_min_qvix_df = ak.index_option_300etf_min_qvix()
print(index_option_300etf_min_qvix_df)
```

数据示例

```
         time   qvix
0     9:30:00  16.59
1     9:31:11  16.96
2     9:32:11  17.13
3     9:33:11  17.44
4     9:34:11  17.39
..        ...    ...
234  14:53:19  18.30
235  14:54:19  18.25
236  14:55:19  18.17
237  14:56:19  18.06
238  15:00:00    NaN
[239 rows x 2 columns]
```

#### 500ETF 期权波动率指数

接口: index_option_500etf_qvix

目标地址: http://1.optbbs.com/s/vix.shtml?500ETF

描述: 500ETF 期权波动率指数 QVIX

限量: 单次返回所有数据

输入参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| -   | -   | -   |

输出参数

| 名称    | 类型      | 描述  |
|-------|---------|-----|
| date  | object  | -   |
| open  | float64 | -   |
| high  | float64 | -   |
| low   | float64 | -   |
| close | float64 | -   |

接口示例

```python
import akshare as ak

index_option_500etf_qvix_df = ak.index_option_500etf_qvix()
print(index_option_500etf_qvix_df)
```

数据示例

```
            date   open   high    low  close
0     2015-02-09    NaN    NaN    NaN    NaN
1     2015-02-10    NaN    NaN    NaN    NaN
2     2015-02-11    NaN    NaN    NaN    NaN
3     2015-02-12    NaN    NaN    NaN    NaN
4     2015-02-13    NaN    NaN    NaN    NaN
...          ...    ...    ...    ...    ...
2393  2024-12-24  27.43  27.43  25.78  26.17
2394  2024-12-25  26.47  27.60  26.10  26.74
2395  2024-12-26  27.16  27.16  24.16  24.51
2396  2024-12-27  24.74  24.76  23.48  23.75
2397  2024-12-30  24.64  24.84  23.86  24.44
[2398 rows x 5 columns]
```

#### 500ETF 期权波动率指数-分时

接口: index_option_500etf_min_qvix

目标地址: http://1.optbbs.com/s/vix.shtml?500ETF

描述: 500ETF 期权波动率指数-分时

限量: 单次返回最近交易日的分时数据

输入参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| -   | -   | -   |

输出参数

| 名称    | 类型      | 描述  |
|-------|---------|-----|
| time  | object  | -   |
| qvix  | float64 | -   |

接口示例

```python
import akshare as ak

index_option_500etf_min_qvix_df = ak.index_option_500etf_min_qvix()
print(index_option_500etf_min_qvix_df)
```

数据示例

```
         time   qvix
0     9:30:00  24.61
1     9:31:51  24.61
2     9:32:51  24.68
3     9:33:51  24.69
4     9:34:51  24.73
..        ...    ...
234  14:53:59  25.22
235  14:54:59  25.15
236  14:55:59  25.17
237  14:56:59  25.21
238  15:00:59  25.21
[239 rows x 2 columns]
```

#### 创业板 期权波动率指数

接口: index_option_cyb_qvix

目标地址: http://1.optbbs.com/s/vix.shtml?CYB

描述: 创业板 期权波动率指数 QVIX

限量: 单次返回所有数据

输入参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| -   | -   | -   |

输出参数

| 名称    | 类型      | 描述  |
|-------|---------|-----|
| date  | object  | -   |
| open  | float64 | -   |
| high  | float64 | -   |
| low   | float64 | -   |
| close | float64 | -   |

接口示例

```python
import akshare as ak

index_option_cyb_qvix_df = ak.index_option_cyb_qvix()
print(index_option_cyb_qvix_df)
```

数据示例

```
            date   open   high    low  close
0     2015-02-09    NaN    NaN    NaN    NaN
1     2015-02-10    NaN    NaN    NaN    NaN
2     2015-02-11    NaN    NaN    NaN    NaN
3     2015-02-12    NaN    NaN    NaN    NaN
4     2015-02-13    NaN    NaN    NaN    NaN
...          ...    ...    ...    ...    ...
2393  2024-12-24  32.25  32.25  29.97  30.39
2394  2024-12-25  30.41  30.53  29.23  29.84
2395  2024-12-26  30.12  30.12  27.44  27.69
2396  2024-12-27  27.72  27.91  26.77  26.95
2397  2024-12-30  27.95  27.98  26.90  27.58
[2398 rows x 5 columns]
```

#### 创业板 期权波动率指数-分时

接口: index_option_cyb_min_qvix

目标地址: http://1.optbbs.com/s/vix.shtml?CYB

描述: 创业板 期权波动率指数-分时

限量: 单次返回最近交易日的分时数据

输入参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| -   | -   | -   |

输出参数

| 名称    | 类型      | 描述  |
|-------|---------|-----|
| time  | object  | -   |
| qvix  | float64 | -   |

接口示例

```python
import akshare as ak

index_option_cyb_min_qvix_df = ak.index_option_cyb_min_qvix()
print(index_option_cyb_min_qvix_df)
```

数据示例

```
         time   qvix
0     9:30:00  27.85
1     9:31:51  27.76
2     9:32:51  27.84
3     9:33:51  27.88
4     9:34:51  27.71
..        ...    ...
234  14:53:59  28.53
235  14:54:59  28.61
236  14:55:59  28.61
237  14:56:59  28.55
238  15:00:59  28.49
[239 rows x 2 columns]
```

#### 科创板 期权波动率指数

接口: index_option_kcb_qvix

目标地址: http://1.optbbs.com/s/vix.shtml?KCB

描述: 科创板 期权波动率指数 QVIX

限量: 单次返回所有数据

输入参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| -   | -   | -   |

输出参数

| 名称    | 类型      | 描述  |
|-------|---------|-----|
| date  | object  | -   |
| open  | float64 | -   |
| high  | float64 | -   |
| low   | float64 | -   |
| close | float64 | -   |

接口示例

```python
import akshare as ak

index_option_kcb_qvix_df = ak.index_option_kcb_qvix()
print(index_option_kcb_qvix_df)
```

数据示例

```
            date   open   high    low  close
0     2015-02-09    NaN    NaN    NaN    NaN
1     2015-02-10    NaN    NaN    NaN    NaN
2     2015-02-11    NaN    NaN    NaN    NaN
3     2015-02-12    NaN    NaN    NaN    NaN
4     2015-02-13    NaN    NaN    NaN    NaN
...          ...    ...    ...    ...    ...
2393  2024-12-24  37.88  37.93  36.76  37.03
2394  2024-12-25  37.04  37.67  36.18  36.18
2395  2024-12-26  35.92  36.68  33.78  34.01
2396  2024-12-27  32.66  33.48  32.37  32.54
2397  2024-12-30  33.50  33.80  33.04  33.37
[2398 rows x 5 columns]
```

#### 科创板 期权波动率指数-分时

接口: index_option_kcb_min_qvix

目标地址: http://1.optbbs.com/s/vix.shtml?KCB

描述: 科创板 期权波动率指数-分时

限量: 单次返回最近交易日的分时数据

输入参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| -   | -   | -   |

输出参数

| 名称    | 类型      | 描述  |
|-------|---------|-----|
| time  | object  | -   |
| qvix  | float64 | -   |

接口示例

```python
import akshare as ak

index_option_kcb_min_qvix_df = ak.index_option_kcb_min_qvix()
print(index_option_kcb_min_qvix_df)
```

数据示例

```
         time   qvix
0     9:30:00  29.91
1     9:31:51  31.34
2     9:32:51  31.34
3     9:33:51  33.52
4     9:34:51  33.82
..        ...    ...
234  14:53:59  34.07
235  14:54:59  34.06
236  14:55:59  34.06
237  14:56:59  33.91
238  15:00:59  33.91
[239 rows x 2 columns]
```

#### 深证100ETF 期权波动率指数

接口: index_option_100etf_qvix

目标地址: http://1.optbbs.com/s/vix.shtml?100ETF

描述: 深证100ETF 期权波动率指数 QVIX

限量: 单次返回所有数据

输入参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| -   | -   | -   |

输出参数

| 名称    | 类型      | 描述  |
|-------|---------|-----|
| date  | object  | -   |
| open  | float64 | -   |
| high  | float64 | -   |
| low   | float64 | -   |
| close | float64 | -   |

接口示例

```python
import akshare as ak

index_option_100etf_qvix_df = ak.index_option_100etf_qvix()
print(index_option_100etf_qvix_df)
```

数据示例

```
            date   open   high    low  close
0     2015-02-09    NaN    NaN    NaN    NaN
1     2015-02-10    NaN    NaN    NaN    NaN
2     2015-02-11    NaN    NaN    NaN    NaN
3     2015-02-12    NaN    NaN    NaN    NaN
4     2015-02-13    NaN    NaN    NaN    NaN
...          ...    ...    ...    ...    ...
2393  2024-12-24  25.46  25.46  24.05  24.21
2394  2024-12-25  24.49  24.61  23.72  24.01
2395  2024-12-26  24.40  24.40  22.18  22.46
2396  2024-12-27  22.81  23.10  22.04  22.05
2397  2024-12-30  23.15  23.15  22.23  22.44
[2398 rows x 5 columns]
```

#### 深证100ETF 期权波动率指数-分时

接口: index_option_100etf_min_qvix

目标地址: http://1.optbbs.com/s/vix.shtml?100ETF

描述: 深证100ETF 期权波动率指数-分时

限量: 单次返回最近交易日的分时数据

输入参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| -   | -   | -   |

输出参数

| 名称    | 类型      | 描述  |
|-------|---------|-----|
| time  | object  | -   |
| qvix  | float64 | -   |

接口示例

```python
import akshare as ak

index_option_100etf_min_qvix_df = ak.index_option_100etf_min_qvix()
print(index_option_100etf_min_qvix_df)
```

数据示例

```
         time   qvix
0     9:30:00  22.80
1     9:31:51  22.95
2     9:32:51  22.85
3     9:33:51  22.75
4     9:34:51  22.66
..        ...    ...
234  14:53:59  23.05
235  14:54:59  22.90
236  14:55:59  23.02
237  14:56:59  23.05
238  15:00:59  23.04
[239 rows x 2 columns]
```

#### 中证300股指 期权波动率指数

接口: index_option_300index_qvix

目标地址: http://1.optbbs.com/s/vix.shtml?Index

描述: 中证300股指 期权波动率指数 QVIX

限量: 单次返回所有数据

输入参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| -   | -   | -   |

输出参数

| 名称    | 类型      | 描述  |
|-------|---------|-----|
| date  | object  | -   |
| open  | float64 | -   |
| high  | float64 | -   |
| low   | float64 | -   |
| close | float64 | -   |

接口示例

```python
import akshare as ak

index_option_300index_qvix_df = ak.index_option_300index_qvix()
print(index_option_300index_qvix_df)
```

数据示例

```
            date   open   high    low  close
0     2015-02-09    NaN    NaN    NaN    NaN
1     2015-02-10    NaN    NaN    NaN    NaN
2     2015-02-11    NaN    NaN    NaN    NaN
3     2015-02-12    NaN    NaN    NaN    NaN
4     2015-02-13    NaN    NaN    NaN    NaN
...          ...    ...    ...    ...    ...
2393  2024-12-24  21.63  21.72  20.88  21.26
2394  2024-12-25  21.53  21.53  20.94  21.07
2395  2024-12-26  21.42  21.42  20.38  20.46
2396  2024-12-27  20.75  20.88  20.04  20.13
2397  2024-12-30  20.85  20.85  19.98  20.06
[2398 rows x 5 columns]
```

#### 中证300股指 期权波动率指数-分时

接口: index_option_300index_min_qvix

目标地址: http://1.optbbs.com/s/vix.shtml?Index

描述: 中证300股指 期权波动率指数-分时

限量: 单次返回最近交易日的分时数据

输入参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| -   | -   | -   |

输出参数

| 名称    | 类型      | 描述  |
|-------|---------|-----|
| time  | object  | -   |
| qvix  | float64 | -   |

接口示例

```python
import akshare as ak

index_option_300index_min_qvix_df = ak.index_option_300index_min_qvix()
print(index_option_300index_min_qvix_df)
```

数据示例

```
         time   qvix
0     9:30:00  20.32
1     9:31:51  20.25
2     9:32:51  20.23
3     9:33:51  20.31
4     9:34:51  20.40
..        ...    ...
234  14:53:59  20.06
235  14:54:59  20.00
236  14:55:59  19.97
237  14:56:59  20.03
238  15:00:59  20.08
[239 rows x 2 columns]
```

#### 中证1000股指 期权波动率指数

接口: index_option_1000index_qvix

目标地址: http://1.optbbs.com/s/vix.shtml?Index1000

描述: 中证1000股指 期权波动率指数 QVIX

限量: 单次返回所有数据

输入参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| -   | -   | -   |

输出参数

| 名称    | 类型      | 描述  |
|-------|---------|-----|
| date  | object  | -   |
| open  | float64 | -   |
| high  | float64 | -   |
| low   | float64 | -   |
| close | float64 | -   |

接口示例

```python
import akshare as ak

index_option_1000index_qvix_df = ak.index_option_1000index_qvix()
print(index_option_1000index_qvix_df)
```

数据示例

```
            date   open   high    low  close
0     2015-02-09    NaN    NaN    NaN    NaN
1     2015-02-10    NaN    NaN    NaN    NaN
2     2015-02-11    NaN    NaN    NaN    NaN
3     2015-02-12    NaN    NaN    NaN    NaN
4     2015-02-13    NaN    NaN    NaN    NaN
...          ...    ...    ...    ...    ...
2393  2024-12-24  28.95  29.11  28.07  28.23
2394  2024-12-25  28.16  29.28  28.16  28.19
2395  2024-12-26  28.54  28.54  26.74  26.90
2396  2024-12-27  27.09  27.17  25.60  25.98
2397  2024-12-30  26.85  27.04  26.15  26.50
[2398 rows x 5 columns]
```

#### 中证1000股指 期权波动率指数-分时

接口: index_option_1000index_min_qvix

目标地址: http://1.optbbs.com/s/vix.shtml?Index1000

描述: 中证1000股指 期权波动率指数-分时

限量: 单次返回最近交易日的分时数据

输入参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| -   | -   | -   |

输出参数

| 名称    | 类型      | 描述  |
|-------|---------|-----|
| time  | object  | -   |
| qvix  | float64 | -   |

接口示例

```python
import akshare as ak

index_option_1000index_min_qvix_df = ak.index_option_1000index_min_qvix()
print(index_option_1000index_min_qvix_df)
```

数据示例

```
         time   qvix
0     9:30:00  26.79
1     9:31:51  26.84
2     9:32:51  26.90
3     9:33:51  26.93
4     9:34:51  26.78
..        ...    ...
234  14:53:59  27.50
235  14:54:59  27.47
236  14:55:59  27.51
237  14:56:59  27.55
238  15:00:59  27.55
[239 rows x 2 columns]
```

#### 上证50股指 期权波动率指数

接口: index_option_50index_qvix

目标地址: http://1.optbbs.com/s/vix.shtml?50index

描述: 上证50股指 期权波动率指数 QVIX

限量: 单次返回所有数据

输入参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| -   | -   | -   |

输出参数

| 名称    | 类型      | 描述  |
|-------|---------|-----|
| date  | object  | -   |
| open  | float64 | -   |
| high  | float64 | -   |
| low   | float64 | -   |
| close | float64 | -   |

接口示例

```python
import akshare as ak

index_option_50index_qvix_df = ak.index_option_50index_qvix()
print(index_option_50index_qvix_df)
```

数据示例

```
            date   open   high    low  close
0     2015-02-09    NaN    NaN    NaN    NaN
1     2015-02-10    NaN    NaN    NaN    NaN
2     2015-02-11    NaN    NaN    NaN    NaN
3     2015-02-12    NaN    NaN    NaN    NaN
4     2015-02-13    NaN    NaN    NaN    NaN
...          ...    ...    ...    ...    ...
2393  2024-12-24  20.78  20.82  20.08  20.43
2394  2024-12-25  20.72  21.19  20.43  20.67
2395  2024-12-26  20.74  20.74  19.67  19.81
2396  2024-12-27  19.96  20.10  19.43  19.54
2397  2024-12-30  20.36  20.40  19.65  19.84
[2398 rows x 5 columns]
```

#### 上证50股指 期权波动率指数-分时

接口: index_option_50index_min_qvix

目标地址: http://1.optbbs.com/s/vix.shtml?50index

描述: 上证50股指 期权波动率指数-分时

限量: 单次返回最近交易日的分时数据

输入参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| -   | -   | -   |

输出参数

| 名称    | 类型      | 描述  |
|-------|---------|-----|
| time  | object  | -   |
| qvix  | float64 | -   |

接口示例

```python
import akshare as ak

index_option_50index_min_qvix_df = ak.index_option_50index_min_qvix()
print(index_option_50index_min_qvix_df)
```

数据示例

```
         time   qvix
0     9:30:00  20.05
1     9:31:51  20.24
2     9:32:51  20.25
3     9:33:51  20.15
4     9:34:51  20.13
..        ...    ...
234  14:53:59  19.61
235  14:54:59  19.73
236  14:55:59  19.70
237  14:56:59  19.62
238  15:00:59  19.57
[239 rows x 2 columns]
```

### 申万一级行业信息

接口: sw_index_first_info

目标地址: https://legulegu.com/stockdata/sw-industry-overview#level1

描述: 申万一级行业信息

输入参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| -   | -   | -   |

输出参数

| 名称         | 类型      | 描述  |
|------------|---------|-----|
| 行业代码       | object  |     |
| 行业名称       | object  |     |
| 成份个数       | int64   |     |
| 静态市盈率      | float64 |     |
| TTM(滚动)市盈率 | float64 |     |
| 市净率        | float64 |     |
| 静态股息率      | float64 |     |

接口示例

```python
import akshare as ak

sw_index_first_info_df = ak.sw_index_first_info()
print(sw_index_first_info_df)
```

数据示例

```
    行业代码  行业名称  成份个数  静态市盈率  TTM(滚动)市盈率   市净率  静态股息率
0   801010.SI  农林牧渔    97  43.49       48.28  2.76   1.39
1   801030.SI  基础化工   341  14.94       13.45  2.40   1.48
2   801040.SI    钢铁    44   6.83        9.66  0.92   6.83
3   801050.SI  有色金属   128  22.40       15.26  2.57   0.92
4   801080.SI    电子   296  21.74       23.02  2.83   0.94
5   801880.SI    汽车   238  27.56       29.76  2.25   1.69
6   801110.SI  家用电器    79  16.70       15.68  2.90   2.97
7   801120.SI  食品饮料   118  38.60       35.83  7.54   1.22
8   801130.SI  纺织服饰   112  14.58       14.85  1.75   3.68
9   801140.SI  轻工制造   142  19.92       23.64  2.26   1.77
10  801150.SI  医药生物   353  24.22       22.19  3.08   1.15
11  801160.SI  公用事业   122  20.54       19.11  1.90   2.41
12  801170.SI  交通运输   123   9.20        8.07  1.24   2.16
13  801180.SI   房地产   115   9.14       10.07  0.94   5.05
14  801200.SI  商贸零售   104  23.41       25.75  2.29   1.35
15  801210.SI  社会服务    73  43.26       42.17  3.18   0.51
16  801780.SI    银行    42   4.67        4.50  0.54   5.52
17  801790.SI  非银金融    87  11.43       12.91  1.16   2.79
18  801230.SI    综合    24  48.95       40.81  2.07   0.66
19  801710.SI  建筑材料    74   8.47        9.11  1.35   2.96
20  801720.SI  建筑装饰   156   7.64        7.29  0.84   2.55
21  801730.SI  电力设备   261  39.23       32.34  4.26   0.46
22  801890.SI  机械设备   390  21.37       24.12  2.09   1.67
23  801740.SI  国防军工    98  54.96       51.40  3.82   0.43
24  801750.SI   计算机   265  30.96       32.51  3.03   1.03
25  801760.SI    传媒   138  16.00       17.11  1.62   2.67
26  801770.SI    通信   105  15.35       13.98  1.27   1.06
27  801950.SI    煤炭    38  11.37        7.84  1.70   3.54
28  801960.SI  石油石化    47  10.37        8.33  0.99   4.07
29  801970.SI    环保   108  16.02       18.38  1.55   1.77
30  801980.SI  美容护理    28  40.38       41.16  5.36   0.78
```

### 申万二级行业信息

接口: sw_index_second_info

目标地址: https://legulegu.com/stockdata/sw-industry-overview#level1

描述: 申万二级行业信息

输入参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| -   | -   | -   |

输出参数

| 名称         | 类型      | 描述  |
|------------|---------|-----|
| 行业代码       | object  |     |
| 行业名称       | object  |     |
| 上级行业       | object  |     |
| 成份个数       | int64   |     |
| 静态市盈率      | float64 |     |
| TTM(滚动)市盈率 | float64 |     |
| 市净率        | float64 |     |
| 静态股息率      | float64 |     |

接口示例

```python
import akshare as ak

sw_index_second_info_df = ak.sw_index_second_info()
print(sw_index_second_info_df)
```

数据示例

```
     行业代码   行业名称  上级行业  成份个数 静态市盈率 TTM(滚动)市盈率 市净率 静态股息率
0    801016.SI    种植业  农林牧渔    19  35.38       37.23  2.69   2.05
1    801015.SI     渔业  农林牧渔     6  29.29       30.42  1.04   0.88
2    801014.SI     饲料  农林牧渔    16  33.07       22.95  3.80   1.22
3    801012.SI  农产品加工  农林牧渔    22  35.96       46.34  2.03   1.26
4    801017.SI    养殖业  农林牧渔    21  10.64        6.61  2.08   1.44
..         ...    ...   ...   ...    ...         ...   ...    ...
119  801963.SI  炼化及贸易  石油石化    31  11.03       11.54  1.04   5.05
120  801971.SI   环境治理    环保   105  18.89       17.79  1.47   1.89
121  801972.SI  环保设备Ⅱ    环保    28  24.18       25.12  1.67   2.27
122  801981.SI   个护用品  美容护理    13  32.54       31.44  2.46   2.59
123  801982.SI    化妆品  美容护理    15  26.26       28.75  2.89   1.21
[124 rows x 8 columns]
```

### 申万三级行业信息

接口: sw_index_third_info

目标地址: https://legulegu.com/stockdata/sw-industry-overview#level1

描述: 申万三级行业信息

输入参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| -   | -   | -   |

输出参数

| 名称         | 类型      | 描述  |
|------------|---------|-----|
| 行业代码       | object  |     |
| 行业名称       | object  |     |
| 成份个数       | int64   |     |
| 静态市盈率      | float64 |     |
| TTM(滚动)市盈率 | float64 |     |
| 市净率        | float64 |     |
| 静态股息率      | float64 |     |

接口示例

```python
import akshare as ak

sw_index_third_info_df = ak.sw_index_third_info()
print(sw_index_third_info_df)
```

数据示例

```
     行业代码      行业名称   上级行业  成份个数  静态市盈率  TTM(滚动)市盈率 市净率 静态股息率
0    850111.SI        种子    种植业     8  49.00       53.27  3.63   0.78
1    850113.SI     其他种植业    种植业     5  71.56       67.46  2.31   0.27
2    850122.SI      水产养殖     渔业     4  70.32       82.58  1.03   0.55
3    850142.SI      畜禽饲料     饲料     9  28.63       64.62  1.84   2.05
4    850151.SI      果蔬加工  农产品加工     5  19.19       30.98  2.97   1.16
..         ...       ...    ...   ...    ...         ...   ...    ...
253  859714.SI    综合环境治理   环境治理    14  11.03       10.15  2.33   1.67
254  859721.SI     环保设备Ⅲ  环保设备Ⅱ    28  24.18       25.12  1.67   2.27
255  859811.SI      生活用纸   个护用品     9  29.10       30.32  2.23   2.72
256  859821.SI  化妆品制造及其他    化妆品     7  23.92       28.49  2.17   1.33
257  859822.SI     品牌化妆品    化妆品     8  26.90       28.81  3.14   1.18
[258 rows x 8 columns]
```

### 申万三级行业成份

接口: sw_index_third_cons

目标地址: https://legulegu.com/stockdata/index-composition?industryCode=851921.SI

描述: 申万三级行业成份

输入参数

| 名称     | 类型  | 描述                                                               |
|--------|-----|------------------------------------------------------------------|
| symbol | str | symbol="850111.SI"; 行业代码; 可以通过 ak.sw_index_third_info() 获取所有行业代码 |

输出参数

| 名称               | 类型      | 描述       |
|------------------|---------|----------|
| 序号               | int64   |          |
| 股票代码             | object  |          |
| 股票简称             | object  |          |
| 纳入时间             | object  |          |
| 申万1级             | object  |          |
| 申万2级             | object  |          |
| 申万3级             | object  |          |
| 价格               | float64 |          |
| 市盈率              | float64 |          |
| 市盈率ttm           | float64 |          |
| 市净率              | float64 |          |
| 股息率              | float64 | 注意单位: %  |
| 市值               | float64 | 注意单位: 亿元 |
| 归母净利润同比增长(09-30) | float64 | 注意单位: %  |
| 归母净利润同比增长(06-30) | float64 | 注意单位: %  |
| 营业收入同比增长(09-30)  | float64 | 注意单位: %  |
| 营业收入同比增长(06-30)  | float64 | 注意单位: %  |

接口示例

```python
import akshare as ak

sw_index_third_cons_df = ak.sw_index_third_cons(symbol="850111.SI")
print(sw_index_third_cons_df)
```

数据示例

```
   序号 股票代码  股票简称  ... 归母净利润同比增长(06-30) 营业收入同比增长(09-30) 营业收入同比增长(06-30)
0   1  600313.SH  农发种业  ...          1433.66           35.62           18.57
1   2  000713.SZ  丰乐种业  ...           -26.10            3.59           -5.45
2   3  000998.SZ  隆平高科  ...          -421.52           55.20            6.98
3   4  300087.SZ  荃银高科  ...           421.95           28.09           42.46
4   5  300189.SZ  神农科技  ...           -39.18          -12.49          -12.68
5   6  600371.SH  万向德农  ...           174.47           11.65            3.10
6   7  600354.SH  敦煌种业  ...           -55.18           -8.59          -13.23
7   8  002041.SZ  登海种业  ...             6.67           10.01            6.12
```

### 商品现货价格指数

接口: spot_goods

目标地址: http://finance.sina.com.cn/futuremarket/spotprice.shtml#titlePos_0

描述: 新浪财经-商品现货价格指数

输入参数

| 名称     | 类型  | 描述                                      |
|--------|-----|-----------------------------------------|
| symbol | str | symbol="波罗的海干散货指数"; 指数目录请参考 **现货指数一览表** |

现货指数一览表

| 名称        | 时间段     |
|-----------|---------|
| 波罗的海干散货指数 | 2007-至今 |
| 钢坯价格指数    | 2005-至今 |
| 澳大利亚粉矿价格  | 2020-至今 |

输出参数

| 名称  | 类型      | 描述  |
|-----|---------|-----|
| 日期  | object  |     |
| 指数  | float64 |     |
| 涨跌额 | float64 |     |
| 涨跌幅 | float64 |     |

接口示例

```python
import akshare as ak

spot_goods_df = ak.spot_goods(symbol="波罗的海干散货指数")
print(spot_goods_df)
```

数据示例

```
         日期      指数    涨跌额   涨跌幅
0     2006-06-23  2808.0   83.0  0.0305
1     2006-06-30  2964.0  156.0  0.0556
2     2006-07-07  2870.0  -94.0 -0.0317
3     2006-07-14  2968.0   98.0  0.0341
4     2006-07-21  3191.0  223.0  0.0751
...          ...     ...    ...     ...
3673  2024-12-18  1028.0  -25.0 -2.3700
3674  2024-12-19   976.0  -52.0 -5.0600
3675  2024-12-20   990.0   14.0  1.4300
3676  2024-12-23   994.0    4.0  0.4000
3677  2024-12-24   997.0    3.0  0.3000
[3678 rows x 4 columns]
```

### 义乌小商品指数

接口: index_yw

目标地址: https://www.ywindex.com/Home/Product/index/

描述: 指定 symbol 的义乌小商品指数的近期历史数据

输入参数

| 名称     | 类型  | 描述                                                    |
|--------|-----|-------------------------------------------------------|
| symbol | str | symbol="周价格指数"; choice of {"周价格指数", "月价格指数", "月景气指数"} |

输出参数-周价格指数

| 名称     | 类型      | 描述  |
|--------|---------|-----|
| 期数     | object  | -   |
| 价格指数   | float64 | -   |
| 场内价格指数 | float64 | -   |
| 网上价格指数 | float64 | -   |
| 订单价格指数 | float64 | -   |
| 出口价格指数 | float64 | -   |

接口示例-周价格指数

```python
import akshare as ak

index_yw_df = ak.index_yw(symbol="周价格指数")
print(index_yw_df)
```

数据示例-周价格指数

```
            期数    价格指数  场内价格指数  网上价格指数  订单价格指数  出口价格指数
0   2023-12-25  102.39  101.53  113.54  100.09  103.37
1   2024-01-01  102.56  102.08  115.00  101.14  105.93
2   2024-01-08  101.93  101.67  113.70  104.33  108.47
3   2024-01-15  102.46  102.53  113.25  102.35  104.82
4   2024-01-22  101.36  101.33  113.67  102.51  104.90
5   2024-01-29  101.70  101.72  111.63  100.24  102.08
6   2024-03-11  101.48  101.62  111.36  100.23  101.47
7   2024-03-18  101.72  101.92  112.56  100.06  101.79
8   2024-03-25  102.20  103.63  114.80  100.22  102.33
9   2024-04-01  101.65  102.00  110.17  101.87  104.52
10  2024-04-08  101.87  102.04  105.75  103.16  103.07
11  2024-04-15  102.13  102.26  104.55  101.91  102.12
```

输出参数-月价格指数

| 名称     | 类型      | 描述  |
|--------|---------|-----|
| 期数     | object  | -   |
| 价格指数   | float64 | -   |
| 场内价格指数 | float64 | -   |
| 网上价格指数 | float64 | -   |
| 订单价格指数 | float64 | -   |
| 出口价格指数 | float64 | -   |

接口示例-月价格指数

```python
import akshare as ak

index_yw_df = ak.index_yw(symbol="月价格指数")
print(index_yw_df)
```

数据示例-月价格指数

```
     期数    价格指数 场内价格指数 网上价格指数 订单价格指数 出口价格指数
0   2023-04-01  100.77  100.94  100.34  100.14  100.09
1   2023-05-01  100.91  101.03  100.42  100.09  100.09
2   2023-06-01  100.82  101.03  100.05  100.04  100.16
3   2023-07-01  100.93  101.21  100.04  100.10  100.39
4   2023-08-01  101.10  101.46   99.15  100.10  100.23
5   2023-09-01  100.98  101.29  100.02  100.07  100.10
6   2023-10-01  100.89  101.14   99.19  100.11  100.05
7   2023-11-01  101.29  101.59  100.02  100.03  100.38
8   2023-12-01  101.75  102.07  100.02  100.15  100.40
9   2024-01-01  101.85  102.01  100.02  100.20  100.29
10  2024-02-01  101.95  102.04  100.03  100.19  100.18
11  2024-04-01  102.23  102.97  100.39  100.19  100.88
```

输出参数-月景气指数

| 名称     | 类型      | 描述  |
|--------|---------|-----|
| 期数     | object  | -   |
| 景气指数   | float64 | -   |
| 规模指数   | float64 | -   |
| 效益指数   | float64 | -   |
| 市场信心指数 | float64 | -   |

接口示例-月景气指数

```python
import akshare as ak

index_yw_df = ak.index_yw(symbol="月景气指数")
print(index_yw_df)
```

数据示例-月景气指数

```
       期数     景气指数   规模指数   效益指数  市场信心指数
0   2023-04-01  1330.19  1096.32  1964.88  1007.34
1   2023-05-01  1352.34  1089.34  2046.42  1008.92
2   2023-06-01  1363.78  1109.05  2048.20  1018.99
3   2023-07-01  1344.06  1064.82  2049.21  1011.24
4   2023-08-01  1379.90  1113.82  2105.74  1008.84
5   2023-09-01  1390.83  1118.74  2134.23  1010.20
6   2023-10-01  1402.84  1109.48  2180.28  1016.54
7   2023-11-01  1416.85  1139.37  2188.47  1015.19
8   2023-12-01  1419.98  1137.22  2203.60  1013.36
9   2024-01-01  1420.00  1131.98  2211.50  1012.54
10  2024-02-01  1393.94  1099.76  2166.03  1014.11
11  2024-04-01  1413.93  1101.44  2237.66  1006.87
```

### 柯桥纺织品指数

接口: index_kq_fz

目标地址: http://www.kqindex.cn/flzs/jiage

描述: 指定 symbol 的柯桥纺织品指数的所有历史数据

输入参数

| 名称     | 类型  | 描述                                                |
|--------|-----|---------------------------------------------------|
| symbol | str | symbol="价格指数"; choice of {'价格指数', '景气指数', '外贸指数'} |

输出参数-价格指数

| 名称  | 类型      | 描述      |
|-----|---------|---------|
| 期次  | object  | -       |
| 指数  | float64 | -       |
| 涨跌幅 | float64 | 注意单位: % |

接口示例-价格指数

```python
import akshare as ak

index_kq_fz_df = ak.index_kq_fz(symbol="价格指数")
print(index_kq_fz_df)
```

数据示例-价格指数

```
    期次      指数    涨跌幅
0   2007-06-04  112.88    NaN
1   2007-06-11   97.77 -13.39
2   2007-06-18   97.98   0.21
3   2007-06-25   99.86   1.92
4   2007-07-02  100.44   0.58
..         ...     ...    ...
751 2023-05-15  106.05  -0.06
752 2023-05-22  106.01  -0.04
753 2023-05-29  105.71  -0.28
754 2023-06-05  105.97   0.25
755 2023-06-12  106.30   0.31
[756 rows x 3 columns]
```

输出参数-景气指数

| 名称     | 类型      | 描述      |
|--------|---------|---------|
| 期次     | object  | -       |
| 总景气指数  | float64 | -       |
| 涨跌幅    | float64 | 注意单位: % |
| 流通景气指数 | float64 | -       |
| 生产景气指数 | float64 | -       |

接口示例-景气指数

```python
import akshare as ak

index_kq_fz_df = ak.index_kq_fz(symbol="景气指数")
print(index_kq_fz_df)
```

数据示例-景气指数

```
    期次    总景气指数    涨跌幅   流通景气指数   生产景气指数
0    2007年06月  1105.95    NaN   862.60  1349.30
1    2007年07月  1090.81  -1.37   855.77  1325.86
2    2007年08月  1254.97  15.05   830.11  1679.83
3    2007年09月  1137.48  -9.36   837.70  1437.26
4    2007年10月  1136.22  -0.11   825.53  1446.91
..        ...      ...    ...      ...      ...
169  2022年12月  1115.95  -3.71  1183.50   908.33
170  2023年02月  1140.71   2.22  1233.00   916.41
171  2023年03月  1235.31   8.29  1389.60   881.62
172  2023年04月  1307.75   5.86  1485.11   902.97
173  2023年05月  1321.09   1.02  1473.07   932.58
[174 rows x 5 columns]
```

输出参数-外贸指数

| 名称       | 类型      | 描述      |
|----------|---------|---------|
| 期次       | object  | -       |
| 价格指数     | float64 | -       |
| 价格指数-涨跌幅 | float64 | 注意单位: % |
| 景气指数     | float64 | -       |
| 景气指数-涨跌幅 | float64 | 注意单位: % |

接口示例-外贸指数

```python
import akshare as ak

index_kq_fz_df = ak.index_kq_fz(symbol="外贸指数")
print(index_kq_fz_df)
```

数据示例-外贸指数

```
     期次    价格指数  价格指数-涨跌幅     景气指数  景气指数-涨跌幅
0    2007年06月  108.57       NaN  1465.48       NaN
1    2007年07月   99.57     -8.29  1376.74     -6.06
2    2007年08月   96.34     -3.24  1453.39      5.57
3    2007年09月   96.42      0.08  1371.33     -5.65
4    2007年10月   93.78     -2.74  1395.79      1.78
..        ...     ...       ...      ...       ...
172  2023年01月  181.50      3.31   693.81    -12.49
173  2023年02月  162.02    -10.73   717.58      3.43
174  2023年03月  173.36      7.00   660.49     -7.96
175  2023年04月  158.99     -8.29   716.59      8.49
176  2023年05月  158.88     -0.07   715.59     -0.14
[177 rows x 5 columns]
```

### 柯桥时尚指数

接口: index_kq_fashion

目标地址: http://ss.kqindex.cn:9559/rinder_web_kqsszs/index/index_page.do

描述: 指定 symbol 的柯桥时尚指数的所有数据

输入参数

| 名称     | 类型  | 描述                                                                                                                                                                                                              |
|--------|-----|-----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| symbol | str | symbol="柯桥时尚指数"; choice of {'柯桥时尚指数', '时尚创意指数', '时尚设计人才数', '新花型推出数', '创意产品成交数', '创意企业数量', '时尚活跃度指数', '电商运行数', '时尚平台拓展数', '新产品销售额占比', '企业合作占比', '品牌传播费用', '时尚推广度指数', '国际交流合作次数', '企业参展次数', '外商驻点数量变化', '时尚评价指数'} |

输出参数-柯桥时尚指数

| 名称  | 类型      | 描述  |
|-----|---------|-----|
| 日期  | object  | -   |
| 指数  | float64 | -   |
| 涨跌值 | float64 | -   |
| 涨跌幅 | float64 | -   |

接口示例-柯桥时尚指数

```python
import akshare as ak

index_kq_fashion_df = ak.index_kq_fashion(symbol='柯桥时尚指数')
print(index_kq_fashion_df)
```

数据示例-柯桥时尚指数

```
       日期          指数        涨跌值       涨跌幅
0   2015-12-01  100.000000        NaN       NaN
1   2016-03-01  107.702370   7.702370  0.077024
2   2016-06-01  116.327075   8.624705  0.080079
3   2016-09-01  124.746246   8.419171  0.072375
4   2016-12-01  131.446198   6.699952  0.053709
5   2017-03-01  115.246828 -16.199370 -0.123240
6   2017-06-01  121.778800   6.531972  0.056678
7   2017-09-01  137.934513  16.155713  0.132664
8   2017-12-01  148.878758  10.944245  0.079344
9   2018-03-01  129.129902 -19.748856 -0.132651
10  2018-06-01  135.685608   6.555706  0.050768
11  2018-09-30  147.089712  11.404103  0.084048
12  2018-12-20  144.066155  -3.023557 -0.020556
13  2019-03-29  134.076287  -9.989867 -0.069342
14  2019-06-28  137.976039   3.899752  0.029086
15  2019-09-25  145.249576   7.273537  0.052716
16  2019-12-27  142.978397  -2.271179 -0.015636
17  2020-03-12  115.442945 -27.535452 -0.192585
18  2020-06-01  127.135395  11.692450  0.101283
19  2020-09-30  138.919644  11.784249  0.092691
20  2020-12-31  135.350774  -3.568870 -0.025690
21  2021-03-31  130.154076  -5.196698 -0.038394
22  2021-06-30  148.746904  18.592828  0.142852
23  2021-09-30  151.610112   2.863208  0.019249
24  2021-12-31  162.610118  11.000006  0.072555
25  2022-03-31  175.577443  12.967325  0.079745
26  2022-06-30  149.228725 -26.348719 -0.150069
27  2022-09-30  132.967252 -16.261473 -0.108970
28  2022-12-30  135.356038   2.388786  0.017965
29  2023-03-31  126.944750  -8.411288 -0.062142
```

接口示例-时尚评价指数

```python
import akshare as ak

index_kq_fashion_df = ak.index_kq_fashion(symbol='时尚评价指数')
print(index_kq_fashion_df)
```

数据示例-时尚评价指数

```
     日期          指数        涨跌值       涨跌幅
0   2016-12-01  154.120000        NaN       NaN
1   2017-03-01  154.120000   0.000000  0.000000
2   2017-06-01  131.830000 -22.290000 -0.144628
3   2017-09-01  129.720000  -2.110000 -0.016005
4   2017-12-01  133.620000   3.900000  0.030065
5   2018-03-01  115.280000 -18.340000 -0.137255
6   2018-06-01  124.520000   9.240000  0.080153
7   2018-09-30  116.717004  -7.802996 -0.062665
8   2018-12-20  124.022727   7.305723  0.062593
9   2019-03-29  124.403503   0.380776  0.003070
10  2019-06-28  123.195409  -1.208094 -0.009711
11  2019-09-25  122.680000  -0.515409 -0.004184
12  2019-12-27  124.985000   2.305000  0.018789
13  2020-03-12  126.970704   1.985704  0.015888
14  2020-06-01  115.856237 -11.114467 -0.087536
15  2020-09-30  128.100000  12.243763  0.105681
16  2020-12-31  126.050000  -2.050000 -0.016003
17  2021-03-31  123.630000  -2.420000 -0.019199
18  2021-06-30  130.000000   6.370000  0.051525
19  2021-09-30  121.579583  -8.420417 -0.064772
20  2021-12-31  129.658713   8.079130  0.066451
21  2022-03-31  128.103292  -1.555421 -0.011996
22  2022-06-30  126.011779  -2.091513 -0.016327
23  2022-09-30  127.401087   1.389308  0.011025
24  2022-12-30  126.630927  -0.770160 -0.006045
25  2023-03-31  127.061311   0.430384  0.003399
```

### 中国食糖指数

接口: index_sugar_msweet

目标地址: http://www.msweet.com.cn/mtkj/sjzx13/index.html

描述: 沐甜科技数据中心-中国食糖指数

输入参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| -   | -   | -   |

输出参数

| 名称   | 类型      | 描述  |
|------|---------|-----|
| 日期   | object  | -   |
| 综合价格 | float64 | -   |
| 原糖价格 | float64 | -   |
| 现货价格 | float64 | -   |

接口示例

```python
import akshare as ak

index_sugar_msweet_df = ak.index_sugar_msweet()
print(index_sugar_msweet_df)
```

数据示例

```
        日期    综合价格   原糖价格    现货价格
0     2005-10-03  3927.0  15.51     NaN
1     2005-10-04  3932.0  11.41     NaN
2     2005-10-10  3950.0  11.62  3550.0
3     2005-10-11  3986.0  11.79  3640.0
4     2005-10-12  4020.0  11.67  3641.0
          ...     ...    ...     ...
4234  2023-06-08  7247.0  25.22  6966.0
4235  2023-06-09  7262.0  24.94  7021.0
4236  2023-06-12  7281.0  25.00  7051.0
4237  2023-06-13  7300.0  24.70  7058.0
4238  2023-06-14  7311.0  25.25  7036.0
[4239 rows x 4 columns]
```

### 配额内进口糖估算指数

接口: index_inner_quote_sugar_msweet

目标地址: http://www.msweet.com.cn/mtkj/sjzx13/index.html

描述: 沐甜科技数据中心-配额内进口糖估算指数

输入参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| -   | -   | -   |

输出参数

| 名称     | 类型      | 描述  |
|--------|---------|-----|
| 日期     | object  | -   |
| 利润空间   | float64 | -   |
| 泰国糖    | float64 | -   |
| 泰国MA5  | float64 | -   |
| 巴西MA5  | float64 | -   |
| 利润MA5  | float64 | -   |
| 巴西MA10 | float64 | -   |
| 巴西糖    | float64 | -   |
| 柳州现货价  | float64 | -   |
| 广州现货价  | float64 | -   |
| 泰国MA10 | float64 | -   |
| 利润MA30 | float64 | -   |
| 利润MA10 | float64 | -   |

接口示例

```python
import akshare as ak

index_inner_quote_sugar_msweet_df = ak.index_inner_quote_sugar_msweet()
print(index_inner_quote_sugar_msweet_df)
```

数据示例

```
      日期      利润空间      泰国糖   泰国MA5  ...  广州现货价  泰国MA10      利润MA30  利润MA10
0     2010-11-01 -1240.220  8913.09     NaN  ...   7400     NaN         NaN     NaN
1     2010-11-02 -1247.110  9038.92     NaN  ...   7500     NaN         NaN     NaN
2     2010-11-03 -1226.750  9043.47     NaN  ...   7500     NaN         NaN     NaN
3     2010-11-04 -1544.135  9359.97     NaN  ...   7500     NaN         NaN     NaN
4     2010-11-05 -1560.100  9375.66     NaN  ...   7500     NaN         NaN     NaN
          ...       ...      ...     ...  ...    ...     ...         ...     ...
2881  2023-01-17   611.500  5140.00  5139.2  ...   5785  5207.4  332.383333  581.95
2882  2023-01-18   690.500  5071.00  5142.2  ...   5785  5179.9  351.883333  603.45
2883  2023-01-19   718.500  5036.00  5114.4  ...   5785  5153.9  374.000000  624.70
2884  2023-01-30   526.500  5336.00  5079.8  ...   5875  5129.7  395.450000  642.20
2885  2023-01-31   429.500  5456.00  5123.8  ...   5910  5140.7  410.283333  636.70
[2886 rows x 13 columns]
```

### 配额外进口糖估算指数

接口: index_outer_quote_sugar_msweet

目标地址: http://www.msweet.com.cn/mtkj/sjzx13/index.html

描述: 沐甜科技数据中心-配额外进口糖估算指数

输入参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| -   | -   | -   |

输出参数

| 名称        | 类型      | 描述  |
|-----------|---------|-----|
| 日期        | object  | -   |
| 巴西糖进口成本   | float64 | -   |
| 泰国糖进口利润空间 | float64 | -   |
| 巴西糖进口利润空间 | float64 | -   |
| 泰国糖进口成本   | float64 | -   |
| 日照现货价     | float64 | -   |

接口示例

```python
import akshare as ak

index_outer_quote_sugar_msweet_df = ak.index_outer_quote_sugar_msweet()
print(index_outer_quote_sugar_msweet_df)
```

数据示例

```
      日期  巴西糖进口成本  泰国糖进口利润空间  巴西糖进口利润空间  泰国糖进口成本  日照现货价
0     2014-12-05   4637.0     4491.0       4770    133.0  279.0
1     2014-12-08   4697.0     4549.0       4770     73.0  221.0
2     2014-12-09   4774.0     4590.0       4770     -4.0  180.0
3     2014-12-10   4782.0     4607.0       4820     38.0  213.0
4     2014-12-11   4758.0     4772.0       4870    112.0   98.0
          ...      ...        ...        ...      ...    ...
1775  2023-01-13   6380.0     6416.0       6260   -156.0 -120.0
1776  2023-01-17   6529.0     6552.0       6260   -292.0 -269.0
1777  2023-01-18   6439.0     6462.0       6260   -202.0 -179.0
1778  2023-01-30   6782.0     6808.0       6260   -548.0 -522.0
1779  2023-01-31   6938.0     6964.0       6260   -704.0 -678.0
[1780 rows x 6 columns]
```

### 排污权指数

接口: index_eri

目标地址: https://zs.zjpwq.net/

描述: 浙江省排污权交易指数的数据

输入参数

| 名称     | 类型  | 描述                                  |
|--------|-----|-------------------------------------|
| symbol | str | symbol="月度"; choice of {"月度", "季度"} |

输出参数

| 名称   | 类型      | 描述      |
|------|---------|---------|
| 日期   | object  |         |
| 交易指数 | float64 |         |
| 成交量  | float64 | 注意单位: 吨 |
| 成交额  | float64 | 注意单位: 元 |

接口示例

```python
import akshare as ak

index_eri_df = ak.index_eri(symbol="月度")
print(index_eri_df)
```

数据示例

```
       日期        交易指数     成交量         成交额
0    2015-01-01  666.723178  1034.4964  1.947994e+07
1    2015-02-01  331.931459  1432.3328  4.688527e+07
2    2015-03-01  280.808046   368.7993  1.286788e+07
3    2015-04-01  306.577138   847.2793  2.034412e+07
4    2015-05-01  175.653651   914.9349  1.267447e+07
..          ...         ...        ...           ...
97   2023-02-01  496.845377   302.1130  1.710103e+07
98   2023-03-01  422.643848   706.8660  3.102841e+07
99   2023-04-01  209.456300  4374.7260  5.408741e+07
100  2023-05-01  330.866173   622.0553  1.696480e+07
101  2023-06-01  351.518957   836.3700  1.942974e+07
[102 rows x 4 columns]
```

### 集装箱指数

接口: drewry_wci_index

目标地址: https://infogram.com/world-container-index-1h17493095xl4zj

描述: Drewry 集装箱指数的数据

限量: 返回指定 symbol 的数据

输入参数

| 名称     | 类型  | 描述                                                                                                                                                                                                    |
|--------|-----|-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| symbol | str | symbol="composite"; choice of {"composite", "shanghai-rotterdam", "rotterdam-shanghai", "shanghai-los angeles", "los angeles-shanghai", "shanghai-genoa", "new york-rotterdam", "rotterdam-new york"} |

输出参数

| 名称   | 类型      | 描述  |
|------|---------|-----|
| date | object  |     |
| wci  | float64 |     |

接口示例

```python
import akshare as ak

drewry_wci_index_df = ak.drewry_wci_index(symbol="composite")
print(drewry_wci_index_df)
```

数据示例

```
           date      wci
0    2016-03-10   700.57
1    2016-03-17   674.41
2    2016-03-24   666.27
3    2016-03-31   849.08
4    2016-04-07   868.06
..          ...      ...
437  2024-08-22  5319.00
438  2024-08-29  5181.00
439  2024-09-05  4775.00
440  2024-09-12  4168.00
441  2024-09-19  3970.00
[442 rows x 2 columns]
```

### 公路物流指数

#### 中国公路物流运价指数

接口: index_price_cflp

目标地址: http://index.0256.cn/expx.htm

描述: 获取指定 symbol 的中国公路物流运价指数的数据

输入参数

| 名称     | 类型  | 描述                                                     |
|--------|-----|--------------------------------------------------------|
| symbol | str | symbol="周指数"; choice of {"周指数", "月指数", "季度指数", "年度指数"} |

输出参数

| 名称   | 类型      | 描述  |
|------|---------|-----|
| 日期   | object  | -   |
| 定基指数 | float64 | -   |
| 环比指数 | float64 | -   |
| 同比指数 | float64 | -   |

接口示例

```python
import akshare as ak

index_price_cflp_df = ak.index_price_cflp(symbol="周指数")
print(index_price_cflp_df)
```

数据示例

```
       日期     定基指数    环比指数   同比指数
0    2015-02-11  1119.47  1000.00  1000.00
1    2015-12-02  1051.89   988.25   960.54
2    2015-12-09  1028.40   977.67   981.28
3    2015-12-16   991.78   964.39   935.04
4    2015-12-23   995.84  1004.10   956.45
..          ...      ...      ...      ...
432  2024-04-24  1040.15  1015.40  1001.07
433  2024-05-01  1024.09   984.55   998.50
434  2024-05-08  1024.10  1000.01   997.71
435  2024-05-15  1024.11  1000.01   998.13
436  2024-05-22  1051.10  1026.36  1024.44
[437 rows x 4 columns]
```

#### 中国公路物流运量指数

接口: index_volume_cflp

目标地址: http://index.0256.cn/expx.htm

描述: 指定 symbol 的中国公路物流运量指数的数据

输入参数

| 名称     | 类型  | 描述                                              |
|--------|-----|-------------------------------------------------|
| symbol | str | symbol="周指数"; choice of {"月指数", "季度指数", "年度指数"} |

输出参数

| 名称   | 类型      | 描述  |
|------|---------|-----|
| 日期   | object  | -   |
| 定基指数 | float64 | -   |
| 环比指数 | float64 | -   |
| 同比指数 | float64 | -   |

接口示例

```python
import akshare as ak

index_volume_cflp_df = ak.index_volume_cflp(symbol="月指数")
print(index_volume_cflp_df)
```

数据示例

```
         日期    定基指数  环比指数  同比指数
0   2016-01-31   94.61  100.00  100.00
1   2016-02-29   52.46   55.45  100.00
2   2016-03-31   93.90  178.99  100.00
3   2016-04-30  102.13  108.76  100.00
4   2016-05-31  105.71  103.51  100.00
5   2016-06-30  103.30   97.72  100.00
6   2016-07-31  102.52   99.24  100.00
7   2016-08-31  106.86  104.23  100.00
8   2016-09-30  111.76  104.59  100.00
9   2016-10-31  108.68   97.24  100.00
10  2016-11-30  116.28  106.99  100.00
11  2016-12-31   99.89   85.90  100.00
12  2017-01-31   96.58   96.69  102.08
13  2017-02-28   56.95   58.97  108.56
14  2017-03-31  102.52  180.02  109.18
15  2017-04-30  110.03  107.33  107.74
16  2017-05-31  114.33  103.91  108.15
17  2017-06-30  110.40   96.56  106.87
18  2017-08-31  120.08  108.77  112.37
19  2017-09-30  125.06  104.15  111.90
20  2017-10-31  119.66   95.68  110.10
21  2017-11-30  125.06  104.51  107.55
22  2017-12-31  119.66   95.68  119.79
23  2018-01-31  125.06  104.51  129.49
24  2018-02-28  125.06  100.00  219.60
25  2018-03-31  125.06  100.00  121.99
26  2018-04-30  120.08   96.02  109.13
27  2018-05-31  115.96   96.57  101.43
28  2018-06-30  125.06  107.85  113.28
29  2018-07-04  125.83  115.84  120.89
30  2018-08-30  130.12  120.15  129.61
31  2018-09-30  135.20  120.64  126.81
32  2018-10-30  131.18  123.25  121.94
33  2018-11-30  126.35  120.28  121.55
34  2018-12-30  133.17  119.12  125.31
35  2019-01-30  126.81  125.38  125.67
36  2019-02-27  130.84  116.89  121.12
37  2019-03-30  132.42  121.11  127.49
38  2019-04-30  127.29  116.83  121.46
39  2019-05-30  128.32  117.60  122.26
40  2019-06-30  130.92  123.20  122.43
41  2019-07-31   94.61  100.00  100.00
42  2019-08-31   52.46   55.45  100.00
43  2019-09-30   93.90  178.99  100.00
44  2019-10-31  102.13  108.76  100.00
45  2019-11-30  105.71  103.51  100.00
46  2019-12-31  103.30   97.72  100.00
47  2020-01-31  102.52   99.24  100.00
48  2020-02-29  106.86  104.23  100.00
49  2020-03-31  111.76  104.59  100.00
50  2020-04-30  108.68   97.24  100.00
51  2020-05-31  116.28  106.99  100.00
52  2020-06-30   99.89   85.90  100.00
53  2020-07-31   96.58   96.69  102.08
54  2020-08-31   56.95   58.97  108.56
```

### 中证指数

接口: stock_zh_index_hist_csindex

目标地址: https://www.csindex.com.cn/zh-CN/indices/index-detail/H30374#/indices/family/detail?indexCode=H30374

描述: 中证指数日频率的数据

限量: 该接口返回指定 symbol 的 start_date 和 end_date 的指数日频率数据

输入参数

| 名称         | 类型  | 描述                    |
|------------|-----|-----------------------|
| symbol     | str | symbol="000928"; 指数代码 |
| start_date | str | start_date="20180526" |
| end_date   | str | end_date="20240604"   |

输出参数

| 名称     | 类型      | 描述       |
|--------|---------|----------|
| 日期     | object  | -        |
| 指数代码   | object  | -        |
| 指数中文全称 | object  | -        |
| 指数中文简称 | object  | -        |
| 指数英文全称 | object  | -        |
| 指数英文简称 | object  | -        |
| 开盘     | float64 | -        |
| 最高     | float64 | -        |
| 最低     | float64 | -        |
| 收盘     | float64 | -        |
| 涨跌     | float64 | -        |
| 涨跌幅    | float64 | 注意单位: %  |
| 成交量    | float64 | 注意单位: 万手 |
| 成交金额   | float64 | 注意单位: 亿元 |
| 样本数量   | float64 | -        |
| 滚动市盈率  | float64 | -        |

接口示例

```python
import akshare as ak

stock_zh_index_hist_csindex_df = ak.stock_zh_index_hist_csindex(symbol="000928", start_date="20100101", end_date="20240604")
print(stock_zh_index_hist_csindex_df)
```

数据示例

```
          日期    指数代码  指数中文全称 指数中文简称  ... 成交量   成交金额 样本数量 滚动市盈率
0     2010-01-01  000928  中证能源指数  800能源  ...   189.61   19.89  38.0  11.51
1     2012-08-31  000928  中证能源指数  800能源  ...   189.61   19.89  38.0  11.51
2     2012-09-03  000928  中证能源指数  800能源  ...   256.16   27.91  38.0    NaN
3     2012-09-04  000928  中证能源指数  800能源  ...   259.30   28.01  38.0  11.51
4     2012-09-05  000928  中证能源指数  800能源  ...   230.82   26.39  38.0  11.43
...          ...     ...     ...    ...  ...      ...     ...   ...    ...
2851  2024-05-29  000928  中证能源指数  800能源  ...  1062.61  114.86  24.0  12.70
2852  2024-05-30  000928  中证能源指数  800能源  ...  1209.83  112.27  24.0  12.45
2853  2024-05-31  000928  中证能源指数  800能源  ...   954.20   94.05  24.0  12.37
2854  2024-06-03  000928  中证能源指数  800能源  ...  1294.04  115.08  24.0  12.41
2855  2024-06-04  000928  中证能源指数  800能源  ...  1169.28  127.39  24.0  12.20
[2856 rows x 16 columns]
```

### 财新指数

#### 综合 PMI

接口: index_pmi_com_cx

目标地址: https://yun.ccxe.com.cn/indices/pmi

描述: 财新数据-指数报告-财新中国 PMI-综合 PMI

限量: 该接口返回所有历史数据

输入参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| -   | -   | -   |

输出参数

| 名称    | 类型      | 描述  |
|-------|---------|-----|
| 日期    | object  | -   |
| 综合PMI | float64 | -   |
| 变化值   | float64 | -   |

接口示例

```python
import akshare as ak

index_pmi_com_cx_df = ak.index_pmi_com_cx()
print(index_pmi_com_cx_df)
```

数据示例

```
       日期     综合PMI 变化值
0   2014-04-29   49.5  0.0
1   2014-05-30   50.2  0.7
2   2014-06-29   52.4  2.2
3   2014-07-30   51.6 -0.8
4   2014-08-30   52.8  1.2
..         ...    ...  ...
90  2021-09-29   51.4  4.2
91  2021-10-30   51.5  0.1
92  2021-11-29   51.2 -0.3
93  2021-12-30   53.0  1.8
94  2022-01-30   50.1 -2.9
```

#### 制造业 PMI

接口: index_pmi_man_cx

目标地址: https://yun.ccxe.com.cn/indices/pmi

描述: 财新数据-指数报告-财新中国 PMI-制造业 PMI

限量: 该接口返回所有历史数据

输入参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| -   | -   | -   |

输出参数

| 名称     | 类型      | 描述  |
|--------|---------|-----|
| 日期     | object  | -   |
| 制造业PMI | float64 | -   |
| 变化值    | float64 | -   |

接口示例

```python
import akshare as ak

index_pmi_man_cx_df = ak.index_pmi_man_cx()
print(index_pmi_man_cx_df)
```

数据示例

```
       日期   制造业PMI  变化值
0   2014-04-29    48.1  0.0
1   2014-05-30    49.4  1.3
2   2014-06-29    50.7  1.3
3   2014-07-30    51.7  1.0
4   2014-08-30    50.2 -1.5
..         ...     ...  ...
90  2021-09-29    50.0  0.8
91  2021-10-30    50.6  0.6
92  2021-11-29    49.9 -0.7
93  2021-12-30    50.9  1.0
94  2022-01-30    49.1 -1.8
```

#### 服务业 PMI

接口: index_pmi_ser_cx

目标地址: https://yun.ccxe.com.cn/indices/pmi

描述: 财新数据-指数报告-财新中国 PMI-服务业 PMI

限量: 该接口返回所有历史数据

输入参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| -   | -   | -   |

输出参数

| 名称     | 类型      | 描述  |
|--------|---------|-----|
| 日期     | object  | -   |
| 服务业PMI | float64 | -   |
| 变化值    | float64 | -   |

接口示例

```python
import akshare as ak

index_pmi_ser_cx_df = ak.index_pmi_ser_cx()
print(index_pmi_ser_cx_df)
```

数据示例

```
       日期  服务业PMI  变化值
0   2014-04-29    51.4  0.0
1   2014-05-30    50.7 -0.7
2   2014-06-29    53.1  2.4
3   2014-07-30    50.0 -3.1
4   2014-08-30    54.1  4.1
..         ...     ...  ...
90  2021-09-29    53.4  6.7
91  2021-10-30    53.8  0.4
92  2021-11-29    52.1 -1.7
93  2021-12-30    53.1  1.0
94  2022-01-30    51.4 -1.7
```

#### 数字经济指数

接口: index_dei_cx

目标地址: https://yun.ccxe.com.cn/indices/dei

描述: 财新指数-数字经济指数

限量: 该接口返回所有历史数据

输入参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| -   | -   | -   |

输出参数

| 名称     | 类型      | 描述  |
|--------|---------|-----|
| 日期     | object  | -   |
| 数字经济指数 | float64 | -   |
| 变化值    | float64 | -   |

接口示例

```python
import akshare as ak

index_dei_cx_df = ak.index_dei_cx()
print(index_dei_cx_df)
```

数据示例

```
     日期      数字经济指数 变化值
0   2015-12-31  100.00    0.00
1   2016-01-31   89.63  -10.37
2   2016-02-29  107.70   18.07
3   2016-03-31  116.13    8.43
4   2016-04-30  126.67   10.54
..         ...     ...     ...
67  2021-07-31  585.00    6.00
68  2021-08-31  439.00 -146.00
69  2021-09-30  413.00  -26.00
70  2021-10-31  593.00  180.00
71  2021-11-30  435.00 -158.00
```

#### 产业指数

接口: index_ii_cx

目标地址: https://yun.ccxe.com.cn/indices/dei

描述: 财新指数-产业指数

限量: 该接口返回所有历史数据

输入参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| -   | -   | -   |

输出参数

| 名称   | 类型      | 描述  |
|------|---------|-----|
| 日期   | object  | -   |
| 产业指数 | float64 | -   |
| 变化值  | float64 | -   |

接口示例

```python
import akshare as ak

index_ii_cx_df = ak.index_ii_cx()
print(index_ii_cx_df)
```

数据示例

```
        日期    产业指数    变化值
0   2015-12-31  100.00   0.00
1   2016-01-31   83.97 -16.03
2   2016-02-29  139.06  55.09
3   2016-03-31  126.96 -12.10
4   2016-04-30  138.11  11.15
..         ...     ...    ...
67  2021-07-31  434.87  -9.52
68  2021-08-31  353.89 -80.98
69  2021-09-30  260.27 -93.62
70  2021-10-31  261.97   1.70
71  2021-11-30  310.74  48.77
```

#### 溢出指数

接口: index_si_cx

目标地址: https://yun.ccxe.com.cn/indices/dei

描述: 财新指数-溢出指数

限量: 该接口返回所有历史数据

输入参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| -   | -   | -   |

输出参数

| 名称   | 类型      | 描述  |
|------|---------|-----|
| 日期   | object  | -   |
| 溢出指数 | float64 | -   |
| 变化值  | float64 | -   |

接口示例

```python
import akshare as ak

index_si_cx_df = ak.index_si_cx()
print(index_si_cx_df)
```

数据示例

```
        日期    溢出指数  变化值
0   2015-12-31  100.00   0.00
1   2016-01-31  137.07  37.07
2   2016-02-29  127.53  -9.54
3   2016-03-31  168.82  41.29
4   2016-04-30  157.51 -11.31
..         ...     ...    ...
67  2021-07-31   70.23   9.28
68  2021-08-31   25.26 -44.97
69  2021-09-30   29.86   4.60
70  2021-10-31   94.92  65.06
71  2021-11-30   48.24 -46.68
```

#### 融合指数

接口: index_fi_cx

目标地址: https://yun.ccxe.com.cn/indices/dei

描述: 财新指数-融合指数

限量: 该接口返回所有历史数据

输入参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| -   | -   | -   |

输出参数

| 名称   | 类型      | 描述  |
|------|---------|-----|
| 日期   | object  | -   |
| 融合指数 | float64 | -   |
| 变化值  | float64 | -   |

接口示例

```python
import akshare as ak

index_fi_cx_df = ak.index_fi_cx()
print(index_fi_cx_df)
```

数据示例

```
      日期      融合指数    变化值
0   2015-12-31  100.00   0.00
1   2016-01-31   83.26 -16.74
2   2016-02-29   86.49   3.23
3   2016-03-31   80.90  -5.59
4   2016-04-30   84.22   3.32
..         ...     ...    ...
67  2021-07-31  254.47  -6.94
68  2021-08-31  244.25 -10.22
69  2021-09-30  246.41   2.16
70  2021-10-31  222.03 -24.38
71  2021-11-30  253.82  31.79
```

#### 基础指数

接口: index_bi_cx

目标地址: https://yun.ccxe.com.cn/indices/dei

描述: 财新指数-基础指数

限量: 该接口返回所有历史数据

输入参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| -   | -   | -   |

输出参数

| 名称   | 类型      | 描述  |
|------|---------|-----|
| 日期   | object  | -   |
| 基础指数 | float64 | -   |
| 变化值  | float64 | -   |

接口示例

```python
import akshare as ak

index_bi_cx_df = ak.index_bi_cx()
print(index_bi_cx_df)
```

数据示例

```
        日期    基础指数    变化值
0   2015-12-31  100.00   0.00
1   2016-01-31   99.15  -0.85
2   2016-02-29  122.47  23.32
3   2016-03-31  106.72 -15.75
4   2016-04-30  117.46  10.74
..         ...     ...    ...
67  2021-07-31   82.76  -5.40
68  2021-08-31   77.20  -5.56
69  2021-09-30   81.56   4.36
70  2021-10-31   83.50   1.94
71  2021-11-30   48.14 -35.36
```

#### 中国新经济指数

接口: index_nei_cx

目标地址: https://yun.ccxe.com.cn/indices/nei

描述: 财新指数-中国新经济指数

限量: 该接口返回所有历史数据

输入参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| -   | -   | -   |

输出参数

| 名称      | 类型      | 描述  |
|---------|---------|-----|
| 日期      | object  | -   |
| 中国新经济指数 | float64 | -   |
| 变化值     | float64 | -   |

接口示例

```python
import akshare as ak

index_nei_cx_df = ak.index_nei_cx()
print(index_nei_cx_df)
```

数据示例

```
     日期    中国新经济指数       变化值
0   2016-04-30  32.030085  0.000000
1   2016-05-31  34.687431  2.657346
2   2016-06-30  32.163383 -2.524048
3   2016-07-31  31.420723 -0.742660
4   2016-08-31  32.140547  0.719824
..         ...        ...       ...
64  2021-08-31  28.225149 -0.311624
65  2021-09-30  28.885370  0.660221
66  2021-10-31  29.643365  0.757995
67  2021-11-30  28.056617 -1.586748
68  2021-12-31  29.464567  1.407950
```

#### 劳动力投入指数

接口: index_li_cx

目标地址: https://yun.ccxe.com.cn/indices/nei

描述: 财新指数-劳动力投入指数

限量: 该接口返回所有历史数据

输入参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| -   | -   | -   |

输出参数

| 名称      | 类型      | 描述  |
|---------|---------|-----|
| 日期      | object  | -   |
| 劳动力投入指数 | float64 | -   |
| 变化值     | float64 | -   |

接口示例

```python
import akshare as ak

index_li_cx_df = ak.index_li_cx()
print(index_li_cx_df)
```

数据示例

```
     日期       劳动力投入指数    变化值
0   2015-07-31  29.741385   0.000000
1   2015-08-31  28.091280  -1.650105
2   2015-09-30  42.663765  14.572485
3   2015-10-31  32.824430  -9.839335
4   2015-11-30  38.791985   5.967555
..         ...        ...        ...
73  2021-08-31  20.802475  -3.385175
74  2021-09-30  22.275790   1.473315
75  2021-10-31  25.326500   3.050710
76  2021-11-30  23.458940  -1.867560
77  2021-12-31  22.587685  -0.871255
```

#### 资本投入指数

接口: index_ci_cx

目标地址: https://yun.ccxe.com.cn/indices/nei

描述: 财新指数-资本投入指数

限量: 该接口返回所有历史数据

输入参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| -   | -   | -   |

输出参数

| 名称     | 类型      | 描述  |
|--------|---------|-----|
| 日期     | object  | -   |
| 资本投入指数 | float64 | -   |
| 变化值    | float64 | -   |

接口示例

```python
import akshare as ak

index_ci_cx_df = ak.index_ci_cx()
print(index_ci_cx_df)
```

数据示例

```
    日期        资本投入指数      变化值
0   2015-07-31  31.994724   0.000000
1   2015-08-31  34.098210   2.103486
2   2015-09-30  22.844344 -11.253866
3   2015-10-31  35.136466  12.292122
4   2015-11-30  28.143112  -6.993354
..         ...        ...        ...
73  2021-08-31  37.308966   3.053250
74  2021-09-30  37.306790  -0.002176
75  2021-10-31  35.004714  -2.302076
76  2021-11-30  34.531942  -0.472772
77  2021-12-31  39.438158   4.906216
```

#### 科技投入指数

接口: index_ti_cx

目标地址: https://yun.ccxe.com.cn/indices/nei

描述: 财新指数-科技投入指数

限量: 该接口返回所有历史数据

输入参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| -   | -   | -   |

输出参数

| 名称     | 类型      | 描述  |
|--------|---------|-----|
| 日期     | object  | -   |
| 科技投入指数 | float64 | -   |
| 变化值    | float64 | -   |

接口示例

```python
import akshare as ak

index_ti_cx_df = ak.index_ti_cx()
print(index_ti_cx_df)
```

数据示例

```
        日期     科技投入指数   变化值
0   2015-07-31  27.562400  0.000000
1   2015-08-31  23.012985 -4.549415
2   2015-09-30  25.229635  2.216650
3   2015-10-31  25.949450  0.719815
4   2015-11-30  24.258245 -1.691205
..         ...        ...       ...
73  2021-08-31  27.384085 -0.104765
74  2021-09-30  27.670710  0.286625
75  2021-10-31  29.044460  1.373750
76  2021-11-30  26.347445 -2.697015
77  2021-12-31  26.504550  0.157105
```

#### 新经济行业入职平均工资水平

接口: index_neaw_cx

目标地址: https://yun.ccxe.com.cn/indices/nei

描述: 财新指数-新经济行业入职平均工资水平

限量: 该接口返回所有历史数据

输入参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| -   | -   | -   |

输出参数

| 名称            | 类型      | 描述  |
|---------------|---------|-----|
| 日期            | object  | -   |
| 新经济行业入职平均工资水平 | float64 | -   |
| 变化值           | float64 | -   |

接口示例

```python
import akshare as ak

index_neaw_cx_df = ak.index_neaw_cx()
print(index_neaw_cx_df)
```

数据示例

```
    日期    新经济行业入职平均工资水平  变化值
0   2015-07-31      7409.0000    0.0000
1   2015-08-31      7307.0000 -102.0000
2   2015-09-30      7367.0000   60.0000
3   2015-10-31      7298.0000  -69.0000
4   2015-11-30      7494.0000  196.0000
..         ...            ...       ...
73  2021-08-31     12905.0505  -27.7426
74  2021-09-30     13100.2551  195.2046
75  2021-10-31     13112.4344   12.1793
76  2021-11-30     13124.6137   12.1793
77  2021-12-31     13241.5176  116.9039
```

#### 新经济入职工资溢价水平

接口: index_awpr_cx

目标地址: https://yun.ccxe.com.cn/indices/nei

描述: 财新指数-新经济入职工资溢价水平

限量: 该接口返回所有历史数据

输入参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| -   | -   | -   |

输出参数

| 名称          | 类型      | 描述  |
|-------------|---------|-----|
| 日期          | object  | -   |
| 新经济入职工资溢价水平 | float64 | -   |
| 变化值         | float64 | -   |

接口示例

```python
import akshare as ak

index_awpr_cx_df = ak.index_awpr_cx()
print(index_awpr_cx_df)
```

数据示例

```
      日期  新经济入职工资溢价水平  变化值
0   2015-07-31   106.858500  0.000000
1   2015-08-31   109.196100  2.337600
2   2015-09-30   108.008300 -1.187800
3   2015-10-31   106.994600 -1.013700
4   2015-11-30   104.243100 -2.751500
..         ...          ...       ...
73  2021-08-31   102.576555 -0.202858
74  2021-09-30   102.680842  0.104288
75  2021-10-31   103.914771  1.233928
76  2021-11-30   102.056202 -1.858569
77  2021-12-31   104.185324  2.129122
```

#### 大宗商品指数

接口: index_cci_cx

目标地址: https://yun.ccxe.com.cn/indices/nei

描述: 财新指数-大宗商品指数

限量: 该接口返回所有历史数据

输入参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| -   | -   | -   |

输出参数

| 名称     | 类型      | 描述  |
|--------|---------|-----|
| 日期     | object  | -   |
| 大宗商品指数 | float64 | -   |
| 变化值    | float64 | -   |

接口示例

```python
import akshare as ak

index_cci_cx_df = ak.index_cci_cx()
print(index_cci_cx_df)
```

数据示例

```
          日期      大宗商品指数    变化值
0     2009-01-09  100.000000  0.000000
1     2009-01-12  101.643734  1.643734
2     2009-01-13   98.992944 -2.607922
3     2009-01-14  100.396208  1.417539
4     2009-01-15  100.151055 -0.244186
...          ...         ...       ...
3735  2024-05-29  361.046067  0.890980
3736  2024-05-30  356.980276 -1.126114
3737  2024-05-31  355.491012 -0.417184
3738  2024-06-03  350.137547 -1.505935
3739  2024-06-04  347.889373 -0.642083
[3740 rows x 3 columns]
```

#### 高质量因子

接口: index_qli_cx

目标地址: https://yun.ccxe.com.cn/indices/qli

描述: 财新指数-高质量因子

限量: 该接口返回所有历史数据

输入参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| -   | -   | -   |

输出参数

| 名称      | 类型      | 描述      |
|---------|---------|---------|
| 日期      | object  | -       |
| 高质量因子指数 | float64 | -       |
| 变化幅度    | float64 | 注意单位: % |

接口示例

```python
import akshare as ak

index_qli_cx_df = ak.index_qli_cx()
print(index_qli_cx_df)
```

数据示例

```
        日期     高质量因子指数   变化幅度
0     2018-05-02  100.015000  0.000000
1     2018-05-03  100.336541  0.321493
2     2018-05-04  101.048384  0.709455
3     2018-05-07  101.663083  0.608322
4     2018-05-08  103.734574  2.037603
...          ...         ...       ...
1475  2024-05-29  142.498375  0.345091
1476  2024-05-30  141.457371 -0.730537
1477  2024-05-31  140.915933 -0.382757
1478  2024-06-03  141.537175  0.440860
1479  2024-06-04  143.046574  1.066433
[1480 rows x 3 columns]
```

#### AI策略指数

接口: index_ai_cx

目标地址: https://yun.ccxe.com.cn/indices/ai

描述: 财新指数-AI策略指数

限量: 该接口返回所有历史数据

输入参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| -   | -   | -   |

输出参数

| 名称     | 类型      | 描述      |
|--------|---------|---------|
| 日期     | object  | -       |
| AI策略指数 | float64 | -       |
| 变化幅度   | float64 | 注意单位: % |

接口示例

```python
import akshare as ak

index_ai_cx_df = ak.index_ai_cx()
print(index_ai_cx_df)
```

数据示例

```
      日期      AI策略指数      变化幅度
0    2020-12-31  100.002000  0.000000
1    2021-01-04  102.925670  2.923611
2    2021-01-05  105.666833  2.663245
3    2021-01-06  106.429747  0.722000
4    2021-01-07  108.591207  2.030880
..          ...         ...       ...
823  2024-05-29  135.800485  0.101018
824  2024-05-30  134.659844 -0.839939
825  2024-05-31  134.193064 -0.346637
826  2024-06-03  133.985240 -0.154869
827  2024-06-04  135.034790  0.783333
[828 rows x 3 columns]
```

#### 基石经济指数

接口: index_bei_cx

目标地址: https://yun.ccxe.com.cn/indices/bei

描述: 财新指数-基石经济指数

限量: 该接口返回所有历史数据

输入参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| -   | -   | -   |

输出参数

| 名称     | 类型      | 描述      |
|--------|---------|---------|
| 日期     | object  | -       |
| 基石经济指数 | float64 | -       |
| 变化幅度   | float64 | 注意单位: % |

接口示例

```python
import akshare as ak

index_bei_cx_df = ak.index_bei_cx()
print(index_bei_cx_df)
```

数据示例

```
         日期      基石经济指数    变化幅度
0     2016-12-26   8337.3924  0.000000
1     2016-12-27   8337.7003  0.003693
2     2016-12-28   8298.5135 -0.469995
3     2016-12-29   8287.3093 -0.135015
4     2016-12-30   8314.6532  0.329949
...          ...         ...       ...
1802  2024-05-29  10352.7516 -0.105441
1803  2024-05-30  10279.0409 -0.711991
1804  2024-05-31  10275.1080 -0.038261
1805  2024-06-03  10217.6457 -0.559238
1806  2024-06-04  10283.8398  0.647841
[1807 rows x 3 columns]
```

#### 新动能指数

接口: index_neei_cx

目标地址: https://yun.ccxe.com.cn/indices/neei

描述: 财新指数-新动能指数

限量: 该接口返回所有历史数据

输入参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| -   | -   | -   |

输出参数

| 名称    | 类型      | 描述      |
|-------|---------|---------|
| 日期    | object  | -       |
| 新动能指数 | float64 | -       |
| 变化幅度  | float64 | 注意单位: % |

接口示例

```python
import akshare as ak

index_neei_cx_df = ak.index_neei_cx()
print(index_neei_cx_df)
```

数据示例

```
     日期      新动能指数      变化幅度
0   2024-05-06  1643.4289  0.000000
1   2024-05-07  1647.7533  0.263133
2   2024-05-08  1625.7008 -1.338337
3   2024-05-09  1651.9138  1.612412
4   2024-05-10  1631.9608 -1.207872
5   2024-05-13  1617.2608 -0.900757
6   2024-05-14  1622.1573  0.302765
7   2024-05-15  1602.1189 -1.235293
8   2024-05-16  1596.2899 -0.363831
9   2024-05-17  1610.1245  0.866672
10  2024-05-20  1612.0331  0.118537
11  2024-05-21  1597.0166 -0.931526
12  2024-05-22  1604.2656  0.453909
13  2024-05-23  1577.2796 -1.682140
14  2024-05-24  1553.5113 -1.506917
15  2024-05-27  1566.6758  0.847403
16  2024-05-28  1553.4361 -0.845082
17  2024-05-29  1554.3290  0.057479
18  2024-05-30  1559.3384  0.322287
19  2024-05-31  1557.3374 -0.128324
20  2024-06-03  1557.0964 -0.015475
21  2024-06-04  1572.3699  0.980896
```

### 指数估值

#### 指数估值-中证

接口: stock_zh_index_value_csindex

目标地址: https://www.csindex.com.cn/zh-CN/indices/index-detail/H30374#/indices/family/detail?indexCode=H30374

描述: 中证指数-指数估值数据

限量: 该接口返回指定的指数的估值数据, 该接口只能返回近期的数据

输入参数

| 名称     | 类型  | 描述                    |
|--------|-----|-----------------------|
| symbol | str | symbol="H30374"; 指数代码 |

输出参数

| 名称     | 类型      | 描述              |
|--------|---------|-----------------|
| 日期     | object  | -               |
| 指数代码   | object  | -               |
| 指数中文全称 | object  | -               |
| 指数中文简称 | object  | -               |
| 指数英文全称 | object  | -               |
| 指数英文简称 | object  | -               |
| 市盈率1   | float64 | 注意: （总股本）P/E1   |
| 市盈率2   | float64 | 注意: （计算用股本）P/E2 |
| 股息率1   | float64 | 注意: （总股本）D/P1   |
| 股息率2   | float64 | 注意: （计算用股本）D/P2 |

接口示例

```python
import akshare as ak

stock_zh_index_value_csindex_df = ak.stock_zh_index_value_csindex(symbol="H30374")
print(stock_zh_index_value_csindex_df)
```

数据示例

```
         日期    指数代码           指数中文全称  指数中文简称  ...   市盈率1  市盈率2 股息率1 股息率2
0   2025-01-02  H30374  中证中国内地企业全球综合指数  中国内地全指  ...  14.34  14.86  2.64  2.41
1   2025-01-01  H30374  中证中国内地企业全球综合指数  中国内地全指  ...  14.72  15.25  2.51  2.28
2   2024-12-31  H30374  中证中国内地企业全球综合指数  中国内地全指  ...  14.72  15.25  2.51  2.28
3   2024-12-30  H30374  中证中国内地企业全球综合指数  中国内地全指  ...  14.93  15.48  2.45  2.23
4   2024-12-27  H30374  中证中国内地企业全球综合指数  中国内地全指  ...  14.94  15.49  2.45  2.22
5   2024-12-26  H30374  中证中国内地企业全球综合指数  中国内地全指  ...  14.93  15.49  2.44  2.22
6   2024-12-25  H30374  中证中国内地企业全球综合指数  中国内地全指  ...  14.89  15.43  2.45  2.23
7   2024-12-24  H30374  中证中国内地企业全球综合指数  中国内地全指  ...  14.92  15.49  2.44  2.22
8   2024-12-23  H30374  中证中国内地企业全球综合指数  中国内地全指  ...  14.75  15.32  2.47  2.24
9   2024-12-20  H30374  中证中国内地企业全球综合指数  中国内地全指  ...  14.83  15.43  2.45  2.22
10  2024-12-19  H30374  中证中国内地企业全球综合指数  中国内地全指  ...  14.82  15.41  2.47  2.26
11  2024-12-18  H30374  中证中国内地企业全球综合指数  中国内地全指  ...  14.83  15.40  2.47  2.26
12  2024-12-17  H30374  中证中国内地企业全球综合指数  中国内地全指  ...  14.75  15.33  2.48  2.27
13  2024-12-16  H30374  中证中国内地企业全球综合指数  中国内地全指  ...  14.86  15.44  2.46  2.25
14  2024-12-13  H30374  中证中国内地企业全球综合指数  中国内地全指  ...  15.07  15.67  2.46  2.24
15  2024-12-12  H30374  中证中国内地企业全球综合指数  中国内地全指  ...  15.37  16.00  2.41  2.19
16  2024-12-11  H30374  中证中国内地企业全球综合指数  中国内地全指  ...  15.21  15.84  2.43  2.22
17  2024-12-10  H30374  中证中国内地企业全球综合指数  中国内地全指  ...  15.18  15.80  2.44  2.22
18  2024-12-09  H30374  中证中国内地企业全球综合指数  中国内地全指  ...  15.16  15.80  2.44  2.22
19  2024-12-06  H30374  中证中国内地企业全球综合指数  中国内地全指  ...  15.06  15.66  2.46  2.24
[20 rows x 10 columns]
```

### 申万宏源研究

#### 基金指数实时行情

接口: index_realtime_fund_sw

目标地址: https://www.swsresearch.com/institute_sw/allIndex/releasedIndex

描述: 申万宏源研究-申万指数-指数发布-基金指数-实时行情

限量: 该接口返回指定 symbol 的数据

输入参数

| 名称     | 类型  | 描述                                                        |
|--------|-----|-----------------------------------------------------------|
| symbol | str | symbol="基础一级"; choice of {"基础一级", "基础二级", "基础三级", "特色指数"} |

输出参数

| 名称   | 类型      | 描述      |
|------|---------|---------|
| 指数代码 | object  | -       |
| 指数名称 | object  | -       |
| 昨收盘  | float64 | -       |
| 日涨跌幅 | float64 | 注意单位: % |
| 年涨跌幅 | float64 | 注意单位: % |

接口示例

```python
import akshare as ak

index_realtime_fund_sw_df = ak.index_realtime_fund_sw(symbol="基础一级")
print(index_realtime_fund_sw_df)
```

数据示例

```
     指数代码          指数名称      昨收盘  日涨跌幅  年涨跌幅
0  807100    申万宏源权益基金指数   770.47 -0.39 -1.40
1  807200    申万宏源债券基金指数  1043.30  0.04  1.24
2  807300    申万宏源混合基金指数   947.76 -0.04  0.70
3  807400    申万宏源货币基金指数  1031.48  0.01  0.50
4  807500    申万宏源另类基金指数  1119.82  0.09  4.45
5  807600    申万宏源组合基金指数   874.51  0.46 -0.98
6  807700  申万宏源QDII基金指数  1063.01  0.03  1.78
```

#### 基金指数历史行情

接口: index_hist_fund_sw

目标地址: https://www.swsresearch.com/institute_sw/allIndex/releasedIndex/fundDetail?code=807100

描述: 申万宏源研究-申万指数-指数发布-基金指数-历史行情

限量: 该接口返回指定 symbol 的数据

输入参数

| 名称     | 类型  | 描述                                               |
|--------|-----|--------------------------------------------------|
| symbol | str | symbol="807200"; 基金指数代码                          |
| period | str | period="day"; choice of {"day", "week", "month"} |

输出参数

| 名称   | 类型      | 描述      |
|------|---------|---------|
| 日期   | object  | -       |
| 收盘指数 | float64 | -       |
| 开盘指数 | float64 | -       |
| 最高指数 | float64 | -       |
| 最低指数 | float64 | -       |
| 涨跌幅  | float64 | 注意单位: % |

接口示例

```python
import akshare as ak

index_hist_fund_sw_df = ak.index_hist_fund_sw(symbol="807200", period="day")
print(index_hist_fund_sw_df)
```

数据示例

```
        日期     收盘指数  开盘指数  最高指数  最低指数   涨跌幅
0     2005-12-30   369.78   0.0   0.0   0.0  0.00
1     2006-01-04   370.80   0.0   0.0   0.0  0.27
2     2006-01-05   371.72   0.0   0.0   0.0  0.25
3     2006-01-06   372.28   0.0   0.0   0.0  0.15
4     2006-01-09   372.64   0.0   0.0   0.0  0.10
          ...      ...   ...   ...   ...   ...
4430  2024-03-27  1041.49   0.0   0.0   0.0  0.01
4431  2024-03-28  1041.83   0.0   0.0   0.0  0.03
4432  2024-03-29  1042.52   0.0   0.0   0.0  0.07
4433  2024-04-01  1042.91   0.0   0.0   0.0  0.04
4434  2024-04-02  1043.30   0.0   0.0   0.0  0.04
[4435 rows x 6 columns]
```

#### 申万指数实时行情

接口: index_realtime_sw

目标地址: https://www.swsresearch.com/institute_sw/allIndex/releasedIndex

描述: 申万宏源研究-指数系列; 注意其中大类风格指数和金创指数的字段

限量: 该接口返回指定 symbol 的数据

输入参数

| 名称     | 类型  | 描述                                                                          |
|--------|-----|-----------------------------------------------------------------------------|
| symbol | str | symbol="市场表征"; choice of {"市场表征", "一级行业", "二级行业", "风格指数", "大类风格指数", "金创指数"} |

输出参数

| 名称   | 类型      | 描述      |
|------|---------|---------|
| 指数代码 | object  | -       |
| 指数名称 | object  | -       |
| 昨收盘  | float64 | -       |
| 今开盘  | float64 | -       |
| 最新价  | float64 | -       |
| 成交额  | float64 | 注意: 百万元 |
| 成交量  | float64 | 注意: 百万股 |
| 最高价  | float64 | -       |
| 最低价  | float64 | -       |

接口示例

```python
import akshare as ak

index_realtime_sw_df = ak.index_realtime_sw(symbol="市场表征")
print(index_realtime_sw_df)
```

数据示例

```
   指数代码    指数名称  昨收盘    今开盘  ...     成交额      成交量    最高价    最低价
0  801001     申万50   2845.16  2844.33  ...   89538.41   4310.15  2853.79  2833.67
1  801002     申万中小  5717.78  5709.18  ...  149570.04  16788.79  5736.68  5697.45
2  801003     申万Ａ指  3409.20  3407.15  ...  819763.29  78217.24  3420.04  3402.91
3  801005     申万创业  2296.71  2295.44  ...  200968.64  13626.32  2319.26  2289.86
4  801250     申万制造  3830.41  3828.93  ...  306683.14  21249.49  3868.12  3815.46
5  801260     申万消费  6539.86  6537.97  ...  127630.86   9024.97  6544.94  6480.16
6  801270     申万投资  3242.39  3240.25  ...  167726.08  21787.65  3262.87  3233.94
7  801280     申万服务  2246.82  2244.55  ...  216829.66  25970.62  2258.63  2240.85
8  801300  申万300指数  2644.59  2642.95  ...  247861.13  15722.26  2652.15  2635.43
[9 rows x 9 columns]
```

#### 申万指数历史行情

接口: index_hist_sw

目标地址: https://www.swsresearch.com//institute_sw/allIndex/releasedIndex/releasedetail?code=801002&name=申万中小

描述: 申万宏源研究-指数发布-指数详情-指数历史数据

限量: 该接口返回指定 symbol 和 period 的数据

输入参数

| 名称     | 类型  | 描述                                               |
|--------|-----|--------------------------------------------------|
| symbol | str | symbol="801030"; 指数代码                            |
| period | str | period="day"; choice of {"day", "week", "month"} |

输出参数

| 名称  | 类型      | 描述  |
|-----|---------|-----|
| 代码  | object  | -   |
| 日期  | object  | -   |
| 收盘  | float64 | -   |
| 开盘  | float64 | -   |
| 最高  | float64 | -   |
| 最低  | float64 | -   |
| 成交量 | float64 | -   |
| 成交额 | float64 | -   |

接口示例

```python
import akshare as ak

index_hist_sw_df = ak.index_hist_sw(symbol="801193", period="day")
print(index_hist_sw_df)
```

数据示例

```
        代码     日期       收盘  ...     最低        成交量          成交额
0     801193  1999-12-30  1000.00  ...  1000.00    0.007658     0.084756
1     801193  2000-01-04  1035.54  ...  1000.64    0.017623     0.199261
2     801193  2000-01-05  1050.67  ...  1023.45    0.033997     0.391641
3     801193  2000-01-06  1096.88  ...  1031.74    0.057862     0.681682
4     801193  2000-01-07  1127.42  ...  1091.98    0.110529     1.353346
...      ...         ...      ...  ...      ...         ...          ...
4081  801193  2024-10-16  6271.85  ...  6103.77   81.423515   849.555269
4082  801193  2024-10-17  6224.13  ...  6166.22   74.126046   793.383788
4083  801193  2024-10-18  6667.02  ...  6152.42  138.155039  1648.638901
4084  801193  2024-10-21  6532.56  ...  6441.85  103.932491  1157.084810
4085  801193  2024-10-22  6591.44  ...  6472.62   78.312127   848.761688
[4086 rows x 8 columns]
```

#### 申万指数分时行情

接口: index_min_sw

目标地址: https://www.swsresearch.com//institute_sw/allIndex/releasedIndex/releasedetail?code=801001&name=申万中小

描述: 申万宏源研究-指数发布-指数详情-指数分时数据

限量: 该接口返回指定 symbol 的数据

输入参数

| 名称     | 类型  | 描述                                               |
|--------|-----|--------------------------------------------------|
| symbol | str | symbol="801030"; 指数代码                            |

输出参数

| 名称  | 类型      | 描述  |
|-----|---------|-----|
| 代码  | object  | -   |
| 名称  | object  | -   |
| 价格  | float64 | -   |
| 日期  | object  | -   |
| 时间  | object  | -   |

接口示例

```python
import akshare as ak

index_min_sw_df = ak.index_min_sw(symbol="801001")
print(index_min_sw_df)
```

数据示例

```
     代码    名称       价格       日期        时间
0    801001  申万50  3076.03  2024-10-23  09:30:00
1    801001  申万50  3074.62  2024-10-23  09:30:10
2    801001  申万50  3072.27  2024-10-23  09:30:20
3    801001  申万50  3070.98  2024-10-23  09:30:30
4    801001  申万50  3068.14  2024-10-23  09:30:40
..      ...   ...      ...         ...       ...
968  801001  申万50  3105.32  2024-10-23  13:41:20
969  801001  申万50  3104.96  2024-10-23  13:41:30
970  801001  申万50  3105.10  2024-10-23  13:41:40
971  801001  申万50  3104.75  2024-10-23  13:41:50
972  801001  申万50  3104.46  2024-10-23  13:42:00
[973 rows x 5 columns]
```

#### 申万指数成分股

接口: index_component_sw

目标地址: https://www.swsresearch.com//institute_sw/allIndex/releasedIndex/releasedetail?code=801001&name=申万中小

描述: 申万宏源研究-指数发布-指数详情-成分股

限量: 该接口返回指定 symbol 的数据

输入参数

| 名称     | 类型  | 描述                                               |
|--------|-----|--------------------------------------------------|
| symbol | str | symbol="801001"; 指数代码                            |

输出参数

| 名称   | 类型      | 描述  |
|------|---------|-----|
| 序号   | int64   | -   |
| 证券代码 | object  | -   |
| 证券名称 | object  | -   |
| 最新权重 | float64 | -   |
| 计入日期 | object  | -   |

接口示例

```python
import akshare as ak

index_component_sw_df = ak.index_component_sw(symbol="801001")
print(index_component_sw_df)
```

数据示例

```
      序号 证券代码  证券名称  最新权重  计入日期
0      1  000001  平安银行  1.2094  2023-07-03
1      2  000002   万科A  1.1122  2023-07-03
2      3  000063  中兴通讯  1.1810  2023-07-03
3      4  000333  美的集团  3.5702  2023-07-03
4      5  000568  泸州老窖  2.2004  2023-07-03
..   ...     ...   ...     ...         ...
145  146  601919  中远海控  0.6996  2024-07-01
146  147  603259  药明康德  1.2209  2024-07-01
147  148  603993  洛阳钼业  0.7059  2024-07-01
148  149  688041  海光信息  1.2586  2024-07-01
149  150  688111  金山办公  0.6166  2024-07-01
[150 rows x 5 columns]
```

#### 申万指数分析-日报表

接口: index_analysis_daily_sw

目标地址: https://www.swsresearch.com//institute_sw/allIndex/analysisIndex

描述: 申万宏源研究-指数分析-日报表

限量: 该接口返回指定参数的数据

输入参数

| 名称         | 类型  | 描述                                                        |
|------------|-----|-----------------------------------------------------------|
| symbol     | str | symbol="市场表征"; choice of {"市场表征", "一级行业", "二级行业", "风格指数"} |
| start_date | str | start_date="20221103"                                     |
| end_date   | str | end_date="20221103"                                       |

输出参数

| 名称     | 类型      | 描述       |
|--------|---------|----------|
| 指数代码   | object  | -        |
| 指数名称   | object  | -        |
| 发布日期   | object  | -        |
| 收盘指数   | float64 | -        |
| 成交量    | float64 | 注意单位: 亿股 |
| 涨跌幅    | float64 | 注意单位: %  |
| 换手率    | float64 | 注意单位: %  |
| 市盈率    | float64 | 注意单位: 倍  |
| 市净率    | float64 | 注意单位: 倍  |
| 均价     | float64 | 注意单位: 元  |
| 成交额占比  | float64 | 注意单位: %  |
| 流通市值   | float64 | 注意单位: 亿元 |
| 平均流通市值 | float64 | 注意单位: 亿元 |
| 股息率    | float64 | 注意单位: %  |

接口示例

```python
import akshare as ak

index_analysis_daily_sw_df = ak.index_analysis_daily_sw(symbol="市场表征", start_date="20241025", end_date="20241025")
print(index_analysis_daily_sw_df)
```

数据示例

```
   指数代码    指数名称   发布日期    收盘指数  ...  成交额占比   流通市值 平均流通市值 股息率
0  801001     申万50   2024-10-25  3073.15  ...    9.48   80271.53  1605.43  2.85
1  801002     申万中小  2024-10-25  6137.80  ...   17.40   58574.66    63.39  1.47
2  801003     申万Ａ指  2024-10-25  3694.53  ...  100.00  349595.86    74.51  1.86
3  801005     申万创业  2024-10-25  2830.78  ...   32.76   58026.76    61.34  0.78
4  801250     申万制造  2024-10-25  4513.51  ...   44.94  121638.16    63.35  1.01
5  801260     申万消费  2024-10-25  6427.74  ...   13.15   72040.49    72.62  2.18
6  801270     申万投资  2024-10-25  3117.80  ...   11.75   49872.16    60.67  2.32
7  801280     申万服务  2024-10-25  2524.14  ...   29.99  112674.97    83.71  2.37
8  801300  申万300指数  2024-10-25  2877.66  ...   31.51  169457.12   564.86  2.46
[9 rows x 14 columns]
```

#### 申万指数分析-周报表

接口: index_analysis_weekly_sw

目标地址: https://www.swsresearch.com//institute_sw/allIndex/analysisIndex

描述: 申万宏源研究-指数分析-周报表

限量: 该接口返回指定参数的数据

输入参数

| 名称     | 类型  | 描述                                                                            |
|--------|-----|-------------------------------------------------------------------------------|
| symbol | str | symbol="市场表征"; choice of {"市场表征", "一级行业", "二级行业", "风格指数"}                     |
| date   | str | start_date="20221104"; 通过调用 ak.index_analysis_week_month_sw(date="week") 接口获取 |

输出参数

| 名称     | 类型      | 描述       |
|--------|---------|----------|
| 指数代码   | object  | -        |
| 指数名称   | object  | -        |
| 发布日期   | object  | -        |
| 收盘指数   | float64 | -        |
| 成交量    | float64 | 注意单位: 亿股 |
| 涨跌幅    | float64 | 注意单位: %  |
| 换手率    | float64 | 注意单位: %  |
| 市盈率    | float64 | 注意单位: 倍  |
| 市净率    | float64 | 注意单位: 倍  |
| 均价     | float64 | 注意单位: 元  |
| 成交额占比  | float64 | 注意单位: %  |
| 流通市值   | float64 | 注意单位: 亿元 |
| 平均流通市值 | float64 | 注意单位: 亿元 |
| 股息率    | float64 | 注意单位: %  |

接口示例

```python
import akshare as ak

index_analysis_weekly_sw_df = ak.index_analysis_weekly_sw(symbol="市场表征", date="20241025")
print(index_analysis_weekly_sw_df)
```

数据示例

```
   指数代码     指数名称 发布日期     收盘指数  ...   成交额占比 流通市值  平均流通市值 股息率
0  801001     申万50   2024-10-25  3073.15  ...   12.21   80271.53  1605.43  2.85
1  801002     申万中小  2024-10-25  6137.80  ...   20.65   58574.66    63.39  1.47
2  801003     申万Ａ指  2024-10-25  3694.53  ...  100.00  349595.86    74.51  1.86
3  801005     申万创业  2024-10-25  2830.78  ...   42.01   58026.76    61.34  0.78
4  801250     申万制造  2024-10-25  4513.51  ...   56.09  121638.16    63.35  1.01
5  801260     申万消费  2024-10-25  6427.74  ...   15.56   72040.49    72.62  2.18
6  801270     申万投资  2024-10-25  3117.80  ...   12.77   49872.16    60.67  2.32
7  801280     申万服务  2024-10-25  2524.14  ...   38.88  112674.97    83.71  2.37
8  801300  申万300指数  2024-10-25  2877.66  ...   33.49  169457.12   564.86  2.46
[9 rows x 14 columns]
```

#### 申万指数分析-月报表

接口: index_analysis_monthly_sw

目标地址: https://www.swsresearch.com/institute_sw/allIndex/analysisIndex

描述: 申万宏源研究-指数分析-月报表

限量: 该接口返回指定参数的数据

输入参数

| 名称     | 类型  | 描述                                                                             |
|--------|-----|--------------------------------------------------------------------------------|
| symbol | str | symbol="市场表征"; choice of {"市场表征", "一级行业", "二级行业", "风格指数"}                      |
| date   | str | start_date="20221031"; 通过调用 ak.index_analysis_week_month_sw(date="month") 接口获取 |

输出参数

| 名称     | 类型      | 描述       |
|--------|---------|----------|
| 指数代码   | object  | -        |
| 指数名称   | object  | -        |
| 发布日期   | object  | -        |
| 收盘指数   | float64 | -        |
| 成交量    | float64 | 注意单位: 亿股 |
| 涨跌幅    | float64 | 注意单位: %  |
| 换手率    | float64 | 注意单位: %  |
| 市盈率    | float64 | 注意单位: 倍  |
| 市净率    | float64 | 注意单位: 倍  |
| 均价     | float64 | 注意单位: 元  |
| 成交额占比  | float64 | 注意单位: %  |
| 流通市值   | float64 | 注意单位: 亿元 |
| 平均流通市值 | float64 | 注意单位: 亿元 |
| 股息率    | float64 | 注意单位: %  |

接口示例

```python
import akshare as ak

index_analysis_monthly_sw_df = ak.index_analysis_monthly_sw(symbol="市场表征", date="20240930")
print(index_analysis_monthly_sw_df)
```

数据示例

```
   指数代码    指数名称   发布日期    收盘指数  ...  成交额占比  流通市值 平均流通市值 股息率
0  801001     申万50   2024-09-30  3152.14  ...   16.29   82379.30  1647.59  2.72
1  801002     申万中小  2024-09-30  5988.52  ...   21.36   57060.17    61.75  1.47
2  801003     申万Ａ指  2024-09-30  3636.02  ...  100.00  343958.32    73.31  1.84
3  801005     申万创业  2024-09-30  2636.53  ...   30.85   53963.62    57.04  0.84
4  801250     申万制造  2024-09-30  4218.84  ...   44.72  113449.40    59.06  1.04
5  801260     申万消费  2024-09-30  6665.38  ...   20.02   74580.85    75.18  2.07
6  801270     申万投资  2024-09-30  3193.45  ...   16.36   50974.14    62.16  2.22
7  801280     申万服务  2024-09-30  2489.15  ...   39.48  110978.29    82.63  2.33
8  801300  申万300指数  2024-09-30  2889.29  ...   35.97  170126.42   567.09  2.39
[9 rows x 14 columns]
```

### 市场情绪指数

#### A 股新闻情绪指数

接口: index_news_sentiment_scope

目标地址: https://www.chinascope.com/reasearch.html

描述: 数库-A股新闻情绪指数

限量: 该接口返回近一年的 A 股新闻情绪指数数据

输入参数

| 名称 | 类型 | 描述 |
|----|----|----|
| -  | -  | -  |

输出参数

| 名称      | 类型      | 描述 |
|---------|---------|----|
| 日期      | object  | -  |
| 市场情绪指数  | float64 | -  |
| 沪深300指数 | float64 | -  |

接口示例

```python
import akshare as ak

index_news_sentiment_scope_df = ak.index_news_sentiment_scope()
print(index_news_sentiment_scope_df)
```

数据示例

```
        日期    市场情绪指数  沪深300指数
0    2023-10-31  0.986883  3607.2454
1    2023-11-01  0.995480  3607.2454
2    2023-11-02  1.005612  3607.2454
3    2023-11-03  1.017992  3607.2454
4    2023-11-06  1.017223  3607.2454
..          ...       ...        ...
236  2024-10-25  0.996452  3956.4210
237  2024-10-28  1.001457  3964.1569
238  2024-10-29  1.008418  3924.6490
239  2024-10-30  1.004648  3889.4487
240  2024-10-31  1.007300  3891.0396
[241 rows x 3 columns]
```

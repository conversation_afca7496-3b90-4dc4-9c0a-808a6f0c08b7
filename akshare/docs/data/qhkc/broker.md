# 席位

## 席位持仓数据

### 接口名称

broker_positions

### 接口描述

席位持仓数据接口

### 请求参数

| 参数名    | 说明   | 举例         |
|:-------|:-----|------------|
| broker | 席位   | 永安期货       |
| date   | 查询日期 | 2018-08-08 |

### 返回参数

| 参数名        | 类型     | 说明      |
|:-----------|:-------|---------|
| code       | string | 合约代号    |
| long       | int    | 多头持仓    |
| short      | int    | 空头持仓    |
| long_chge  | int    | 多头持仓变化量 |
| short_chge | int    | 空头持仓变化量 |
| symbol     | string | 品种编码    |

### 示例代码

```python
from akshare import pro_api
pro = pro_api(token="在此处输入您的 token, 可以联系奇货可查网站管理员获取")
broker_positions_df = pro.broker_positions(broker="永安期货", date="2018-08-08")
print(broker_positions_df)
```

### 返回示例

```
       code   long  long_chge  short  short_chge symbol
0     a1809   2354       -175      0           0      A
1     a1901   4247        426   5159         493      A
2    ag1812   6155        212  13692          99     AG
3    al1808    455         -5      0           0     AL
4    al1809   2284       -181      0           0     AL
5    al1810   2451        158      0           0     AL
6    al1811    633          0    544         544     AL
7    al1812    419          8      0           0     AL
8    ap1810   1311        -38   1506        -205     AP
9    ap1811      0          0    194         -14     AP
10   ap1901   4090         58   4735         233     AP
11   ap1905   4017         65   2386         -17     AP
12   au1812   2381       -226    837          98     AU
13    b1809      0      -1153      0           0      B
14    b1901      0          0    175         175      B
15   bu1812  18412       -654  24973        -689     BU
16    c1809   9137        966  11309        -613      C
17    c1811     23         16      0           0      C
18    c1901  13987     -12150  47226        5144      C
19    c1905   1928        429   6739          15      C
20   cf1809   7983        729   2337         -40     CF
21   cf1901  10338       -336  35354        -798     CF
22   cf1905  14971       1429   9244        2901     CF
23   cs1809   1768       -654   2343         629     CS
24   cs1901   2169        939   1108         -22     CS
25   cu1808      0          0      0           0     CU
26   cu1809   4197       -104   2658         -11     CU
27   cu1810   2288        436   3520         167     CU
28   cu1811   1021         33   2057           6     CU
29   cu1812    391          7      0           0     CU
..      ...    ...        ...    ...         ...    ...
82   ru1905    553        -33   2218         149     RU
83   sf1809   2117        161   4946         269     SF
84   sf1901   1373        351     74          74     SF
85   sm1809   2794       -609      0           0     SM
86   sm1901    958        135    816          91     SM
87   sn1809    401        -15    152           8     SN
88   sr1809      0          0  14071       -1029     SR
89   sr1901  16912      -1215  12602         634     SR
90   sr1905   3261       -263    920          -2     SR
91    t1809   3784        606    729        -992      T
92    t1812    376         -4   3688         855      T
93   ta1809  23345       2469   9325       -1143     TA
94   ta1811   3180       -165   3441       -1042     TA
95   ta1901  57249     -12008  22708        7136     TA
96   ta1903    373         69    338          34     TA
97   ta1905    451          5    401        -241     TA
98   tf1809   1264        375      0           0     TF
99    v1809   3182       -485  15277       -7299      V
100   v1901   7957       3085   7496        2581      V
101  wh1809     20          8    150           0     WH
102   y1809   2575       -326   7995         520      Y
103   y1901  25191        838  13977        1369      Y
104   y1905   2615        132   9344          53      Y
105  zc1809   5558      -6447   2611         616     ZC
106  zc1811    690        -81    710        -115     ZC
107  zc1901   3908      -2077   4385         769     ZC
108  zn1808    175        -15   1465        -230     ZN
109  zn1809   3858       -335   3482         353     ZN
110  zn1810   6913       1081   4272         -79     ZN
111  zn1811   1463          5   1057          34     ZN
```

## 席位盈亏数据

### 接口名称

broker_calendar

### 接口描述

席位盈亏数据接口

### 请求参数

| 参数名        | 说明     | 举例         |
|:-----------|:-------|------------|
| broker     | 席位     | 永安期货       |
| start_date | 查询开始日期 | 2018-07-08 |
| end_date   | 查询结束日期 | 2018-08-08 |

### 返回参数

| 参数名        | 类型    | 说明       |
|:-----------|:------|----------|
| trans_date | date  | 查询日期     |
| profit     | float | 席位盈亏，单位元 |

### 示例代码

```python
from akshare import pro_api
pro = pro_api(token="在此处输入您的 token, 可以联系奇货可查网站管理员获取")
broker_calendar_df = pro.broker_calendar(broker="永安期货", start_date="2018-07-08", end_date="2018-08-08")
print(broker_calendar_df)
```

### 返回示例

```
       profit  trans_date
0    28534980  2018-07-09
1     -793730  2018-07-10
2   116090590  2018-07-11
3    71637460  2018-07-12
4   -67672690  2018-07-13
5  -101844070  2018-07-16
6    12677005  2018-07-17
7    28082910  2018-07-18
8    13328330  2018-07-19
9   152125816  2018-07-20
10   85702590  2018-07-23
11  111615970  2018-07-24
12   66728815  2018-07-25
13  -20638805  2018-07-26
14  106367725  2018-07-27
15  170801705  2018-07-30
16   16650180  2018-07-31
17  -11149860  2018-08-01
18    7460090  2018-08-02
19  153065340  2018-08-03
20  157935930  2018-08-06
21   95817915  2018-08-07
22   34925715  2018-08-08
```

## 席位每日大资金流动数据

### 接口名称

broker_flow

### 接口描述

席位每日大资金流动数据接口

### 请求参数

| 参数名    | 说明            | 举例         |
|:-------|:--------------|------------|
| broker | 席位            | 永安期货       |
| date   | 查询日期          | 2018-08-08 |
| offset | 查询阈值，大于这个值才返回 | 1000000    |

### 返回参数

| 参数名     | 类型     | 说明                     |
|:--------|:-------|------------------------|
| variety | string | 品种编码                   |
| money   | float  | 保证金流动量，正数为流多，负数为流空，单位元 |

### 示例代码

```python
from akshare import pro_api
pro = pro_api(token="在此处输入您的 token, 可以联系奇货可查网站管理员获取")
broker_flow_df = pro.broker_flow(broker="永安期货", date="2018-08-08", offset="1000000")
print(broker_flow_df)
```

### 返回示例

```
          money variety
0  -52131000.00      焦炭
1  -36507715.20     动力煤
2  -28648310.40     PTA
3   28275580.00      十债
4   26149929.30     螺纹钢
5   21465962.40      沪镍
6  -17617080.00     铁矿石
7   14739000.00      五债
8  -14430302.50      玉米
9   13557552.50     PVC
10  12419352.00      IC
11  -8928432.00      菜粕
12   8908188.00      焦煤
13   8834167.50      豆粕
14   5643888.00      沪锌
15   5459755.20      热卷
16  -4566014.60      甲醇
17  -4448322.00      IF
18  -4361850.00      沪金
19   3838632.00      沪铜
20  -3736382.00      豆油
21  -3677985.00      PP
22  -3081456.00      IH
23  -2878634.00      白糖
24  -2866627.75      沪铝
25  -2269088.00      豆二
26  -2136599.00     棕榈油
27  -1830276.00     燃料油
28  -1723621.90      锰硅
29   1565850.00      塑料
30   1551873.50      菜油
31  -1518107.50      棉花
32   1409305.80      鸡蛋
33  -1308532.50      橡胶
34  -1168420.00      玻璃
```

## 席位多空比数据

### 接口名称

broker_bbr

### 接口描述

席位多空比数据接口

### 请求参数

| 参数名    | 说明   | 举例         |
|:-------|:-----|------------|
| broker | 席位   | 永安期货       |
| date   | 查询日期 | 2018-08-08 |

### 返回参数

| 参数名 | 类型    | 说明  |
|:----|:------|-----|
| bbr | float | 多空比 |

### 示例代码

```python
from akshare import pro_api
pro = pro_api(token="在此处输入您的 token, 可以联系奇货可查网站管理员获取")
broker_bbr_df = pro.broker_bbr(broker="永安期货", date="2018-08-08")
print(broker_bbr_df)
```

### 返回示例

```
     broker_bbr
bbr    54.36208
```

## 席位净持仓保证金变化数据

### 接口名称

broker_net_money_chge

### 接口描述

席位净持仓保证金变化数据接口

### 请求参数

| 参数名    | 说明   | 举例         |
|:-------|:-----|------------|
| broker | 席位   | 永安期货       |
| date   | 查询日期 | 2018-08-08 |

### 返回参数

| 参数名    | 类型     | 说明                        |
|:-------|:-------|---------------------------|
| symbol | string | 品种编码                      |
| value  | float  | 净持仓保证金变化量，正数为流多，负数为流空，单位元 |

### 示例代码

```python
from akshare import pro_api
pro = pro_api(token="在此处输入您的 token, 可以联系奇货可查网站管理员获取")
broker_net_money_chge_df = pro.broker_net_money_chge(broker="永安期货", date="2018-08-08")
print(broker_net_money_chge_df)
```

### 返回示例

```
   symbol        value
0       J -52131000.00
1      ZC -36507715.20
2      TA -28648310.40
3       I -17617080.00
4       C -14430302.50
5      RM  -8928432.00
6      MA  -4566014.60
7      IF  -4448322.00
8      AU  -4361850.00
9       Y  -3736382.00
10     PP  -3677985.00
11     IH  -3081456.00
12     SR  -2878634.00
13     AL  -2866627.75
14      B  -2269088.00
15      P  -2136599.00
16     FU  -1830276.00
17     SM  -1723621.90
18     CF  -1518107.50
19     RU  -1308532.50
20     FG  -1168420.00
21      A   -438953.50
22     CS   -356861.00
23     SN   -236251.40
24     PM         0.00
25     WH     79808.00
26     BU     95480.00
27     PB    167724.00
28     AG    374357.70
29     SF    402589.60
30     AP    980350.80
31     JD   1409305.80
32     OI   1551873.50
33      L   1565850.00
34     CU   3838632.00
35     HC   5459755.20
36     ZN   5643888.00
37      M   8834167.50
38     JM   8908188.00
39     IC  12419352.00
40      V  13557552.50
41     TF  14739000.00
42     NI  21465962.40
43     RB  26149929.30
44      T  28275580.00
```

## 席位净持仓保证金数据

### 接口名称

broker_net_money

### 接口描述

席位净持仓保证金数据接口

### 请求参数

| 参数名    | 说明   | 举例         |
|:-------|:-----|------------|
| broker | 席位   | 永安期货       |
| date   | 查询日期 | 2018-08-08 |

### 返回参数

| 参数名    | 类型     | 说明                     |
|:-------|:-------|------------------------|
| symbol | string | 品种编码                   |
| value  | float  | 净持仓保证金，正数为净多，负数为净空，单位元 |

### 示例代码

```python
from akshare import pro_api
pro = pro_api(token="在此处输入您的 token, 可以联系奇货可查网站管理员获取")
broker_net_money_df = pro.broker_net_money(broker="永安期货", date="2018-08-08")
print(broker_net_money_df)
```

### 返回示例

```
   symbol      value
0      RU -126518247
1      IC  -73082712
2      CF  -59002530
3       L  -38443465
4       C  -37922269
5      JM  -27913493
6       P  -25204953
7      AG  -24969327
8       V  -21574018
9      BU  -17898408
10      I  -17824820
11     SR  -17713710
12     FG  -14825526
13     MA  -14759199
14      T   -9789992
15     HC   -6135685
16     SF   -3881907
17     RM   -3477467
18      Y   -2180168
19     WH   -1296880
20     FU   -1029732
21      B    -313600
22     PM     -17340
23     CS     581311
24      A    2531008
25     SN    2557678
26     IH    5062392
27     JD    5589189
28     AP    8217133
29     IF    8381628
30     SM    8913145
31     ZC    9053171
32     CU    9817385
33     ZN   18126276
34     AU   20786100
35     PB   24281126
36     NI   28679642
37     AL   28819661
38     TF   49680256
39     PP   63960902
40     OI   79174077
41     TA   98338372
42      M  158924122
43      J  199714680
44     RB  218919026
```

## 席位总持仓保证金数据

### 接口名称

broker_total_money

### 接口描述

席位总持仓保证金数据接口

### 请求参数

| 参数名    | 说明   | 举例         |
|:-------|:-----|------------|
| broker | 席位   | 永安期货       |
| date   | 查询日期 | 2018-08-08 |

### 返回参数

| 参数名    | 类型     | 说明         |
|:-------|:-------|------------|
| symbol | string | 品种编码       |
| value  | float  | 总持仓保证金，单位元 |

### 示例代码

```python
from akshare import pro_api
pro = pro_api(token="在此处输入您的 token, 可以联系奇货可查网站管理员获取")
broker_total_money_df = pro.broker_total_money(broker="永安期货", date="2018-08-08")
print(broker_total_money_df)
```

### 返回示例

```
   symbol      value
0       C   84535414
1       L  100876943
2      CF  348457275
3      RU  441062073
4       V   62613259
5       P   68744951
6      FG   29085142
7      AG   65751126
8      SR  121266350
9      BU  118354280
10     MA  188504328
11     JM   65540875
12      I  173409484
13     RM   30997475
14     HC  165703202
15     SF   21051245
16      Y  180289358
17     IC  355803249
18     FU   49778988
19      T  328004328
20      B     313600
21     WH    1695920
22     PM      17340
23     IH   33529176
24     IF  279349824
25     SN    5680305
26     CS    8618401
27     CU  309904039
28     AP  225718060
29     TF   49680256
30      A   21727647
31     AU   43322325
32     JD   19585812
33     ZN  195664900
34     ZC   66069647
35     SM   13684950
36     NI  280050806
37     PB   36902798
38     AL   34360301
39      J  533383080
40     OI  111939375
41     PP  163976343
42     TA  245508829
43     RB  959491721
44      M  399846143
```

## 席位的商品盈亏数据

### 接口名称

broker_profit

### 接口描述

席位的商品盈亏数据接口

### 请求参数

| 参数名        | 说明     | 举例         |
|:-----------|:-------|------------|
| broker     | 席位     | 永安期货       |
| start_date | 查询开始日期 | 2018-07-08 |
| end_date   | 查询结束日期 | 2018-07-08 |

### 返回参数

| 参数名          | 类型     | 说明       |
|:-------------|:-------|----------|
| symbol       | string | 品种编码     |
| total_profit | float  | 盈亏数据，单位元 |

### 示例代码

```python
from akshare import pro_api
pro = pro_api(token="在此处输入您的 token, 可以联系奇货可查网站管理员获取")
broker_profit_df = pro.broker_profit(broker="永安期货", start_date="2018-07-08", end_date="2018-08-08")
print(broker_profit_df)
```

### 返回示例

```
   symbol  total_profit
0       V     -48104250
1      MA     -44697020
2      ZC     -39435720
3       L     -30429900
4      PB     -23105875
5      RU     -22609450
6      AP     -15512460
7      CF     -13747625
8      AL     -13160850
9       Y     -12917280
10     NI      -9893720
11     AU      -9268650
12     TF      -9065400
13      C      -7829490
14     OI      -7340560
15      I      -6386050
16     IF      -5309736
17      A      -3110200
18     FU      -2245870
19     JM      -2199690
20      P      -2157100
21     JD       -981440
22      B       -813930
23     CS       -416630
24     PM         19200
25     WH         33600
26     SN        297220
27     FG        773020
28     RM       1176220
29     SF       2391770
30     IH       3532800
31     ZN       3822275
32     BU       4279600
33     SM       4757220
34     AG       5508285
35     IC      10934632
36     CU      23284400
37     SR      24980560
38      T      35304400
39     HC      51397670
40      M      76153330
41     PP     109996275
42     RB     200050400
43     TA     422540380
44      J     576955550
```

## 席位盈利排行

### 接口名称

broker_in_profit_list

### 接口描述

席位盈利排行数据接口

### 请求参数

| 参数名        | 说明     | 举例         |
|:-----------|:-------|------------|
| start_date | 查询开始日期 | 2018-07-08 |
| end_date   | 查询结束日期 | 2018-07-08 |
| count      | 返回数据条数 | 10; 默认10条  |

### 返回参数

| 参数名    | 类型     | 说明         |
|:-------|:-------|------------|
| broker | string | 席位         |
| profit | float  | 席位盈利金额，单位元 |

### 示例代码

```python
from akshare import pro_api
pro = pro_api(token="在此处输入您的 token, 可以联系奇货可查网站管理员获取")
broker_in_profit_list_df = pro.broker_in_profit_list(start_date="2018-07-08", end_date="2018-08-08", count="10")
print(broker_in_profit_list_df)
```

### 返回示例

```
  broker      profit
0   永安期货  1227449911
1   兴证期货   439465699
2   海通期货   380472107
3   国泰君安   340562538
4   东航期货   258435685
5   申银万国   232668456
6   方正中期   232440966
7   华泰期货   207665450
8   中信期货   187602856
9   鲁证期货   154923679
```

## 席位亏损排行

### 接口名称

broker_in_loss_list

### 接口描述

席位亏损排行数据接口

### 请求参数

| 参数名        | 说明     | 举例         |
|:-----------|:-------|------------|
| start_date | 查询开始日期 | 2018-07-08 |
| end_date   | 查询结束日期 | 2018-07-08 |
| count      | 返回数据条数 | 10; 默认10条  |

### 返回参数

| 参数名    | 类型     | 说明         |
|:-------|:-------|------------|
| broker | string | 席位         |
| profit | float  | 席位亏损金额，单位元 |

### 示例代码

```python
from akshare import pro_api
pro = pro_api(token="在此处输入您的 token, 可以联系奇货可查网站管理员获取")
broker_in_loss_list_df = pro.broker_in_loss_list(start_date="2018-07-08", end_date="2018-08-08", count="10")
print(broker_in_loss_list_df)
```

### 返回示例

```
  broker     profit
0   银河期货 -419310016
1   建信期货 -331576221
2   国贸期货 -287396120
3   南华期货 -286119224
4   格林大华 -282453330
5   光大期货 -278484680
6   招商期货 -200409531
7   国富期货 -183709404
8   中粮期货 -181432280
9   中辉期货 -165149465
```

## 所有席位数据

### 接口名称

broker_all

### 接口描述

所有席位数据接口

### 请求参数

| 参数名         | 说明               | 举例                |
|:------------|:-----------------|-------------------|
| offset_days | 过滤阈值，N个交易日内上过龙虎榜 | 默认为365，即一年内上过榜的席位 |

### 返回参数

| 参数名    | 类型     | 说明             |
|:-------|:-------|----------------|
| broker | string | 席位             |
| grade  | string | 盈利评级，最好为A，最差为E |

### 示例代码

```python
from akshare import pro_api
pro = pro_api(token="在此处输入您的 token, 可以联系奇货可查网站管理员获取")
broker_all_df = pro.broker_all(offset_days="365")
print(broker_all_df)
```

### 返回示例

```
    broker grade
0     安粮期货     C
1     渤海期货     B
2     北京首创     C
3     倍特期货     C
4     宝城期货     B
5     长江期货     D
6     创元期货     D
7     长安期货     D
8     长城期货     D
9     财达期货     D
10    东航期货     B
11    德盛期货     D
12    道通期货     C
13    东方期货     C
14    大地期货     B
15    东亚期货     B
16    东海期货     B
17    大通期货     D
18    大连良运     D
19    第一创业     D
20    东吴期货     D
21    东方财富     E
22    大越期货     B
23    东华期货     D
24    东证期货     D
25    东方汇金     D
26    大有期货     D
27    东兴期货     D
28    方正中期     E
29    福能期货     C
..     ...   ...
131   银河期货     B
132   永安期货     A
133   英大期货     D
134   云晨期货     B
135   一德期货     A
136  云财富期货     D
137   浙商期货     C
138   中天期货     C
139   中融汇信     D
140   中信期货     C
141   中投期货     D
142   中辉期货     E
143   中大期货     C
144   中航期货     D
145   中银国际     B
146   中信建投     E
147   中投天琪     D
148   中财期货     B
149   中国国际     D
150   中粮期货     B
151   中钢期货     C
152   招金期货     E
153    中电投     D
154  浙江新世纪     D
155   中州期货     D
156   中衍期货     C
157   中原期货     D
158  中电投先融     C
159   中金期货     B
160   招商期货     D
```

## 建仓过程

### 接口名称

broker_positions_process

### 接口描述

建仓过程数据接口

### 请求参数

| 参数名        | 说明   | 举例                 |
|:-----------|:-----|--------------------|
| broker     | 席位   | 永安期货               |
| code       | 合约代号 | rb1810             |
| start_date | 开始日期 | "2020-02-03"; 可选参数 |
| end_date   | 结束日期 | "2020-06-03"; 可选参数 |

### 返回参数

| 参数名          | 类型     | 说明                     |
|:-------------|:-------|------------------------|
| long         | int    | 多头持仓                   |
| short        | int    | 空头持仓                   |
| trans_date   | date   | 日期                     |
| code         | string | 合约代号                   |
| profit       | float  | 当日盈亏金额，正数为盈利，负数为亏损，单位元 |
| net_hold     | int    | 净持仓，多头持仓-空头持仓          |
| total_profit | float  | 累计盈亏金额，正数为盈利，负数为亏损，单位元 |
| cost         | float  | 估算盈亏平衡价格               |

### 示例代码

```python
from akshare import pro_api
pro = pro_api(token="在此处输入您的 token, 可以联系奇货可查网站管理员获取")
broker_positions_process_df = pro.broker_positions_process(broker="永安期货", code="rb2010", start_date="2020-02-03", end_date="2020-06-03")
print(broker_positions_process_df)
```

### 返回示例

```
      long  short  trans_date  ... settle_price  total_profit     cost
0    42187  17225  2020-02-03  ...         3196     -26297250  3301.35
1    50394  17694  2020-02-04  ...         3260     -10321570  3291.56
2    52270  20802  2020-02-05  ...         3292        142430  3291.55
3    63213  24253  2020-02-06  ...         3312       6436030  3295.48
4    64025  27434  2020-02-07  ...         3337      16176030  3292.79
..     ...    ...         ...  ...          ...           ...      ...
79  101358  80513  2020-05-28  ...         3492     101931690  3003.00
80   99001  73076  2020-05-29  ...         3533     110478140  3106.85
81   95185  55351  2020-06-01  ...         3580     122662890  3272.06
82  133541  62476  2020-06-02  ...         3608     133816410  3419.70
83  144905  55558  2020-06-03  ...         3640     156557210  3464.78
```

## 席位对对碰

### 接口名称

broker_pk

### 接口描述

席位对对碰数据接口

### 请求参数

| 参数名     | 说明   | 举例   |
|:--------|:-----|------|
| broker1 | 席位1  | 永安期货 |
| broker2 | 席位2  | 兴证期货 |
| symbol  | 品种编码 | 螺纹钢  |

### 返回参数

| 参数名              | 类型  | 说明           |
|:-----------------|:----|--------------|
| total_count      | int | 统计数据量        |
| win_count        | int | 席位1获胜次数      |
| recent_win_count | int | 席位1最近10次获胜次数 |

### 示例代码

```python
from akshare import pro_api
pro = pro_api(token="在此处输入您的 token, 可以联系奇货可查网站管理员获取")
broker_pk_df = pro.broker_pk(broker1="永安期货", broker2="兴证期货", symbol="RB")
print(broker_pk_df)
```

### 返回示例

```
                  broker_pk
total_count             614
win_count               295
recent_win_count          4
```

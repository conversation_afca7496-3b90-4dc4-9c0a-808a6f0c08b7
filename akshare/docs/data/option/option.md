## [AKShare](https://github.com/akfamily/akshare) 期权数据

### 期权基础信息

主要提供股指期权、ETF 期权和商品期货期权相关的数据

#### 期权交易所

此表包括目前所有具有期权品种的交易所

| 交易所名称                                | 交易所代码 | 首页地址                    |
|--------------------------------------|-------|-------------------------|
| [中国金融期货交易所](http://www.cffex.com.cn) | CFFEX | http://www.cffex.com.cn |
| [上海期货交易所](https://www.shfe.com.cn)   | SHFE  | https://www.shfe.com.cn |
| [上海国际能源交易中心](https://www.ine.cn)     | INE   | https://www.ine.cn      |
| [郑州商品交易所](http://www.czce.com.cn)    | CZCE  | http://www.czce.com.cn  |
| [大连商品交易所](http://www.dce.com.cn)     | DCE   | http://www.dce.com.cn   |
| [广州期货交易所](http://www.gfex.com.cn)    | GFEX  | http://www.gfex.com.cn  |
| [上海证券交易所](https://www.sse.com.cn)    | SSE   | https://www.sse.com.cn  |
| [深圳证券交易所](https://www.szse.cn)       | SZSE  | https://www.szse.cn     |

#### 期权交易时间

##### 商品期权

| 交易所        | 交易所代码 | 品种名称    | 品种代码 | 集合竞价                     | 日盘时间                                  | 夜盘时间        |
|------------|-------|---------|------|--------------------------|---------------------------------------|-------------|
| 上海期货交易所    | SHFE  | 铝期权     | al-o | 20:55-21:00, 08:55-09:00 | 09:00-10:15, 10:30-11:30, 13:30-15:00 | 21:00-01:00 |
| 上海期货交易所    | SHFE  | 氧化铝期权   | ao-o | 20:55-21:00, 08:55-09:00 | 09:00-10:15, 10:30-11:30, 13:30-15:00 | 21:00-01:00 |
| 上海期货交易所    | SHFE  | 铜期权     | cu-o | 20:55-21:00, 08:55-09:00 | 09:00-10:15, 10:30-11:30, 13:30-15:00 | 21:00-01:00 |
| 上海期货交易所    | SHFE  | 镍期权     | ni-o | 20:55-21:00, 08:55-09:00 | 09:00-10:15, 10:30-11:30, 13:30-15:00 | 21:00-01:00 |
| 上海期货交易所    | SHFE  | 铅期权     | pb-o | 20:55-21:00, 08:55-09:00 | 09:00-10:15, 10:30-11:30, 13:30-15:00 | 21:00-01:00 |
| 上海期货交易所    | SHFE  | 锡期权     | sn-o | 20:55-21:00, 08:55-09:00 | 09:00-10:15, 10:30-11:30, 13:30-15:00 | 21:00-01:00 |
| 上海期货交易所    | SHFE  | 锌期权     | zn-o | 20:55-21:00, 08:55-09:00 | 09:00-10:15, 10:30-11:30, 13:30-15:00 | 21:00-01:00 |
| 上海期货交易所    | SHFE  | 螺纹钢期权   | rb-o | 20:55-21:00, 08:55-09:00 | 09:00-10:15, 10:30-11:30, 13:30-15:00 | 21:00-23:00 |
| 上海期货交易所    | SHFE  | 天然橡胶期权  | ru-o | 20:55-21:00, 08:55-09:00 | 09:00-10:15, 10:30-11:30, 13:30-15:00 | 21:00-23:00 |
| 上海期货交易所    | SHFE  | 合成橡胶期权  | br-o | 20:55-21:00, 08:55-09:00 | 09:00-10:15, 10:30-11:30, 13:30-15:00 | 21:00-23:00 |
| 上海期货交易所    | SHFE  | 白银期权    | ag-o | 20:55-21:00, 08:55-09:00 | 09:00-10:15, 10:30-11:30, 13:30-15:00 | 21:00-02:30 |
| 上海期货交易所    | SHFE  | 黄金期权    | au-o | 20:55-21:00, 08:55-09:00 | 09:00-10:15, 10:30-11:30, 13:30-15:00 | 21:00-02:30 |
| 上海国际能源交易中心 | INE   | 原油期权    | sc-o | 20:55-21:00, 08:55-09:00 | 09:00-10:15, 10:30-11:30, 13:30-15:00 | 21:00-02:30 |
| 上海国际能源交易中心 | INE   | 20号胶期权  | nr-o | 20:55-21:00, 08:55-09:00 | 09:00-10:15, 10:30-11:30, 13:30-15:00 | 21:00-23:00 |
| 大连商品交易所    | DCE   | 黄大豆1号期权 | a-o  | 20:55-21:00, 08:55-09:00 | 09:00-10:15, 10:30-11:30, 13:30-15:00 | 21:00-23:00 |
| 大连商品交易所    | DCE   | 黄大豆2号期权 | b-o  | 20:55-21:00, 08:55-09:00 | 09:00-10:15, 10:30-11:30, 13:30-15:00 | 21:00-23:00 |
| 大连商品交易所    | DCE   | 玉米期权    | c-o  | 20:55-21:00, 08:55-09:00 | 09:00-10:15, 10:30-11:30, 13:30-15:00 | 21:00-23:00 |
| 大连商品交易所    | DCE   | 玉米淀粉期权  | cs-o | 20:55-21:00, 08:55-09:00 | 09:00-10:15, 10:30-11:30, 13:30-15:00 | 21:00-23:00 |
| 大连商品交易所    | DCE   | 苯乙烯期权   | eb-o | 20:55-21:00, 08:55-09:00 | 09:00-10:15, 10:30-11:30, 13:30-15:00 | 21:00-23:00 |
| 大连商品交易所    | DCE   | 乙二醇期权   | eg-o | 20:55-21:00, 08:55-09:00 | 09:00-10:15, 10:30-11:30, 13:30-15:00 | 21:00-23:00 |
| 大连商品交易所    | DCE   | 铁矿石期权   | i-o  | 20:55-21:00, 08:55-09:00 | 09:00-10:15, 10:30-11:30, 13:30-15:00 | 21:00-23:00 |
| 大连商品交易所    | DCE   | 聚乙烯期权   | l-o  | 20:55-21:00, 08:55-09:00 | 09:00-10:15, 10:30-11:30, 13:30-15:00 | 21:00-23:00 |
| 大连商品交易所    | DCE   | 豆粕期权    | m-o  | 20:55-21:00, 08:55-09:00 | 09:00-10:15, 10:30-11:30, 13:30-15:00 | 21:00-23:00 |
| 大连商品交易所    | DCE   | 棕榈油期权   | p-o  | 20:55-21:00, 08:55-09:00 | 09:00-10:15, 10:30-11:30, 13:30-15:00 | 21:00-23:00 |
| 大连商品交易所    | DCE   | 液化石油气期权 | pg-o | 20:55-21:00, 08:55-09:00 | 09:00-10:15, 10:30-11:30, 13:30-15:00 | 21:00-23:00 |
| 大连商品交易所    | DCE   | 聚丙烯期权   | pp-o | 20:55-21:00, 08:55-09:00 | 09:00-10:15, 10:30-11:30, 13:30-15:00 | 21:00-23:00 |
| 大连商品交易所    | DCE   | 聚氯乙烯期权  | v-o  | 20:55-21:00, 08:55-09:00 | 09:00-10:15, 10:30-11:30, 13:30-15:00 | 21:00-23:00 |
| 大连商品交易所    | DCE   | 豆油期权    | y-o  | 20:55-21:00, 08:55-09:00 | 09:00-10:15, 10:30-11:30, 13:30-15:00 | 21:00-23:00 |
| 大连商品交易所    | DCE   | 鸡蛋期权    | jd-o | 08:55-09:00              | 09:00-10:15, 10:30-11:30, 13:30-15:00 | -           |
| 大连商品交易所    | DCE   | 生猪期权    | lh-o | 08:55-09:00              | 09:00-10:15, 10:30-11:30, 13:30-15:00 | -           |
| 大连商品交易所    | DCE   | 原木期权    | lg-o | 08:55-09:00              | 09:00-10:15, 10:30-11:30, 13:30-15:00 | -           |
| 郑州商品交易所    | CZCE  | 棉花期权    | CF-O | 20:55-21:00, 08:55-09:00 | 09:00-10:15, 10:30-11:30, 13:30-15:00 | 21:00-23:00 |
| 郑州商品交易所    | CZCE  | 玻璃期权    | FG-O | 20:55-21:00, 08:55-09:00 | 09:00-10:15, 10:30-11:30, 13:30-15:00 | 21:00-23:00 |
| 郑州商品交易所    | CZCE  | 甲醇期权    | MA-O | 20:55-21:00, 08:55-09:00 | 09:00-10:15, 10:30-11:30, 13:30-15:00 | 21:00-23:00 |
| 郑州商品交易所    | CZCE  | 菜籽油期权   | OI-O | 20:55-21:00, 08:55-09:00 | 09:00-10:15, 10:30-11:30, 13:30-15:00 | 21:00-23:00 |
| 郑州商品交易所    | CZCE  | 短纤期权    | PF-O | 20:55-21:00, 08:55-09:00 | 09:00-10:15, 10:30-11:30, 13:30-15:00 | 21:00-23:00 |
| 郑州商品交易所    | CZCE  | 菜粕期权    | RM-O | 20:55-21:00, 08:55-09:00 | 09:00-10:15, 10:30-11:30, 13:30-15:00 | 21:00-23:00 |
| 郑州商品交易所    | CZCE  | 纯碱期权    | SA-O | 20:55-21:00, 08:55-09:00 | 09:00-10:15, 10:30-11:30, 13:30-15:00 | 21:00-23:00 |
| 郑州商品交易所    | CZCE  | 白糖期权    | SR-O | 20:55-21:00, 08:55-09:00 | 09:00-10:15, 10:30-11:30, 13:30-15:00 | 21:00-23:00 |
| 郑州商品交易所    | CZCE  | PTA期权   | TA-O | 20:55-21:00, 08:55-09:00 | 09:00-10:15, 10:30-11:30, 13:30-15:00 | 21:00-23:00 |
| 郑州商品交易所    | CZCE  | 动力煤期权   | ZC-O | 20:55-21:00, 08:55-09:00 | 09:00-10:15, 10:30-11:30, 13:30-15:00 | 21:00-23:00 |
| 郑州商品交易所    | CZCE  | 对二甲苯期权  | PX-O | 20:55-21:00, 08:55-09:00 | 09:00-10:15, 10:30-11:30, 13:30-15:00 | 21:00-23:00 |
| 郑州商品交易所    | CZCE  | 烧碱期权    | SH-O | 20:55-21:00, 08:55-09:00 | 09:00-10:15, 10:30-11:30, 13:30-15:00 | 21:00-23:00 |
| 郑州商品交易所    | CZCE  | 苹果期权    | AP-O | 08:55-09:00              | 09:00-10:15, 10:30-11:30, 13:30-15:00 | -           |
| 郑州商品交易所    | CZCE  | 红枣期权    | CJ-O | 08:55-09:00              | 09:00-10:15, 10:30-11:30, 13:30-15:00 | -           |
| 郑州商品交易所    | CZCE  | 花生期权    | PK-O | 08:55-09:00              | 09:00-10:15, 10:30-11:30, 13:30-15:00 | -           |
| 郑州商品交易所    | CZCE  | 硅铁期权    | SF-O | 08:55-09:00              | 09:00-10:15, 10:30-11:30, 13:30-15:00 | -           |
| 郑州商品交易所    | CZCE  | 硅锰期权    | SM-O | 08:55-09:00              | 09:00-10:15, 10:30-11:30, 13:30-15:00 | -           |
| 郑州商品交易所    | CZCE  | 尿素期权    | UR-O | 08:55-09:00              | 09:00-10:15, 10:30-11:30, 13:30-15:00 | -           |
| 广州期货交易所    | GFEX  | 工业硅期权   | SI-O | 08:55-09:00              | 09:00-10:15, 10:30-11:30, 13:30-15:00 | -           |
| 广州期货交易所    | GFEX  | 碳酸锂期权   | LC-O | 08:55-09:00              | 09:00-10:15, 10:30-11:30, 13:30-15:00 | -           |
| 广州期货交易所    | GFEX  | 多晶硅期权   | PS-O | 08:55-09:00              | 09:00-10:15, 10:30-11:30, 13:30-15:00 | -           |

*郑州商品交易所：夜盘品种不参加竞价，夜盘未成交的申报单可在 8：55-8：59 分撤单*

##### 股指期权

| 交易所       | 交易所代码 | 品种名称       | 品种代码 | 集合竞价                     | 交易时间                     | 夜盘时间 |
|-----------|-------|------------|------|--------------------------|--------------------------|------|
| 中国金融期货交易所 | CFFEX | 沪深300股指期权  | IO   | 09:25-09:30, 14:57-15:00 | 09:30-11:30, 13:00-14:57 | -    |
| 中国金融期货交易所 | CFFEX | 上证50股指期权   | HO   | 09:25-09:30, 14:57-15:00 | 09:30-11:30, 13:00-14:57 | -    |
| 中国金融期货交易所 | CFFEX | 中证1000股指期权 | MO   | 09:25-09:30, 14:57-15:00 | 09:30-11:30, 13:00-14:57 | -    |

##### ETF期权

| 交易所     | 交易所代码 | 品种名称           | 品种代码   | 集合竞价                     | 交易时间                     | 夜盘时间 |
|---------|-------|----------------|--------|--------------------------|--------------------------|------|
| 上海证券交易所 | SSE   | 华夏上证50ETF期权    | 510050 | 09:15-09:25, 14:57-15:00 | 09:30-11:30, 13:00-14:57 | -    |
| 上海证券交易所 | SSE   | 华泰柏瑞沪深300ETF期权 | 510300 | 09:15-09:25, 14:57-15:00 | 09:30-11:30, 13:00-14:57 | -    |
| 上海证券交易所 | SSE   | 南方中证500ETF期权   | 510500 | 09:15-09:25, 14:57-15:00 | 09:30-11:30, 13:00-14:57 | -    |
| 上海证券交易所 | SSE   | 华夏科创50ETF期权    | 588000 | 09:15-09:25, 14:57-15:00 | 09:30-11:30, 13:00-14:57 | -    |
| 上海证券交易所 | SSE   | 易方达科创50ETF期权   | 588080 | 09:15-09:25, 14:57-15:00 | 09:30-11:30, 13:00-14:57 | -    |
| 深圳证券交易所 | SZSE  | 嘉实沪深300ETF期权   | 159919 | 09:15-09:25, 14:57-15:00 | 09:30-11:30, 13:00-14:57 | -    |
| 深圳证券交易所 | SZSE  | 嘉实中证500ETF期权   | 159922 | 09:15-09:25, 14:57-15:00 | 09:30-11:30, 13:00-14:57 | -    |
| 深圳证券交易所 | SZSE  | 易方达创业板ETF期权    | 159915 | 09:15-09:25, 14:57-15:00 | 09:30-11:30, 13:00-14:57 | -    |
| 深圳证券交易所 | SZSE  | 易方达深证100ETF    | 159901 | 09:15-09:25, 14:57-15:00 | 09:30-11:30, 13:00-14:57 | -    |

#### 期权上市时间

##### 金融期权

###### 中国金融期货交易所

| 名称             | 类型    | 交易所       | 上市时间       |
|----------------|-------|-----------|------------|
| 沪深300股指期权      | 股指期权  | 中国金融期货交易所 | 2019-12-23 |
| 中证1000股指期权     | 股指期权  | 中国金融期货交易所 | 2022-07-22 |
| 上证50股指期权       | 股指期权  | 中国金融期货交易所 | 2022-12-19 |

###### 上海证券交易所

| 名称             | 类型    | 交易所       | 上市时间       |
|----------------|-------|-----------|------------|
| 华夏上证50ETF期权    | ETF期权 | 上海证券交易所   | 2015-02-09 |
| 华泰柏瑞沪深300ETF期权 | ETF期权 | 上海证券交易所   | 2019-12-23 |
| 南方中证500ETF期权   | ETF期权 | 上海证券交易所   | 2022-09-19 |
| 华夏科创50ETF期权    | ETF期权 | 上海证券交易所   | 2023-06-05 |
| 易方达科创50ETF期权   | ETF期权 | 上海证券交易所   | 2023-06-05 |

###### 深圳证券交易所

| 名称             | 类型    | 交易所       | 上市时间       |
|----------------|-------|-----------|------------|
| 嘉实沪深300ETF期权   | ETF期权 | 深圳证券交易所   | 2019-12-23 |
| 嘉实中证500ETF期权   | ETF期权 | 深圳证券交易所   | 2022-09-19 |
| 易方达创业板ETF期权    | ETF期权 | 深圳证券交易所   | 2022-09-19 |
| 易方达深证100ETF    | ETF期权 | 深圳证券交易所   | 2022-12-12 |

##### 商品期权

###### 上海期货交易所

| 交易所     | 对应名称   | 上市时间       |
|---------|--------|------------|
| 上海期货交易所 | 铜期权    | 2018-09-21 |
| 上海期货交易所 | 天然橡胶期权 | 2019-01-28 |
| 上海期货交易所 | 黄金期权   | 2019-12-20 |
| 上海期货交易所 | 铝期权    | 2020-08-10 |
| 上海期货交易所 | 锌期权    | 2020-08-10 |
| 上海期货交易所 | 原油期权   | 2021-06-21 |
| 上海期货交易所 | 螺纹钢期权  | 2022-12-26 |
| 上海期货交易所 | 白银期权   | 2022-12-26 |
| 上海期货交易所 | 合成橡胶期权 | 2023-07-28 |
| 上海期货交易所 | 铅期权    | 2024-09-02 |
| 上海期货交易所 | 镍期权    | 2024-09-02 |
| 上海期货交易所 | 锡期权    | 2024-09-02 |
| 上海期货交易所 | 氧化铝期权  | 2024-09-02 |

###### 大连商品交易所

| 交易所     | 对应名称    | 上市时间       |
|---------|---------|------------|
| 大连商品交易所 | 豆粕期权    | 2017-03-31 |
| 大连商品交易所 | 玉米期权    | 2019-01-28 |
| 大连商品交易所 | 铁矿石期权   | 2019-12-09 |
| 大连商品交易所 | 液化石油气期权 | 2020-03-30 |
| 大连商品交易所 | 聚乙烯期权   | 2020-07-06 |
| 大连商品交易所 | 聚氯乙烯期权  | 2020-07-06 |
| 大连商品交易所 | 聚丙烯期权   | 2020-07-06 |
| 大连商品交易所 | 棕榈油期权   | 2021-06-18 |
| 大连商品交易所 | 黄大豆1号期权 | 2022-08-08 |
| 大连商品交易所 | 黄大豆2号期权 | 2022-08-08 |
| 大连商品交易所 | 豆油期权    | 2022-08-08 |
| 大连商品交易所 | 乙二醇期权   | 2023-05-15 |
| 大连商品交易所 | 苯乙烯期权   | 2023-05-15 |
| 大连商品交易所 | 鸡蛋期权    | 2024-08-23 |
| 大连商品交易所 | 玉米淀粉期权  | 2024-08-23 |
| 大连商品交易所 | 生猪期权    | 2024-08-23 |
| 大连商品交易所 | 原木期权    | 2024-11-19 |

###### 郑州商品交易所

| 交易所     | 品种名称  | 行权方式 | 上市时间       |
|---------|-------|------|------------|
| 郑州商品交易所 | 白糖期权  | 美式   | 2017-04-19 |
| 郑州商品交易所 | 棉花期权  | 美式   | 2019-01-28 |
| 郑州商品交易所 | 甲醇期权  | 美式   | 2019-12-16 |
| 郑州商品交易所 | PTA期权 | 美式   | 2019-12-16 |
| 郑州商品交易所 | 菜籽粕期权 | 美式   | 2020-01-16 |
| 郑州商品交易所 | 动力煤期权 | 美式   | 2020-06-30 |
| 郑州商品交易所 | 菜籽油期权 | 美式   | 2022-08-26 |
| 郑州商品交易所 | 花生期权  | 美式   | 2022-08-26 |
| 郑州商品交易所 | 烧碱期权  | 美式   | 2023-09-15 |
| 郑州商品交易所 | 二甲苯期权 | 美式   | 2023-09-15 |
| 郑州商品交易所 | 短纤期权  | 美式   | 2023-10-20 |
| 郑州商品交易所 | 纯碱期权  | 美式   | 2023-10-20 |
| 郑州商品交易所 | 锰硅期权  | 美式   | 2023-10-20 |
| 郑州商品交易所 | 硅铁期权  | 美式   | 2023-10-20 |
| 郑州商品交易所 | 尿素期权  | 美式   | 2023-10-20 |
| 郑州商品交易所 | 苹果期权  | 美式   | 2023-10-20 |
| 郑州商品交易所 | 红枣期权  | 美式   | 2024-06-21 |
| 郑州商品交易所 | 玻璃期权  | 美式   | 2024-06-21 |
| 郑州商品交易所 | 瓶片期权  | 美式   | 2024-12-27 |

###### 广州期货交易所

| 交易所     | 品种名称  | 行权方式 | 上市时间       |
|---------|-------|------|------------|
| 广州期货交易所 | 工业硅期权 | 美式   | 2022-12-23 |
| 广州期货交易所 | 碳酸锂期权 | 美式   | 2023-07-24 |
| 广州期货交易所 | 多晶硅期权 | 美式   | 2024-12-26 |

### 金融期权-三大交易所

#### 行情数据

接口: option_finance_board

目标地址:

1. http://www.sse.com.cn/assortment/options/price/
2. http://www.szse.cn/market/derivative/derivative_list/index.html
3. http://www.cffex.com.cn/hs300gzqq/
4. http://www.cffex.com.cn/zz1000gzqq/

描述: 上海证券交易所、深圳证券交易所、中国金融期货交易所的金融期权行情数据

限量: 单次返回当前交易日指定合约期权行情数据

P.S. 可以通过调用 ak.option_finance_sse_underlying(symbol="华夏上证50ETF期权") 来获取上海证券交易所
金融期权标的物当日行情数据

输入参数

| 名称        | 类型  | 描述                                                |
|-----------|-----|---------------------------------------------------|
| symbol    | str | symbol="华泰柏瑞沪深300ETF期权"; 合约名称: **期权基础信息-金融期权**    |
| end_month | str | end_month="2306"; 合约到期月份: 2023 年 6 月, 只能获取近期合约的数据 |

输出参数

华夏上证 50ETF 期权

| 名称     | 类型      | 描述       |
|--------|---------|----------|
| 日期     | object  | 日期时间     |
| 合约交易代码 | object  |          |
| 当前价    | float64 |          |
| 涨跌幅    | float64 |          |
| 前结价    | float64 |          |
| 行权价    | float64 |          |
| 数量     | int64   | 当前总的合约数量 |

接口示例

```python
import akshare as ak

option_finance_board_df = ak.option_finance_board(symbol="华夏上证50ETF期权", end_month="2212")
print(option_finance_board_df)
```

数据示例

```
       日期             合约交易代码     当前价    涨跌幅  前结价   行权价  数量
0   20220810122215  510050C2212M02500  0.3278  -6.48  0.3505  2.50  32
1   20220810122215  510050C2212M02550  0.2870  -6.97  0.3085  2.55  32
2   20220810122215  510050C2212M02600  0.2481  -8.38  0.2708  2.60  32
3   20220810122215  510050C2212M02650  0.2167  -8.33  0.2364  2.65  32
4   20220810122215  510050C2212M02700  0.1866  -7.81  0.2024  2.70  32
5   20220810122215  510050C2212M02750  0.1566  -9.22  0.1725  2.75  32
6   20220810122215  510050C2212M02800  0.1312  -9.89  0.1456  2.80  32
7   20220810122215  510050C2212M02850  0.1092 -10.34  0.1218  2.85  32
8   20220810122215  510050C2212M02900  0.0896 -11.37  0.1011  2.90  32
9   20220810122215  510050P2212M02500  0.0342  14.00  0.0300  2.50  32
10  20220810122215  510050P2212M02550  0.0449  13.38  0.0396  2.55  32
11  20220810122215  510050P2212M02600  0.0578  11.80  0.0517  2.60  32
12  20220810122215  510050P2212M02650  0.0725  10.86  0.0654  2.65  32
13  20220810122215  510050P2212M02700  0.0910  10.98  0.0820  2.70  32
14  20220810122215  510050P2212M02750  0.1126  10.83  0.1016  2.75  32
15  20220810122215  510050P2212M02800  0.1369   9.87  0.1246  2.80  32
16  20220810122215  510050P2212M02850  0.1648   9.72  0.1502  2.85  32
17  20220810122215  510050P2212M02900  0.1948   9.13  0.1785  2.90  32
18  20220810122215  510050C2212M02950  0.0735 -11.34  0.0829  2.95  32
19  20220810122215  510050P2212M02950  0.2274   8.08  0.2104  2.95  32
20  20220810122215  510050C2212M03000  0.0597 -11.03  0.0671  3.00  32
21  20220810122215  510050P2212M03000  0.2631   7.78  0.2441  3.00  32
22  20220810122215  510050C2212M03100  0.0388 -11.62  0.0439  3.10  32
23  20220810122215  510050P2212M03100  0.3440   7.40  0.3203  3.10  32
24  20220810122215  510050C2212M03200  0.0257  -8.21  0.0280  3.20  32
25  20220810122215  510050P2212M03200  0.4283   5.62  0.4055  3.20  32
26  20220810122215  510050C2212M03300  0.0155 -14.36  0.0181  3.30  32
27  20220810122215  510050P2212M03300  0.5200   4.21  0.4990  3.30  32
28  20220810122215  510050C2212M03400  0.0104 -11.86  0.0118  3.40  32
29  20220810122215  510050P2212M03400  0.6100   1.84  0.5990  3.40  32
30  20220810122215  510050C2212M03500  0.0077 -10.47  0.0086  3.50  32
31  20220810122215  510050P2212M03500  0.7047   0.82  0.6990  3.50  32
```

华泰柏瑞沪深300ETF期权

| 名称     | 类型      | 描述  |
|--------|---------|-----|
| 日期     | object  | -   |
| 合约交易代码 | object  | -   |
| 当前价    | float64 | -   |
| 涨跌幅    | float64 | -   |
| 前结价    | float64 | -   |
| 行权价    | float64 | -   |
| 数量     | int64   | -   |

接口示例

```python
import akshare as ak

option_finance_board_df = ak.option_finance_board(symbol="华泰柏瑞沪深300ETF期权", end_month="2212")
print(option_finance_board_df)
```

数据示例

```
     日期             合约交易代码     当前价    涨跌幅     前结价  行权价  数量
0   20220810122445  510300C2212M03500  0.6766  -5.13  0.7132  3.5  30
1   20220810122445  510300C2212M03600  0.0000   0.00  0.6240  3.6  30
2   20220810122445  510300C2212M03700  0.5100  -5.36  0.5389  3.7  30
3   20220810122445  510300C2212M03800  0.4318  -5.64  0.4576  3.8  30
4   20220810122445  510300C2212M03900  0.3600  -6.15  0.3836  3.9  30
5   20220810122445  510300C2212M04000  0.2925  -7.05  0.3147  4.0  30
6   20220810122445  510300C2212M04100  0.2361  -7.16  0.2543  4.1  30
7   20220810122445  510300C2212M04200  0.1848  -7.88  0.2006  4.2  30
8   20220810122445  510300C2212M04300  0.1427  -8.88  0.1566  4.3  30
9   20220810122445  510300P2212M03500  0.0263  15.86  0.0227  3.5  30
10  20220810122445  510300P2212M03600  0.0367  13.27  0.0324  3.6  30
11  20220810122445  510300P2212M03700  0.0520  11.59  0.0466  3.7  30
12  20220810122445  510300P2212M03800  0.0717  11.34  0.0644  3.8  30
13  20220810122445  510300P2212M03900  0.0979   8.90  0.0899  3.9  30
14  20220810122445  510300P2212M04000  0.1322  10.07  0.1201  4.0  30
15  20220810122445  510300P2212M04100  0.1732   9.55  0.1581  4.1  30
16  20220810122445  510300P2212M04200  0.2231   9.52  0.2037  4.2  30
17  20220810122445  510300P2212M04300  0.2805   7.80  0.2602  4.3  30
18  20220810122445  510300C2212M04400  0.1088  -9.71  0.1205  4.4  30
19  20220810122445  510300P2212M04400  0.3411   5.15  0.3244  4.4  30
20  20220810122445  510300C2212M04500  0.0812  -9.68  0.0899  4.5  30
21  20220810122445  510300P2212M04500  0.4150   5.38  0.3938  4.5  30
22  20220810122445  510300C2212M04600  0.0601 -10.03  0.0668  4.6  30
23  20220810122445  510300P2212M04600  0.4695   0.00  0.4695  4.6  30
24  20220810122445  510300C2212M04700  0.0442  -9.98  0.0491  4.7  30
25  20220810122445  510300P2212M04700  0.0000   0.00  0.5516  4.7  30
26  20220810122445  510300C2212M04800  0.0328  -9.64  0.0363  4.8  30
27  20220810122445  510300P2212M04800  0.0000   0.00  0.6373  4.8  30
28  20220810122445  510300C2212M04900  0.0237  -7.78  0.0257  4.9  30
29  20220810122445  510300P2212M04900  0.7510   3.43  0.7261  4.9  30
```

南方中证500ETF期权

| 名称     | 类型      | 描述  |
|--------|---------|-----|
| 日期     | object  | -   |
| 合约交易代码 | object  | -   |
| 当前价    | float64 | -   |
| 涨跌幅    | float64 | -   |
| 前结价    | float64 | -   |
| 行权价    | float64 | -   |
| 数量     | int64   | -   |

接口示例

```python
import akshare as ak

option_finance_board_df = ak.option_finance_board(symbol="南方中证500ETF期权", end_month="2306")
print(option_finance_board_df)
```

数据示例

```
     日期             合约交易代码     当前价    涨跌幅     前结价   行权价  数量
0   20230605155959  510500C2306M05000  1.1560  -0.42  1.1609  5.00  24
1   20230605155959  510500C2306M05250  0.9084  -0.33  0.9114  5.25  24
2   20230605155959  510500C2306M05500  0.6543  -1.76  0.6660  5.50  24
3   20230605155959  510500C2306M05750  0.4063  -2.68  0.4175  5.75  24
4   20230605155959  510500C2306M06000  0.1789  -6.63  0.1916  6.00  24
5   20230605155959  510500C2306M06250  0.0385 -15.75  0.0457  6.25  24
6   20230605155959  510500C2306M06500  0.0042 -19.23  0.0052  6.50  24
7   20230605155959  510500C2306M06750  0.0011 -15.38  0.0013  6.75  24
8   20230605155959  510500C2306M07000  0.0006 -14.29  0.0007  7.00  24
9   20230605155959  510500P2306M05000  0.0006  20.00  0.0005  5.00  24
10  20230605155959  510500P2306M05250  0.0005   0.00  0.0005  5.25  24
11  20230605155959  510500P2306M05500  0.0014 -17.65  0.0017  5.50  24
12  20230605155959  510500P2306M05750  0.0055  -3.51  0.0057  5.75  24
13  20230605155959  510500P2306M06000  0.0295  -0.67  0.0297  6.00  24
14  20230605155959  510500P2306M06250  0.1371   4.26  0.1315  6.25  24
15  20230605155959  510500P2306M06500  0.3483   0.66  0.3460  6.50  24
16  20230605155959  510500P2306M06750  0.5948  -0.20  0.5960  6.75  24
17  20230605155959  510500P2306M07000  0.8468   0.09  0.8460  7.00  24
18  20230605155959  510500C2306M04900  1.2372  -1.92  1.2614  4.90  24
19  20230605155959  510500P2306M04900  0.0006  20.00  0.0005  4.90  24
20  20230605155959  510500C2306M07250  0.0005 -16.67  0.0006  7.25  24
21  20230605155959  510500P2306M07250  1.0960   0.00  1.0960  7.25  24
22  20230605155959  510500C2306M07500  0.0003   0.00  0.0003  7.50  24
23  20230605155959  510500P2306M07500  1.3660   0.00  1.3460  7.50  24
```

华夏科创50ETF期权

| 名称     | 类型      | 描述  |
|--------|---------|-----|
| 日期     | object  | -   |
| 合约交易代码 | object  | -   |
| 当前价    | float64 | -   |
| 涨跌幅    | float64 | -   |
| 前结价    | float64 | -   |
| 行权价    | float64 | -   |
| 数量     | int64   | -   |

接口示例

```python
import akshare as ak

option_finance_board_df = ak.option_finance_board(symbol="华夏科创50ETF期权", end_month="2306")
print(option_finance_board_df)
```

数据示例

```
     日期             合约交易代码     当前价    涨跌幅     前结价   行权价  数量
0   20230605155959  588000C2306M00900  0.1983  -1.78  0.2019  0.90  18
1   20230605155959  588000C2306M00950  0.1475  -3.02  0.1521  0.95  18
2   20230605155959  588000C2306M01000  0.0990  -3.88  0.1030  1.00  18
3   20230605155959  588000C2306M01050  0.0528  -9.74  0.0585  1.05  18
4   20230605155959  588000C2306M01100  0.0200 -21.57  0.0255  1.10  18
5   20230605155959  588000C2306M01150  0.0055 -31.25  0.0080  1.15  18
6   20230605155959  588000C2306M01200  0.0015 -16.67  0.0018  1.20  18
7   20230605155959  588000C2306M01250  0.0006 -33.33  0.0009  1.25  18
8   20230605155959  588000C2306M01300  0.0004   0.00  0.0004  1.30  18
9   20230605155959  588000P2306M00900  0.0002   0.00  0.0002  0.90  18
10  20230605155959  588000P2306M00950  0.0003 -25.00  0.0004  0.95  18
11  20230605155959  588000P2306M01000  0.0009   0.00  0.0009  1.00  18
12  20230605155959  588000P2306M01050  0.0053 -15.87  0.0063  1.05  18
13  20230605155959  588000P2306M01100  0.0220  -5.17  0.0232  1.10  18
14  20230605155959  588000P2306M01150  0.0582   4.68  0.0556  1.15  18
15  20230605155959  588000P2306M01200  0.1034   3.40  0.1000  1.20  18
16  20230605155959  588000P2306M01250  0.1515   1.00  0.1500  1.25  18
17  20230605155959  588000P2306M01300  0.2019   0.95  0.2000  1.30  18
```

易方达科创50ETF期权

| 名称     | 类型      | 描述  |
|--------|---------|-----|
| 日期     | object  | -   |
| 合约交易代码 | object  | -   |
| 当前价    | float64 | -   |
| 涨跌幅    | float64 | -   |
| 前结价    | float64 | -   |
| 行权价    | float64 | -   |
| 数量     | int64   | -   |

接口示例

```python
import akshare as ak

option_finance_board_df = ak.option_finance_board(symbol="易方达科创50ETF期权", end_month="2306")
print(option_finance_board_df)
```

数据示例

```
     日期             合约交易代码     当前价    涨跌幅     前结价   行权价  数量
0   20230605160009  588080C2306M00900  0.1768  -1.72  0.1799  0.90  18
1   20230605160009  588080C2306M00950  0.1285  -1.31  0.1302  0.95  18
2   20230605160009  588080C2306M01000  0.0807  -1.82  0.0822  1.00  18
3   20230605160009  588080C2306M01050  0.0369 -11.30  0.0416  1.05  18
4   20230605160009  588080C2306M01100  0.0123 -20.65  0.0155  1.10  18
5   20230605160009  588080C2306M01150  0.0032 -20.00  0.0040  1.15  18
6   20230605160009  588080C2306M01200  0.0009  28.57  0.0007  1.20  18
7   20230605160009  588080C2306M01250  0.0005   0.00  0.0005  1.25  18
8   20230605160009  588080C2306M01300  0.0003   0.00  0.0003  1.30  18
9   20230605160009  588080P2306M00900  0.0001 -50.00  0.0002  0.90  18
10  20230605160009  588080P2306M00950  0.0003 -50.00  0.0006  0.95  18
11  20230605160009  588080P2306M01000  0.0015 -28.57  0.0021  1.00  18
12  20230605160009  588080P2306M01050  0.0096 -15.79  0.0114  1.05  18
13  20230605160009  588080P2306M01100  0.0352   0.00  0.0352  1.10  18
14  20230605160009  588080P2306M01150  0.0747   1.49  0.0736  1.15  18
15  20230605160009  588080P2306M01200  0.1227   0.57  0.1220  1.20  18
16  20230605160009  588080P2306M01250  0.1692  -1.63  0.1720  1.25  18
17  20230605160009  588080P2306M01300  0.2198  -0.99  0.2220  1.30  18
```

嘉实沪深300ETF期权

| 名称    | 类型      | 描述       |
|-------|---------|----------|
| 合约编码  | object  | -        |
| 合约简称  | object  | -        |
| 标的名称  | object  | -        |
| 类型    | float64 | -        |
| 行权价   | float64 | 注意单位: 元  |
| 合约单位  | float64 | 注意单位:  份 |
| 期权行权日 | object  | -        |
| 行权交收日 | object  | -        |

接口示例

```python
import akshare as ak

option_finance_board_df = ak.option_finance_board(symbol="嘉实沪深300ETF期权", end_month="2003")
print(option_finance_board_df)
```

数据示例

```
         合约编码            合约简称    标的名称  类型   行权价   合约单位      期权行权日       行权交收日
0    90000335  300ETF购3月4260A  300ETF  认购  4.26  10330 2021-03-24  2021-03-25
1    90000336  300ETF购3月4356A  300ETF  认购  4.36  10330 2021-03-24  2021-03-25
2    90000337  300ETF购3月4453A  300ETF  认购  4.45  10330 2021-03-24  2021-03-25
3    90000338  300ETF购3月4550A  300ETF  认购  4.55  10330 2021-03-24  2021-03-25
4    90000339  300ETF购3月4647A  300ETF  认购  4.65  10330 2021-03-24  2021-03-25
5    90000340  300ETF购3月4744A  300ETF  认购  4.74  10330 2021-03-24  2021-03-25
6    90000341  300ETF购3月4840A  300ETF  认购  4.84  10330 2021-03-24  2021-03-25
7    90000342  300ETF购3月5082A  300ETF  认购  5.08  10330 2021-03-24  2021-03-25
8    90000343  300ETF购3月5325A  300ETF  认购  5.33  10330 2021-03-24  2021-03-25
9    90000344  300ETF沽3月4260A  300ETF  认沽  4.26  10330 2021-03-24  2021-03-25
10   90000345  300ETF沽3月4356A  300ETF  认沽  4.36  10330 2021-03-24  2021-03-25
11   90000346  300ETF沽3月4453A  300ETF  认沽  4.45  10330 2021-03-24  2021-03-25
12   90000347  300ETF沽3月4550A  300ETF  认沽  4.55  10330 2021-03-24  2021-03-25
13   90000348  300ETF沽3月4647A  300ETF  认沽  4.65  10330 2021-03-24  2021-03-25
14   90000349  300ETF沽3月4744A  300ETF  认沽  4.74  10330 2021-03-24  2021-03-25
15   90000350  300ETF沽3月4840A  300ETF  认沽  4.84  10330 2021-03-24  2021-03-25
16   90000351  300ETF沽3月5082A  300ETF  认沽  5.08  10330 2021-03-24  2021-03-25
17   90000352  300ETF沽3月5325A  300ETF  认沽  5.33  10330 2021-03-24  2021-03-25
18   90000353  300ETF购3月4066A  300ETF  认购  4.07  10330 2021-03-24  2021-03-25
19   90000354  300ETF购3月4163A  300ETF  认购  4.16  10330 2021-03-24  2021-03-25
20   90000355  300ETF沽3月4066A  300ETF  认沽  4.07  10330 2021-03-24  2021-03-25
21   90000356  300ETF沽3月4163A  300ETF  认沽  4.16  10330 2021-03-24  2021-03-25
22   90000357  300ETF购3月5567A  300ETF  认购  5.57  10330 2021-03-24  2021-03-25
23   90000358  300ETF沽3月5567A  300ETF  认沽  5.57  10330 2021-03-24  2021-03-25
24   90000359  300ETF购3月5809A  300ETF  认购  5.81  10330 2021-03-24  2021-03-25
25   90000360  300ETF沽3月5809A  300ETF  认沽  5.81  10330 2021-03-24  2021-03-25
26   90000439   300ETF购3月4200  300ETF  认购  4.20  10000 2021-03-24  2021-03-25
27   90000440   300ETF购3月4300  300ETF  认购  4.30  10000 2021-03-24  2021-03-25
28   90000441   300ETF购3月4400  300ETF  认购  4.40  10000 2021-03-24  2021-03-25
29   90000442   300ETF购3月4500  300ETF  认购  4.50  10000 2021-03-24  2021-03-25
30   90000443   300ETF购3月4600  300ETF  认购  4.60  10000 2021-03-24  2021-03-25
31   90000444   300ETF购3月4700  300ETF  认购  4.70  10000 2021-03-24  2021-03-25
32   90000445   300ETF购3月4800  300ETF  认购  4.80  10000 2021-03-24  2021-03-25
33   90000446   300ETF购3月4900  300ETF  认购  4.90  10000 2021-03-24  2021-03-25
34   90000447   300ETF购3月5000  300ETF  认购  5.00  10000 2021-03-24  2021-03-25
35   90000448   300ETF沽3月4200  300ETF  认沽  4.20  10000 2021-03-24  2021-03-25
36   90000449   300ETF沽3月4300  300ETF  认沽  4.30  10000 2021-03-24  2021-03-25
37   90000450   300ETF沽3月4400  300ETF  认沽  4.40  10000 2021-03-24  2021-03-25
38   90000451   300ETF沽3月4500  300ETF  认沽  4.50  10000 2021-03-24  2021-03-25
39   90000452   300ETF沽3月4600  300ETF  认沽  4.60  10000 2021-03-24  2021-03-25
40   90000453   300ETF沽3月4700  300ETF  认沽  4.70  10000 2021-03-24  2021-03-25
41   90000454   300ETF沽3月4800  300ETF  认沽  4.80  10000 2021-03-24  2021-03-25
42   90000455   300ETF沽3月4900  300ETF  认沽  4.90  10000 2021-03-24  2021-03-25
43   90000456   300ETF沽3月5000  300ETF  认沽  5.00  10000 2021-03-24  2021-03-25
44   90000463   300ETF购3月5250  300ETF  认购  5.25  10000 2021-03-24  2021-03-25
45   90000464   300ETF沽3月5250  300ETF  认沽  5.25  10000 2021-03-24  2021-03-25
46   90000491   300ETF购3月5500  300ETF  认购  5.50  10000 2021-03-24  2021-03-25
47   90000492   300ETF沽3月5500  300ETF  认沽  5.50  10000 2021-03-24  2021-03-25
68   90000517   300ETF购3月5750  300ETF  认购  5.75  10000 2021-03-24  2021-03-25
69   90000518   300ETF沽3月5750  300ETF  认沽  5.75  10000 2021-03-24  2021-03-25
72   90000525   300ETF购3月6000  300ETF  认购  6.00  10000 2021-03-24  2021-03-25
73   90000526   300ETF沽3月6000  300ETF  认沽  6.00  10000 2021-03-24  2021-03-25
118  90000571   300ETF购3月6250  300ETF  认购  6.25  10000 2021-03-24  2021-03-25
119  90000572   300ETF沽3月6250  300ETF  认沽  6.25  10000 2021-03-24  2021-03-25
126  90000579   300ETF购3月6500  300ETF  认购  6.50  10000 2021-03-24  2021-03-25
127  90000580   300ETF沽3月6500  300ETF  认沽  6.50  10000 2021-03-24  2021-03-25
```

沪深300股指期权

| 名称         | 类型      | 描述  |
|------------|---------|-----|
| instrument | object  | 期权  |
| position   | float64 |     |
| volume     | float64 |     |
| lastprice  | float64 |     |
| updown     | float64 |     |
| bprice     | float64 |     |
| bamount    | float64 |     |
| sprice     | float64 |     |
| samount    | float64 |     |

接口示例

```python
import akshare as ak

option_finance_board_df = ak.option_finance_board(symbol="沪深300股指期权", end_month="2306")
print(option_finance_board_df)
```

数据示例

```
       instrument  position  volume  ...  bamount  sprice  samount
0   IO2306-C-3100       138       0  ...        1   741.2        1
1   IO2306-C-3200       162      11  ...        1   640.8        1
2   IO2306-C-3300        99       3  ...        2   540.6        1
3   IO2306-C-3400       107       3  ...        1   440.8        2
4   IO2306-C-3450        25       6  ...        1   390.8        1
..            ...       ...     ...  ...      ...     ...      ...
59  IO2306-P-4600       148       0  ...        1   769.4        1
60  IO2306-P-4700       165       9  ...        1   869.2        1
61  IO2306-P-4800       119       2  ...        1   969.6        1
62  IO2306-P-4900       169       2  ...        1  1069.6        1
63  IO2306-P-5000       541       6  ...        1  1170.2        1
```

中证1000股指期权

| 名称         | 类型      | 描述  |
|------------|---------|-----|
| instrument | object  | 期权  |
| position   | float64 |     |
| volume     | float64 |     |
| lastprice  | float64 |     |
| updown     | float64 |     |
| bprice     | float64 |     |
| bamount    | float64 |     |
| sprice     | float64 |     |
| samount    | float64 |     |

接口示例

```python
import akshare as ak

option_finance_board_df = ak.option_finance_board(symbol="中证1000股指期权", end_month="2306")
print(option_finance_board_df)
```

数据示例

```
       instrument  position  volume  ...  bamount  sprice  samount
0   MO2306-C-5200       161       1  ...        1  1426.8        1
1   MO2306-C-5400       108       0  ...        1  1226.8        1
2   MO2306-C-5600        46       2  ...        1  1024.0        1
3   MO2306-C-5800        79       0  ...        1   824.2        1
4   MO2306-C-5900        43       1  ...        1   725.8        1
5   MO2306-C-6000       114       1  ...        2   625.2        1
6   MO2306-C-6100        92       9  ...        1   520.8        1
7   MO2306-C-6200       277      29  ...        1   422.4        1
8   MO2306-C-6300       292      58  ...        1   320.4        1
9   MO2306-C-6400       804     390  ...        1   227.8        1
10  MO2306-C-6500      1950    1823  ...        2   144.2        1
11  MO2306-C-6600      4701    6212  ...        2    75.8        2
12  MO2306-C-6700      5208    5370  ...        1    34.0        2
13  MO2306-C-6800      4182    3258  ...       19    12.2       10
14  MO2306-C-6900      3573     746  ...        2     4.0       11
15  MO2306-C-7000      3835     201  ...       11     2.0        9
16  MO2306-C-7100      1048      56  ...        3     1.0        9
17  MO2306-C-7200      1233      45  ...        9     1.0       13
18  MO2306-C-7300       220      49  ...        2     0.8       14
19  MO2306-C-7400       399      16  ...        2     0.6        3
20  MO2306-C-7500       262      71  ...        3     0.8       13
21  MO2306-C-7600       541      80  ...        2     0.8       37
22  MO2306-C-7700       146      79  ...        2     0.6        9
23  MO2306-C-7800       405      40  ...        3     0.6       10
24  MO2306-C-8000       702      91  ...        3     0.6       10
25  MO2306-C-8200       683      46  ...       43     0.4       10
26  MO2306-P-5200       432      35  ...       81     0.6       17
27  MO2306-P-5400       286      15  ...        6     0.6       10
28  MO2306-P-5600       620      59  ...       12     0.8       11
29  MO2306-P-5800       386     134  ...        2     0.8       11
30  MO2306-P-5900      1206      94  ...        4     1.0        3
31  MO2306-P-6000      1383     221  ...        4     1.0        2
32  MO2306-P-6100       967     168  ...        1     1.6       10
33  MO2306-P-6200      2744     556  ...       30     2.8        3
34  MO2306-P-6300      2669    1307  ...        9     5.0        2
35  MO2306-P-6400      3238    2406  ...        3    12.2        4
36  MO2306-P-6500      2759    4260  ...        7    27.2        1
37  MO2306-P-6600      2613    5091  ...        1    62.8        1
38  MO2306-P-6700      1532    2160  ...        1   118.0        1
39  MO2306-P-6800      1334     424  ...        1   198.2        1
40  MO2306-P-6900      1708      35  ...        1   293.0        1
41  MO2306-P-7000       754       2  ...        2   388.4        1
42  MO2306-P-7100       230       1  ...        1   488.0        1
43  MO2306-P-7200       139       2  ...        2   594.4        1
44  MO2306-P-7300        73       0  ...        1   693.4        1
45  MO2306-P-7400        56       0  ...        1   794.0        1
46  MO2306-P-7500        28       0  ...        1   885.2        2
47  MO2306-P-7600        72       0  ...        1   992.4        1
48  MO2306-P-7700        30       0  ...        1  1095.8        1
49  MO2306-P-7800        77       0  ...        1  1196.0        1
50  MO2306-P-8000        61       0  ...        1  1395.8        1
51  MO2306-P-8200       148       0  ...        1  1596.0        1
```

上证50股指期权

| 名称         | 类型      | 描述  |
|------------|---------|-----|
| instrument | object  | 期权  |
| position   | float64 |     |
| volume     | float64 |     |
| lastprice  | float64 |     |
| updown     | float64 |     |
| bprice     | float64 |     |
| bamount    | float64 |     |
| sprice     | float64 |     |
| samount    | float64 |     |

接口示例

```python
import akshare as ak

option_finance_board_df = ak.option_finance_board(symbol="上证50股指期权", end_month="2306")
print(option_finance_board_df)
```

数据示例

```
       instrument  position  volume  ...  bamount  sprice  samount
0   HO2306-C-2225         0       0  ...        1   300.0        1
1   HO2306-C-2250         6       0  ...        1   275.0        1
2   HO2306-C-2275         6       0  ...        1   249.2        1
3   HO2306-C-2300        54       2  ...        1   224.4        2
4   HO2306-C-2325        10       0  ...        1   199.4        1
5   HO2306-C-2350        42       0  ...        1   174.6        1
6   HO2306-C-2375        49      38  ...        1   150.2        1
7   HO2306-C-2400        80      47  ...        1   126.0        1
8   HO2306-C-2425       206     159  ...        1   101.2        1
9   HO2306-C-2450       498     650  ...        1    79.4        1
10  HO2306-C-2475       672     636  ...        1    59.6        1
11  HO2306-C-2500      1814    3399  ...        4    41.8        1
12  HO2306-C-2550      3060    4613  ...        1    18.6        2
13  HO2306-C-2600      5316    2978  ...        8     7.4        5
14  HO2306-C-2650      5612    1086  ...       27     2.8        5
15  HO2306-C-2700      6334    1357  ...        8     1.4       24
16  HO2306-C-2750      3295     449  ...        1     0.8       42
17  HO2306-C-2800      3100     133  ...        3     0.6       57
18  HO2306-C-2850      1294       8  ...       17     0.6        8
19  HO2306-C-2900       900      41  ...       51     0.6       19
20  HO2306-C-2950       481      66  ...       14     0.4        1
21  HO2306-C-3000      1095      62  ...       36     0.6       49
22  HO2306-C-3050       552       0  ...       14     0.4        2
23  HO2306-C-3100       663     102  ...       18     0.4       41
24  HO2306-C-3200       831      60  ...       14     0.4        2
25  HO2306-P-2225        14       6  ...       13     0.4        2
26  HO2306-P-2250        30       0  ...       14     0.4        2
27  HO2306-P-2275        61      12  ...       13     0.4        2
28  HO2306-P-2300       228      32  ...        5     0.6        7
29  HO2306-P-2325       153      51  ...        2     0.6        3
30  HO2306-P-2350       489      51  ...       47     0.8        7
31  HO2306-P-2375       505     157  ...       27     1.0        1
32  HO2306-P-2400      1055     369  ...       14     2.0        4
33  HO2306-P-2425      1114     361  ...        5     3.6        8
34  HO2306-P-2450      2034    1590  ...        8     6.8        5
35  HO2306-P-2475      1448    1996  ...        1    11.8        2
36  HO2306-P-2500      2125    5050  ...        4    20.0        3
37  HO2306-P-2550      1711    2878  ...        6    47.4        1
38  HO2306-P-2600      1525     561  ...        1    87.2        1
39  HO2306-P-2650      1137     158  ...        2   134.2        1
40  HO2306-P-2700       913      73  ...        2   182.6        1
41  HO2306-P-2750       399       4  ...        3   229.0        1
42  HO2306-P-2800       389       3  ...        1   283.2        1
43  HO2306-P-2850       160       0  ...        1   333.4        1
44  HO2306-P-2900        95       0  ...        1   383.2        1
45  HO2306-P-2950        47       0  ...        1   433.6        1
46  HO2306-P-3000        73       0  ...        1   483.6        1
47  HO2306-P-3050        28       0  ...        1   533.6        1
48  HO2306-P-3100        46       0  ...        1   583.4        1
49  HO2306-P-3200        58       0  ...        1   684.0        1
```

#### 风险指标-上海证券交易所

接口: option_risk_indicator_sse

目标地址: http://www.sse.com.cn/assortment/options/risk/

描述: 上海证券交易所-产品-股票期权-期权风险指标数据

限量: 单次返回指定 date 的数据

输入参数

| 名称   | 类型  | 描述                                  |
|------|-----|-------------------------------------|
| date | str | date="20240626"; 交易日; 从 20150209 开始 |

输出参数

| 名称              | 类型      | 描述  |
|-----------------|---------|-----|
| TRADE_DATE      | object  | -   |
| SECURITY_ID     | object  | -   |
| CONTRACT_ID     | object  | -   |
| CONTRACT_SYMBOL | object  | -   |
| DELTA_VALUE     | float64 | -   |
| THETA_VALUE     | float64 | -   |
| GAMMA_VALUE     | float64 | -   |
| VEGA_VALUE      | float64 | -   |
| RHO_VALUE       | float64 | -   |
| IMPLC_VOLATLTY  | float64 | -   |

接口示例

```python
import akshare as ak

option_risk_indicator_sse_df = ak.option_risk_indicator_sse(date="20240626")
print(option_risk_indicator_sse_df)
```

数据示例

```
     TRADE_DATE SECURITY_ID  ... RHO_VALUE IMPLC_VOLATLTY
0    2024-06-26    10007437  ...     0.163          0.182
1    2024-06-26    10007425  ...     0.164          0.149
2    2024-06-26    10007333  ...     0.152          0.141
3    2024-06-26    10007334  ...     0.128          0.131
4    2024-06-26    10007335  ...     0.089          0.129
..          ...         ...  ...       ...            ...
391  2024-06-26    10007225  ...    -0.253          0.279
392  2024-06-26    10007226  ...    -0.308          0.306
393  2024-06-26    10007227  ...    -0.353          0.336
394  2024-06-26    10007228  ...    -0.392          0.367
395  2024-06-26    10007238  ...    -0.426          0.401
[396 rows x 10 columns]
```

#### 每日统计-上海证券交易所

接口: option_daily_stats_sse

目标地址: http://www.sse.com.cn/assortment/options/date/

描述: 上海证券交易所-产品-股票期权-每日统计

限量: 单次返回指定 date 的数据

输入参数

| 名称   | 类型  | 描述                   |
|------|-----|----------------------|
| date | str | date="20240626"; 交易日 |

输出参数

| 名称       | 类型           | 描述       |
|----------|--------------|----------|
| 合约标的代码   | object       | -        |
| 合约标的名称   | object       | -        |
| 合约数量     | int64        | -        |
| 总成交额     | int64        | 注意单位: 万元 |
| 总成交量     | int64        | 注意单位: 张  |
| 认购成交量    | int64        | 注意单位: 张  |
| 认沽成交量    | int64        | 注意单位: 张  |
| 认沽/认购    | float64      | 注意单位: %  |
| 未平仓合约总数  | int64        | -        |
| 未平仓认购合约数 | floaint64t64 | -        |
| 未平仓认沽合约数 | int64        | -        |

接口示例

```python
import akshare as ak

option_daily_stats_sse_df = ak.option_daily_stats_sse(date="20240626")
print(option_daily_stats_sse_df)
```

数据示例

```
   合约标的代码 合约标的名称 合约数量 总成交额  ... 未平仓合约总数  未平仓认购合约数  未平仓认沽合约数   交易日
0  510050   上证50ETF   126   47808  ...  1162840    623212    539628  2024-06-26
1  510300  沪深300ETF   126   60857  ...   963032    540989    422043  2024-06-26
2  510500  中证500ETF   178  127869  ...   803581    434857    368724  2024-06-26
3  588000   科创50ETF    92   15273  ...   790569    526095    264474  2024-06-26
4  588080  科创板50ETF    92    7633  ...   377438    232943    144495  2024-06-26
[5 rows x 12 columns]
```

#### 每日统计-深圳证券交易所

接口: option_daily_stats_szse

目标地址: https://investor.szse.cn/market/option/day/index.html

描述: 深圳证券交易所-市场数据-期权数据-日度概况

限量: 单次返回指定 date 的数据

输入参数

| 名称   | 类型  | 描述                   |
|------|-----|----------------------|
| date | str | date="20240626"; 交易日 |

输出参数

| 名称       | 类型      | 描述      |
|----------|---------|---------|
| 合约标的代码   | object  | -       |
| 合约标的名称   | object  | -       |
| 成交量      | int64   | 注意单位: 张 |
| 认购成交量    | int64   | 注意单位: 张 |
| 认沽成交量    | int64   | 注意单位: 张 |
| 认沽/认购持仓比 | float64 | 注意单位: % |
| 未平仓合约总数  | int64   | 注意单位: 张 |
| 未平仓认购合约数 | int64   | 注意单位: 张 |
| 未平仓认沽合约数 | int64   | 注意单位: 张 |
| 交易日      | object  | -       |


接口示例

```python
import akshare as ak

option_daily_stats_szse_df = ak.option_daily_stats_szse(date="20240626")
print(option_daily_stats_szse_df)
```

数据示例

```
   合约标的代码 合约标的名称 成交量 认购成交量  ... 未平仓合约总数 未平仓认购合约数 未平仓认沽合约数  交易日
0  159901  深证100ETF    87949   47218  ...    89303     45316     43987  2024-06-26
1  159915    创业板ETF  1059560  578092  ...   772281    429029    343252  2024-06-26
2  159919  沪深300ETF   249020  122046  ...   178740     96555     82185  2024-06-26
3  159922  中证500ETF   313967  155196  ...   206706    113215     93491  2024-06-26
[4 rows x 10 columns]
```

### 金融期权-新浪

#### 中金所

##### 上证50指数列表

接口: option_cffex_sz50_list_sina

目标地址: https://stock.finance.sina.com.cn/futures/view/optionsCffexDP.php/ho/cffex

描述: 中金所-上证50指数-所有合约, 返回的第一个合约为主力合约

限量: 单次返回所有合约

输入参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| -   | -   | -   |

输出参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| -   | -   | -   |

接口示例

```python
import akshare as ak

option_cffex_sz50_list_sina_df = ak.option_cffex_sz50_list_sina()
print(option_cffex_sz50_list_sina_df)
```

数据示例

```
{'上证50指数': ['ho2301', 'ho2303', 'ho2302', 'ho2306', 'ho2312', 'ho2309']}
```

##### 沪深300指数列表

接口: option_cffex_hs300_list_sina

目标地址: https://stock.finance.sina.com.cn/futures/view/optionsCffexDP.php

描述: 中金所-沪深300指数-所有合约, 返回的第一个合约为主力合约

限量: 单次返回所有合约

输入参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| -   | -   | -   |

输出参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| -   | -   | -   |

接口示例

```python
import akshare as ak

option_cffex_hs300_list_sina_df = ak.option_cffex_hs300_list_sina()
print(option_cffex_hs300_list_sina_df)
```

数据示例

```
{'沪深300指数': ['io2003', 'io2002', 'io2004', 'io2006', 'io2012', 'io2009']}
```

##### 中证1000指数列表

接口: option_cffex_zz1000_list_sina

目标地址: https://stock.finance.sina.com.cn/futures/view/optionsCffexDP.php

描述: 中金所-中证1000指数-所有合约, 返回的第一个合约为主力合约

限量: 单次返回所有合约

输入参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| -   | -   | -   |

输出参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| -   | -   | -   |

接口示例

```python
import akshare as ak

option_cffex_zz1000_list_sina_df = ak.option_cffex_zz1000_list_sina()
print(option_cffex_zz1000_list_sina_df)
```

数据示例

```
{'中证1000指数': ['mo2208', 'mo2209', 'mo2212', 'mo2210', 'mo2306', 'mo2303']}
```

##### 实时行情-上证50指数

接口: option_cffex_sz50_spot_sina

目标地址: https://stock.finance.sina.com.cn/futures/view/optionsCffexDP.php/ho/cffex

描述: 新浪财经-中金所-上证50指数-指定合约-实时行情

限量: 单次返回指定合约的实时行情

输入参数

| 名称     | 类型  | 描述              |
|--------|-----|-----------------|
| symbol | str | symbol="ho2303" |

输出参数

| 名称       | 类型      | 描述     |
|----------|---------|--------|
| 看涨合约-买量  | int64   | -      |
| 看涨合约-买价  | float64 | -      |
| 看涨合约-最新价 | float64 | -      |
| 看涨合约-卖价  | float   | -      |
| 看涨合约-卖量  | int64   | -      |
| 看涨合约-持仓量 | int64   | -      |
| 看涨合约-涨跌  | float64 | -      |
| 行权价      | int64   | -      |
| 看涨合约-标识  | object  | 看涨合约代码 |
| 看跌合约-买量  | int64   | -      |
| 看跌合约-买价  | float64 | -      |
| 看跌合约-最新价 | float64 | -      |
| 看跌合约-卖价  | float64 | -      |
| 看跌合约-卖量  | int64   | -      |
| 看跌合约-持仓量 | int64   | -      |
| 看跌合约-涨跌  | float64 | -      |
| 看跌合约-标识  | object  | 看跌合约代码 |

接口示例

```python
import akshare as ak

option_cffex_sz50_spot_sina_df = ak.option_cffex_sz50_spot_sina(symbol="ho2303")
print(option_cffex_sz50_spot_sina_df)
```

数据示例

```
    看涨合约-买量  看涨合约-买价  看涨合约-最新价  ...  看跌合约-持仓量  看跌合约-涨跌      看跌合约-标识
0         1    301.2     318.4  ...        82    10.71  ho2303P2325
1         6    302.4     306.0  ...        86    -7.89  ho2303P2350
2         1    270.8     276.2  ...        67   -19.67  ho2303P2375
3         1    265.6     269.8  ...        45    14.67  ho2303P2400
4         1    222.2     219.0  ...        18    -5.06  ho2303P2425
5         1    225.2     234.6  ...        29    -3.09  ho2303P2450
6         1    184.0     188.0  ...        13   -14.40  ho2303P2475
7         1    184.2     178.0  ...        50    19.02  ho2303P2500
8         1    148.6     157.0  ...        42    -5.96  ho2303P2550
9         1    117.0     115.8  ...       117    -9.39  ho2303P2600
10        1     91.2      94.4  ...        74    -0.68  ho2303P2650
11       28     69.2      74.0  ...       670    -5.25  ho2303P2700
12        1     52.0      53.0  ...        11    -3.49  ho2303P2750
13        1     38.0      38.8  ...         9    -2.91  ho2303P2800
14       22     26.8      28.0  ...        14     9.94  ho2303P2850
15       32     19.0      20.4  ...         3    -5.88  ho2303P2900
16       10     13.6      13.6  ...         6    -3.05  ho2303P2950
17        6     10.0      10.4  ...         6    15.58  ho2303P3000
```

##### 实时行情-沪深300指数

接口: option_cffex_hs300_spot_sina

目标地址: https://stock.finance.sina.com.cn/futures/view/optionsCffexDP.php

描述: 新浪财经-中金所-沪深300指数-指定合约-实时行情

限量: 单次返回指定合约的实时行情

输入参数

| 名称     | 类型  | 描述              |
|--------|-----|-----------------|
| symbol | str | symbol="io2104" |

输出参数

| 名称       | 类型      | 描述     |
|----------|---------|--------|
| 看涨合约-买量  | int64   | -      |
| 看涨合约-买价  | float64 | -      |
| 看涨合约-最新价 | float64 | -      |
| 看涨合约-卖价  | float   | -      |
| 看涨合约-卖量  | int64   | -      |
| 看涨合约-持仓量 | int64   | -      |
| 看涨合约-涨跌  | float64 | -      |
| 行权价      | int64   | -      |
| 看涨合约-标识  | object  | 看涨合约代码 |
| 看跌合约-买量  | int64   | -      |
| 看跌合约-买价  | float64 | -      |
| 看跌合约-最新价 | float64 | -      |
| 看跌合约-卖价  | float64 | -      |
| 看跌合约-卖量  | int64   | -      |
| 看跌合约-持仓量 | int64   | -      |
| 看跌合约-涨跌  | float64 | -      |
| 看跌合约-标识  | object  | 看跌合约代码 |

接口示例

```python
import akshare as ak

option_cffex_hs300_spot_sina_df = ak.option_cffex_hs300_spot_sina(symbol="io2104")
print(option_cffex_hs300_spot_sina_df)
```

数据示例

```
   看涨合约_买量  看涨合约_买价 看涨合约_最新价  看涨合约_卖价  ... 看跌合约_卖量 看跌合约_持仓量  看跌合约_涨跌      看跌合约_标识
0        1  587.800  597.000  598.200  ...      12     1150    50.00  io2202P4250
1        1  505.400  501.200  516.000  ...      41     2238    50.00  io2202P4300
2        1  489.400  498.600  494.400  ...       7     1715    58.33  io2202P4350
3        1  440.400  450.000  445.600  ...       3     2980    48.39  io2202P4400
4        1  361.000  369.600  365.600  ...       9     2261    43.24  io2202P4450
5        1  313.800  313.200  318.600  ...       6     1941    33.33  io2202P4500
6        1  267.600  271.400  272.800  ...       1     1485    38.33  io2202P4550
7        2  226.400  226.400  226.600  ...       1     1865    29.27  io2202P4600
8        1  185.000  185.200  187.200  ...       1     1251    30.00  io2202P4650
9        1  144.600  144.800  145.000  ...       3     2641    25.79  io2202P4700
10       8  112.000  113.400  114.200  ...       4     2134    21.72  io2202P4750
11       4   83.200   83.400   83.600  ...       1     4509    23.79  io2202P4800
12       2   59.000   59.200   59.600  ...       4     2765    20.69  io2202P4850
13       3   43.200   43.200   43.400  ...       1     3977    17.75  io2202P4900
14      13   29.200   29.200   31.000  ...       1     1773    18.36  io2202P4950
15      10   21.200   21.400   21.600  ...       1     2492    17.74  io2202P5000
16       5   10.400   10.400   10.600  ...       1     1873    11.90  io2202P5100
17       2    6.400    6.400    6.600  ...       1      858     9.74  io2202P5200
18       5    3.800    4.000    4.000  ...       1      635     4.63  io2202P5300
19       5    2.800    3.000    3.000  ...       1       49     7.23  io2202P5400
20       3    2.400    2.400    2.600  ...       1       71    -6.37  io2202P5500
21       2    1.600    1.800    2.000  ...       1      196     5.18  io2202P5600
```

##### 实时行情-中证1000指数

接口: option_cffex_zz1000_spot_sina

目标地址: https://stock.finance.sina.com.cn/futures/view/optionsCffexDP.php

描述: 新浪财经-中金所-中证1000指数-指定合约-实时行情

限量: 单次返回指定合约的实时行情

输入参数

| 名称     | 类型  | 描述              |
|--------|-----|-----------------|
| symbol | str | symbol="mo2208" |

输出参数

| 名称       | 类型      | 描述     |
|----------|---------|--------|
| 看涨合约-买量  | int64   | -      |
| 看涨合约-买价  | float64 | -      |
| 看涨合约-最新价 | float64 | -      |
| 看涨合约-卖价  | float   | -      |
| 看涨合约-卖量  | int64   | -      |
| 看涨合约-持仓量 | int64   | -      |
| 看涨合约-涨跌  | float64 | -      |
| 行权价      | int64   | -      |
| 看涨合约-标识  | object  | 看涨合约代码 |
| 看跌合约-买量  | int64   | -      |
| 看跌合约-买价  | float64 | -      |
| 看跌合约-最新价 | float64 | -      |
| 看跌合约-卖价  | float64 | -      |
| 看跌合约-卖量  | int64   | -      |
| 看跌合约-持仓量 | int64   | -      |
| 看跌合约-涨跌  | float64 | -      |
| 看跌合约-标识  | object  | 看跌合约代码 |

接口示例

```python
import akshare as ak

option_cffex_zz1000_spot_sina_df = ak.option_cffex_zz1000_spot_sina(symbol="mo2208")
print(option_cffex_zz1000_spot_sina_df)
```

数据示例

```
    看涨合约-买量  看涨合约-买价  看涨合约-最新价  ...  看跌合约-持仓量  看跌合约-涨跌      看跌合约-标识
0         1    760.4     783.2  ...       644   353.13  mo2208P6200
1         1    666.8     690.0  ...       719   304.65  mo2208P6300
2         1    575.0     644.0  ...       586   265.00  mo2208P6400
3         1    494.4     491.4  ...      1018   236.90  mo2208P6500
4         1    408.4     405.6  ...       829   210.34  mo2208P6600
5         1    329.8     321.2  ...       894   179.04  mo2208P6700
6         1    257.8     257.6  ...      1081   147.93  mo2208P6800
7         1    197.4     197.4  ...      1155   122.44  mo2208P6900
8         1    138.6     138.6  ...      1295   106.80  mo2208P7000
9         1     96.6      96.8  ...       846    87.83  mo2208P7100
10        1     63.8      63.8  ...       356    78.02  mo2208P7200
11        1     40.2      40.2  ...       146    64.20  mo2208P7300
12        2     26.0      26.0  ...       129    54.71  mo2208P7400
13        1     16.2      16.4  ...       104    49.04  mo2208P7500
14        1     10.6      10.6  ...        39    33.54  mo2208P7600
15        1      6.8       7.0  ...        41    21.32  mo2208P7700
16       14      5.0       5.2  ...        37    34.30  mo2208P7800
17        6      3.4       3.6  ...        21    30.39  mo2208P7900
```

##### 日频行情-上证50指数

接口: option_cffex_sz50_daily_sina

目标地址: https://stock.finance.sina.com.cn/futures/view/optionsCffexDP.php/ho/cffex

描述: 中金所-上证50指数-指定合约-日频行情

限量: 单次返回指定合约的日频行情

输入参数

| 名称     | 类型  | 描述                                                                                         |
|--------|-----|--------------------------------------------------------------------------------------------|
| symbol | str | symbol="ho2303P2350"; 具体合约代码(包括看涨和看跌标识), 可以通过 ak.option_cffex_sz50_spot_sina 中的 call-标识 获取 |

输出参数

| 名称     | 类型      | 描述  |
|--------|---------|-----|
| date   | object  | -   |
| open   | float64 | -   |
| high   | float64 | -   |
| low    | float64 | -   |
| close  | float64 | -   |
| volume | int64   | -   |

接口示例

```python
import akshare as ak

option_cffex_sz50_daily_sina_df = ak.option_cffex_sz50_daily_sina(symbol="ho2303P2350")
print(option_cffex_sz50_daily_sina_df)
```

数据示例

```
         date  open  high   low  close  volume
0  2022-12-21  16.8  16.8  16.8   16.8       6
1  2022-12-22  11.2  14.0  11.2   14.0      64
2  2022-12-23  14.0  17.8  14.0   16.0      14
3  2022-12-26  14.0  14.6  12.2   14.6      41
4  2022-12-27  11.2  11.4   9.6    9.6      62
5  2022-12-28   9.0  11.0   9.0   10.4     126
6  2022-12-29  10.4  13.0  10.2   10.8      63
7  2022-12-30   9.8  11.8   9.6    9.8      83
8  2023-01-03  10.6  12.0   7.4    7.6       4
```

##### 日频行情-沪深300指数

接口: option_cffex_hs300_daily_sina

目标地址: https://stock.finance.sina.com.cn/futures/view/optionsCffexDP.php

描述: 中金所-沪深300指数-指定合约-日频行情

限量: 单次返回指定合约的日频行情

输入参数

| 名称     | 类型  | 描述                                                                                          |
|--------|-----|---------------------------------------------------------------------------------------------|
| symbol | str | symbol="io2202P4350"; 具体合约代码(包括看涨和看跌标识), 可以通过 ak.option_cffex_hs300_spot_sina 中的 call-标识 获取 |

输出参数

| 名称     | 类型      | 描述  |
|--------|---------|-----|
| date   | object  | -   |
| open   | float64 | -   |
| high   | float64 | -   |
| low    | float64 | -   |
| close  | float64 | -   |
| volume | int64   | -   |

接口示例

```python
import akshare as ak

option_cffex_hs300_daily_sina_df = ak.option_cffex_hs300_daily_sina(symbol="io2004C4450")
print(option_cffex_hs300_daily_sina_df)
```

数据示例

```
          date  open  high   low  close  volume
0   2021-11-29  15.0  17.0  15.0   15.8      49
1   2021-11-30  14.6  18.8  14.6   18.6      36
2   2021-12-01  16.8  16.8  14.2   14.2      38
3   2021-12-02  16.0  16.0  12.6   13.6      98
4   2021-12-03  10.6  11.0   9.0    9.6     234
5   2021-12-06   9.4  11.4   8.2   10.8     112
6   2021-12-07  10.2  12.6  10.2   10.6     133
7   2021-12-08  10.6  11.2   8.4    8.4     215
8   2021-12-09   8.2  10.0   7.0    7.0     241
9   2021-12-10   8.6  10.0   6.0    6.0      95
10  2021-12-13   4.8   9.2   4.8    6.2     135
11  2021-12-14   6.6   8.4   5.2    5.2      92
12  2021-12-15   6.0   7.4   2.0    7.4     102
13  2021-12-16   7.2   7.6   6.0    6.0     104
14  2021-12-17   6.4   8.8   6.4    6.8      92
15  2021-12-20   7.4  10.6   7.2    9.8     188
16  2021-12-21   8.6   8.8   6.8    8.6     206
17  2021-12-22   8.2  10.4   7.6    9.6     120
18  2021-12-23   8.8   8.8   5.2    5.2     186
19  2021-12-24   5.2   6.0   4.8    5.6     240
20  2021-12-27   5.2   6.0   4.8    4.8      49
21  2021-12-28   4.8   5.0   4.0    4.0      29
22  2021-12-29   4.2   7.6   4.2    7.4      89
23  2021-12-30   7.2   7.2   5.2    0.0     119
24  2021-12-31   4.6   5.6   4.4    5.2     126
25  2022-01-04   4.6   7.2   4.6    5.2     180
26  2022-01-05   5.4   7.2   4.8    7.0     217
27  2022-01-06   7.2  14.0   7.2   10.4     274
28  2022-01-07   8.8   8.8   6.6    8.0     161
29  2022-01-10   7.2  10.6   6.6    8.0     351
30  2022-01-11   8.2  11.0   6.2   10.2     342
31  2022-01-12   8.8   8.8   5.4    5.4     440
32  2022-01-13   5.8  11.8   5.2   11.2     377
33  2022-01-14  11.2  16.6  10.8   14.4     862
34  2022-01-17  13.0  13.0   8.4    9.2     806
35  2022-01-18   8.8  10.4   6.4    6.6     420
36  2022-01-19   6.8   8.8   6.2    7.2     369
37  2022-01-20   7.0   7.2   4.6    4.8     366
38  2022-01-21   5.8   7.6   5.4    7.6     265
```

##### 日频行情-中证1000指数

接口: option_cffex_zz1000_daily_sina

目标地址: https://stock.finance.sina.com.cn/futures/view/optionsCffexDP.php

描述: 中金所-中证1000指数-指定合约-日频行情

限量: 单次返回指定合约的日频行情

输入参数

| 名称     | 类型  | 描述                                                                                           |
|--------|-----|----------------------------------------------------------------------------------------------|
| symbol | str | symbol="mo2208P6200"; 具体合约代码(包括看涨和看跌标识), 可以通过 ak.option_cffex_zz1000_spot_sina 中的 call-标识 获取 |

输出参数

| 名称     | 类型      | 描述  |
|--------|---------|-----|
| date   | object  | -   |
| open   | float64 | -   |
| high   | float64 | -   |
| low    | float64 | -   |
| close  | float64 | -   |
| volume | int64   | -   |

接口示例

```python
import akshare as ak

option_cffex_zz1000_daily_sina_df = ak.option_cffex_zz1000_daily_sina(symbol="mo2208P6200")
print(option_cffex_zz1000_daily_sina_df)
```

数据示例

```
         date  open  high  low  close  volume
0  2022-07-26  17.2  20.2  7.8    7.8     460
1  2022-07-27   7.8   8.2  6.4    6.8     475
2  2022-07-28   5.4   6.8  4.4    5.6     779
3  2022-07-29   5.4   8.8  4.6    8.4     462
4  2022-08-01   8.4  12.4  6.0    6.4     572
```

#### 上交所

##### 合约到期月份列表

接口: option_sse_list_sina

目标地址: https://stock.finance.sina.com.cn/futures/view/optionsCffexDP.php

描述: 获取期权-上交所-50ETF-合约到期月份列表

限量: 单次返回指定品种的到期月份列表

输入参数

| 名称       | 类型  | 描述                                  |
|----------|-----|-------------------------------------|
| symbol   | str | symbol="50ETF"; "50ETF" or "300ETF" |
| exchange | str | exchange="null"                     |

输出参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| -   | -   | -   |

接口示例

```python
import akshare as ak

option_sse_list_sina_df = ak.option_sse_list_sina(symbol="50ETF", exchange="null")
print(option_sse_list_sina_df)
```

数据示例

```
['202002', '202003', '202006', '202009']
```

##### 合约到期月份列表

接口: option_sse_expire_day_sina

目标地址: https://stock.finance.sina.com.cn/futures/view/optionsCffexDP.php

描述: 获取指定到期月份指定品种的剩余到期时间

限量: 单次返回指定品种的品种的剩余到期时间

输入参数

| 名称         | 类型  | 描述                                  |
|------------|-----|-------------------------------------|
| trade_date | str | trade_date="202002";                |
| symbol     | str | symbol="50ETF"; "50ETF" or "300ETF" |
| exchange   | str | exchange="null"                     |

输出参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| -   | -   | -   |

接口示例

```python
import akshare as ak

option_sse_expire_day_sina_df = ak.option_sse_expire_day_sina(trade_date="202002", symbol="50ETF", exchange="null")
print(option_sse_expire_day_sina_df)
```

数据示例

```
('2020-02-26', 3)
```

##### 所有合约的代码

接口: option_sse_codes_sina

目标地址: https://stock.finance.sina.com.cn/futures/view/optionsCffexDP.php

描述: 新浪期权-看涨看跌合约合约的代码

限量: 单次返回指定 symbol 合约的代码

输入参数

| 名称         | 类型  | 描述                                        |
|------------|-----|-------------------------------------------|
| symbol     | str | symbol="看涨期权"; choice of {"看涨期权", "看跌期权"} |
| trade_date | str | trade_date="202002";                      |
| underlying | str | underlying="510300"                       |

输出参数

| 名称   | 类型     | 描述  |
|------|--------|-----|
| 序号   | int64  | -   |
| 期权代码 | object | -   |

接口示例

```python
import akshare as ak

option_sse_codes_sina_df = ak.option_sse_codes_sina(trade_date="202002", underlying="510300")
print(option_sse_codes_sina_df)
```

数据示例

```
    序号   期权代码
0    1  10003887
1    2  10003765
2    3  10003709
3    4  10003766
4    5  10003710
5    6  10003767
6    7  10003711
7    8  10003768
8    9  10003712
9   10  10003769
10  11  10003713
11  12  10003770
12  13  10003714
13  14  10003771
14  15  10003715
15  16  10003772
16  17  10003716
17  18  10003773
18  19  10003717
19  20  10003821
20  21  10003829
```

##### 实时数据

接口: option_sse_spot_price_sina

目标地址: https://stock.finance.sina.com.cn/futures/view/optionsCffexDP.php

描述: 期权实时数据

限量: 单次返回期权实时数据

输入参数

| 名称     | 类型  | 描述                |
|--------|-----|-------------------|
| symbol | str | symbol="10002273" |

输出参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| 字段  | str | -   |
| 值   | str | -   |

接口示例

```python
import akshare as ak

option_sse_spot_price_sina_df = ak.option_sse_spot_price_sina(symbol="10002273")
print(option_sse_spot_price_sina_df)
```

数据示例

```
        字段                    值
0       买量                    1
1       买价               0.4518
2      最新价               0.4550
3       卖价               0.4590
4       卖量                    1
5      持仓量                  979
6       涨幅                -2.57
7      行权价               2.5000
8      昨收价               0.4585
9      开盘价               0.4550
10     涨停价               0.7637
11     跌停价               0.1703
12    申卖价五               0.4735
13    申卖量五                    1
14    申卖价四               0.4700
15    申卖量四                    1
16    申卖价三               0.4684
17    申卖量三                    1
18    申卖价二               0.4600
19    申卖量二                    2
20    申卖价一               0.4590
21    申卖量一                    1
22    申买价一               0.4518
23   申买量一                     1
24    申买价二               0.4500
25    申买量二                    3
26    申买价三               0.4467
27    申买量三                    1
28    申买价四               0.4426
29    申买量四                    1
30    申买价五               0.4419
31    申买量五                    1
32    行情时间  2020-02-21 14:56:45
33  主力合约标识                    0
34     状态码                 E 01
35  标的证券类型                  EBS
36    标的股票               510050
37  期权合约简称         50ETF购2月2500
38      振幅                 7.02
39     最高价               0.4799
40     最低价               0.4477
41     成交量                  626
42     成交额           2851394.00
```

##### 期权标的物的实时数据

接口: option_sse_underlying_spot_price_sina

目标地址: https://stock.finance.sina.com.cn/futures/view/optionsCffexDP.php

描述: 获取期权标的物的实时数据

限量: 单次返回期权标的物的实时数据

输入参数

| 名称     | 类型  | 描述                |
|--------|-----|-------------------|
| symbol | str | symbol="sh510300" |

输出参数

| 名称 | 类型     | 描述 |
|----|--------|----|
| 字段 | object | -  |
| 值  | object | -  |

接口示例

```python
import akshare as ak

option_sse_underlying_spot_price_sina_df = ak.option_sse_underlying_spot_price_sina(symbol="sh510300")
print(option_sse_underlying_spot_price_sina_df)
```

数据示例

```
       字段               值
0    证券简称          300ETF
1   今日开盘价           4.123
2   昨日收盘价           4.131
3   最近成交价           4.145
4   最高成交价           4.178
5   最低成交价           4.117
6     买入价           4.144
7     卖出价           4.146
8    成交数量       444470153
9    成交金额  1839049777.000
10   买数量一          364200
11   买价位一           4.144
12   买数量二          659700
13   买价位二           4.143
14   买数量三           82400
15   买价位三           4.142
16   买数量四            2600
17   买价位四           4.141
18   买数量五          864800
19   买价位五           4.140
20   卖数量一            2400
21   卖价位一           4.146
22   卖数量二          763100
23   卖价位二           4.147
24   卖数量三          556300
25   卖价位三           4.148
26   卖数量四           86500
27   卖价位四           4.149
28   卖数量五          351400
29   卖价位五           4.150
30   行情日期      2020-02-21
31   行情时间        15:00:00
32   停牌状态              00
```

##### 期权希腊字母信息表

接口: option_sse_greeks_sina

目标地址: https://stock.finance.sina.com.cn/futures/view/optionsCffexDP.php

描述: 新浪财经-期权希腊字母信息表

限量: 单次返回当前交易日的期权希腊字母信息表

输入参数

| 名称     | 类型  | 描述                |
|--------|-----|-------------------|
| symbol | str | symbol="10002273" |

输出参数

| 名称  | 类型     | 描述  |
|-----|--------|-----|
| 字段  | object | -   |
| 值   | object | -   |

接口示例

```python
import akshare as ak

option_sse_greeks_sina_df = ak.option_sse_greeks_sina(symbol="10002273")
print(option_sse_greeks_sina_df)
```

数据示例

```
        字段                  值
0   期权合约简称       50ETF购2月2500
1      成交量                626
2    Delta                  1
3    Gamma                  0
4    Theta               -0.1
5     Vega                  0
6    隐含波动率             0.0008
7      最高价             0.4799
8      最低价             0.4477
9     交易代码  510050C2002M02500
10     行权价             2.5000
11     最新价             0.4550
12    理论价值             0.4591
```

##### 期权行情分钟数据

接口: option_sse_minute_sina

目标地址: https://stock.finance.sina.com.cn/futures/view/optionsCffexDP.php

描述: 期权行情分钟数据, 只能返还当天的分钟数据

限量: 单次返回期权行情分钟数据

输入参数

| 名称     | 类型  | 描述                |
|--------|-----|-------------------|
| symbol | str | symbol="10002273" |

输出参数

| 名称  | 类型      | 描述    |
|-----|---------|-------|
| 日期  | object  | 当前交易日 |
| 时间  | object  | -     |
| 价格  | float64 | -     |
| 成交  | int64   | -     |
| 持仓  | int64   | -     |
| 均价  | float64 | -     |

接口示例

```python
import akshare as ak

option_sse_minute_sina_df = ak.option_sse_minute_sina(symbol="10003720")
print(option_sse_minute_sina_df)
```

数据示例

```
      日期        时间      价格  成交     持仓      均价
0    2022-01-21  09:26:00  0.0000   0      0  0.0000
1    2022-01-21  09:27:00  0.0000   0      0  0.0000
2    2022-01-21  09:28:00  0.0000   0      0  0.0000
3    2022-01-21  09:29:00  0.0000   0      0  0.0000
4    2022-01-21  09:30:00  0.0011  14  32758  0.0011
..          ...       ...     ...  ..    ...     ...
330  2022-01-21  14:56:00  0.0009  40  31816  0.0010
331  2022-01-21  14:57:00  0.0008   0  31866  0.0010
332  2022-01-21  14:58:00  0.0008   0      0  0.0010
333  2022-01-21  14:59:00  0.0008   0      0  0.0010
334  2022-01-21  15:00:00  0.0008   0  31866  0.0010
```

##### 期权行情日数据

接口: option_sse_daily_sina

目标地址: https://stock.finance.sina.com.cn/futures/view/optionsCffexDP.php

描述: 期权行情日数据

限量: 单次返回期权行情日数据

输入参数

| 名称     | 类型  | 描述                |
|--------|-----|-------------------|
| symbol | str | symbol="10002273" |

输出参数

| 名称  | 类型      | 描述  |
|-----|---------|-----|
| 时间  | object  | -   |
| 开盘  | float64 | -   |
| 最高  | float64 | -   |
| 最低  | float64 | -   |
| 收盘  | float64 | -   |
| 成交  | int64   | -   |

接口示例

```python
import akshare as ak

option_sse_daily_sina_df = ak.option_sse_daily_sina(symbol="10002273")
print(option_sse_daily_sina_df)
```

数据示例

```
     日期      开盘      最高      最低      收盘        成交
0   2020-02-04  0.2200  0.2870  0.2151  0.2850  13729899
1   2020-02-05  0.2868  0.3159  0.2711  0.3010  10716172
2   2020-02-06  0.3050  0.3581  0.2939  0.3420   8849637
3   2020-02-07  0.3265  0.3416  0.3110  0.3410   3538617
4   2020-02-10  0.3251  0.3389  0.3117  0.3390   3569910
5   2020-02-11  0.3402  0.3737  0.3391  0.3621   4518172
6   2020-02-12  0.3600  0.3776  0.3550  0.3721   1020605
7   2020-02-13  0.3756  0.3898  0.3556  0.3610   1956981
8   2020-02-14  0.3662  0.3848  0.3626  0.3833   1130476
9   2020-02-17  0.3800  0.4415  0.3800  0.4359   2606707
10  2020-02-18  0.4250  0.4334  0.4042  0.4121    827370
11  2020-02-19  0.4066  0.4300  0.4051  0.4122    720243
12  2020-02-20  0.4200  0.4666  0.4050  0.4585   2621812
13  2020-02-21  0.4550  0.4799  0.4477  0.4550   1892291
```

##### 期权行情分时数据-新浪

接口: option_finance_minute_sina

目标地址: https://stock.finance.sina.com.cn/option/quotes.html

描述: 新浪财经-金融期权-股票期权分时行情数据

限量: 单次返回指定期权的分时行情数据

输入参数

| 名称     | 类型  | 描述                                                      |
|--------|-----|---------------------------------------------------------|
| symbol | str | symbol="10002530"; 通过 **ak.option_sse_codes_sina()** 获取 |

输出参数

| 名称            | 类型      | 描述  |
|---------------|---------|-----|
| date          | object  | -   |
| time          | object  | -   |
| price         | float64 | -   |
| average_price | float64 | -   |
| volume        | int64   | -   |

接口示例

```python
import akshare as ak

option_finance_minute_sina_df = ak.option_finance_minute_sina(symbol="10002415")
print(option_finance_minute_sina_df)
```

数据示例

```
            date      time   price average_price volume
0     2020-07-13  09:26:00  0.0000        0.0000      0
1     2020-07-13  09:27:00  0.0000        0.0000      0
2     2020-07-13  09:28:00  0.0000        0.0000      0
3     2020-07-13  09:29:00  0.0000        0.0000      0
4     2020-07-13  09:30:00  0.0000        0.0000      0
          ...       ...     ...           ...    ...
1219  2020-07-17  14:56:00  1.3699        1.3677      0
1220  2020-07-17  14:57:00  1.3699        1.3677      0
1221  2020-07-17  14:58:00  1.3699        1.3677      0
1222  2020-07-17  14:59:00  1.3699        1.3677      0
1223  2020-07-17  15:00:00  1.3757        1.3679      1
```

##### 期权行情分时数据-东财

接口: option_minute_em

目标地址: https://wap.eastmoney.com/quote/stock/151.cu2404P61000.html

描述: 东方财富网-行情中心-期权市场-分时行情

限量: 单次返回指定 symbol 的分时行情数据; 只能获取近期合约的数据

输入参数

| 名称     | 类型  | 描述                                                       |
|--------|-----|----------------------------------------------------------|
| symbol | str | symbol="MO2402-C-5400"; 通过 **ak.option_current_em()** 获取 |

输出参数

| 名称     | 类型     | 描述      |
|--------|--------|---------|
| time   | object | -       |
| close  | int64  | -       |
| high   | int64  | -       |
| low    | int64  | -       |
| volume | int64  | 注意单位: 手 |
| amount | int64  | -       |

接口示例

```python
import akshare as ak

option_minute_em_df = ak.option_minute_em(symbol="MO2402-C-5400")
print(option_minute_em_df)
```

数据示例

```
                 time  close  high   low  volume    amount
0    2024-01-31 09:30   16.4  16.4  16.4       0       0.0
1    2024-01-31 09:31   20.8  21.6  16.2     103  193580.0
2    2024-01-31 09:32   20.8  21.6  20.0      63  130940.0
3    2024-01-31 09:33   21.2  21.8  20.4      97  206420.0
4    2024-01-31 09:34   21.0  21.0  20.0      63  130580.0
..                ...    ...   ...   ...     ...       ...
236  2024-01-31 14:56    9.2   9.8   9.2      19   18060.0
237  2024-01-31 14:57    9.4   9.4   9.2      25   23380.0
238  2024-01-31 14:58    9.4   9.4   9.4       0       0.0
239  2024-01-31 14:59    9.4   9.4   9.4       0       0.0
240  2024-01-31 15:00    9.2   9.2   9.2      64   92200.0
[241 rows x 6 columns]
```

### 期权实时行情-东方财富

接口: option_current_em

目标地址: https://quote.eastmoney.com/center/qqsc.html

描述: 东方财富网-行情中心-期权市场

限量: 单次返回全部合约的实时行情

输入参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| -   | -   | -   |

输出参数

| 名称   | 类型      | 描述      |
|------|---------|---------|
| 代码   | object  | -       |
| 名称   | object  | -       |
| 最新价  | float64 | -       |
| 涨跌额  | float64 | -       |
| 涨跌幅  | float64 | 注意单位: % |
| 成交量  | float64 | -       |
| 成交额  | float64 | -       |
| 持仓量  | float64 | -       |
| 行权价  | float64 | -       |
| 剩余日  | float64 | -       |
| 日增   | float64 | -       |
| 昨结   | float64 | -       |
| 今开   | float64 | -       |
| 市场标识 | int64   | -       |

接口示例

```python
import akshare as ak

option_current_em_df = ak.option_current_em()
print(option_current_em_df)
```

数据示例

```
          序号     代码                名称   最新价  ...      日增    昨结    今开  市场标识
0          1    pg2406p3500     LPG24年06月沽3500  19.0  ...    16.0   0.2   9.8   140
1          2     b2406c4500      豆二24年06月购4500  40.5  ...     1.0   0.5  40.5   140
2          3    pg2406p3600     LPG24年06月沽3600  11.8  ...     0.0   0.2  11.8   140
3          4    pg2406p3550     LPG24年06月沽3550   9.6  ...     0.0   0.2   9.6   140
4          5    pg2403p3800     LPG24年03月沽3800   9.2  ...   511.0   0.2   1.2   140
      ...            ...                ...   ...  ...     ...   ...   ...   ...
16835  16836  MO2402-C-5500  中证1000购24年02月5500   5.8  ...   381.0  11.4  10.0   221
16836  16837  MO2402-C-5100  中证1000购24年02月5100  38.2  ...   982.0  75.6  71.0   221
16837  16838  MO2402-C-5200  中证1000购24年02月5200  24.6  ...  1606.0  50.0  45.2   221
16838  16839  MO2402-C-5600  中证1000购24年02月5600   3.2  ...   448.0   6.6   6.6   221
16839  16840  MO2402-C-5400  中证1000购24年02月5400   9.2  ...    32.0  19.0  16.4   221
[16840 rows x 15 columns]
```

### 期权龙虎榜-金融期权

接口: option_lhb_em

目标地址: https://data.eastmoney.com/other/qqlhb.html

描述: 东方财富网-数据中心-期货期权-期权龙虎榜单-金融期权

限量: 单次返回指定 symbol, indicator 和 trade_date 的所有数据

输入参数

| 名称         | 类型  | 描述                                                                                                  |
|------------|-----|-----------------------------------------------------------------------------------------------------|
| symbol     | str | symbol="510050"; choice of {"510050", "510300", "159919"}                                           |
| indicator  | str | indicator="期权交易情况-认沽交易量"; choice of {"期权交易情况-认沽交易量","期权持仓情况-认沽持仓量", "期权交易情况-认购交易量", "期权持仓情况-认购持仓量"} |
| trade_date | str | trade_date="20220121"                                                                               |

输出参数

| 名称      | 类型      | 描述                   |
|---------|---------|----------------------|
| 交易类型    | object  | -                    |
| 交易日期    | object  | -                    |
| 证券代码    | object  | -                    |
| 标的名称    | object  | -                    |
| 名次      | float64 | -                    |
| 机构      | object  | -                    |
| XX量     | float64 | 注意: 根据 indicator 而变化 |
| 增减      | float64 | -                    |
| 净XX量    | float64 | 注意: 根据 indicator 而变化 |
| 占总交易量比例 | float64 | -                    |

接口示例

```python
import akshare as ak

option_lhb_em_df = ak.option_lhb_em(symbol="510300", indicator="期权持仓情况-认沽持仓量", trade_date="20220121")
print(option_lhb_em_df)
```

数据示例

```
    交易类型        交易日期    证券代码    标的名称  ...      持仓量    增减   净持仓量  占总交易量比例
0  认沽持仓量  2022-01-21  510300  300ETF  ...   173021.0 -11602.0   -4494.0  0.083968
1  认沽持仓量  2022-01-21  510300  300ETF  ...   153486.0  14177.0  -35428.0  0.074487
2  认沽持仓量  2022-01-21  510300  300ETF  ...   127108.0  -4549.0  -29379.0  0.061686
3  认沽持仓量  2022-01-21  510300  300ETF  ...   125984.0    -20.0       NaN  0.061140
4  认沽持仓量  2022-01-21  510300  300ETF  ...   121145.0  -2193.0       NaN  0.058792
5  认沽持仓量  2022-01-21  510300  300ETF  ...  2060568.0 -25036.0 -350814.0  1.000000
6  认沽持仓量  2022-01-21  510300  300ETF  ...   700744.0  -4187.0  -65535.0  0.340073
```

### 期权价值分析-金融期权

接口: option_value_analysis_em

目标地址: https://data.eastmoney.com/other/valueAnal.html

描述: 东方财富网-数据中心-特色数据-期权价值分析

限量: 单次返回所有数据

输入参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| -   | -   | -   |

输出参数

| 名称       | 类型      | 描述                                                        |
|----------|---------|-----------------------------------------------------------|
| 期权代码     | object  | -                                                         |
| 期权名称     | object  | -                                                         |
| 最新价      | float64 | -                                                         |
| 时间价值     | float64 | 注意: 指在期权剩余有效期内，合约标的价格变动有利于期权权利方的可能性。时间价值和内在价值共同构成期权的总价值。  |
| 内在价值     | float64 | 注意: 指假如期权立即履行时该期权的价值，只能为正数或者为零。内在价值与时间价值共同构成期权的总价值。       |
| 隐含波动率    | float64 | 注意: 指期权市场投资者在进行期权交易时对未来波动率的认识，且该认识已反映在期权的定价过程中。           |
| 理论价格     | float64 | 注意: 采用 Black-Scholes 期权定价模型，推导出的期权理论价格。                   |
| 标的名称     | object  | -                                                         |
| 标的最新价    | float64 | -                                                         |
| 标的近一年波动率 | float64 | 注意: 指一种衡量股票价格变化剧烈程度的指标，一般用百分数表示。股价波动率与认购期权、认沽期权价值均为正相关关系。 |
| 到期日      | object  | -                                                         |

接口示例

```python
import akshare as ak

option_value_analysis_em_df = ak.option_value_analysis_em()
print(option_value_analysis_em_df)
```

数据示例

```
     期权代码       期权名称     最新价  ...  标的最新价  标的近一年波动率  到期日
0    10008904  500ETF沽9月7000  1.1278  ...  6.054     26.54  2025-09-24
1    10008903  500ETF购9月7000  0.0863  ...  6.054     26.54  2025-09-24
2    10008896   50ETF沽9月2900  0.2159  ...  2.744     19.41  2025-09-24
3    10008895   50ETF购9月2900  0.0998  ...  2.744     19.41  2025-09-24
4    10008894    科创50沽9月1300  0.2361  ...  1.157     41.15  2025-09-24
..        ...            ...     ...  ...    ...       ...         ...
659  10008577   50ETF购2月2750  0.0153  ...  2.744     19.41  2025-02-26
660  10008576   50ETF购2月2700  0.0479  ...  2.744     19.41  2025-02-26
661  10008575   50ETF购2月2650  0.0946  ...  2.744     19.41  2025-02-26
662  10008574   50ETF购2月2600  0.1445  ...  2.744     19.41  2025-02-26
663  10008573   50ETF购2月2550  0.1933  ...  2.744     19.41  2025-02-26
[664 rows x 11 columns]
```

### 期权风险分析-金融期权

接口: option_risk_analysis_em

目标地址: https://data.eastmoney.com/other/riskanal.html

描述: 东方财富网-数据中心-特色数据-期权风险分析

限量: 单次返回所有数据

输入参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| -   | -   | -   |

输出参数

| 名称     | 类型      | 描述                                                                                 |
|--------|---------|------------------------------------------------------------------------------------|
| 期权代码   | object  | -                                                                                  |
| 期权名称   | object  | -                                                                                  |
| 最新价    | float64 | -                                                                                  |
| 涨跌幅    | float64 | 注意单位: %;                                                                           |
| 杠杆比率   | float64 | 注意: 杠杆比率=标价价格÷期权价格，杠杆反映投资标的相对投资期权的成本比例。                                            |
| 实际杠杆比率 | float64 | 注意: 实际杠杆比率=对冲值×杠杆比率，透过实际杠杆比率，投资者可知道当标的涨跌1%时，期权的理论价格会变动多少个百分点。                      |
| Delta  | float64 | 注意: 指期权标的股票价格变化对期权价格的影响程度。Delta=期权价格变化/期权标的股票价格变化。股票价格与认购期权价值为正相关关系，与认沽期权价值为负相关关系。 |
| Gamma  | float64 | 注意: 指期权标的股票价格变化对Delta值的影响程度。Gamma=Delta的变化／期权标的股票价格变化。                             |
| Vega   | float64 | 注意: 指合约标的证券价格波动率变化对期权价值的影响程度。Vega=期权价值变化/波动率的变化。波动率与认购、认沽期权价值均为正相关关系。              |
| Rho    | float64 | 注意: 指无风险利率变化对期权价格的影响程度。Rho=期权价格的变化／无风险利率的变化。市场无风险利率与认购期权价值为正相关，与认沽期权为负相关。          |
| Theta  | float64 | 注意: 指到期时间变化对期权价值的影响程度。Theta=期权价值变化/到期时间变化。到期期限与认购、认沽期权价值均为正相关关系。                   |
| 到期日    | object  | -                                                                                  |

接口示例

```python
import akshare as ak

option_risk_analysis_em_df = ak.option_risk_analysis_em()
print(option_risk_analysis_em_df)
```

数据示例

```
       期权代码        期权名称     最新价  ...     Rho   Theta         到期日
0    10008665     科创50购2月1300  0.0024  ...  0.0003 -0.0692  2025-02-26
1    10008653    科创板50购2月1250  0.0027  ...  0.0004 -0.0891  2025-02-26
2    10008633     科创50购2月1150  0.0216  ...  0.0101 -0.7413  2025-02-26
3    10008634     科创50购2月1200  0.0087  ...  0.0047 -0.5992  2025-02-26
4    10008651    科创板50购2月1150  0.0105  ...  0.0059 -0.6495  2025-02-26
..        ...             ...     ...  ...     ...     ...         ...
659  10008583    50ETF沽2月2600  0.0001  ... -0.0006 -0.0748  2025-02-26
660  10008621  500ETF沽2月5657A  0.0006  ... -0.0021 -0.3251  2025-02-26
661  10008658    科创板50沽2月1050  0.0013  ... -0.0017 -0.3033  2025-02-26
662  10008641     科创50沽2月1100  0.0022  ... -0.0031 -0.4518  2025-02-26
663  10008744   500ETF沽2月5500  0.0001  ... -0.0001 -0.0435  2025-02-26
[664 rows x 12 columns]
```

### 期权折溢价-金融期权

接口: option_premium_analysis_em

目标地址: https://data.eastmoney.com/other/premium.html

描述: 东方财富网-数据中心-特色数据-期权折溢价

限量: 单次返回所有数据

输入参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| -   | -   | -   |

输出参数

| 名称    | 类型      | 描述                                                  |
|-------|---------|-----------------------------------------------------|
| 期权代码  | object  | -                                                   |
| 期权名称  | object  | -                                                   |
| 最新价   | float64 | -                                                   |
| 涨跌幅   | float64 | 注意单位: %;                                            |
| 行权价   | float64 | -                                                   |
| 折溢价率  | float64 | 注意: 折溢价反映的是投资者以现价买入某期权并持有至到期时，标的需要上升或下跌多少才能使这笔投资保本。 |
| 标的名称  | object  | -                                                   |
| 标的最新价 | float64 | -                                                   |
| 标的涨跌幅 | float64 | -                                                   |
| 盈亏平衡价 | float64 | 注意: 指期权投资者实现投资收益为零时标的证券的价格。                         |
| 到期日   | object  | -                                                   |

接口示例

```python
import akshare as ak

option_premium_analysis_em_df = ak.option_premium_analysis_em()
print(option_premium_analysis_em_df)
```

数据示例

```
     期权代码       期权名称     最新价   涨跌幅  ... 标的最新价  标的涨跌幅 盈亏平衡价 到期日
0    10007852     科创50沽3月450  0.0002   0.00  ...  1.157   6.05  0.450  2025-03-26
1    10007830    科创板50沽3月450  0.0002   0.00  ...  1.122   6.05  0.450  2025-03-26
2    10007708     科创50沽3月500  0.0003  50.00  ...  1.157   6.05  0.500  2025-03-26
3    10007688    科创板50沽3月500  0.0003  50.00  ...  1.122   6.05  0.500  2025-03-26
4    10007658     科创50沽3月550  0.0005  66.67  ...  1.157   6.05  0.550  2025-03-26
..        ...            ...     ...    ...  ...    ...    ...    ...         ...
659  10007651     科创50购3月650  0.4869  10.41  ...  1.157   6.05  1.137  2025-03-26
660  10008359    科创板50购6月750  0.3520  14.29  ...  1.122   6.05  1.102  2025-06-25
661  10008357     科创50购6月800  0.3361  15.50  ...  1.157   6.05  1.136  2025-06-25
662  10008883     科创50购9月800  0.3339  14.74  ...  1.157   6.05  1.134  2025-09-24
663  10008768  500ETF购6月4900  0.9787  -6.97  ...  6.054   1.71  5.879  2025-06-25
[664 rows x 11 columns]
```

### 商品期权-新浪

#### 当前合约

接口: option_commodity_contract_sina

目标地址: https://stock.finance.sina.com.cn/futures/view/optionsDP.php

描述: 新浪财经-商品期权当前在交易的合约

限量: 单次返回指定 symbol 的所有合约数据

输入参数

| 名称     | 类型  | 描述            |
|--------|-----|---------------|
| symbol | str | symbol="玉米期权" |

输出参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| 序号  | str | -   |
| 合约  | str | -   |

接口示例

```python
import akshare as ak

option_commodity_contract_sina_df = ak.option_commodity_contract_sina(symbol="黄金期权")
print(option_commodity_contract_sina_df)
```

数据示例

```
   序号   合约
0   1  au2204
1   2  au2202
2   3  au2206
3   4  au2203
```

#### 当前合约

接口: option_commodity_contract_table_sina

目标地址: https://stock.finance.sina.com.cn/futures/view/optionsDP.php

描述: 新浪财经-商品期权的 T 型报价表

限量: 单次返回指定 symbol 和 contract 的所有数据

输入参数

| 名称       | 类型  | 描述                                                               |
|----------|-----|------------------------------------------------------------------|
| symbol   | str | symbol="玉米期权"                                                    |
| contract | str | contract="au2204"; 可以通过 ak.option_commodity_contract_sina() 接口获取 |

输出参数

| 名称          | 类型      | 描述     |
|-------------|---------|--------|
| 看涨合约-买量     | int64   | -      |
| 看涨合约-买价     | float64 | -      |
| 看涨合约-最新价    | float64 | -      |
| 看涨合约-卖价     | float   | -      |
| 看涨合约-卖量     | int64   | -      |
| 看涨合约-持仓量    | int64   | -      |
| 看涨合约-涨跌     | float64 | -      |
| 行权价         | int64   | -      |
| 看涨合约-看涨期权合约 | object  | 看涨合约代码 |
| 看跌合约-买量     | int64   | -      |
| 看跌合约-买价     | float64 | -      |
| 看跌合约-最新价    | float64 | -      |
| 看跌合约-卖价     | float64 | -      |
| 看跌合约-卖量     | int64   | -      |
| 看跌合约-持仓量    | int64   | -      |
| 看跌合约-涨跌     | float64 | -      |
| 看跌合约-看跌期权合约 | object  | 看跌合约代码 |

接口示例

```python
import akshare as ak

option_commodity_contract_table_sina_df = ak.option_commodity_contract_table_sina(symbol="动力煤期权", contract="zc2103")
print(option_commodity_contract_table_sina_df)
```

数据示例

```
    看涨合约-买量  看涨合约-买价  看涨合约-最新价  看涨合约-卖价  ...  看跌合约-卖量  看跌合约-持仓量  看跌合约-涨跌  看跌合约-看跌期权合约
0       0.0      0.0      52.0      0.0  ...       91       540     0.00   zc2103P500
1       1.0     41.1      51.8      0.0  ...        1       305     0.00   zc2103P510
2       1.0     20.2      45.3     67.0  ...       18       255     0.00   zc2103P520
3       0.0      0.0      53.0      0.0  ...        1       260     0.00   zc2103P530
4       1.0    145.4     196.0    210.2  ...        9       280     0.00   zc2103P540
5       0.0      0.0      57.1      0.0  ...        1       277     0.00   zc2103P550
6       0.0      0.0      46.9      0.0  ...        0       252     0.00   zc2103P560
7       1.0      7.4      45.3      0.0  ...       21       174     0.00   zc2103P570
8       1.0      1.6      32.0      0.0  ...        5       479     0.00   zc2103P580
9       1.0      1.3      23.4      0.0  ...        1       293     0.00   zc2103P590
10      5.0      4.9       4.9     21.7  ...      504      1145   -66.67   zc2103P600
11      2.0      2.9       3.0      3.0  ...       96       451   -83.33   zc2103P610
12      0.0      0.0       0.1      0.1  ...       58       758   -81.82   zc2103P620
13      0.0      0.0       0.1      0.1  ...      103       840   -96.00   zc2103P630
14      0.0      0.0       0.1      0.1  ...       18       414   -98.21   zc2103P640
15      0.0      0.0       0.1      0.1  ...        0       276   -33.64   zc2103P650
16      0.0      0.0       0.1      0.1  ...        6       218    -5.49   zc2103P660
17      0.0      0.0       0.1      0.1  ...        0       164     1.87   zc2103P670
18      0.0      0.0       0.1      0.1  ...        0       199    -3.05   zc2103P680
19      0.0      0.0       0.1      0.1  ...        0       138     1.53   zc2103P690
20      0.0      0.0       0.1      0.1  ...        0       283     0.36   zc2103P700
21      0.0      0.0       0.1      0.1  ...        0       183     0.00   zc2103P710
22      0.0      0.0       0.1      0.1  ...        0       215    88.25   zc2103P720
23      0.0      0.0       0.1      0.1  ...        0       140    52.98   zc2103P730
24      0.0      0.0       0.1      0.1  ...        0       112     1.46   zc2103P740
25      0.0      0.0       0.1      0.1  ...        0       120    18.85   zc2103P750
26      0.0      0.0       0.1      0.1  ...        0       111    48.73   zc2103P760
27      0.0      0.0       0.1      0.1  ...        0       190    37.22   zc2103P770
28      0.0      0.0       0.1      0.1  ...        0       161    -2.29   zc2103P780
29      0.0      0.0       0.1      0.1  ...        0        79     7.46   zc2103P790
30      0.0      0.0       0.1      0.1  ...        1       141    -0.53   zc2103P800
31      0.0      0.0       0.1      0.1  ...        1        83     1.94   zc2103P810
32      0.0      0.0       0.1      0.1  ...        0       113   -10.08   zc2103P820
33      0.0      0.0       0.1      0.1  ...        0        40    25.82   zc2103P830
34      0.0      0.0       0.1      0.1  ...        0        36     6.71   zc2103P840
35      0.0      0.0       0.1      0.1  ...        0        17    16.98   zc2103P850
36      NaN      NaN       NaN      NaN  ...        1        25    16.06   zc2103P860
37      NaN      NaN       NaN      NaN  ...        0         0    14.76   zc2103P870
38      NaN      NaN       NaN      NaN  ...        0         0    13.83   zc2103P880
```

#### 历史行情

接口: option_commodity_hist_sina

目标地址: https://stock.finance.sina.com.cn/futures/view/optionsDP.php

描述: 新浪财经-商品期权的历史行情数据-日频率

限量: 单次返回指定合约的历史行情数据

输入参数

| 名称     | 类型  | 描述                                                                           |
|--------|-----|------------------------------------------------------------------------------|
| symbol | str | symbol="au2012C328"; 可以通过 ak.option_commodity_contract_table_sina() 获取具体合约代码 |

输出参数

| 名称     | 类型      | 描述  |
|--------|---------|-----|
| date   | object  | -   |
| open   | float64 | -   |
| high   | float64 | -   |
| low    | float64 | -   |
| close  | float64 | -   |
| volume | int64   | -   |

接口示例

```python
import akshare as ak

option_commodity_hist_sina_df = ak.option_commodity_hist_sina(symbol="au2012C328")
print(option_commodity_hist_sina_df)
```

数据示例

```
         date     open     high      low    close volume
0  2019-12-20   0.0000   0.0000   0.0000  22.9200      0
1  2019-12-23   0.0000   0.0000   0.0000  25.9000      0
2  2019-12-24  25.0600  25.0600  25.0600  25.0600      2
3  2019-12-25  27.8400  27.8400  23.4400  27.2000     12
4  2020-01-07  38.1800  38.1800  38.1800  38.1800      1
5  2020-02-11  40.2200  40.2200  35.6000  35.6000      2
6  2020-03-16  33.2800  33.2800  33.2800  33.2800      2
7  2020-03-24  46.0400  46.0400  46.0400  46.0400      1
8  2020-05-07  58.3200  58.3200  57.1400  58.2200      3
```

### 商品期权

#### 商品期权手续费

接口: option_comm_info

目标地址: https://www.9qihuo.com/qiquanshouxufei

描述: 九期网-商品期权手续费数据

限量: 单次返回指定 symbol 的所有数据

输入参数

| 名称     | 类型  | 描述                                                     |
|--------|-----|--------------------------------------------------------|
| symbol | str | symbol="工业硅期权"; 可以通过 ak.option_comm_symbol() 所有 symbol |

输出参数

| 名称       | 类型     | 描述 |
|----------|--------|----|
| 期权品种     | object | -  |
| 现价       | int64  | -  |
| 涨/跌停板    | object | -  |
| 成交量      | int64  | -  |
| 类型       | object | -  |
| 权利金      | object | -  |
| 开仓       | object | -  |
| 平昨       | object | -  |
| 平今       | object | -  |
| 行权       | object | -  |
| 每跳毛利/元   | int64  | -  |
| 手续费(开+平) | object | -  |
| 每跳净利/元   | int64  | -  |
| 备注       | object | -  |
| 交易所      | object | -  |
| 手续费更新时间  | object | -  |
| 价格更新时间   | object | -  |

接口示例

```python
import akshare as ak

option_comm_info_df = ak.option_comm_info(symbol="工业硅期权")
print(option_comm_info_df)
```

数据示例

```
                                  期权品种  ...                   价格更新时间
0    工业硅期权2409--10000 (si2409-C-10000)  ...  2024-07-05 15:00:49.769
1    工业硅期权2409--10200 (si2409-C-10200)  ...  2024-07-05 15:00:49.769
2    工业硅期权2409--10400 (si2409-C-10400)  ...  2024-07-05 15:00:49.769
3    工业硅期权2409--10600 (si2409-C-10600)  ...  2024-07-05 15:00:49.769
4    工业硅期权2409--10800 (si2409-C-10800)  ...  2024-07-05 15:00:49.769
..                                 ...  ...                      ...
303  工业硅期权2506--14400 (si2506-P-14400)  ...  2024-07-05 15:00:49.769
304  工业硅期权2506--16000 (si2506-P-16000)  ...  2024-07-05 15:00:49.769
305  工业硅期权2506--16200 (si2506-P-16200)  ...  2024-07-05 15:00:49.769
306  工业硅期权2506--16400 (si2506-P-16400)  ...  2024-07-05 15:00:49.769
307  工业硅期权2506--16600 (si2506-P-16600)  ...  2024-07-05 15:00:49.769
[308 rows x 17 columns]
```

#### 上海期货交易所

接口: option_shfe_daily

目标地址: https://www.shfe.com.cn/reports/tradedata/dailyandweeklydata/

描述: 上海期货交易所-商品期权数据

限量: 单次返回指定 symbol 和 trade_date 的期权行情数据, 只能获取 20200824 之后的数据

输入参数

| 名称         | 类型  | 描述                                                                                                                              |
|------------|-----|---------------------------------------------------------------------------------------------------------------------------------|
| symbol     | str | symbol="铜期权"; choice of {'原油期权', '铜期权', '铝期权', '锌期权', '铅期权', '螺纹钢期权', '镍期权', '锡期权', '氧化铝期权', '黄金期权', '白银期权', '丁二烯橡胶期权', '天胶期权'} |
| trade_date | str | trade_date="20191017"                                                                                                           |

输出参数

Part-1: 上海期货交易所期权合约行情

| 名称    | 类型      | 描述    |
|-------|---------|-------|
| 合约代码  | object  |       |
| 开盘价   | float64 |       |
| 最高价   | float64 |       |
| 最低价   | float64 |       |
| 收盘价   | float64 |       |
| 前结算价  | float64 |       |
| 结算价   | float64 |       |
| 涨跌1   | float64 |       |
| 涨跌2   | float64 |       |
| 成交量   | float64 |       |
| 持仓量   | float64 |       |
| 持仓量变化 | float64 |       |
| 成交额   | float64 |       |
| 德尔塔   | float64 | Delta |
| 行权量   | float64 |       |

注:
1. 期权报价单位: 铜、天然橡胶为元/吨.
2. 期权交易单位: 铜为 5 吨/手；天然橡胶为 10 吨/手.
3. 成交量、持仓量、持仓量变化单位为手, 双边计算；成交额双边计算.
4. 涨跌1=收盘价-前结算价, 涨跌2=结算价-前结算价.
5. 合约系列: 具有相同月份标的期货合约的所有期权合约的统称.
6. 隐含波动率: 根据期权市场交易价格, 利用期权定价模型计算出来的标的期货合约的价格波动率数值.

Part-2: 上海期货交易所隐含波动参考值

| 名称    | 类型      | 描述     |
|-------|---------|--------|
| 合约系列  | object  |        |
| 成交量   | float64 | 注意单位：手 |
| 持仓量   | float64 | 注意单位：手 |
| 持仓量变化 | float64 | 注意单位：手 |
| 成交额   | float64 | 注意单位：手 |
| 行权量   | float64 | 注意单位：手 |
| 隐含波动率 | float64 |        |

接口示例

```python
import akshare as ak

option_shfe_daily_one, option_shfe_daily_two = ak.option_shfe_daily(symbol="铝期权", trade_date="20200827")
print(option_shfe_daily_one)
print(option_shfe_daily_two)
```

数据示例

part_1: 上海期货交易所期权合约行情

```
     合约代码 开盘价 最高价 最低价  ...  持仓量变化  成交额       德尔塔  行权量
588  al2010C12400                                ...      0  0.0  0.999999    0
589  al2010C12500                                ...      0  0.0  0.999999    0
590  al2010C12600                                ...      0  0.0  0.999842    0
591  al2010C12700                                ...      0  0.0  0.998645    0
592  al2010C12800                                ...      0  0.0  0.996855    0
..                              ...  ..  ..  ..  ...    ...  ...       ...  ...
901  al2101P15500                                ...      0  0.0 -0.835443    0
902  al2101P15600                                ...      0  0.0 -0.853291    0
903  al2101P15700                                ...      0  0.0 -0.869626    0
904  al2101P15800                                ...      0  0.0 -0.884810    0
905  al2101P15900                                ...      0  0.0 -0.898378    0
```

part_2: 上海期货交易所隐含波动参考值

```
   合约系列   成交量   持仓量  持仓量变化       成交额 行权量     隐含波动率
11  al2010                          2008  8373    435  135.3745   0  0.178040
12  al2011                           292   906      0   12.1665   0  0.150241
13  al2012                             0    66      0    0.0000   0  0.150241
14  al2101                             0    66      0    0.0000   0  0.150241
```

#### 大连商品交易所

接口: option_dce_daily

目标地址: http://www.dce.com.cn/dalianshangpin/xqsj/tjsj26/rtj/rxq/index.html

描述: 大连商品交易所-商品期权数据

限量: 单次返回指定 symbol 和 trade_date 的期权行情数据

输入参数

| 名称         | 类型  | 描述                    |
|------------|-----|-----------------------|
| symbol     | str | symbol="玉米期权"         |
| trade_date | str | trade_date="20191017" |

输出参数

Part-1: 大连商品交易所期权合约行情

| 名称    | 类型      | 描述  |
|-------|---------|-----|
| 商品名称  | object  |     |
| 合约名称  | object  |     |
| 开盘价   | float64 |     |
| 最高价   | float64 |     |
| 最低价   | float64 |     |
| 收盘价   | float64 |     |
| 前结算价  | float64 |     |
| 结算价   | float64 |     |
| 涨跌    | float64 |     |
| 涨跌1   | float64 |     |
| 成交量   | float64 |     |
| 持仓量   | float64 |     |
| 持仓量变化 | float64 |     |
| 成交额   | float64 |     |
| 行权量   | float64 |     |

说明:
1. 价格: 元/吨, 鸡蛋为元/500千克, 纤维板、胶合板为元/张
2. 成交量、持仓量: 手(按双边计算)
3. 成交额: 万元(按双边计算)
4. 涨跌＝收盘价－前结算价
5. 涨跌1=今结算价-前结算价
6. 合约系列: 具有相同月份标的期货合约的所有期权合约的统称
7. 隐含波动率: 根据期权市场价格, 利用期权定价模型计算的标的期货合约价格波动率

Part-2: 隐含波动率参考值

| 名称     | 类型      | 描述  |
|--------|---------|-----|
| 合约系列   | object  |     |
| 隐含波动率% | float64 |     |

接口示例

```python
import akshare as ak

part_1, part_2 = ak.option_dce_daily(symbol="玉米期权", trade_date="20191017")
print(part_1)
print(part_2)
```

数据示例

part_1: 大连商品交易所期权合约行情

```
            商品名称          合约名称    开盘价    最高价    最低价    收盘价   前结算价    结算价   涨跌  涨跌1  \
0     玉米  c2001-C-1680  168.5  168.5  168.5  168.5  168.0  167.5  0.5 -0.5
1     玉米  c2001-C-1700      0    0.0    0.0  148.0  148.0  148.0  0.0  0.0
2     玉米  c2001-C-1720      0    0.0    0.0  129.0  128.0  129.0  1.0  1.0
3     玉米  c2001-C-1740    115  115.0  115.0  115.0  108.0  111.0  7.0  3.0
4     玉米  c2001-C-1760     89   95.5   89.0   95.5   89.0   93.5  6.5  4.5
..   ...           ...    ...    ...    ...    ...    ...    ...  ...  ...
239   玉米  c2009-P-2040      0    0.0    0.0   91.0   88.5   91.0  2.5  2.5
240   玉米  c2009-P-2060      0    0.0    0.0  106.0  104.0  106.0  2.0  2.0
241   玉米  c2009-P-2080      0    0.0    0.0  121.5  120.5  121.5  1.0  1.0
242   玉米  c2009-P-2100      0    0.0    0.0  138.5  137.5  138.5  1.0  1.0
243   玉米  c2009-P-2120      0    0.0    0.0  155.5  155.5  155.5  0.0  0.0
     Delta 成交量    持仓量 持仓量变化   成交额  行权量
0     0.98   2    236     0  0.34  0.0
1     0.96   0    236     0     0  0.0
2     0.94   0    210     0     0  0.0
3     0.90  20  1,040     0   2.3  0.0
4     0.85  12    680     0  1.11  0.0
..     ...  ..    ...   ...   ...  ...
239  -0.70   0     30     0     0  0.0
240  -0.75   0     50     0     0  0.0
241  -0.80   0     20     0     0  0.0
242  -0.84   0     10     0     0  0.0
243  -0.88   0      0     0     0  0.0
```

part_2: 隐含波动率参考值

```
   合约系列 隐含波动率(%)
1  c2001    12.95
2  c2003     8.74
3  c2005     8.75
4  c2007      7.7
5  c2009     6.85
```

#### 郑州商品交易所

接口: option_czce_daily

目标地址: http://www.czce.com.cn/cn/jysj/mrhq/H770301index_1.htm

描述: 郑州商品交易所-商品期权数据

限量: 单次返回指定 symbol 和 trade_date 的期权行情数据

输入参数

| 名称         | 类型  | 描述                                              |
|------------|-----|-------------------------------------------------|
| symbol     | str | symbol="白糖期权"; 交易所上市的期权品种，最早上市的为 20170419 的白糖期权 |
| trade_date | str | trade_date="20191017"                           |

输出参数

郑州商品交易所-期权合约行情

| 名称      | 类型      | 描述  |
|---------|---------|-----|
| 品种代码    | object  |     |
| 昨结算     | float64 |     |
| 今开盘     | float64 |     |
| 最高价     | float64 |     |
| 最低价     | float64 |     |
| 今收盘     | float64 |     |
| 今结算     | float64 |     |
| 涨跌1     | float64 |     |
| 涨跌2     | float64 |     |
| 成交量(手)  | float64 |     |
| 空盘量     | float64 |     |
| 增减量     | float64 |     |
| 成交额(万元) | float64 |     |
| DELTA   | float64 |     |
| 隐含波动率   | float64 |     |
| 行权量     | float64 |     |

说明:

1. 价格: 元/吨
2. 成交量、空盘量: 手
3. 成交额: 万元
4. 涨跌一: 今收盘-昨结算
5. 涨跌二: 今结算-昨结算
6. 隐含波动率: 将当日期权合约的结算价代入期权定价模型, 反推出来的波动率数值
7. 交易所早期的字段名不统一，本接口字段统一为最新值

接口示例

```python
import akshare as ak

option_czce_daily_df = ak.option_czce_daily(symbol="白糖期权", trade_date="20240711")
print(option_czce_daily_df)
```

数据示例

```
      合约代码    昨结算   今开盘   最高价 最低价  ... 增减量 成交额(万元) DELTA 隐含波动率 行权量
0    SR409C5400  774.5    0.0    0.0    0.0  ...  0.0     0.00  0.9888  20.73  0.0
1    SR409C5500  675.0  691.0  696.0  691.0  ... -1.0     2.77  0.9814  19.42  0.0
2    SR409C5600  576.0    0.0    0.0    0.0  ...  0.0     0.00  0.9701  18.06  0.0
3    SR409C5700  478.0  507.0  509.0  507.0  ... -2.0     1.51  0.9529  16.65  0.0
4    SR409C5800  381.5  382.5  411.0  382.5  ... -8.0    60.66  0.9259  15.18  0.0
..          ...    ...    ...    ...    ...  ...  ...      ...     ...    ...  ...
177  SR505P6100  375.5    0.0    0.0    0.0  ...  0.0     0.00 -0.6236  11.80  0.0
178  SR505P6200  446.5    0.0    0.0    0.0  ...  0.0     0.00 -0.6807  11.86  0.0
179  SR505P6300  522.5    0.0    0.0    0.0  ...  0.0     0.00 -0.7318  11.97  0.0
180  SR505P6400  604.0    0.0    0.0    0.0  ...  0.0     0.00 -0.7763  12.18  0.0
181  SR505P6500  686.5    0.0    0.0    0.0  ...  0.0     0.00 -0.8193  12.18  0.0
[182 rows x 16 columns]
```

#### 广州期货交易所

接口: option_gfex_daily

目标地址: http://www.gfex.com.cn/gfex/rihq/hqsj_tjsj.shtml

描述: 广州期货交易所-商品期权数据

限量: 单次返回指定 symbol 和 trade_date 的期权行情数据

输入参数

| 名称         | 类型  | 描述                                     |
|------------|-----|----------------------------------------|
| symbol     | str | symbol="工业硅"; choice of {"工业硅", "碳酸锂"} |
| trade_date | str | trade_date="20230724"; 交易日             |

输出参数

广州期货交易所期权合约行情

| 名称    | 类型      | 描述 |
|-------|---------|----|
| 商品名称  | object  |    |
| 合约名称  | object  |    |
| 开盘价   | float64 |    |
| 最高价   | float64 |    |
| 最低价   | float64 |    |
| 收盘价   | float64 |    |
| 前结算价  | float64 |    |
| 结算价   | float64 |    |
| 涨跌    | float64 |    |
| 涨跌1   | float64 |    |
| Delta | float64 |    |
| 成交量   | int64   |    |
| 持仓量   | int64   |    |
| 持仓量变化 | int64   |    |
| 成交额   | float64 |    |
| 行权量   | int64   |    |
| 隐含波动率 | float64 |    |

说明:

1. 价格：元/吨
2. 成交量、持仓量、持仓量变化：手（按单边计算）
3. 成交额：万元（按单边计算）
4. 涨跌＝收盘价-前结算价
5. 涨跌1=今结算价-前结算价
6. 合约系列：具有相同月份标的期货合约的所有期权合约的统称
7. 隐含波动率：根据期权市场价格，利用期权定价模型计算的标的期货合约价格波动率

接口示例-广州期货交易所期权合约行情(工业硅)

```python
import akshare as ak

option_gfex_daily_df = ak.option_gfex_daily(symbol="工业硅", trade_date="20230418")
print(option_gfex_daily_df)
```

数据示例-广州期货交易所期权合约行情(工业硅)

```
      商品名称  合约名称  开盘价  最高价  ...  持仓量变化      成交额  行权量      隐含波动率
0      工业硅  si2308-C-13000  0.0  0.0  ...      0    0.000    0  22.436477
1      工业硅  si2308-C-13200  0.0  0.0  ...      0    0.000    0  22.538374
2      工业硅  si2308-C-13400  0.0  0.0  ...      0    0.000    0  22.493164
3      工业硅  si2308-C-13600  0.0  0.0  ...      0    0.000    0  22.467333
4      工业硅  si2308-C-13800  0.0  0.0  ...      0    0.000    0  22.530720
..     ...             ...  ...  ...  ...    ...      ...  ...        ...
597    工业硅  si2403-P-17800  0.0  0.0  ...      0    0.000    0  19.727143
598    工业硅  si2403-P-18000  0.0  0.0  ...      0    0.000    0  19.728669
599    工业硅  si2403-P-18200  0.0  0.0  ...      0    0.000    0  19.719663
600  工业硅小计                  NaN  NaN  ...   -427  494.246    1        NaN
601     总计                  NaN  NaN  ...   -427  494.246    1        NaN
```

#### 广州期货交易所-隐含波动参考值

接口: option_gfex_vol_daily

目标地址: http://www.gfex.com.cn/gfex/rihq/hqsj_tjsj.shtml

描述: 广州期货交易所-商品期权数据-隐含波动参考值

限量: 单次返回指定 symbol 和 trade_date 的期权行情数据

输入参数

| 名称         | 类型  | 描述                                     |
|------------|-----|----------------------------------------|
| symbol     | str | symbol="工业硅"; choice of {"工业硅", "碳酸锂"} |
| trade_date | str | trade_date="20230724"                  |

输出参数

广州期货交易所-隐含波动参考值

| 名称    | 类型      | 描述 |
|-------|---------|----|
| 合约系列  | object  |    |
| 隐含波动率 | float64 |    |

说明:

1. 价格：元/吨
2. 成交量、持仓量、持仓量变化：手（按单边计算）
3. 成交额：万元（按单边计算）
4. 涨跌＝收盘价-前结算价
5. 涨跌1=今结算价-前结算价
6. 合约系列：具有相同月份标的期货合约的所有期权合约的统称
7. 隐含波动率：根据期权市场价格，利用期权定价模型计算的标的期货合约价格波动率

接口示例-广州期货交易所-隐含波动参考值(工业硅)

```python
import akshare as ak

option_gfex_vol_daily_df = ak.option_gfex_vol_daily(symbol="工业硅", trade_date="20230418")
print(option_gfex_vol_daily_df)
```

数据示例-广州期货交易所-隐含波动参考值(工业硅)

```
   合约系列  隐含波动率
0  si2308  22.542314
1  si2309  21.018517
2  si2310  21.018517
3  si2311  21.018517
4  si2312  19.894257
5  si2401  19.894257
6  si2402  19.894257
7  si2403  19.729307
```

#### 历史数据

接口: option_czce_hist

目标地址: http://www.czce.com.cn/cn/jysj/lshqxz/H770319index_1.htm

描述: 郑州商品交易所的商品期权历史行情数据

限量: 单次返回指定年份指定品种期权历史行情数据

输入参数

| 名称     | 类型  | 描述                                                                                                                                                                                                                                                                |
|--------|-----|-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| year   | str | year="2019"; 指定年份                                                                                                                                                                                                                                                 |
| symbol | str | symbol="SR"; choice of {"白糖": "SR", "棉花": "CF", "PTA": "TA", "甲醇": "MA", "菜籽粕": "RM", "动力煤": "ZC", "菜籽油": "OI", "花生": "PK", "对二甲苯": "PX", "烧碱": "SH", "纯碱": "SA", "短纤": "PF", "锰硅": "SM", "硅铁": "SF", "尿素": "UR", "苹果": "AP", "红枣": "CJ", "玻璃": "FG", "瓶片": "PR"} |

输出参数

| 名称      | 类型      | 描述  |
|---------|---------|-----|
| 交易日期    | object  |     |
| 品种代码    | object  |     |
| 昨结算     | float64 |     |
| 今开盘     | float64 |     |
| 最高价     | float64 |     |
| 最低价     | float64 |     |
| 今收盘     | float64 |     |
| 今结算     | float64 |     |
| 涨跌1     | float64 |     |
| 涨跌2     | float64 |     |
| 成交量(手)  | object  |     |
| 空盘量     | object  |     |
| 增减量     | object  |     |
| 成交额(万元) | object  |     |
| DELTA   | float64 |     |
| 隐含波动率   | float64 |     |
| 行权量     | float64 |     |

说明:

1. 价格: 元/吨
2. 成交量、空盘量: 手
3. 成交额: 万元
4. 涨跌一: 今收盘-昨结算
5. 涨跌二: 今结算-昨结算
6. 隐含波动率: 将当日期权合约的结算价代入期权定价模型, 反推出来的波动率数值

接口示例

```python
import akshare as ak

option_czce_hist_df = ak.option_czce_hist(symbol="RM", year="2025")
print(option_czce_hist_df)
```

数据示例

```
       交易日期     合约代码     ...  隐含波动率                       行权量
0      2025-01-02  RM503C1925  ...  22.64  0
1      2025-01-02  RM503C1950  ...  22.42  0
2      2025-01-02  RM503C1975  ...  22.25  0
3      2025-01-02  RM503C2000  ...  22.12  0
4      2025-01-02  RM503C2025  ...  22.05  0
...           ...         ...  ...    ...                              ...
16067  2025-03-21  RM601P2600  ...  21.49  0
16068  2025-03-21  RM601P2650  ...  21.78  0
16069  2025-03-21  RM601P2700  ...  22.11  0
16070  2025-03-21  RM601P2750  ...  22.48  0
16071  2025-03-21  RM601P2800  ...  22.90  0
[16072 rows x 17 columns]
```

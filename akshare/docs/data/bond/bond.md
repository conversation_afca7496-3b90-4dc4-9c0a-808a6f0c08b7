## [AKShare](https://github.com/akfamily/akshare) 债券数据

#### 债券查询

接口: bond_info_cm

目标地址: https://www.chinamoney.com.cn/chinese/scsjzqxx/

描述: 中国外汇交易中心暨全国银行间同业拆借中心-数据-债券信息-信息查询

输入参数

| 名称          | 类型  | 描述                                                      |
|-------------|-----|---------------------------------------------------------|
| bond_name   | str | bond_name=""; 默认为空                                      |
| bond_code   | str | bond_code=""; 默认为空                                      |
| bond_issue  | str | bond_issue=""; 默认为空, 通过 ak.bond_info_cm_query() 查询相关参数  |
| bond_type   | str | bond_type=""; 默认为空, 通过 ak.bond_info_cm_query() 查询相关参数   |
| coupon_type | str | coupon_type=""; 默认为空, 通过 ak.bond_info_cm_query() 查询相关参数 |
| issue_year  | str | issue_year=""; 默认为空                                     |
| underwriter | str | underwriter=""; 默认为空, 通过 ak.bond_info_cm_query() 查询相关参数 |
| grade       | str | grade=""; 默认为空                                          |

输出参数

| 名称       | 类型     | 描述  |
|----------|--------|-----|
| 债券简称     | object | -   |
| 债券代码     | object | -   |
| 发行人/受托机构 | object | -   |
| 债券类型     | object | -   |
| 发行日期     | object | -   |
| 最新债项评级   | object | -   |
| 查询代码     | object | -   |

接口示例

```python
import akshare as ak

bond_info_cm_df = ak.bond_info_cm(bond_name="", bond_code="", bond_issue="", bond_type="短期融资券", coupon_type="零息式", issue_year="2019", grade="A-1", underwriter="重庆农村商业银行股份有限公司")
print(bond_info_cm_df)
```

数据示例

```
          债券简称       债券代码      发行人/受托机构   债券类型        发行日期 最新债项评级        查询代码
0   19渝机电CP002  041900474  重庆机电控股(集团)公司  短期融资券  2019-12-16    A-1  06006vznk4
1   19渝机电CP001  041900229  重庆机电控股(集团)公司  短期融资券  2019-06-13    A-1  786875qtsi
2  19万林投资CP001  041900126  重庆万林投资发展有限公司  短期融资券  2019-03-25    A-1  695327xh9n
```

#### 债券基础信息

接口: bond_info_detail_cm

目标地址: https://www.chinamoney.com.cn/chinese/zqjc/?bondDefinedCode=egfjh08154

描述: 中国外汇交易中心暨全国银行间同业拆借中心-数据-债券信息-信息查询-债券详情

输入参数

| 名称     | 类型  | 描述                                                 |
|--------|-----|----------------------------------------------------|
| symbol | str | symbol="19万林投资CP001"; 通过 ak.bond_info_cm() 查询 债券简称 |

输出参数

| 名称    | 类型     | 描述  |
|-------|--------|-----|
| name  | object | -   |
| value | object | -   |

接口示例

```python
import akshare as ak

bond_info_detail_cm_df = ak.bond_info_detail_cm(symbol="19万林投资CP001")
print(bond_info_detail_cm_df)
```

数据示例

```
        name                       value
0        bondFullName  重庆万林投资发展有限公司2019年度第一期短期融资券
1     bondDefinedCode                  695327xh9n
2            bondName                 19万林投资CP001
3            bondCode                   041900126
4            isinCode                         ---
..                ...                         ...
59        chrgngMthds                         ---
60             crdtEv                         ---
61     brchStlmntMthd                         ---
62  rgstrtnCnfrmtnDay                         ---
63             inptTp                           0
[64 rows x 2 columns]
```

### 债券基础名词

#### 固定收益证券

是指持券人可以在特定的时间内取得固定的收益并预先知道取得收益的数量和时间, 如固定利率债券、优先股股票等.

#### 国债

国债又称国家公债, 是国家以其信用为基础, 按照债券的一般原则, 通过向社会发行债券筹集资金所形成的债权债务关系. 国债是中央政府为筹集财政资金而发行的一种政府债券, 由中央政府向投资者出具的、承诺在一定时期支付利息和到期偿还本金的债权债务凭证, 由于国债的发行主体是国家, 所以它具有最高的信用度, 被公认为是最安全的投资工具.

### 上交所债券

#### 债券现券市场概览

接口: bond_cash_summary_sse

目标地址: https://bond.sse.com.cn/data/statistics/overview/bondow/

描述: 上登债券信息网-市场数据-市场统计-市场概览-债券现券市场概览

限量: 单次返回指定交易日的债券现券市场概览数据

输入参数

| 名称   | 类型  | 描述              |
|------|-----|-----------------|
| date | str | date='20200111' |

输出参数

| 名称   | 类型      | 描述       |
|------|---------|----------|
| 债券现货 | object  | -        |
| 托管只数 | int64   | -        |
| 托管市值 | float64 | 注意单位: 亿元 |
| 托管面值 | float64 | 注意单位: 亿元 |
| 数据日期 | object  | -        |

接口示例

```python
import akshare as ak

bond_cash_summary_sse_df = ak.bond_cash_summary_sse(date='20210111')
print(bond_cash_summary_sse_df)
```

数据示例

```
        债券现货   托管只数       托管市值       托管面值        数据日期
0         国债    193    6815.47    6758.46  2021-01-11
1        地方债   5317    6713.69    6709.22  2021-01-11
2        金融债     19     989.76     970.70  2021-01-11
3        企业债   2233    7497.15    7433.93  2021-01-11
4  非公开发行公司债券   4974   43857.61   44191.15  2021-01-11
5       可交换债     57    1352.85    1280.10  2021-01-11
6   公开发行公司债券   3393   46382.41   46644.57  2021-01-11
7     可转换公司债    142    3655.79    3179.81  2021-01-11
8     资产支持证券   4141   14838.03   14829.47  2021-01-11
9         合计  20469  132102.75  131997.40  2021-01-11
```

#### 债券成交概览

接口: bond_deal_summary_sse

目标地址: http://bond.sse.com.cn/data/statistics/overview/turnover/

描述: 上登债券信息网-市场数据-市场统计-市场概览-债券成交概览

限量: 单次返回指定交易日的债券成交概览数据

输入参数

| 名称   | 类型  | 描述              |
|------|-----|-----------------|
| date | str | date='20200104' |

输出参数

| 名称     | 类型      | 描述       |
|--------|---------|----------|
| 债券类型   | object  | -        |
| 当日成交笔数 | int64   | -        |
| 当日成交金额 | float64 | 注意单位: 万元 |
| 当年成交笔数 | int64   | -        |
| 当年成交金额 | float64 | 注意单位: 万元 |
| 数据日期   | object  | -        |

接口示例

```python
import akshare as ak

bond_summary_sse_df = ak.bond_deal_summary_sse(date='20210104')
print(bond_summary_sse_df)
```

数据示例

```
        债券类型  当日成交笔数 当日成交金额  当年成交笔数  当年成交金额  数据日期
0      记账式国债    3685   363349.44    3685   363349.44  2021-01-04
1      地方政府债      11    81284.62      11    81284.62  2021-01-04
2        金融债     568    11075.26     568    11075.26  2021-01-04
3        企业债     234   124537.76     234   124537.76  2021-01-04
4    中小企业私募债     298   802906.69     298   802906.69  2021-01-04
5    公开发行公司债    1570  1442430.80    1570  1442430.80  2021-01-04
6   非公开发行公司债      26    66038.67      26    66038.67  2021-01-04
7        可转债  923863  1566929.29  923863  1566929.29  2021-01-04
8       可交换债    3962    26873.67    3962    26873.67  2021-01-04
9        分离债       0        0.00       0        0.00  2021-01-04
10  企业资产支持证券      44   215197.41      44   215197.41  2021-01-04
11  信贷资产支持证券       0        0.00       0        0.00  2021-01-04
12      其他债券       0        0.00       0        0.00  2021-01-04
13        合计  934261  4700623.60  934261  4700623.60  2021-01-04
```

### 债券基础数据

#### 银行间市场债券发行基础数据

接口: bond_debt_nafmii

目标地址: http://zhuce.nafmii.org.cn/fans/publicQuery/manager

描述: 中国银行间市场交易商协会-非金融企业债务融资工具注册信息系统

限量: 单次获取指定 page 页面数据的 50 条数据

输入参数

| 名称   | 类型  | 描述                     |
|------|-----|------------------------|
| page | str | page="1", 需要获取第 page 页 |

输出参数

| 名称      | 类型      | 描述      |
|---------|---------|---------|
| 债券名称    | object  | -       |
| 品种      | object  | -       |
| 注册或备案   | object  | -       |
| 金额      | float64 | 注意单位：亿元 |
| 注册通知书文号 | object  | -       |
| 更新日期    | object  | -       |
| 项目状态    | object  | -       |

接口示例

```python
import akshare as ak

bond_debt_nafmii_df = ak.bond_debt_nafmii(page="2")
print(bond_debt_nafmii_df)
```

数据示例

```
                                          债券名称        品种  ...        更新日期  项目状态
0       绍兴市柯桥区开发经营集团有限公司关于2024年度第一期超短期融资券的注册报告   SCP  ...  2024-03-15    20
1         上海市北高新（集团）有限公司关于2024年度第一期超短期融资券的注册报告   SCP  ...  2024-03-15    40
2           江苏黄海金融控股集团有限公司关于2024年度第一期中期票据的注册报告   MTN  ...  2024-03-15    40
3            成都香城城市发展有限公司关于2024年度第一期短期融资券的注册报告    CP  ...  2024-03-15    20
4           张家口建设发展集团有限公司关于2024年度第一期短期融资券的注册报告    CP  ...  2024-03-15    20
5     浙江余姚工业园区开发建设投资有限公司关于2024年度第一期超短期融资券的注册报告   SCP  ...  2024-03-15    20
6             苏州恒泰控股集团有限公司关于2024年度第一期中期票据的注册报告   MTN  ...  2024-03-15    40
7            义乌市国有资本运营有限公司关于2024年度第三期中期票据的注册报告   MTN  ...  2024-03-15    20
8           内蒙古包钢钢联股份有限公司关于2024年度第一期短期融资券的注册报告    CP  ...  2024-03-15    20
9       中国蓝星（集团）股份有限公司关于2024-2026年度债务融资工具的注册报告   DFI  ...  2024-03-15    20
10         溧阳市城市建设发展集团有限公司关于2024年度第二期中期票据的注册报告   MTN  ...  2024-03-15    20
11      重庆长寿开发投资（集团）有限公司关于2024年度第一期超短期融资券的注册报告   SCP  ...  2024-03-15    20
12           邯郸市交通投资集团有限公司关于2024年度第二期中期票据的注册报告   MTN  ...  2024-03-15    20
13              山东出版集团有限公司关于2024年度第一期中期票据的注册报告   MTN  ...  2024-03-15    20
14      乌鲁木齐城市建设投资（集团）有限公司关于2024年度第四期中期票据的注册报告   MTN  ...  2024-03-15    20
15           周口城投发展集团有限公司关于2024年度第一期短期融资券的注册报告    CP  ...  2024-03-15    20
16        重庆市江津区珞璜开发建设有限公司关于2024年度第一期中期票据的注册报告   MTN  ...  2024-03-15    40
17            上海城投控股股份有限公司关于2024年度第一期中期票据的注册报告   MTN  ...  2024-03-15    20
18   北京中关村科技创业金融服务集团有限公司关于2024年度第一期超短期融资券的注册报告   SCP  ...  2024-03-15    20
19          枣庄矿业（集团）有限责任公司关于2024年度第一期中期票据的注册报告   MTN  ...  2024-03-15    20
20         靖江市北辰城乡投资建设有限公司关于2024年度第一期中期票据的注册报告   MTN  ...  2024-03-15    40
21        茅台（上海）融资租赁有限公司关于2024年度第一期超短期融资券的注册报告   SCP  ...  2024-03-15    30
22     山东寿光金鑫投资发展控股集团有限公司关于2024年度第一期短期融资券的注册报告    CP  ...  2024-03-15    30
23              横店集团控股有限公司关于2024年度第一期中期票据的注册报告   MTN  ...  2024-03-15    30
24  中国公路工程咨询集团有限公司关于2024年度第一期超短期融资券（科创票据）的注册报告   SCP  ...  2024-03-15    30
25            厦门金圆投资集团有限公司关于2024年度第一期中期票据的注册报告   MTN  ...  2024-03-15    30
26       青岛西海岸公用事业集团有限公司关于2024年度第一期超短期融资券的注册报告   SCP  ...  2024-03-15    30
27            昆明轨道交通集团有限公司关于2024年度第一期中期票据的注册报告   MTN  ...  2024-03-15    30
28            横店集团控股有限公司关于2024年度第一期超短期融资券的注册报告   SCP  ...  2024-03-15    30
29            济南高新控股集团有限公司关于2024年度第二期中期票据的注册报告   MTN  ...  2024-03-15    20
30           温州市交通运输集团有限公司关于2024年度第一期中期票据的注册报告   MTN  ...  2024-03-15    20
31        重庆长寿开发投资（集团）有限公司关于2024年度第一期中期票据的注册报告   MTN  ...  2024-03-15    40
32        华能国际电力股份有限公司关于2024-2026年度债务融资工具的注册报告  TDFI  ...  2024-03-15    40
33           江苏新海连发展集团有限公司关于2024年度第三期中期票据的注册报告   MTN  ...  2024-03-15    20
34    吉安市井冈山开发区金庐陵经济发展有限公司关于2024年度第一期中期票据的注册报告   MTN  ...  2024-03-14    20
35          海安开发区建设投资有限公司关于2024年度第四期短期融资券的注册报告    CP  ...  2024-03-14    20
36          保利商业保理有限公司关于2024年度第一期邦鑫资产支持票据的注册报告   ABN  ...  2024-03-14    40
37     湖北省广播电视信息网络股份有限公司关于2024年度第一期超短期融资券的注册报告   SCP  ...  2024-03-14    40
38       湖北省广播电视信息网络股份有限公司关于2024年度第一期中期票据的注册报告   MTN  ...  2024-03-14    40
39            红狮控股集团有限公司关于2024年度第一期超短期融资券的注册报告   SCP  ...  2024-03-14    40
40            湖南电广传媒股份有限公司关于2024年度第一期中期票据的注册报告   MTN  ...  2024-03-14    20
41          广州珠江实业集团有限公司关于2024年度第一期超短期融资券的注册报告   SCP  ...  2024-03-14    20
42          湖北农谷实业集团有限责任公司关于2024年度第一期中期票据的注册报告   MTN  ...  2024-03-14    20
43        开封经济技术开发（集团）有限公司关于2024年度第一期中期票据的注册报告   MTN  ...  2024-03-14    20
44           福州市金融控股集团有限公司关于2024年度第一期中期票据的注册报告   MTN  ...  2024-03-14    20
45          嘉兴湾北城市发展集团有限公司关于2024年度第一期中期票据的注册报告   MTN  ...  2024-03-14    20
46       合肥东部新城投资控股集团有限公司关于2024年度第一期短期融资券的注册报告    CP  ...  2024-03-14    20
47            日照高新发展集团有限公司关于2024年度第一期中期票据的注册报告   MTN  ...  2024-03-14    20
48            天津能源投资集团有限公司关于2024年度第一期中期票据的注册报告   MTN  ...  2024-03-14    20
49            浙江英特集团股份有限公司关于2024年度第一期中期票据的注册报告   MTN  ...  2024-03-14    20
[50 rows x 7 columns]
```

### 中国债券市场行情数据

#### 现券市场做市报价

接口: bond_spot_quote

目标地址: https://www.chinamoney.com.cn/chinese/mkdatabond/

描述: 中国外汇交易中心暨全国银行间同业拆借中心-市场数据-市场行情-债券市场行情-现券市场做市报价

限量: 单次返回所有数据

输入参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| -   | -   | -   |

输出参数

| 名称    | 类型      | 描述      |
|-------|---------|---------|
| 报价机构  | object  |         |
| 债券简称  | object  |         |
| 买入净价  | float64 | 注意单位: 元 |
| 卖出净价  | float64 | 注意单位: 元 |
| 买入收益率 | float64 | 注意单位: % |
| 卖出收益率 | float64 | 注意单位: % |

接口示例

```python
import akshare as ak

bond_spot_quote_df = ak.bond_spot_quote()
print(bond_spot_quote_df)
```

数据示例

```
     报价机构       债券简称  买入净价 卖出净价  买入收益率 卖出收益率
0   星展银行(中国)  21进出10  100.34  102.44  3.335  3.0750
1   星展银行(中国)  20农发08  101.88  102.87  2.882  2.5901
2   星展银行(中国)  20农发07  100.57  101.26  2.640  2.1401
3   星展银行(中国)  20农发02   99.79  100.03  2.400  2.1701
4   星展银行(中国)  20进出13  101.00  101.71  2.641  2.1605
5   星展银行(中国)  20进出05  100.40  101.02  2.790  2.5700
6       平安证券  22进出05   98.38   99.40  2.970  2.7409
7       平安证券  22国开01   99.85   99.95  2.265  2.1401
8       平安证券  20农发08  101.88  102.90  2.882  2.5820
9       平安证券  20农发02   99.82  100.00  2.370  2.2000
10      平安证券  20进出05  100.48  101.02  2.760  2.5700
11      平安证券  20国开12  101.63  102.27  2.820  2.6200
12      平安证券  20国开08  100.21  100.85  2.820  2.6150
13      平安证券  20国开07  100.71  100.95  2.479  2.3074
14      平安证券  18进出03  102.35  102.52  2.295  2.1000
```

#### 现券市场成交行情

接口: bond_spot_deal

目标地址: https://www.chinamoney.com.cn/chinese/mkdatabond/

描述: 中国外汇交易中心暨全国银行间同业拆借中心-市场数据-市场行情-债券市场行情-现券市场成交行情

限量: 单次返回所有即期数据

输入参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| -   | -   | -   |

输出参数

| 名称    | 类型      | 描述       |
|-------|---------|----------|
| 债券简称  | object  | -        |
| 成交净价  | float64 | 注意单位: 元  |
| 最新收益率 | float64 | 注意单位: %  |
| 涨跌    | float64 | 注意单位: BP |
| 加权收益率 | float64 | 注意单位: %  |
| 交易量   | float64 | 注意单位: 亿  |

接口示例

```python
import akshare as ak

bond_spot_deal_df = ak.bond_spot_deal()
print(bond_spot_deal_df)
```

数据示例

```
                       债券简称    成交净价   最新收益率     涨跌   加权收益率  交易量
0                  23附息国债26  103.20  2.3000  -0.80  2.3168  NaN
1                  23附息国债18  101.31  2.3650  -0.80  2.3733  NaN
2                    20人民财险  101.15  4.1810  -0.23  4.1810  NaN
3              23无锡建投MTN003  100.87  2.7369    NaN  2.7369  NaN
4     22国新控股MTN005(能源保供特别债)  100.88  2.5900    NaN  2.5900  NaN
                     ...     ...     ...    ...     ...  ...
1775           22滨江房产MTN001  100.64  3.2700  -0.01  3.2700  NaN
1776            23建设银行CD059   97.44  1.8492   1.00  1.8492  NaN
1777            23华夏银行CD085   97.43  1.8505   1.00  1.8505  NaN
1778            24招商银行CD015   97.84  2.2099    NaN  2.2100  NaN
1779               24贴现国债13   99.88  1.7497  65.65  1.7497  NaN
[1780 rows x 6 columns]
```

#### 国债及其他债券收益率曲线

接口: bond_china_yield

目标地址: https://yield.chinabond.com.cn/cbweb-pbc-web/pbc/historyQuery?startDate=2019-02-07&endDate=2020-02-04&gjqx=0&qxId=ycqx&locale=cn_ZH

描述: 中国债券信息网-国债及其他债券收益率曲线

限量: 单次返回所有指定日期间 start_date 到 end_date 需要小于一年的所有数据

输入参数

| 名称         | 类型  | 描述                                                          |
|------------|-----|-------------------------------------------------------------|
| start_date | str | start_date="20190204", 指定开始日期; start_date 到 end_date 需要小于一年 |
| end_date   | str | end_date="20200204", 指定结束日期; start_date 到 end_date 需要小于一年   |

输出参数

| 名称   | 类型      | 描述  |
|------|---------|-----|
| 曲线名称 | object  |     |
| 日期   | object  |     |
| 3月   | float64 |     |
| 6月   | float64 |     |
| 1年   | float64 |     |
| 3年   | float64 |     |
| 5年   | float64 |     |
| 7年   | float64 |     |
| 10年  | float64 |     |
| 30年  | float64 |     |

接口示例

```python
import akshare as ak

bond_china_yield_df = ak.bond_china_yield(start_date="20210201", end_date="20220201")
print(bond_china_yield_df)
```

数据示例

```
                    曲线名称          日期      3月  ...      7年     10年     30年
0      中债中短期票据收益率曲线(AAA)  2021-02-01  3.2000  ...  3.9929  4.1465     NaN
1    中债商业银行普通债收益率曲线(AAA)  2021-02-01  3.1085  ...  3.8172  3.9554  4.5813
2              中债国债收益率曲线  2021-02-01  2.4501  ...  3.1725  3.1712  3.7342
3              中债国债收益率曲线  2021-02-02  2.4001  ...  3.1829  3.1862  3.7415
4      中债中短期票据收益率曲线(AAA)  2021-02-02  3.0242  ...  3.9707  4.1243     NaN
..                   ...         ...     ...  ...     ...     ...     ...
748  中债商业银行普通债收益率曲线(AAA)  2022-01-29  2.3008  ...  3.3258  3.4677  3.8345
749            中债国债收益率曲线  2022-01-29  1.7876  ...  2.6521  2.7013  3.2750
750    中债中短期票据收益率曲线(AAA)  2022-01-30  2.4486  ...  3.4477  3.6091     NaN
751  中债商业银行普通债收益率曲线(AAA)  2022-01-30  2.2908  ...  3.3122  3.4541  3.8209
752            中债国债收益率曲线  2022-01-30  1.7756  ...  2.6555  2.6997  3.2718
[753 rows x 10 columns]
```

### 沪深债券

#### 实时行情数据

接口: bond_zh_hs_spot

目标地址: https://vip.stock.finance.sina.com.cn/mkt/#hs_z

描述: 新浪财经-债券-沪深债券-实时行情数据

限量: 单次返回所有沪深债券的实时行情数据

输入参数

| 名称         | 类型  | 描述                                |
|------------|-----|-----------------------------------|
| start_page | str | start_page="1"; 开始获取的页面，每页 80 条数据 |
| end_page   | str | end_page="10"; 结束获取的页面，每页 80 条数据  |

输出参数

| 名称  | 类型      | 描述      |
|-----|---------|---------|
| 代码  | object  | -       |
| 名称  | object  | -       |
| 最新价 | float64 | -       |
| 涨跌额 | float64 | -       |
| 涨跌幅 | float64 | -       |
| 买入  | float64 | -       |
| 卖出  | float64 | -       |
| 昨收  | float64 | -       |
| 今开  | float64 | -       |
| 最高  | float64 | -       |
| 最低  | float64 | -       |
| 成交量 | int64   | 注意单位: 手 |
| 成交额 | int64   | 注意单位: 万 |

接口示例

```python
import akshare as ak

bond_zh_hs_spot_df = ak.bond_zh_hs_spot(start_page="1", end_page="5")
print(bond_zh_hs_spot_df)
```

数据示例

```
      代码      名称      最新价   涨跌额  ...   最高       最低    成交量   成交额
0    sh010107   21国债⑺  100.010  0.00  ...    0.000    0.000      0        0
1    sh010303   03国债⑶  100.010  0.00  ...    0.000    0.000      0        0
2    sh010504   05国债⑷  102.396  0.05  ...  102.397  102.395  10390  1063895
3    sh010512   05国债⑿  100.050  0.00  ...    0.000    0.000      0        0
4    sh010609   06国债⑼  100.000  0.00  ...    0.000    0.000      0        0
..        ...     ...      ...   ...  ...      ...      ...    ...      ...
395  sh020418  21贴债21   99.550  0.00  ...    0.000    0.000      0        0
396  sh020419  21贴债22   99.560  0.00  ...    0.000    0.000      0        0
397  sh020420  21贴债23   99.570  0.00  ...    0.000    0.000      0        0
398  sh020421  21贴债24   99.560  0.00  ...    0.000    0.000      0        0
399  sh020422  21贴债25   99.030  0.00  ...    0.000    0.000      0        0
[400 rows x 13 columns]
```

#### 历史行情数据

接口: bond_zh_hs_daily

目标地址: https://money.finance.sina.com.cn/bond/quotes/sh019315.html

描述: 新浪财经-债券-沪深债券-历史行情数据, 历史数据按日频率更新

限量: 单次返回具体某个沪深转债的所有历史行情数据

输入参数

| 名称     | 类型  | 描述                |
|--------|-----|-------------------|
| symbol | str | symbol="sh010107" |

输出参数

| 名称     | 类型      | 描述  |
|--------|---------|-----|
| date   | object  | -   |
| open   | float64 | -   |
| high   | float64 | -   |
| low    | float64 | -   |
| close  | float64 | -   |
| volume | float64 | -   |

接口示例

```python
import akshare as ak

bond_zh_hs_daily_df = ak.bond_zh_hs_daily(symbol="sh010107")
print(bond_zh_hs_daily_df)
```

数据示例

```
            date     open     high      low    close    volume
0     2001-08-20  101.255  102.835  101.255  102.695  60765500
1     2001-08-21  102.743  103.213  102.683  103.133  19927710
2     2001-08-22  103.332  103.402  103.022  103.222  13132740
3     2001-08-23  103.260  103.300  103.080  103.110   9544530
4     2001-08-24  103.158  103.158  102.908  102.958   7068480
          ...      ...      ...      ...      ...       ...
4801  2021-07-26  100.030  100.060  100.000  100.010   1757900
4802  2021-07-27  100.010  100.030  100.000  100.030   1635540
4803  2021-07-28  100.010  100.030  100.000  100.020   1425370
4804  2021-07-29  100.000  100.020  100.000  100.010    244680
4805  2021-07-30  100.000  100.010   99.990  100.010    150850
[4806 rows x 6 columns]
```

### 沪深可转债

#### 可转债-详情资料

接口: bond_cb_profile_sina

目标地址: https://money.finance.sina.com.cn/bond/info/sz128039.html

描述: 新浪财经-债券-可转债-详情资料

限量: 单次返回指定 symbol 的可转债-详情资料数据

输入参数

| 名称     | 类型  | 描述                            |
|--------|-----|-------------------------------|
| symbol | str | symbol="sz128039"; 带市场标识的转债代码 |

输出参数

| 名称    | 类型     | 描述 |
|-------|--------|----|
| item  | object | -  |
| value | object | -  |

接口示例

```python
import akshare as ak

bond_cb_profile_sina_df = ak.bond_cb_profile_sina(symbol="sz128039")
print(bond_cb_profile_sina_df)
```

数据示例

```
        item                                              value
0       债券名称                          2018年三力士股份有限公司公开发行可转换公司债券
1       债券简称                                               三力转债
2       债券代码                                           sz128039
3       债券类型                                             可转换企业债
4    债券面值（元）                                                100
5    债券年限（年）                                                  6
6    票面利率（%）                                                 --
7        到期日                                         2024-06-08
8        兑付日                                         2024-06-08
9        摘牌日                                                 --
10      计息方式                                               递进利率
11      利率说明  本次发行的可转债票面利率第一年0.3%、第二年0.5%、第三年1.0%、第四年1.3%、第五...
12      付息方式                                              周期性付息
13      起息日期                                         2018-06-08
14      止息日期                                         2024-06-07
15      付息日期                                              06-08
16     年付息次数                                                  1
17   发行价格（元）                                                100
18  发行规模（亿元）                                                6.2
19      发行日期                                         2018-06-08
20      上市日期                                         2018-06-29
21      上市场所                                              深圳交易所
22      信用等级                                                 A+
23  内部信用增级方式                                                 --
24  外部信用增级方式                                                 --
```

#### 可转债-债券概况

接口: bond_cb_summary_sina

目标地址: https://money.finance.sina.com.cn/bond/quotes/sh155255.html

描述: 新浪财经-债券-可转债-债券概况

限量: 单次返回指定 symbol 的可转债-债券概况数据

输入参数

| 名称     | 类型  | 描述                            |
|--------|-----|-------------------------------|
| symbol | str | symbol="sh155255"; 带市场标识的转债代码 |

输出参数

| 名称    | 类型     | 描述 |
|-------|--------|----|
| item  | object | -  |
| value | object | -  |

接口示例

```python
import akshare as ak

bond_cb_summary_sina_df = ak.bond_cb_summary_sina(symbol="sh155255")
print(bond_cb_summary_sina_df)
```

数据示例

```
        item       value
0       债券类型       普通企业债
1       计息方式        固定利率
2       付息方式       周期性付息
3    票面利率（%）        5.50
4      每年付息日       03-20
5    发行价格（元）         100
6   发行规模（亿元）          17
7    债券面值（元）         100
8    债券年限（年）           5
9       到期日期  2024-03-20
10     全价（元）          --
11   剩余年限（年）        0.62
12  到期收益率（%）          --
13      修正久期          --
14        凸性          --
```

#### 实时行情数据

接口: bond_zh_hs_cov_spot

目标地址: https://vip.stock.finance.sina.com.cn/mkt/#hskzz_z

描述: 新浪财经-沪深可转债数据

限量: 单次返回所有沪深可转债的实时行情数据

输入参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| -   | -   | -   |

输出参数

| 名称  | 类型  | 描述    |
|-----|-----|-------|
| -   | -   | 不逐一列出 |

接口示例

```python
import akshare as ak

bond_zh_hs_cov_spot_df = ak.bond_zh_hs_cov_spot()
print(bond_zh_hs_cov_spot_df)
```

数据示例

```
       symbol  name    trade pricechange  ...   volume     amount     code    ticktime
0    sh110044  广电转债  164.404      -7.464  ...   463810   78867792  110044  15:00:01
1    sh110045  海澜转债  135.440      -0.737  ...   814670  110615438  110045  15:00:01
2    sh110047  山鹰转债  109.574      -0.846  ...   610430   67002925  110047  15:00:01
3    sh110048  福能转债  161.464       0.083  ...  1146040  187753075  110048  15:00:01
4    sh110052  贵广转债  121.081      -4.359  ...   466970   58073701  110052  15:00:01
..        ...   ...      ...         ...  ...      ...        ...     ...       ...
555  sz128138  侨银转债  117.980      -0.220  ...    84537    9982766  128138  15:00:00
556  sz128141  旺能转债  114.511      -3.023  ...   328740   38135547  128141  15:00:00
557  sz128142  新乳转债  108.094      -0.223  ...   106070   11479168  128142  15:00:00
558  sz128143  锋龙转债  132.097      -7.166  ...  1053140  142958765  128143  15:00:00
559  sz128144  利民转债  104.313      -1.542  ...   161388   16911258  128144  15:00:00
[560 rows x 15 columns]
```

#### 历史行情数据-日频

接口: bond_zh_hs_cov_daily

目标地址: https://biz.finance.sina.com.cn/suggest/lookup_n.php?q=sh110048

描述: 新浪财经-历史行情数据，日频率更新, 新上的标的需要次日更新数据

限量: 单次返回具体某个沪深可转债的所有历史行情数据

输入参数

| 名称     | 类型  | 描述                |
|--------|-----|-------------------|
| symbol | str | symbol="sh113542" |

输出参数

| 名称     | 类型      | 描述  |
|--------|---------|-----|
| date   | object  | -   |
| open   | float64 | -   |
| high   | float64 | -   |
| low    | float64 | -   |
| close  | float64 | -   |
| volume | float64 | -   |

接口示例

```python
import akshare as ak

bond_zh_hs_cov_daily_df = ak.bond_zh_hs_cov_daily(symbol="sz128039")
print(bond_zh_hs_cov_daily_df)
```

数据示例

```
            date     open     high      low    close   volume
0     2018-06-29   91.470   95.200   89.400   89.500  1456563
1     2018-07-02   90.100   91.000   88.202   88.279   465913
2     2018-07-03   88.400   89.400   88.010   89.400   430800
3     2018-07-04   89.397   89.448   88.515   88.608   215772
4     2018-07-05   88.580   88.690   88.103   88.200   117494
          ...      ...      ...      ...      ...      ...
1370  2024-02-22  108.000  108.555  107.901  108.200   182005
1371  2024-02-23  108.200  109.296  107.600  107.800   125660
1372  2024-02-26  107.800  108.254  107.555  108.099   118131
1373  2024-02-27  107.980  108.222  107.672  107.800    81086
1374  2024-02-28  107.878  108.106  105.847  105.980   149757
[1375 rows x 6 columns]
```

#### 历史行情数据-分时

接口: bond_zh_hs_cov_min

目标地址: https://quote.eastmoney.com/concept/sz128039.html

描述: 东方财富网-可转债-分时行情

限量: 单次返回指定可转债、指定频率、复权调整和时间区间的分时数据, 其中 1 分钟数据只返回近 1 个交易日数据且不复权; 其余 period 只能获取近期的数据

输入参数

| 名称         | 类型  | 描述                                                                                                  |
|------------|-----|-----------------------------------------------------------------------------------------------------|
| symbol     | str | symbol='sz123106'; 转债代码                                                                             |
| period     | str | period='5'; choice of {'1', '5', '15', '30', '60'}; 其中 1 分钟数据返回近 1 个交易日数据且不复权                       |
| adjust     | str | adjust=''; choice of {'', 'qfq', 'hfq'}; '': 不复权, 'qfq': 前复权, 'hfq': 后复权, 其中 1 分钟数据返回近 1 个交易日数据且不复权 |
| start_date | str | start_date="1979-09-01 09:32:00"; 日期时间; 默认返回所有数据                                                    |
| end_date   | str | end_date="2222-01-01 09:32:00"; 日期时间; 默认返回所有数据                                                      |

输出参数-1分钟数据

| 名称  | 类型      | 描述      |
|-----|---------|---------|
| 时间  | object  | -       |
| 开盘  | float64 | -       |
| 收盘  | float64 | -       |
| 最高  | float64 | -       |
| 最低  | float64 | -       |
| 成交量 | float64 | 注意单位: 手 |
| 成交额 | float64 | -       |
| 最新价 | float64 | -       |

接口示例-1分钟数据

```python
import akshare as ak

bond_zh_hs_cov_min_df = ak.bond_zh_hs_cov_min(symbol="sz123124", period='1', adjust='', start_date="1979-09-01 09:32:00", end_date="2222-01-01 09:32:00")
print(bond_zh_hs_cov_min_df)
```

数据示例-1分钟数据

```
            时间           开盘     收盘  ...    成交量   成交额       最新价
0    2021-11-02 09:30:00  120.000  120.000  ...  195  234000.0  120.0000
1    2021-11-02 09:31:00  119.980  119.889  ...  390  467090.0  119.8855
2    2021-11-02 09:32:00  119.889  119.741  ...  115  137730.0  119.8657
3    2021-11-02 09:33:00  119.900  120.100  ...  542  649973.0  119.9286
4    2021-11-02 09:34:00  120.100  120.022  ...   83   99593.0  119.9326
..                   ...      ...      ...  ...  ...       ...       ...
236  2021-11-02 14:56:00  120.018  120.060  ...  150  180075.0  120.3760
237  2021-11-02 14:57:00  120.060  120.095  ...  289  346535.0  120.3732
238  2021-11-02 14:58:00  120.095  120.095  ...    0       0.0  120.3732
239  2021-11-02 14:59:00  120.095  120.095  ...    0       0.0  120.3732
240  2021-11-02 15:00:00  120.099  120.099  ...  198  237796.0  120.3719
```

输出参数-其他

| 名称  | 类型      | 描述      |
|-----|---------|---------|
| 时间  | object  | -       |
| 开盘  | float64 | -       |
| 收盘  | float64 | -       |
| 最高  | float64 | -       |
| 最低  | float64 | -       |
| 涨跌幅 | float64 | 注意单位: % |
| 涨跌额 | float64 | -       |
| 成交量 | float64 | 注意单位: 手 |
| 成交额 | float64 | -       |
| 振幅  | float64 | 注意单位: % |
| 换手率 | float64 | 注意单位: % |

接口示例-其他

```python
import akshare as ak

bond_zh_hs_cov_min_df = ak.bond_zh_hs_cov_min(symbol="sz123124", period='5', adjust='hfq', start_date="2021-09-01 09:32:00", end_date="2021-09-06 09:32:00")
print(bond_zh_hs_cov_min_df)
```

数据示例-其他

```
              时间       开盘       收盘  ...         成交额    振幅   换手率
0     2021-09-10 09:35:00  128.048  130.164  ...  12380872.0  1.78  1.83
1     2021-09-10 09:40:00  130.306  129.720  ...  11514303.0  1.12  1.70
2     2021-09-10 09:45:00  129.800  129.781  ...  12926171.0  0.87  1.90
3     2021-09-10 09:50:00  129.815  129.700  ...   9881294.0  0.53  1.45
4     2021-09-10 09:55:00  129.682  129.868  ...   7237449.0  0.51  1.07
                   ...      ...      ...  ...         ...   ...   ...
1483  2021-11-02 14:40:00  119.979  119.940  ...    462174.0  0.04  0.07
1484  2021-11-02 14:45:00  119.940  119.960  ...    582866.0  0.05  0.09
1485  2021-11-02 14:50:00  119.960  120.000  ...    394934.0  0.08  0.06
1486  2021-11-02 14:55:00  120.002  120.050  ...    634978.0  0.07  0.10
1487  2021-11-02 15:00:00  120.018  120.099  ...    764406.0  0.10  0.12
```

#### 历史行情数据-盘前分时

接口: bond_zh_hs_cov_pre_min

目标地址: https://quote.eastmoney.com/concept/sz128039.html

描述: 东方财富网-可转债-分时行情-盘前分时

限量: 单次返回指定可转债在最近一个交易日的盘前分时数据

输入参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| -   | -   | -   |

输出参数

| 名称  | 类型      | 描述      |
|-----|---------|---------|
| 时间  | object  | -       |
| 开盘  | float64 | -       |
| 收盘  | float64 | -       |
| 最高  | float64 | -       |
| 最低  | float64 | -       |
| 成交量 | float64 | 注意单位: 手 |
| 成交额 | float64 | -       |
| 最新价 | float64 | -       |

接口示例

```python
import akshare as ak

bond_zh_hs_cov_pre_min_df = ak.bond_zh_hs_cov_pre_min(symbol="sh113570")
print(bond_zh_hs_cov_pre_min_df)
```

数据示例

```
                 时间      开盘     收盘  ...  成交量     成交额    最新价
0    2022-07-27 09:15:00  128.14  128.14  ...    0       0.0  128.1400
1    2022-07-27 09:16:00  128.14  128.14  ...    0       0.0  127.9800
2    2022-07-27 09:17:00  128.03  128.03  ...    0       0.0  127.9800
3    2022-07-27 09:18:00  128.30  128.30  ...    0       0.0  127.9800
4    2022-07-27 09:19:00  128.30  128.30  ...    0       0.0  127.9800
..                   ...     ...     ...  ...  ...       ...       ...
131  2022-07-27 11:26:00  128.89  128.89  ...   37   47688.0  128.4185
132  2022-07-27 11:27:00  128.83  128.71  ...   27   34765.0  128.4193
133  2022-07-27 11:28:00  128.71  128.80  ...   21   27043.0  128.4201
134  2022-07-27 11:29:00  128.80  128.81  ...   85  109487.0  128.4232
135  2022-07-27 11:30:00  128.81  128.78  ...    7    9016.0  128.4235
```

#### 可转债数据一览表

接口: bond_zh_cov

目标地址: https://data.eastmoney.com/kzz/default.html

描述: 东方财富网-数据中心-新股数据-可转债数据一览表

限量: 单次返回当前交易时刻的所有可转债数据

输入参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| -   | -   | -   |

输出参数

| 名称          | 类型      | 描述       |
|-------------|---------|----------|
| 债券代码        | object  | -        |
| 债券简称        | object  | -        |
| 申购日期        | object  | -        |
| 申购代码        | object  | -        |
| 申购上限        | float64 | 注意单位: 万元 |
| 正股代码        | object  | -        |
| 正股简称        | object  | -        |
| 正股价         | float64 | -        |
| 转股价         | float64 | -        |
| 转股价值        | float64 | -        |
| 债现价         | float64 | -        |
| 转股溢价率       | float64 | 注意单位: %  |
| 原股东配售-股权登记日 | float64 | -        |
| 原股东配售-每股配售额 | object  | -        |
| 发行规模        | float64 | 注意单位: 亿元 |
| 中签号发布日      | object  | -        |
| 中签率         | float64 | 注意单位: %  |
| 上市时间        | object  | -        |
| 信用评级        | object  | -        |

接口示例

```python
import akshare as ak

bond_zh_cov_df = ak.bond_zh_cov()
print(bond_zh_cov_df)
```

数据示例

```
     债券代码   债券简称   申购日期    申购代码  ...   中签号发布日   中签率  上市时间  信用评级
0    113682   益丰转债  2024-03-04  754939  ...  2024-03-06  0.010597         NaT    AA
1    127105   龙星转债  2024-02-01  072442  ...  2024-02-05  0.005491  2024-03-06   AA-
2    123240   楚天转债  2024-01-31  370358  ...  2024-02-02  0.004307  2024-02-29    AA
3    127104   姚记转债  2024-01-25  072605  ...  2024-01-29  0.001866  2024-02-26    A+
4    123239   锋工转债  2024-01-19  370488  ...  2024-01-23  0.000734  2024-02-22    A+
..      ...    ...         ...     ...  ...         ...       ...         ...   ...
915  110227   赤化转债  2007-10-10  733227  ...  2007-10-16  0.158854  2007-10-23   AAA
916  126006  07深高债  2007-10-09  733548  ...  2007-10-15  0.290304  2007-10-30   AAA
917  110971   恒源转债  2007-09-24  733971  ...  2007-09-28  5.311774  2007-10-12   AAA
918  110567   山鹰转债  2007-09-05  733567  ...  2007-09-11  0.496391  2007-09-17    AA
919  110026   中海转债  2007-07-02  733026  ...  2007-07-06  1.333453  2007-07-12   AAA
[920 rows x 19 columns]
```

#### 可转债详情

接口: bond_zh_cov_info

目标地址: https://data.eastmoney.com/kzz/detail/123121.html

描述: 东方财富网-数据中心-新股数据-可转债详情

限量: 单次返回指定 symbol 的可转债详情数据

输入参数

| 名称        | 类型  | 描述                                                                                  |
|-----------|-----|-------------------------------------------------------------------------------------|
| symbol    | str | symbol="123121"; 可转债代码                                                              |
| indicator | str | indicator="基本信息"; choice of {"基本信息", "中签号", "筹资用途", "重要日期"}, 其中 "可转债重要条款" 在 "基本信息中" |

输出参数

| 名称   | 类型     | 描述        |
|------|--------|-----------|
| 债券代码 | object | 返回 67 个字段 |

接口示例

```python
import akshare as ak

bond_zh_cov_info_df = ak.bond_zh_cov_info(symbol="123121", indicator="基本信息")
print(bond_zh_cov_info_df)
```

数据示例

```
  SECURITY_CODE   SECUCODE TRADE_MARKET  ... IS_CONVERT_STOCK IS_REDEEM IS_SELLBACK
0        123121  123121.SZ       CNSESZ  ...                是         是           是
```

#### 可转债详情-同花顺

接口: bond_zh_cov_info_ths

目标地址: https://data.10jqka.com.cn/ipo/bond/

描述: 同花顺-数据中心-可转债

限量: 单次返回所有数据

输入参数

| 名称 | 类型 | 描述 |
|----|----|----|
| -  | -  | -  |

输出参数

| 名称     | 类型      | 描述      |
|--------|---------|---------|
| 债券代码   | object  | -       |
| 债券简称   | object  | -       |
| 申购日期   | object  | -       |
| 申购代码   | object  | -       |
| 原股东配售码 | object  | -       |
| 每股获配额  | float64 | -       |
| 计划发行量  | float64 | -       |
| 实际发行量  | float64 | -       |
| 中签公布日  | object  | -       |
| 中签号    | object  | -       |
| 上市日期   | object  | -       |
| 正股代码   | object  | -       |
| 正股简称   | object  | -       |
| 转股价格   | float64 | -       |
| 到期时间   | object  | -       |
| 中签率    | object  | 注意单位: % |

接口示例

```python
import akshare as ak

bond_zh_cov_info_ths_df = ak.bond_zh_cov_info_ths()
print(bond_zh_cov_info_ths_df)
```

数据示例

```
    债券代码  债券简称   申购日期    申购代码  ...  正股简称   转股价格   到期时间      中签率
0    123247  万凯转债  2024-08-16  371216  ...  万凯新材   0.00  2030-08-16  0.00000000
1    123246  远信转债  2024-08-16  371053  ...  远信工业   0.00  2030-08-16  0.00000000
2    123245  集智转债  2024-08-14  370553  ...  集智股份  23.54  2030-08-14  0.00000000
3    127106  伟隆转债  2024-08-13  072871  ...  伟隆股份   8.60  2030-08-13  0.00042180
4    110096  豫光转债  2024-08-12  733531  ...  豫光金铅   6.17  2030-08-12  0.00408851
..      ...   ...         ...     ...  ...   ...    ...         ...         ...
844  128015  久其转债  2017-06-08  072279  ...  久其软件   5.00  2023-06-08  2.38141745
845  127004  模塑转债  2017-06-02  070700  ...  模塑科技   7.24  2023-06-02  0.50864100
846  128014  永东转债  2017-04-17  072753  ...  永东股份   9.28  2023-04-17  1.32286600
847  113012  骆驼转债  2017-03-24  783311  ...  骆驼股份   9.86  2023-03-24  0.15814823
848  113011  光大转债  2017-03-17  783818  ...  光大银行   3.35  2023-03-17  0.49992510
[849 rows x 16 columns]
```

#### 可转债比价表

接口: bond_cov_comparison

目标地址: https://quote.eastmoney.com/center/fullscreenlist.html#convertible_comparison

描述: 东方财富网-行情中心-债券市场-可转债比价表

限量: 单次返回当前交易时刻的所有可转债比价数据

输入参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| -   | -   | -   |

输出参数

| 名称    | 类型      | 描述      |
|-------|---------|---------|
| 序号    | int32   | -       |
| 转债代码  | object  | -       |
| 转债名称  | object  | -       |
| 转债最新价 | object  | -       |
| 转债涨跌幅 | object  | 注意单位: % |
| 正股代码  | object  | -       |
| 正股名称  | object  | -       |
| 正股最新价 | object  | -       |
| 正股涨跌幅 | object  | 注意单位: % |
| 转股价   | object  | -       |
| 转股价值  | object  | -       |
| 转股溢价率 | object  | 注意单位: % |
| 纯债溢价率 | object  | 注意单位: % |
| 回售触发价 | object  | -       |
| 强赎触发价 | object  | -       |
| 到期赎回价 | object  | -       |
| 纯债价值  | float64 | -       |
| 开始转股日 | object  | -       |
| 上市日期  | object  | -       |
| 申购日期  | object  | -       |

接口示例

```python
import akshare as ak

bond_cov_comparison_df = ak.bond_cov_comparison()
print(bond_cov_comparison_df)
```

数据示例

```
      序号 转债代码  转债名称  转债最新价  ... 纯债价值   开始转股日  上市日期   申购日期
0      1  127105   龙星转债        -  ...   87.5843  20240807  20240306  20240201
1      2  123240   楚天转债        -  ...   95.2117  20240806  20240229  20240131
2      3  127104   姚记转债  145.688  ...   78.7398  20240731  20240226  20240125
3      4  123239   锋工转债   120.99  ...   77.9967  20240725  20240222  20240119
4      5  123238  卡倍转02  120.322  ...   78.1354  20240717  20240201  20240111
..   ...     ...    ...      ...  ...       ...       ...       ...       ...
545  546  127006   敖东转债  105.995  ...  104.9099  20180919  20180511  20180313
546  547  127005   长证转债   104.89  ...  104.9193  20180917  20180411  20180312
547  548  128036   金农转债  105.732  ...  105.6951  20180917  20180402  20180309
548  549  113505   杭电转债   107.85  ...  107.9525  20180912  20180327  20180306
549  550  113504   艾华转债  105.924  ...  105.9800  20180910  20180323  20180302
[550 rows x 20 columns]
```

#### 可转债价值分析

接口: bond_zh_cov_value_analysis

目标地址: https://data.eastmoney.com/kzz/detail/113527.html

描述: 东方财富网-行情中心-新股数据-可转债数据-可转债价值分析

限量: 单次返回所有可转债价值分析数据

输入参数

| 名称     | 类型  | 描述                     |
|--------|-----|------------------------|
| symbol | str | symbol="113527"; 可转债代码 |

输出参数

| 名称    | 类型      | 描述      |
|-------|---------|---------|
| 日期    | object  | -       |
| 收盘价   | float64 | 注意单位: 元 |
| 纯债价值  | float64 | 注意单位: 元 |
| 转股价值  | float64 | 注意单位: 元 |
| 纯债溢价率 | float64 | 注意单位: % |
| 转股溢价率 | float64 | 注意单位: % |

接口示例

```python
import akshare as ak

bond_zh_cov_value_analysis_df = ak.bond_zh_cov_value_analysis(symbol="113527")
print(bond_zh_cov_value_analysis_df)
```

数据示例

```
        日期      收盘价      纯债价值      转股价值    纯债溢价率    转股溢价率
0     2019-01-24      NaN   90.156612   98.195187  10.918098   1.837985
1     2019-01-25      NaN   90.015417   98.729947  11.092081   1.286391
2     2019-01-28      NaN   90.070170   97.593583  11.024549   2.465753
3     2019-01-29      NaN   90.075261   94.585561  11.018274   5.724382
4     2019-01-30      NaN   89.976132   94.184492  11.140586   6.174592
          ...      ...         ...         ...        ...        ...
1229  2024-02-22  122.183  112.436703   98.963731   8.668252  23.462403
1230  2024-02-23  121.745  112.471066   99.274611   8.245618  22.634577
1231  2024-02-26  122.580  112.497496  102.383420   8.962425  19.726417
1232  2024-02-27  122.030  112.525381  103.212435   8.446645  18.231878
1233  2024-02-28  116.601  112.526608   92.953368   3.620825  25.440318
[1234 rows x 6 columns]
```

#### 可转债溢价率分析

接口: bond_zh_cov_value_analysis

目标地址: https://data.eastmoney.com/kzz/detail/113527.html

描述: 东方财富网-行情中心-新股数据-可转债数据-可转债溢价率分析

限量: 单次返回所有可转债溢价率分析数据；此接口同 bond_zh_cov_value_analysis

输入参数

| 名称     | 类型  | 描述                     |
|--------|-----|------------------------|
| symbol | str | symbol="113527"; 可转债代码 |

输出参数

| 名称    | 类型      | 描述      |
|-------|---------|---------|
| 日期    | object  | -       |
| 收盘价   | float64 | 注意单位: 元 |
| 纯债价值  | float64 | 注意单位: 元 |
| 转股价值  | float64 | 注意单位: 元 |
| 纯债溢价率 | float64 | 注意单位: % |
| 转股溢价率 | float64 | 注意单位: % |

接口示例

```python
import akshare as ak

bond_zh_cov_value_analysis_df = ak.bond_zh_cov_value_analysis(symbol="113527")
print(bond_zh_cov_value_analysis_df)
```

数据示例

```
      日期     收盘价      纯债价值      转股价值    纯债溢价率    转股溢价率
0    2019-01-24     NaN   90.186522   98.195187  10.881313   1.837985
1    2019-01-25     NaN   90.045626   98.729947  11.054811   1.286391
2    2019-01-28     NaN   90.100390   97.593583  10.987310   2.465753
3    2019-01-29     NaN   90.105909   94.585561  10.980512   5.724382
4    2019-01-30     NaN   90.006260   94.184492  11.103384   6.174592
..          ...     ...         ...         ...        ...        ...
766  2022-03-24  123.46  107.178436  102.871795  15.191082  20.013460
767  2022-03-25  122.83  107.241827  103.076923  14.535535  19.163433
768  2022-03-28  123.37  107.300045  102.564103  14.976652  20.285750
769  2022-03-29  122.37  107.319545  101.025641  14.023964  21.127665
770  2022-03-30  122.75  107.335419  102.358974  14.361132  19.921092
```

### 质押式回购

#### 上证质押式回购

接口: bond_sh_buy_back_em

目标地址: https://quote.eastmoney.com/center/gridlist.html#bond_sh_buyback

描述: 东方财富网-行情中心-债券市场-上证质押式回购

限量: 单次返回所有行情数据

输入参数

| 名称 | 类型 | 描述 |
|----|----|----|
| -  | -  | -  |

输出参数

| 名称  | 类型      | 描述 |
|-----|---------|----|
| 序号  | int64   | -  |
| 代码  | object  | -  |
| 名称  | object  | -  |
| 最新价 | float64 | -  |
| 涨跌额 | float64 | -  |
| 涨跌幅 | float64 | -  |
| 今开  | float64 | -  |
| 最高  | float64 | -  |
| 最低  | float64 | -  |
| 昨收  | float64 | -  |
| 成交量 | float64 | -  |
| 成交额 | float64 | -  |

接口示例

```python
import akshare as ak

bond_sh_buy_back_em_df = ak.bond_sh_buy_back_em()
print(bond_sh_buy_back_em_df)
```

数据示例

```
   序号 代码    名称    最新价  ...  最低    昨收    成交量          成交额
0   1  204001  GC001  1.600  ...  1.200  1.440  1820652598  1.820653e+12
1   2  204007  GC007  1.735  ...  1.600  1.715   189609010  1.896090e+11
2   3  204014  GC014  1.745  ...  1.600  1.750    26214422  2.621442e+10
3   4  204004  GC004  1.560  ...  1.200  1.475    18866666  1.886667e+10
4   5  204003  GC003  1.565  ...  1.250  1.485    11683291  1.168329e+10
5   6  204028  GC028  1.795  ...  1.630  1.810    10913644  1.091364e+10
6   7  204002  GC002  1.580  ...  1.200  1.470    10655418  1.065542e+10
7   8  204091  GC091  1.795  ...  1.655  1.795      751463  7.514630e+08
8   9  204182  GC182  1.755  ...  1.600  1.800       84107  8.410700e+07
[9 rows x 12 columns]
```

#### 深证质押式回购

接口: bond_sz_buy_back_em

目标地址: https://quote.eastmoney.com/center/gridlist.html#bond_sz_buyback

描述: 东方财富网-行情中心-债券市场-深证质押式回购

限量: 单次返回所有行情数据

输入参数

| 名称 | 类型 | 描述 |
|----|----|----|
| -  | -  | -  |

输出参数

| 名称  | 类型      | 描述 |
|-----|---------|----|
| 序号  | int64   | -  |
| 代码  | object  | -  |
| 名称  | object  | -  |
| 最新价 | float64 | -  |
| 涨跌额 | float64 | -  |
| 涨跌幅 | float64 | -  |
| 今开  | float64 | -  |
| 最高  | float64 | -  |
| 最低  | float64 | -  |
| 昨收  | float64 | -  |
| 成交量 | float64 | -  |
| 成交额 | float64 | -  |

接口示例

```python
import akshare as ak

bond_sz_buy_back_em_df = ak.bond_sz_buy_back_em()
print(bond_sz_buy_back_em_df)
```

数据示例

```
   序号  代码    名称   最新价  ...    最低   昨收     成交量          成交额
0   1  131810  Ｒ-001  1.555  ...  1.305  1.410  213447602  2.134476e+11
1   2  131801  Ｒ-007  1.735  ...  1.405  1.715   19034425  1.903442e+10
2   3  131800  Ｒ-003  1.490  ...  1.015  1.430    3258575  3.258575e+09
3   4  131809  Ｒ-004  1.500  ...  0.110  1.465    2571117  2.571117e+09
4   5  131811  Ｒ-002  1.525  ...  1.200  1.455    1620576  1.620576e+09
5   6  131802  Ｒ-014  1.750  ...  0.010  1.785    1312069  1.312069e+09
6   7  131803  Ｒ-028  1.740  ...  1.600  1.790     986564  9.865640e+08
7   8  131805  Ｒ-091  1.680  ...  1.000  1.815      20476  2.047600e+07
8   9  131806  Ｒ-182  1.660  ...  1.600  1.730      14124  1.412400e+07
[9 rows x 12 columns]
```

#### 质押式回购历史数据

接口: bond_buy_back_hist_em

目标地址: https://quote.eastmoney.com/center/gridlist.html#bond_sh_buyback

描述: 东方财富网-行情中心-债券市场-质押式回购-历史数据

限量: 单次返回所有历史行情数据

输入参数

| 名称     | 类型  | 描述                       |
|--------|-----|--------------------------|
| symbol | str | symbol="204001"; 质押式回购代码 |

输出参数

| 名称  | 类型      | 描述 |
|-----|---------|----|
| 日期  | int64   | -  |
| 开盘  | float64 | -  |
| 收盘  | float64 | -  |
| 最高  | float64 | -  |
| 最低  | float64 | -  |
| 成交量 | float64 | -  |
| 成交额 | float64 | -  |

接口示例

```python
import akshare as ak

bond_buy_back_hist_em_df = ak.bond_buy_back_hist_em(symbol="204001")
print(bond_buy_back_hist_em_df)
```

数据示例

```
           日期    开盘  收盘    最高    最低        成交量     成交额
0     2006-05-08  1.50  1.500  1.500  1.50         100  1.000000e+05
1     2006-05-16  1.65  1.650  1.650  1.65       10000  1.000000e+07
2     2006-05-22  1.62  1.620  1.620  1.62         500  5.000000e+05
3     2006-06-01  3.06  3.060  3.060  3.06       50000  5.000000e+07
4     2006-06-12  1.62  1.620  1.620  1.62         400  4.000000e+05
...          ...   ...    ...    ...   ...         ...           ...
4565  2025-03-28  2.25  3.595  5.100  2.22  1777635143  1.777635e+12
4566  2025-03-31  2.90  2.780  3.900  1.30  1900163681  1.900164e+12
4567  2025-04-01  2.11  1.745  2.130  1.65  1848921966  1.848922e+12
4568  2025-04-02  1.86  1.440  1.955  1.20  1797375867  1.797376e+12
4569  2025-04-03  1.80  1.600  1.815  1.20  1820652598  1.820653e+12
[4570 rows x 7 columns]
```

### 可转债实时数据-集思录

接口: bond_cb_jsl

目标地址: https://www.jisilu.cn/data/cbnew/#cb

描述: 集思录可转债实时数据，包含行情数据（涨跌幅，成交量和换手率等）及可转债基本信息（转股价，溢价率和到期收益率等）

限量: 单次返回当前交易时刻的所有数据

输入参数

| 名称     | 类型  | 描述                                                 |
|--------|-----|----------------------------------------------------|
| cookie | str | cookie=''; 此处输入您的集思录 cookie 就可以获取完整数据，否则只能返回前 30 条 |

1. 需要查看的链接为：https://app.jisilu.cn/data/cbnew/
2. 需要复制的 Cookie 为：![](https://pic2.zhimg.com/80/v2-c66f56a334e2c5642a9c8e2975b2f871_1440w.webp)
3. 参考文章：[如何拿到集思录的可转债实时数据](https://zhuanlan.zhihu.com/p/607755294)

输出参数

| 名称     | 类型      | 描述       |
|--------|---------|----------|
| 代码     | object  | -        |
| 转债名称   | object  | -        |
| 现价     | float64 | -        |
| 涨跌幅    | float64 | 注意单位: %  |
| 正股代码   | object  | -        |
| 正股名称   | object  | -        |
| 正股价    | float64 | -        |
| 正股涨跌   | float64 | 注意单位: %  |
| 正股PB   | float64 | -        |
| 转股价    | float64 | -        |
| 转股价值   | float64 | -        |
| 转股溢价率  | float64 | 注意单位: %  |
| 债券评级   | object  | -        |
| 回售触发价  | float64 | -        |
| 强赎触发价  | float64 | -        |
| 转债占比   | float64 | 注意单位: %  |
| 到期时间   | object  | -        |
| 剩余年限   | float64 | -        |
| 剩余规模   | float64 | 注意单位: 亿元 |
| 成交额    | float64 | 注意单位: 万元 |
| 换手率    | float64 | 注意单位: %  |
| 到期税前收益 | float64 | 注意单位: %  |
| 双低     | float64 | -        |

接口示例

```python
import akshare as ak

bond_cb_jsl_df = ak.bond_cb_jsl(cookie="您的集思录 cookie")
print(bond_cb_jsl_df)
```

数据示例

```
    代码   转债名称      现价    涨跌幅  ...    成交额    换手率  到期税前收益  双低
0   404004   汇车退债   41.318 -18.13  ...     985.32     1.02  124.58   23.96
1   123174   精锻转债  171.157   9.82  ...  619050.35   571.01   -9.27  167.83
2   127094   红墙转债  169.460   0.87  ...  694959.33  1337.11   -7.53  168.43
3   123243   严牌转债  130.278  -0.09  ...    9868.03    18.47   -1.79  129.40
4   113690  豪24转债  250.879   4.94  ...  205346.07   197.79  -13.14  250.20
5   123078   Z凯转债  123.999  -1.98  ...   78852.22  1028.44     NaN  123.38
6   123249   英搏转债  181.826  -1.82  ...   50419.49    36.36   -8.19  181.23
7   113637   华翔转债  155.824   9.49  ...  332671.71   786.13  -10.01  155.35
8   123213   天源转债  235.149   0.32  ...   21350.97    18.73  -15.55  234.73
9   127055   精装转债  163.000   2.71  ...   35810.85    78.76  -11.02  162.62
10  128133   奇正转债  131.500   1.53  ...   10088.62     9.87  -10.26  131.30
11  113050   南银转债  129.367  -0.13  ...   15545.42     1.32   -7.67  129.31
12  110079   杭银转债  134.733  -0.23  ...   41122.60     5.79  -10.46  134.68
13  127053   豪美转债  206.500   4.67  ...  124653.85   210.95  -17.97  206.67
14  113065   齐鲁转债  127.507  -0.11  ...   16181.16     1.92   -3.18  127.72
15  128144   利民转债  172.187   1.58  ...   71165.54    93.54  -21.66  172.73
16  127076   中宠转2  232.000   2.43  ...  202273.42   152.24  -18.48  232.56
17  118023   广大转债  128.185   0.89  ...   13487.13     6.83   -2.12  129.06
18  111011   冠盛转债  229.721   1.93  ...   75634.98   115.93  -16.71  230.61
19  118004   博瑞转债  142.506  -0.45  ...   15675.52    23.43   -6.72  143.42
20  127081   中旗转债  351.000   3.85  ...  799563.46   687.46  -25.67  351.93
21  123186   志特转债  158.000  -4.24  ...   32129.43    47.22   -6.95  159.07
22  127043   川恒转债  137.600   3.39  ...   31220.26    21.94   -6.40  138.91
23  123089   九洲转2  144.450  -1.40  ...   55067.00   189.12  -12.25  145.79
24  123235   亿田转债  187.132  -0.46  ...   18737.02    20.04   -9.35  188.72
25  123204   金丹转债  132.370   0.97  ...    8120.82    11.76   -2.43  134.06
26  123209   聚隆转债  153.098   2.74  ...   39885.48   144.38   -6.04  154.90
27  123245   集智转债  235.000   4.65  ...   91552.39   256.32  -12.07  237.05
28  113561   正裕转债  139.872   1.99  ...    4939.26    25.79  -31.90  142.01
29  113030   东风转债  133.583  -0.05  ...    4672.41    17.79  -26.68  135.83
[30 rows x 23 columns]
```

### 可转债强赎

接口: bond_cb_redeem_jsl

目标地址: https://www.jisilu.cn/data/cbnew/#redeem

描述: 集思录可转债-强赎

限量: 单次返回所有数据

输入参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| -   | -   | -   |

输出参数

| 名称    | 类型      | 描述      |
|-------|---------|---------|
| 代码    | object  | -       |
| 名称    | object  | -       |
| 现价    | float64 | -       |
| 正股代码  | object  | -       |
| 正股名称  | object  | -       |
| 规模    | float64 | 注意单位: 亿 |
| 剩余规模  | float64 | -       |
| 转股起始日 | object  | -       |
| 最后交易日 | object  | -       |
| 到期日   | object  | -       |
| 转股价   | float64 | -       |
| 强赎触发比 | int64   | 注意单位: % |
| 强赎触发价 | float64 | -       |
| 正股价   | float64 | -       |
| 强赎价   | float64 | -       |
| 强赎天计数 | object  | -       |
| 强赎条款  | object  | -       |
| 强赎状态  | object  | -       |

接口示例

```python
import akshare as ak

bond_cb_redeem_jsl_df = ak.bond_cb_redeem_jsl()
print(bond_cb_redeem_jsl_df)
```

数据示例

```
         代码    名称       现价  ...       强赎天计数                           强赎条款   强赎状态
0    123084  高澜转债  144.600  ...  25/15 | 30  如果公司股票在任意连续三十个交易日中至少十五个交易日...  已公告强赎
1    123070  鹏辉转债  369.543  ...  30/20 | 30  如果公司A股股票连续30个交易日中至少有20个交易日...  已公告强赎
2    113599  嘉友转债  139.980  ...  22/15 | 30  如果公司股票连续三十个交易日中至少有十五个交易日的收...  已公告强赎
3    113630  赛伍转债  131.550  ...  20/15 | 30  如果公司 A 股股票连续 30 个交易日中至少有15...  已公告强赎
4    123073  同和转债  148.460  ...  25/20 | 30  如果公司A股股票连续三十个交易日中至少有二十个交易日...  已公告强赎
..      ...   ...      ...  ...         ...                            ...    ...
439  113639  华正转债  113.270  ...   0/15 | 30  如果公司股票在任何连续三十个交易日中至少十五个交易日...
440  123136  城市转债  244.200  ...   0/15 | 30  如果公司股票在任意连续三十个交易日中至少有十五个交易...
441  123137  锦浪转债  166.490  ...   0/15 | 30  如果公司股票在任意连续三十个交易日中至少十五个交易日...
442  123138  丝路转债  144.700  ...   0/15 | 30  如果公司股票在任何连续三十个交易日中至少十五个交易日...
443  113646  永吉转债  253.950  ...   0/15 | 30  公司股票在任何连续三十个交易日中至少有十五个交易日的...
```

### 集思录可转债等权指数

接口: bond_cb_index_jsl

目标地址: https://www.jisilu.cn/web/data/cb/index

描述: 可转债-集思录可转债等权指数

限量: 单次返回所有历史数据数据

输入参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| -   | -   | -   |

输出参数

| 名称                  | 类型      | 描述        |
|---------------------|---------|-----------|
| price_dt            | object  | 日期        |
| price               | float64 | 指数        |
| amount              | float64 | 剩余规模(亿元)  |
| volume              | float64 | 成交额(亿元)   |
| count               | int64   | 数量        |
| increase_val        | float64 | 涨跌        |
| increase_rt         | float64 | 涨幅        |
| avg_price           | float64 | 平均价格(元)   |
| mid_price           | float64 | 中位数价格(元)  |
| mid_convert_value   | float64 | 中位数转股价值   |
| avg_dblow           | float64 | 平均双底      |
| avg_premium_rt      | float64 | 平均溢价率     |
| mid_premium_rt      | float64 | 中位数溢价率    |
| avg_ytm_rt          | float64 | 平均收益率     |
| turnover_rt         | float64 | 换手率       |
| price_90            | int64   | >90       |
| price_90_100        | int64   | 90~100    |
| price_100_110       | int64   | 100~110   |
| price_110_120       | int64   | 110~120   |
| price_120_130       | int64   | 120~130   |
| price_130           | int64   | >130      |
| increase_rt_90      | float64 | >90涨幅     |
| increase_rt_90_100  | float64 | 90~100涨幅  |
| increase_rt_100_110 | float64 | 100~110涨幅 |
| increase_rt_110_120 | float64 | 110~120涨幅 |
| increase_rt_120_130 | float64 | 120~130涨幅 |
| increase_rt_130     | float64 | >130涨幅    |
| idx_price           | float64 | 沪深300指数   |
| idx_increase_rt     | float64 | 沪深300指数涨幅 |

接口示例

```python
import akshare as ak

bond_cb_index_jsl_df = ak.bond_cb_index_jsl()
print(bond_cb_index_jsl_df)
```

数据示例

```
        price_dt     price  ...  idx_price  idx_increase_rt
0     2017-12-29  1000.000  ...   4030.850             0.30
1     2018-01-02  1008.831  ...   4087.400             1.40
2     2018-01-03  1018.808  ...   4111.390             0.59
3     2018-01-04  1024.344  ...   4128.810             0.42
4     2018-01-05  1034.655  ...   4138.750             0.24
...          ...       ...  ...        ...              ...
1146  2022-09-19  1997.231  ...   3928.000            -0.12
1147  2022-09-20  2012.310  ...   3932.836             0.12
1148  2022-09-21  2019.373  ...   3903.735            -0.74
1149  2022-09-22  2017.313  ...   3869.344            -0.88
1150  2022-09-23  1998.653  ...   3856.021            -0.34
```

### 可转债转股价格调整记录-集思录

接口: bond_cb_adj_logs_jsl

目标地址: https://app.jisilu.cn/data/cbnew/#cb; 点击带红色星号的转股价会弹出转股价调整记录

描述: 集思录-单个可转债的转股价格-调整记录

限量: 返回当前时刻该可转债的所有转股价格调整记录

输入参数

| 名称     | 类型  | 描述                     |
|--------|-----|------------------------|
| symbol | str | symbol="128013"; 可转债代码 |

输出参数

| 名称       | 类型      | 描述  |
|----------|---------|-----|
| 转债名称     | object  | -   |
| 股东大会日    | object  | -   |
| 下修前转股价   | float64 | -   |
| 下修后转股价   | float64 | -   |
| 新转股价生效日期 | object  | -   |
| 下修底价     | float64 | -   |

接口示例

```python
import akshare as ak

bond_convert_adj_logs_jsl_df = ak.bond_cb_adj_logs_jsl(symbol="128013")
print(bond_convert_adj_logs_jsl_df)
```

数据示例

```
   转债名称  股东大会日  下修前转股价 下修后转股价 新转股价生效日期 下修底价
0  洪涛转债  2021-02-23    3.10    2.32  2021-02-24  2.32
1  洪涛转债  2020-06-29    8.00    3.12  2020-06-30  3.12
2  洪涛转债  2019-11-20    9.97    8.00  2019-11-21  3.10
```

### 收盘收益率曲线历史数据

接口: bond_china_close_return

目标地址: https://www.chinamoney.com.cn/chinese/bkcurvclosedyhis/?bondType=CYCC000&reference=1

描述: 收盘收益率曲线历史数据, 该接口只能获取近 3 个月的数据，且每次获取的数据不超过 1 个月

输入参数

| 名称         | 类型  | 描述                                                                       |
|------------|-----|--------------------------------------------------------------------------|
| symbol     | str | symbol="政策性金融债(进出口行)"; 通过网页查询或调用 **ak.bond_china_close_return_map()** 获取 |
| period     | str | period: str = "1"; 期限间隔, choice of {'0.1', '0.5', '1'}                   |
| start_date | str | start_date="20231101"; 结束日期, 结束日期和开始日期不要超过 1 个月                          |
| end_date   | str | end_date="20231101"; 结束日期, 结束日期和开始日期不要超过 1 个月                            |

输出参数

| 名称    | 类型      | 描述  |
|-------|---------|-----|
| 日期    | object  | -   |
| 期限    | float64 | -   |
| 到期收益率 | float64 | -   |
| 即期收益率 | float64 | -   |
| 远期收益率 | float64 | -   |

接口示例

```python
import akshare as ak

bond_china_close_return_df = ak.bond_china_close_return(symbol="国债", period="1", start_date="20231101", end_date="20231101")
print(bond_china_close_return_df)
```

数据示例

```
       日期       期限   到期收益率 即期收益率  远期收益率
0   2023-11-01   0.083  1.7031  1.7031     NaN
1   2023-11-01   0.250  2.3102  2.3102     NaN
2   2023-11-01   0.500  2.3825  2.3825     NaN
3   2023-11-01   0.750  2.2547  2.2547     NaN
4   2023-11-01   1.000  2.2452  2.2452  2.5188
5   2023-11-01   2.000  2.3803  2.3819  2.5649
6   2023-11-01   3.000  2.4403  2.4429  2.6295
7   2023-11-01   4.000  2.4858  2.4895  2.7256
8   2023-11-01   5.000  2.5313  2.5367  3.0216
9   2023-11-01   6.000  2.6076  2.6174  3.1898
10  2023-11-01   7.000  2.6838  2.6989  2.6956
11  2023-11-01   8.000  2.6851  2.6985  2.6986
12  2023-11-01   9.000  2.6865  2.6985  2.7016
13  2023-11-01  10.000  2.6878  2.6988  2.9891
14  2023-11-01  11.000  2.7115  2.7252  3.0458
```

### 中美国债收益率

接口: bond_zh_us_rate

目标地址: https://data.eastmoney.com/cjsj/zmgzsyl.html

描述: 东方财富网-数据中心-经济数据-中美国债收益率历史数据

限量: 返回 start_date 开始后的所有交易日的数据; 数据从 19901219 开始

输入参数

| 名称         | 类型  | 描述                    |
|------------|-----|-----------------------|
| start_date | str | start_date="19901219" |

输出参数

| 名称            | 类型      | 描述  |
|---------------|---------|-----|
| 日期            | object  | -   |
| 中国国债收益率2年     | float64 | -   |
| 中国国债收益率5年     | float64 | -   |
| 中国国债收益率10年    | float64 | -   |
| 中国国债收益率30年    | float64 | -   |
| 中国国债收益率10年-2年 | float64 | -   |
| 中国GDP年增率      | float64 | -   |
| 美国国债收益率2年     | float64 | -   |
| 美国国债收益率5年     | float64 | -   |
| 美国国债收益率10年    | float64 | -   |
| 美国国债收益率30年    | float64 | -   |
| 美国国债收益率10年-2年 | float64 | -   |
| 美国GDP年增率      | float64 | -   |

接口示例

```python
import akshare as ak

bond_zh_us_rate_df = ak.bond_zh_us_rate(start_date="19901219")
print(bond_zh_us_rate_df)
```

数据示例

```
     日期  中国国债收益率2年  中国国债收益率5年  ...  美国国债收益率30年  美国国债收益率10年-2年  美国GDP年增率
0     1990-12-19        NaN        NaN  ...        8.19           0.79       NaN
1     1990-12-20        NaN        NaN  ...        8.22           0.80       NaN
2     1990-12-21        NaN        NaN  ...        8.28           0.81       NaN
3     1990-12-24        NaN        NaN  ...        8.36           0.85       NaN
4     1990-12-26        NaN        NaN  ...        8.30           0.88       NaN
          ...        ...        ...  ...         ...            ...       ...
8595  2023-11-27     2.4719     2.6012  ...        4.53          -0.45       NaN
8596  2023-11-28     2.4369     2.5762  ...        4.52          -0.39       NaN
8597  2023-11-29     2.4460     2.5840  ...        4.44          -0.37       NaN
8598  2023-11-30     2.4259     2.5691  ...        4.54          -0.36       NaN
8599  2023-12-01     2.4169     2.5665  ...        4.40          -0.34       NaN
[8600 rows x 13 columns]
```

### 债券发行

#### 国债发行

接口: bond_treasure_issue_cninfo

目标地址: https://webapi.cninfo.com.cn/#/thematicStatistics

描述: 巨潮资讯-数据中心-专题统计-债券报表-债券发行-国债发行

输入参数

| 名称         | 类型  | 描述                    |
|------------|-----|-----------------------|
| start_date | str | start_date="20210911" |
| end_date   | str | end_date="20211110"   |

输出参数

| 名称     | 类型      | 描述       |
|--------|---------|----------|
| 债券代码   | object  | -        |
| 债券简称   | object  | -        |
| 发行起始日  | object  | -        |
| 发行终止日  | object  | -        |
| 计划发行总量 | float64 | 注意单位: 亿元 |
| 实际发行总量 | float64 | 注意单位: 亿元 |
| 发行价格   | float64 | 注意单位: 元  |
| 单位面值   | int64   | 注意单位: 元  |
| 缴款日    | object  | -        |
| 增发次数   | int64   | -        |
| 交易市场   | object  | -        |
| 发行方式   | object  | -        |
| 发行对象   | object  | -        |
| 公告日期   | object  | -        |
| 债券名称   | object  | -        |

接口示例

```python
import akshare as ak

bond_treasure_issue_cninfo_df = ak.bond_treasure_issue_cninfo(start_date="20210910", end_date="20211109")
print(bond_treasure_issue_cninfo_df)
```

数据示例

```
       债券代码      债券简称  ...        公告日期                债券名称
0    020450    21贴债53  ...  2021-10-29  2021年记账式贴现(五十三期)国债
1    108450    贴债2153  ...  2021-10-29  2021年记账式贴现(五十三期)国债
2    219953  21贴现国债53  ...  2021-10-29  2021年记账式贴现(五十三期)国债
3    020451    21贴债54  ...  2021-11-04  2021年记账式贴现(五十四期)国债
4    108451    贴债2154  ...  2021-11-04  2021年记账式贴现(五十四期)国债
..      ...       ...  ...         ...                 ...
99   102103    国债2103  ...  2021-09-03    2021年记账式附息(三期)国债
100  210003  21附息国债03  ...  2021-09-03    2021年记账式附息(三期)国债
101  020441    21贴债44  ...  2021-09-09  2021年记账式贴现(四十四期)国债
102  108441    贴债2144  ...  2021-09-09  2021年记账式贴现(四十四期)国债
103  219944  21贴现国债44  ...  2021-09-09  2021年记账式贴现(四十四期)国债
[104 rows x 15 columns]
```

#### 地方债发行

接口: bond_local_government_issue_cninfo

目标地址: https://webapi.cninfo.com.cn/#/thematicStatistics

描述: 巨潮资讯-数据中心-专题统计-债券报表-债券发行-地方债发行

输入参数

| 名称         | 类型  | 描述                    |
|------------|-----|-----------------------|
| start_date | str | start_date="20210911" |
| end_date   | str | end_date="20211110"   |

输出参数

| 名称     | 类型      | 描述       |
|--------|---------|----------|
| 债券代码   | object  | -        |
| 债券简称   | object  | -        |
| 发行起始日  | object  | -        |
| 发行终止日  | object  | -        |
| 计划发行总量 | float64 | 注意单位: 亿元 |
| 实际发行总量 | float64 | 注意单位: 亿元 |
| 发行价格   | float64 | 注意单位: 元  |
| 单位面值   | int64   | 注意单位: 元  |
| 缴款日    | object  | -        |
| 增发次数   | int64   | -        |
| 交易市场   | object  | -        |
| 发行方式   | object  | -        |
| 发行对象   | object  | -        |
| 公告日期   | object  | -        |
| 债券名称   | object  | -        |

接口示例

```python
import akshare as ak

bond_local_government_issue_cninfo_df = ak.bond_local_government_issue_cninfo(start_date="20210911", end_date="20211110")
print(bond_local_government_issue_cninfo_df)
```

数据示例

```
         债券代码  ...                                             债券名称
0      186882  ...               2021年新疆维吾尔自治区(新疆生产建设兵团)再融资一般债券(二期)
1      192633  ...               2021年新疆维吾尔自治区(新疆生产建设兵团)再融资一般债券(二期)
2     2171216  ...               2021年新疆维吾尔自治区(新疆生产建设兵团)再融资一般债券(二期)
3      186883  ...               2021年新疆维吾尔自治区(新疆生产建设兵团)再融资专项债券(二期)
4      192634  ...               2021年新疆维吾尔自治区(新疆生产建设兵团)再融资专项债券(二期)
...       ...  ...                                              ...
1444   192226  ...         2021年山东省政府棚改专项债券(八期)-2021年山东省政府专项债券(四十期)
1445  2105885  ...         2021年山东省政府棚改专项债券(八期)-2021年山东省政府专项债券(四十期)
1446   186469  ...  2021年山东省政府支持中小银行发展专项债券(一期)-2021年山东省政府专项债券(四十一期)
1447   192227  ...  2021年山东省政府支持中小银行发展专项债券(一期)-2021年山东省政府专项债券(四十一期)
1448  2105886  ...  2021年山东省政府支持中小银行发展专项债券(一期)-2021年山东省政府专项债券(四十一期)
[1449 rows x 15 columns]
```

#### 企业债发行

接口: bond_corporate_issue_cninfo

目标地址: https://webapi.cninfo.com.cn/#/thematicStatistics

描述: 巨潮资讯-数据中心-专题统计-债券报表-债券发行-企业债发行

输入参数

| 名称         | 类型  | 描述                    |
|------------|-----|-----------------------|
| start_date | str | start_date="20210911" |
| end_date   | str | end_date="20211110"   |

输出参数

| 名称         | 类型      | 描述       |
|------------|---------|----------|
| 债券代码       | object  | -        |
| 债券简称       | object  | -        |
| 公告日期       | object  | -        |
| 交易所网上发行起始日 | object  | -        |
| 交易所网上发行终止日 | object  | -        |
| 计划发行总量     | float64 | 注意单位: 万元 |
| 实际发行总量     | float64 | 注意单位: 万元 |
| 发行面值       | float64 | -        |
| 发行价格       | int64   | 注意单位: 元  |
| 发行方式       | object  | -        |
| 发行对象       | object  | -        |
| 发行范围       | object  | -        |
| 承销方式       | object  | -        |
| 最小认购单位     | float64 | 注意单位: 万元 |
| 募资用途说明     | object  | -        |
| 最低认购额      | float64 | 注意单位: 万元 |
| 债券名称       | object  | -        |

接口示例

```python
import akshare as ak

bond_corporate_issue_cninfo_df = ak.bond_corporate_issue_cninfo(start_date="20210911", end_date="20211110")
print(bond_corporate_issue_cninfo_df)
```

数据示例

```
        债券代码       债券简称  ...       最低认购额                                      债券名称
0     184109     21开泰01  ...  10000000.0        2021年如东县开泰城建投资有限公司城市停车场建设专项债券(品种一)
1    2180441  21开泰专项债01  ...  10000000.0        2021年如东县开泰城建投资有限公司城市停车场建设专项债券(品种一)
2     184114     21云发01  ...   1000000.0             2021年第一期株洲市云龙发展投资控股集团有限公司公司债券
3    2180450    21株云发01  ...   1000000.0             2021年第一期株洲市云龙发展投资控股集团有限公司公司债券
4     188968     21电力Y3  ...  10000000.0  2021年国投电力控股股份有限公司面向专业投资者公开发行可续期公司债券(第三期)
..       ...        ...  ...         ...                                       ...
429   188725     21通用01  ...  10000000.0   2021年中国通用技术(集团)控股有限责任公司公开发行公司债券(第一期)品种一
430   188702     21正奇01  ...  10000000.0       2021年正奇控股股份有限公司面向专业投资者公开发行公司债券(第一期)
431   188738     21舟城02  ...  10000000.0   2021年舟山海城建设投资集团有限公司面向专业投资者公开发行公司债券(第二期)
432   188758     21一航Y2  ...   1000000.0        2021年中交第一航务工程局有限公司公开发行可续期公司债券(第二期)
433   149632     H1碧地03  ...   1000000.0      2021年碧桂园地产集团有限公司面向专业投资者公开发行公司债券(第三期)
[434 rows x 17 columns]
```

#### 可转债发行

接口: bond_cov_issue_cninfo

目标地址: https://webapi.cninfo.com.cn/#/thematicStatistics

描述: 巨潮资讯-数据中心-专题统计-债券报表-债券发行-可转债发行

输入参数

| 名称         | 类型  | 描述                    |
|------------|-----|-----------------------|
| start_date | str | start_date="20210913" |
| end_date   | str | end_date="20211112"   |

输出参数

| 名称              | 类型      | 描述       |
|-----------------|---------|----------|
| 债券代码            | object  | -        |
| 债券简称            | object  | -        |
| 公告日期            | object  | -        |
| 发行起始日           | object  | -        |
| 发行终止日           | object  | -        |
| 计划发行总量          | float64 | 注意单位: 万元 |
| 实际发行总量          | float64 | 注意单位: 万元 |
| 发行面值            | int64   | 注意单位: 元  |
| 发行价格            | float64 | 注意单位: 元  |
| 发行方式            | object  | -        |
| 发行对象            | object  | -        |
| 发行范围            | object  | -        |
| 承销方式            | object  | -        |
| 募资用途说明          | object  | -        |
| 初始转股价格          | float64 | 注意单位: 元  |
| 转股开始日期          | object  | -        |
| 转股终止日期          | object  | -        |
| 网上申购日期          | object  | -        |
| 网上申购代码          | object  | -        |
| 网上申购简称          | object  | -        |
| 网上申购数量上限        | float64 | 注意单位: 万元 |
| 网上申购数量下限        | float64 | 注意单位: 万元 |
| 网上申购单位          | float64 | -        |
| 网上申购中签结果公告日及退款日 | object  | -        |
| 优先申购日           | object  | -        |
| 配售价格            | float64 | 注意单位: 元  |
| 债权登记日           | object  | -        |
| 优先申购缴款日         | object  | -        |
| 转股代码            | object  | -        |
| 交易市场            | object  | -        |
| 债券名称            | object  | -        |

接口示例

```python
import akshare as ak

bond_cov_issue_cninfo_df = ak.bond_cov_issue_cninfo(start_date="20210913", end_date="20211112")
print(bond_cov_issue_cninfo_df)
```

数据示例

```
      债券代码  债券简称  ... 交易市场                                       债券名称
0   110083  苏租转债  ...  上交所             2021年江苏金融租赁股份有限公司公开发行A股可转换公司债券
1   123130  设研转债  ...  深交所  2021年河南省交通规划设计研究院股份有限公司创业板向不特定对象发行可转换公司债券
2   127050  麒麟转债  ...  深交所              2021年青岛森麒麟轮胎股份有限公司公开发行可转换公司债券
3   111001  山玻转债  ...  上交所               2021年山东玻纤集团股份有限公司公开发行可转换公司债券
4   113631  皖天转债  ...  上交所             2021年安徽省天然气开发股份有限公司公开发行可转换公司债券
5   123129  锦鸡转债  ...  深交所        2021年江苏锦鸡实业股份有限公司创业板向不特定对象发行可转换公司债券
6   127049  希望转2  ...  深交所                2021年新希望六和股份有限公司公开发行可转换公司债券
7   123128  首华转债  ...  深交所    2021年首华燃气科技(上海)股份有限公司创业板向不特定对象发行可转换公司债券
8   123127  耐普转债  ...  深交所           2021年江西耐普矿机股份有限公司向不特定对象发行可转换公司债券
9   110082  宏发转债  ...  上交所                 2021年宏发科技股份有限公司公开发行可转换公司债券
10  113630  赛伍转债  ...  上交所                  苏州赛伍应用技术股份有限公司公开发行可转换公司债券
11  127048  中大转债  ...  深交所                宁波中大力德智能传动股份有限公司公开发行可转换公司债券
12  127047  帝欧转债  ...  深交所                 2021年帝欧家居股份有限公司公开发行可转换公司债券
13  127046  百润转债  ...  深交所           2021年上海百润投资控股集团股份有限公司公开发行可转换公司债券
14  113629  泉峰转债  ...  上交所           2021年南京泉峰汽车精密技术股份有限公司公开发行可转换公司债券
[15 rows x 31 columns]
```

#### 可转债转股

接口: bond_cov_stock_issue_cninfo

目标地址: https://webapi.cninfo.com.cn/#/thematicStatistics

描述: 巨潮资讯-数据中心-专题统计-债券报表-债券发行-可转债转股

输入参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| -   | -   | -   |

输出参数

| 名称       | 类型      | 描述      |
|----------|---------|---------|
| 债券代码     | object  | -       |
| 债券简称     | object  | -       |
| 公告日期     | object  | -       |
| 转股代码     | object  | -       |
| 转股简称     | object  | -       |
| 转股价格     | float64 | 注意单位: 元 |
| 自愿转换期起始日 | object  | -       |
| 自愿转换期终止日 | object  | -       |
| 标的股票     | object  | -       |
| 债券名称     | object  | -       |

接口示例

```python
import akshare as ak

bond_cov_stock_issue_cninfo_df = ak.bond_cov_stock_issue_cninfo()
print(bond_cov_stock_issue_cninfo_df)
```

数据示例

```
       债券代码   债券简称  ...    标的股票                                 债券名称
0    113685  升24转债  ...  603305     2024年宁波旭升集团股份有限公司向不特定对象发行可转换公司债券
1    111019   宏柏转债  ...  605366    2024年江西宏柏新材料股份有限公司向不特定对象发行可转换公司债券
2    113684   湘泵转债  ...  603319      2024年湖南机油泵股份有限公司向不特定对象发行可转换公司债券
3    113683  伟24转债  ...  603568     2024年浙江伟明环保股份有限公司向不特定对象发行可转换公司债券
4    113682   益丰转债  ...  603939    2024年益丰大药房连锁股份有限公司向不特定对象发行可转换公司债券
..      ...    ...  ...     ...                                  ...
281  118029   富淼转债  ...  688350     2022年江苏富淼科技股份有限公司向不特定对象发行可转换公司债券
282  123172   漱玉转债  ...  301017  2022年漱玉平民大药房连锁股份有限公司向不特定对象发行可转换公司债券
283  123172   漱玉转债  ...  301017  2022年漱玉平民大药房连锁股份有限公司向不特定对象发行可转换公司债券
284  127078   优彩转债  ...  002998       2022年优彩环保资源科技股份有限公司公开发行可转换公司债券
285  127078   优彩转债  ...  002998       2022年优彩环保资源科技股份有限公司公开发行可转换公司债券
[286 rows x 10 columns]
```

### 中债指数

#### 总指数

##### 综合类指数

###### 新综合指数

接口: bond_new_composite_index_cbond

目标地址: https://yield.chinabond.com.cn/cbweb-mn/indices/single_index_query

描述: 中国债券信息网-中债指数-中债指数族系-总指数-综合类指数-中债-新综合指数

输入参数

| 名称        | 类型  | 描述                                                                                                                                                                                                       |
|-----------|-----|----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| indicator | str | indicator="财富"; choice of {"全价", "净价", "财富", "平均市值法久期", "平均现金流法久期", "平均市值法凸性", "平均现金流法凸性", "平均现金流法到期收益率", "平均市值法到期收益率", "平均基点价值", "平均待偿期", "平均派息率", "指数上日总市值", "财富指数涨跌幅", "全价指数涨跌幅", "净价指数涨跌幅", "现券结算量"} |
| period    | str | period="总值"; choice of {"总值", "1年以下", "1-3年", "3-5年", "5-7年", "7-10年", "10年以上", "0-3个月", "3-6个月", "6-9个月", "9-12个月", "0-6个月", "6-12个月"}                                                                  |

输出参数

| 名称    | 类型      | 描述   |
|-------|---------|------|
| date  | object  | -    |
| value | float64 | 注意单位 |

接口示例

```python
import akshare as ak

bond_new_composite_index_cbond_df = ak.bond_new_composite_index_cbond(indicator="财富", period="总值")
print(bond_new_composite_index_cbond_df)
```

数据示例

```
            date     value
0     2002-01-04   99.9731
1     2002-01-07  100.0149
2     2002-01-08   99.8273
3     2002-01-09  100.0203
4     2002-01-10   99.9317
          ...       ...
5440  2023-09-27  226.5951
5441  2023-09-28  226.7308
5442  2023-10-07  226.9880
5443  2023-10-08  227.0376
5444  2023-10-09  226.9609
[5445 rows x 2 columns]
```

###### 综合指数

接口: bond_composite_index_cbond

目标地址: https://yield.chinabond.com.cn/cbweb-mn/indices/single_index_query

描述: 中国债券信息网-中债指数-中债指数族系-总指数-综合类指数-中债-综合指数

输入参数

| 名称        | 类型  | 描述                                                                                                                                                                                                       |
|-----------|-----|----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| indicator | str | indicator="财富"; choice of {"全价", "净价", "财富", "平均市值法久期", "平均现金流法久期", "平均市值法凸性", "平均现金流法凸性", "平均现金流法到期收益率", "平均市值法到期收益率", "平均基点价值", "平均待偿期", "平均派息率", "指数上日总市值", "财富指数涨跌幅", "全价指数涨跌幅", "净价指数涨跌幅", "现券结算量"} |
| period    | str | period="总值"; choice of {"总值", "1年以下", "1-3年", "3-5年", "5-7年", "7-10年", "10年以上", "0-3个月", "3-6个月", "6-9个月", "9-12个月", "0-6个月", "6-12个月"}                                                                  |

输出参数

| 名称    | 类型      | 描述   |
|-------|---------|------|
| date  | object  | -    |
| value | float64 | 注意单位 |

接口示例

```python
import akshare as ak

bond_composite_index_cbond_df = ak.bond_composite_index_cbond(indicator="财富", period="总值")
print(bond_composite_index_cbond_df)
```

数据示例

```
            date     value
0     2002-01-03   99.9731
1     2002-01-06  100.0149
2     2002-01-07   99.8273
3     2002-01-08  100.0203
4     2002-01-09   99.9317
          ...       ...
5429  2023-09-11  232.4624
5430  2023-09-12  232.5571
5431  2023-09-13  232.6593
5432  2023-09-14  232.5994
5433  2023-09-17  232.5021
[5434 rows x 2 columns]
```

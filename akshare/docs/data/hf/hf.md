## [AKShare](https://github.com/akfamily/akshare) 高频数据

### 标普 500 指数

接口: hf_sp_500

目标地址: https://github.com/FutureSharks/financial-data

描述: 获取标普 500 指数的分钟数据, 由于数据量比较大, 需要等待, 由于服务器在国外, 建议使用代理访问

输入参数

| 名称   | 类型  | 描述                                   |
|------|-----|--------------------------------------|
| year | str | year="2017"; 只能获取 **2012-2018** 年的数据 |

输出参数

| 名称    | 类型      | 描述   |
|-------|---------|------|
| date  | object  | 日期时间 |
| open  | float64 | 开盘价  |
| high  | float64 | 最高价  |
| low   | float64 | 最低价  |
| close | float64 | 收盘价  |

接口示例

```python
import akshare as ak

hf_sp_500_df = ak.hf_sp_500(year="2017")
print(hf_sp_500_df)
```

数据示例

```
              date     open     high      low    close  price
0       2017-01-02  2241.00  2244.50  2241.00  2243.50      0
1       2017-01-02  2243.75  2243.75  2243.00  2243.00      0
2       2017-01-02  2243.25  2243.25  2243.00  2243.25      0
3       2017-01-02  2243.00  2243.00  2243.00  2243.00      0
4       2017-01-02  2243.25  2243.75  2243.25  2243.75      0
...            ...      ...      ...      ...      ...    ...
222021  2017-12-29  2669.50  2669.75  2669.25  2669.25      0
222022  2017-12-29  2669.00  2669.25  2669.00  2669.00      0
222023  2017-12-29  2668.75  2668.75  2668.00  2668.25      0
222024  2017-12-29  2667.75  2668.50  2667.75  2668.00      0
222025  2017-12-29  2668.25  2668.50  2667.75  2668.50      0
```

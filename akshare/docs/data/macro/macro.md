## [AKShare](https://github.com/akfamily/akshare) 宏观数据

### 中国宏观

#### 中国宏观杠杆率

接口: macro_cnbs

目标地址: http://114.115.232.154:8080/

描述: 中国国家金融与发展实验室-中国宏观杠杆率数据

限量: 单次返回所有历史数据

输入参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| -   | -   | -   |

输出参数

| 名称      | 类型      | 描述      |
|---------|---------|---------|
| 年份      | object  | 日期, 年-月 |
| 居民部门    | float64 | -       |
| 非金融企业部门 | float64 | -       |
| 政府部门    | float64 | -       |
| 中央政府    | float64 | -       |
| 地方政府    | float64 | -       |
| 实体经济部门  | float64 | -       |
| 金融部门资产方 | float64 | -       |
| 金融部门负债方 | float64 | -       |

接口示例

```python
import akshare as ak

macro_cnbs_df = ak.macro_cnbs()
print(macro_cnbs_df)
```

数据示例

```
       年份  居民部门  非金融企业部门  政府部门 中央政府 地方政府 实体经济部门 金融部门资产方 金融部门负债方
0    1992-12   7.5     90.0   8.3   4.4   3.9   105.8      7.8      7.2
1    1993-03   7.5     91.1   8.1   4.2   3.9   106.7      7.8      7.3
2    1993-06   7.4     91.1   8.2   4.4   3.8   106.7      7.7      7.3
3    1993-09   7.3     90.2   8.3   4.6   3.7   105.8      7.7      7.3
4    1993-12   7.0     87.8   7.8   4.2   3.6   102.6      8.9      7.1
..       ...   ...      ...   ...   ...   ...     ...      ...      ...
121  2023-03  63.6    167.7  51.7  21.5  30.2   283.0     52.9     65.9
122  2023-06  63.6    168.4  52.1  21.5  30.6   284.1     53.8     66.8
123  2023-09  63.9    169.2  53.9  22.7  31.2   287.0     52.1     65.5
124  2023-12  63.5    168.4  56.1  23.8  32.3   288.0     52.5     67.2
125  2024-03  64.0    174.1  56.7  23.9  32.8   294.8     53.6     68.8
[126 rows x 9 columns]
```

#### 国民经济运行状况

##### 经济状况

###### 企业商品价格指数

接口: macro_china_qyspjg

目标地址: http://data.eastmoney.com/cjsj/qyspjg.html

描述: 东方财富-经济数据一览-中国-企业商品价格指数, 数据区间从 20050101-至今

限量: 单次返回所有历史数据

输入参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| -   | -   | -   |

输出参数

| 名称       | 类型      | 描述      |
|----------|---------|---------|
| 月份       | object  | -       |
| 总指数-指数值  | float64 | -       |
| 总指数-同比增长 | float64 | 注意单位: % |
| 总指数-环比增长 | float64 | 注意单位: % |
| 农产品-指数值  | float64 | -       |
| 农产品-同比增长 | float64 | 注意单位: % |
| 农产品-环比增长 | float64 | 注意单位: % |
| 矿产品-指数值  | float64 | -       |
| 矿产品-同比增长 | float64 | 注意单位: % |
| 矿产品-环比增长 | float64 | 注意单位: % |
| 煤油电-指数值  | float64 | -       |
| 煤油电-同比增长 | float64 | 注意单位: % |
| 煤油电-环比增长 | float64 | 注意单位: % |

接口示例

```python
import akshare as ak

macro_china_qyspjg_df = ak.macro_china_qyspjg()
print(macro_china_qyspjg_df)
```

数据示例

```
     月份  总指数-指数值  总指数-同比增长  ...  煤油电-指数值   煤油电-同比增长  煤油电-环比增长
0    2022年09月份   101.70 -5.746061  ...   111.80  -4.931973 -2.272727
1    2022年08月份   102.30 -4.925651  ...   114.40  -0.608167 -1.970865
2    2022年07月份   103.10 -4.448563  ...   116.70   1.214224 -3.233831
3    2022年06月份   105.20 -2.682701  ...   120.60   4.960836  0.249377
4    2022年05月份   105.10 -3.666361  ...   120.30   4.427083 -1.635323
..         ...      ...       ...  ...      ...        ...       ...
208  2005年05月份   103.16 -5.738304  ...   122.53  11.168572  1.625612
209  2005年04月份   102.81 -5.903350  ...   120.57  12.640135  0.920733
210  2005年03月份   103.48 -4.485878  ...   119.47  14.292548  0.538585
211  2005年02月份   104.82 -2.046538  ...   118.83  13.974679  2.590003
212  2005年01月份   104.67 -1.957662  ...   115.83   8.903723 -1.814021
```

###### 外商直接投资数据

接口: macro_china_fdi

目标地址: https://data.eastmoney.com/cjsj/fdi.html

描述: 东方财富-经济数据一览-中国-外商直接投资数据, 数据区间从 200801-202307

限量: 单次返回所有历史数据

输入参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| -   | -   | -   |

输出参数

| 名称      | 类型      | 描述       |
|---------|---------|----------|
| 月份      | object  | -        |
| 当月      | int64   | -        |
| 当月-同比增长 | float64 | 注意单位: 美元 |
| 当月-环比增长 | float64 | 注意单位: %  |
| 累计      | float64 | 注意单位: 美元 |
| 累计-同比增长 | float64 | 注意单位: %  |

接口示例

```python
import akshare as ak

macro_china_fdi_df = ak.macro_china_fdi()
print(macro_china_fdi_df)
```

数据示例

```
     月份          当月        当月-同比增长 当月-环比增长  累计  累计-同比增长
0    2008年01月份  11200250.0  116.422424 -14.460445   11200250   109.78
1    2008年02月份   6928190.0   52.801205 -38.142541   18128440    75.19
2    2008年03月份   9285600.0   50.171752  34.026347   27414040    61.26
3    2008年04月份   7602890.0   70.214971 -18.121715   35016930    59.32
4    2008年05月份   7760960.0   58.421856   2.079078   42777890    54.97
..         ...         ...         ...        ...        ...      ...
180  2023年01月份  19020000.0   20.075758  72.126697   19020000    10.00
181  2023年02月份  20690000.0   -6.039964   8.780231   39710000     1.00
182  2023年04月份         NaN         NaN        NaN   73500000    -3.30
183  2023年05月份  10850000.0  -18.421053        NaN   84350000    -5.60
184  2023年07月份         NaN         NaN        NaN  111800000    -9.80
[185 rows x 6 columns]
```

###### LPR品种数据

接口: macro_china_lpr

目标地址: https://data.eastmoney.com/cjsj/globalRateLPR.html

描述: 中国 LPR 品种数据, 数据区间从 19910421-至今

限量: 单次返回所有历史数据

输入参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| -   | -   | -   |

输出参数

| 名称         | 类型      | 描述                  |
|------------|---------|---------------------|
| TRADE_DATE | object  | 日期                  |
| LPR1Y      | float64 | LPR_1Y利率(%)         |
| LPR5Y      | float64 | LPR_5Y利率(%)         |
| RATE_1     | float64 | 短期贷款利率:6个月至1年(含)(%) |
| RATE_2     | float64 | 中长期贷款利率:5年以上(%)     |

接口示例

```python
import akshare as ak

macro_china_lpr_df = ak.macro_china_lpr()
print(macro_china_lpr_df)
```

数据示例

```
      TRADE_DATE  LPR1Y  LPR5Y  RATE_1  RATE_2
0     1991-04-21    NaN    NaN    8.64    9.72
1     1993-05-15    NaN    NaN    9.36   12.24
2     1993-07-11    NaN    NaN   10.98   14.04
3     1995-01-01    NaN    NaN   10.98   14.76
4     1995-07-01    NaN    NaN   12.06   15.30
          ...    ...    ...     ...     ...
1533  2023-03-20   3.65    4.3    4.35    4.90
1534  2023-04-20   3.65    4.3    4.35    4.90
1535  2023-05-22   3.65    4.3    4.35    4.90
1536  2023-06-20   3.55    4.2    4.35    4.90
1537  2023-07-20   3.55    4.2    4.35    4.90
[1538 rows x 5 columns]
```

###### 城镇调查失业率

接口: macro_china_urban_unemployment

目标地址: https://data.stats.gov.cn/easyquery.htm?cn=A01&zb=A0203&sj=202304

描述: 国家统计局-月度数据-城镇调查失业率

限量: 单次返回所有历史数据

输入参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| -   | -   | -   |

输出参数

| 名称    | 类型      | 描述 |
|-------|---------|----|
| date  | object  | 年月 |
| item  | object  | -  |
| value | float64 | -  |

接口示例

```python
import akshare as ak

macro_china_urban_unemployment_df = ak.macro_china_urban_unemployment()
print(macro_china_urban_unemployment_df)
```

数据示例

```
       date                     item  value
0    201812         全国城镇16—24岁劳动力失业率   10.1
1    201812  全国城镇不包含在校生的25—29岁劳动力失业率    0.0
2    201812  全国城镇不包含在校生的16—24岁劳动力失业率    0.0
3    201812            企业就业人员周平均工作时间    0.0
4    201812         全国城镇25—59岁劳动力失业率    4.4
..      ...                      ...    ...
715  202411         全国城镇16—24岁劳动力失业率    0.0
716  202411  全国城镇不包含在校生的16—24岁劳动力失业率    0.0
717  202411         全国城镇25—59岁劳动力失业率    0.0
718  202411           全国城镇本地户籍劳动力失业率    0.0
719  202411                全国城镇调查失业率    0.0
[720 rows x 3 columns]
```

###### 社会融资规模增量统计

接口: macro_china_shrzgm

目标地址: http://data.mofcom.gov.cn/gnmy/shrzgm.shtml

描述: 商务数据中心-国内贸易-社会融资规模增量统计, 数据区间从 201501-至今

限量: 单次返回所有历史数据

输入参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| -   | -   | -   |

输出参数

| 名称             | 类型      | 描述              |
|----------------|---------|-----------------|
| 月份             | object  | 年月              |
| 社会融资规模增量       | float64 | 注意单位: 亿元        |
| 其中-人民币贷款       | float64 | 注意单位: 亿元        |
| 其中-委托贷款外币贷款    | float64 | 注意单位: 折合人民币, 亿元 |
| 其中-委托贷款        | float64 | 注意单位: 亿元        |
| 其中-信托贷款        | float64 | 注意单位: 亿元        |
| 其中-未贴现银行承兑汇票   | float64 | 注意单位: 亿元        |
| 其中-企业债券        | float64 | 注意单位: 亿元        |
| 其中-非金融企业境内股票融资 | float64 | 注意单位: 亿元        |

接口示例

```python
import akshare as ak

macro_china_shrzgm_df = ak.macro_china_shrzgm()
print(macro_china_shrzgm_df)
```

数据示例

```
        月份  社会融资规模增量  其中-人民币贷款  ...  其中-未贴现银行承兑汇票  其中-企业债券  其中-非金融企业境内股票融资
0   201501     20516     14708  ...          1946     1868           526.0
1   201502     13609     11437  ...          -592      716           542.0
2   201503     12433      9920  ...          -910     1344           639.0
3   201504     10582      8045  ...           -74     1616           597.0
4   201505     12397      8510  ...           961     1710           584.0
..     ...       ...       ...  ...           ...      ...             ...
82  202111     25983     13021  ...          -383     4006          1294.0
83  202112     23682     10350  ...         -1419     2167          2075.0
84  202201     61750     41988  ...          4733     5829          1439.0
85  202202     11928      9084  ...         -4228     3377           585.0
86  202203     46531     32291  ...           287     3573           958.0
```

###### 中国 GDP 年率

接口: macro_china_gdp_yearly

目标地址: https://datacenter.jin10.com/reportType/dc_chinese_gdp_yoy

描述: 金十数据中心-中国 GDP 年率报告, 数据区间从 20110120-至今

限量: 单次返回所有历史数据

输入参数

| 名称 | 类型 | 描述 |
|----|----|----|
| -  | -  | -  |

输出参数

| 名称  | 类型      | 描述      |
|-----|---------|---------|
| 商品  | object  | -       |
| 日期  | object  | -       |
| 今值  | float64 | 注意单位: % |
| 预测值 | float64 | 注意单位: % |
| 前值  | float64 | 注意单位: % |

接口示例

```python
import akshare as ak

macro_china_gdp_yearly_df = ak.macro_china_gdp_yearly()
print(macro_china_gdp_yearly_df)
```

数据示例

```
           商品          日期    今值   预测值    前值
0   中国GDP年率报告  2011-01-20   9.8   9.5   9.6
1   中国GDP年率报告  2011-04-15   9.7   9.4   9.8
2   中国GDP年率报告  2011-07-13   9.5   9.5   9.7
3   中国GDP年率报告  2011-10-18   9.1   9.2   9.5
4   中国GDP年率报告  2012-01-17   8.9   8.8   9.1
5   中国GDP年率报告  2012-04-13   8.1   8.3   8.9
6   中国GDP年率报告  2012-07-13   7.6   7.6   8.1
7   中国GDP年率报告  2012-10-18   7.4   7.4   7.6
8   中国GDP年率报告  2013-01-18   7.9   7.8   7.4
9   中国GDP年率报告  2013-04-15   7.7   8.0   7.9
10  中国GDP年率报告  2013-07-15   7.5   7.5   7.7
11  中国GDP年率报告  2013-10-18   7.8   7.8   7.5
12  中国GDP年率报告  2014-01-20   7.7   7.6   7.8
13  中国GDP年率报告  2014-04-16   7.4   7.3   7.7
14  中国GDP年率报告  2014-07-16   7.5   7.4   7.4
15  中国GDP年率报告  2014-10-21   7.3   7.2   7.5
16  中国GDP年率报告  2015-01-20   7.3   7.2   7.3
17  中国GDP年率报告  2015-04-15   7.0   7.0   7.3
18  中国GDP年率报告  2015-07-15   7.0   6.9   7.0
19  中国GDP年率报告  2015-10-19   6.9   6.8   7.0
20  中国GDP年率报告  2016-01-19   6.8   6.8   6.9
21  中国GDP年率报告  2016-04-15   6.7   6.7   6.8
22  中国GDP年率报告  2016-07-15   6.7   6.6   6.7
23  中国GDP年率报告  2016-10-19   6.7   6.7   6.7
24  中国GDP年率报告  2017-01-20   6.8   6.7   6.7
25  中国GDP年率报告  2017-04-17   6.9   6.8   6.8
26  中国GDP年率报告  2017-07-17   6.9   6.8   6.9
27  中国GDP年率报告  2017-10-19   6.8   6.8   6.9
28  中国GDP年率报告  2018-01-18   6.8   6.7   6.8
29  中国GDP年率报告  2018-04-17   6.8   6.8   6.8
30  中国GDP年率报告  2018-07-16   6.7   6.7   6.8
31  中国GDP年率报告  2018-10-19   6.5   6.6   6.7
32  中国GDP年率报告  2019-01-21   6.4   6.4   6.5
33  中国GDP年率报告  2019-04-17   6.4   6.3   6.4
34  中国GDP年率报告  2019-07-15   6.2   6.2   6.4
35  中国GDP年率报告  2019-10-18   6.0   6.1   6.2
36  中国GDP年率报告  2020-01-17   6.0   6.0   6.0
37  中国GDP年率报告  2020-04-17  -6.8  -6.5   6.0
38  中国GDP年率报告  2020-07-16   3.2   2.5  -6.8
39  中国GDP年率报告  2020-10-19   4.9   5.2   3.2
40  中国GDP年率报告  2021-01-18   6.5   6.1   4.9
41  中国GDP年率报告  2021-04-16  18.3  19.0   6.5
42  中国GDP年率报告  2021-07-15   7.9   8.1  18.3
43  中国GDP年率报告  2021-10-18   4.9   5.2   7.9
44  中国GDP年率报告  2022-01-17   4.0   3.6   4.9
45  中国GDP年率报告  2022-04-18   4.8   4.4   4.0
46  中国GDP年率报告  2022-07-15   0.4   1.0   4.8
47  中国GDP年率报告  2022-10-18   NaN   3.4   0.4
48  中国GDP年率报告  2022-10-24   3.9   3.4   0.4
49  中国GDP年率报告  2022-10-26   NaN   3.4   0.4
50  中国GDP年率报告  2023-01-17   2.9   1.8   3.9
51  中国GDP年率报告  2023-04-18   4.5   4.0   2.9
52  中国GDP年率报告  2023-07-17   6.3   7.3   4.5
53  中国GDP年率报告  2023-10-18   4.9   4.4   6.3
54  中国GDP年率报告  2024-01-17   5.2   5.3   4.9
55  中国GDP年率报告  2024-04-16   NaN   NaN   5.2
```

##### 物价水平

###### 中国 CPI 年率报告

接口: macro_china_cpi_yearly

目标地址: https://datacenter.jin10.com/reportType/dc_chinese_cpi_yoy

描述: 中国年度 CPI 数据, 数据区间从 19860201-至今

限量: 单次返回所有历史数据

输入参数

| 名称 | 类型 | 描述 |
|----|----|----|
| -  | -  | -  |

输出参数

| 名称  | 类型      | 描述      |
|-----|---------|---------|
| 商品  | object  | -       |
| 日期  | object  | -       |
| 今值  | float64 | 注意单位: % |
| 预测值 | float64 | 注意单位: % |
| 前值  | float64 | 注意单位: % |

接口示例

```python
import akshare as ak

macro_china_cpi_yearly_df = ak.macro_china_cpi_yearly()
print(macro_china_cpi_yearly_df)
```

数据示例

```
            商品          日期   今值  预测值   前值
0    中国CPI年率报告  1986-02-01  7.1  NaN  NaN
1    中国CPI年率报告  1986-03-01  7.1  NaN  7.1
2    中国CPI年率报告  1986-04-01  7.1  NaN  7.1
3    中国CPI年率报告  1986-05-01  7.1  NaN  7.1
4    中国CPI年率报告  1986-06-01  7.1  NaN  7.1
..         ...         ...  ...  ...  ...
454  中国CPI年率报告  2023-12-09 -0.5 -0.1 -0.2
455  中国CPI年率报告  2024-01-12 -0.3 -0.4 -0.5
456  中国CPI年率报告  2024-02-08 -0.8 -0.5 -0.3
457  中国CPI年率报告  2024-03-09  0.7  0.3 -0.8
458  中国CPI年率报告  2024-04-11  NaN  NaN  0.7
[459 rows x 5 columns]
```

###### 中国 CPI 月率报告

接口: macro_china_cpi_monthly

目标地址: https://datacenter.jin10.com/reportType/dc_chinese_cpi_mom

描述: 中国月度 CPI 数据, 数据区间从 19960201-至今

限量: 单次返回所有历史数据

输入参数

| 名称 | 类型 | 描述 |
|----|----|----|
| -  | -  | -  |

输出参数

| 名称  | 类型      | 描述      |
|-----|---------|---------|
| 商品  | object  | -       |
| 日期  | object  | -       |
| 今值  | float64 | 注意单位: % |
| 预测值 | float64 | 注意单位: % |
| 前值  | float64 | 注意单位: % |

接口示例

```python
import akshare as ak

macro_china_cpi_monthly_df = ak.macro_china_cpi_monthly()
print(macro_china_cpi_monthly_df)
```

数据示例

```
            商品          日期   今值  预测值   前值
0    中国CPI月率报告  1996-02-01  2.1  NaN  NaN
1    中国CPI月率报告  1996-03-01  2.3  NaN  2.1
2    中国CPI月率报告  1996-04-01  0.6  NaN  2.3
3    中国CPI月率报告  1996-05-01  0.7  NaN  0.6
4    中国CPI月率报告  1996-06-01 -0.5  NaN  0.7
..         ...         ...  ...  ...  ...
334  中国CPI月率报告  2023-12-09 -0.5 -0.1 -0.1
335  中国CPI月率报告  2024-01-12  0.1  0.2 -0.5
336  中国CPI月率报告  2024-02-08  0.3  0.4  0.1
337  中国CPI月率报告  2024-03-09  1.0  0.7  0.3
338  中国CPI月率报告  2024-04-11  NaN  NaN  1.0
[339 rows x 5 columns]
```

###### 中国 PPI 年率报告

接口: macro_china_ppi_yearly

目标地址: https://datacenter.jin10.com/reportType/dc_chinese_ppi_yoy

描述: 中国年度 PPI 数据, 数据区间从 19950801-至今

限量: 单次返回所有历史数据

输入参数

| 名称 | 类型 | 描述 |
|----|----|----|
| -  | -  | -  |

输出参数

| 名称  | 类型      | 描述      |
|-----|---------|---------|
| 商品  | object  | -       |
| 日期  | object  | -       |
| 今值  | float64 | 注意单位: % |
| 预测值 | float64 | 注意单位: % |
| 前值  | float64 | 注意单位: % |

接口示例

```python
import akshare as ak

macro_china_ppi_yearly_df = ak.macro_china_ppi_yearly()
print(macro_china_ppi_yearly_df)
```

数据示例

```
            商品          日期    今值  预测值    前值
0    中国PPI年率报告  1995-08-01  13.5  NaN   NaN
1    中国PPI年率报告  1995-09-01  13.0  NaN  13.5
2    中国PPI年率报告  1995-10-01  12.9  NaN  13.0
3    中国PPI年率报告  1995-11-01  12.5  NaN  12.9
4    中国PPI年率报告  1995-12-01  11.1  NaN  12.5
..         ...         ...   ...  ...   ...
340  中国PPI年率报告  2023-12-09  -3.0 -2.8  -2.6
341  中国PPI年率报告  2024-01-12  -2.7 -2.6  -3.0
342  中国PPI年率报告  2024-02-08  -2.5 -2.6  -2.7
343  中国PPI年率报告  2024-03-09  -2.7 -2.5  -2.5
344  中国PPI年率报告  2024-04-11   NaN  NaN  -2.7
[345 rows x 5 columns]
```

#### 贸易状况

##### 以美元计算出口年率

接口: macro_china_exports_yoy

目标地址: https://datacenter.jin10.com/reportType/dc_chinese_exports_yoy

描述: 中国以美元计算出口年率报告, 数据区间从 19820201-至今

限量: 单次返回所有历史数据

输入参数

| 名称 | 类型 | 描述 |
|----|----|----|
| -  | -  | -  |

输出参数

| 名称  | 类型      | 描述      |
|-----|---------|---------|
| 商品  | object  | -       |
| 日期  | object  | -       |
| 今值  | float64 | 注意单位: % |
| 预测值 | float64 | 注意单位: % |
| 前值  | float64 | 注意单位: % |

接口示例

```python
import akshare as ak

macro_china_exports_yoy_df = ak.macro_china_exports_yoy()
print(macro_china_exports_yoy_df)
```

数据示例

```
                商品          日期    今值  预测值    前值
0    中国以美元计算出口年率报告  1982-02-01   8.7  NaN   NaN
1    中国以美元计算出口年率报告  1982-03-01  23.2  NaN   8.7
2    中国以美元计算出口年率报告  1982-04-01  12.2  NaN  23.2
3    中国以美元计算出口年率报告  1982-05-01  -2.5  NaN  12.2
4    中国以美元计算出口年率报告  1982-06-01  41.5  NaN  -2.5
..             ...         ...   ...  ...   ...
505  中国以美元计算出口年率报告  2023-12-07   0.5 -1.1  -6.4
506  中国以美元计算出口年率报告  2024-01-12   2.3  1.7   0.5
507  中国以美元计算出口年率报告  2024-01-13   NaN  NaN   0.5
508  中国以美元计算出口年率报告  2024-03-07   7.1  1.9   2.3
509  中国以美元计算出口年率报告  2024-04-12   NaN  NaN   7.1
[510 rows x 5 columns]
```

##### 以美元计算进口年率

接口: macro_china_imports_yoy

目标地址: https://datacenter.jin10.com/reportType/dc_chinese_imports_yoy

描述: 中国以美元计算进口年率报告, 数据区间从 19960201-至今

限量: 单次返回所有历史数据

输入参数

| 名称 | 类型 | 描述 |
|----|----|----|
| -  | -  | -  |

输出参数

| 名称  | 类型      | 描述      |
|-----|---------|---------|
| 商品  | object  | -       |
| 日期  | object  | -       |
| 今值  | float64 | 注意单位: % |
| 预测值 | float64 | 注意单位: % |
| 前值  | float64 | 注意单位: % |

接口示例

```python
import akshare as ak

macro_china_imports_yoy_df = ak.macro_china_imports_yoy()
print(macro_china_imports_yoy_df)
```

数据示例

```
                商品          日期    今值  预测值    前值
0    中国以美元计算进口年率报告  1996-02-01  55.8  NaN   NaN
1    中国以美元计算进口年率报告  1996-03-01  14.2  NaN  55.8
2    中国以美元计算进口年率报告  1996-04-01   8.7  NaN  14.2
3    中国以美元计算进口年率报告  1996-05-01   6.4  NaN   8.7
4    中国以美元计算进口年率报告  1996-06-01   4.5  NaN   6.4
..             ...         ...   ...  ...   ...
343  中国以美元计算进口年率报告  2023-12-07  -0.6  3.3   3.0
344  中国以美元计算进口年率报告  2024-01-12   0.2  0.3  -0.6
345  中国以美元计算进口年率报告  2024-01-13   NaN  NaN  -0.6
346  中国以美元计算进口年率报告  2024-03-07   3.5  1.5   0.2
347  中国以美元计算进口年率报告  2024-04-12   NaN  NaN   3.5
[348 rows x 5 columns]
```

##### 以美元计算贸易帐(亿美元)

接口: macro_china_trade_balance

目标地址: https://datacenter.jin10.com/reportType/dc_chinese_trade_balance

描述: 中国以美元计算贸易帐报告, 数据区间从19810201-至今

限量: 单次返回所有历史数据

输入参数

| 名称 | 类型 | 描述 |
|----|----|----|
| -  | -  | -  |

输出参数

| 名称  | 类型      | 描述        |
|-----|---------|-----------|
| 商品  | object  | -         |
| 日期  | object  | -         |
| 今值  | float64 | 注意单位: 亿美元 |
| 预测值 | float64 | 注意单位: 亿美元 |
| 前值  | float64 | 注意单位: 亿美元 |

接口示例

```python
import akshare as ak

macro_china_trade_balance_df = ak.macro_china_trade_balance()
print(macro_china_trade_balance_df)
```

数据示例

```
               商品          日期      今值     预测值      前值
0    中国以美元计算贸易帐报告  1981-02-01    -2.2     NaN     NaN
1    中国以美元计算贸易帐报告  1981-03-01    -4.9     NaN    -2.2
2    中国以美元计算贸易帐报告  1981-04-01    -7.4     NaN    -4.9
3    中国以美元计算贸易帐报告  1981-05-01    -4.8     NaN    -7.4
4    中国以美元计算贸易帐报告  1981-06-01    -5.4     NaN    -4.8
..            ...         ...     ...     ...     ...
528  中国以美元计算贸易帐报告  2023-12-07   683.9   580.0   565.3
529  中国以美元计算贸易帐报告  2024-01-12   753.4   747.5   683.9
530  中国以美元计算贸易帐报告  2024-01-13     NaN     NaN   683.9
531  中国以美元计算贸易帐报告  2024-03-07  1251.6  1103.0   753.4
532  中国以美元计算贸易帐报告  2024-04-12     NaN     NaN  1251.6
[533 rows x 5 columns]
```

#### 产业指标

##### 工业增加值增长

接口: macro_china_gyzjz

目标地址: https://data.eastmoney.com/cjsj/gyzjz.html

描述: 东方财富-中国工业增加值增长, 数据区间从 2008 - 至今

限量: 单次返回所有历史数据

输入参数

| 名称 | 类型 | 描述 |
|----|----|----|
| -  | -  | -  |

输出参数

| 名称   | 类型      | 描述      |
|------|---------|---------|
| 月份   | object  | -       |
| 同比增长 | float64 | 注意单位: % |
| 累计增长 | float64 | 注意单位: % |
| 发布时间 | object  | -       |

接口示例

```python
import akshare as ak

macro_china_gyzjz_df = ak.macro_china_gyzjz()
print(macro_china_gyzjz_df)
```

数据示例

```
           月份  同比增长  累计增长 发布时间
0    2008年02月份  15.4  15.4  2008-02-01
1    2008年03月份  17.8  16.4  2008-03-01
2    2008年04月份  15.7  16.3  2008-04-01
3    2008年05月份  16.0  16.3  2008-05-01
4    2008年06月份  16.0  16.3  2008-06-01
..         ...   ...   ...         ...
165  2023年02月份   NaN   2.4  2023-02-01
166  2023年03月份   3.9   3.0  2023-03-01
167  2023年04月份   5.6   3.6  2023-04-01
168  2023年05月份   3.5   3.6  2023-05-01
169  2023年06月份   4.4   3.8  2023-06-01
[170 rows x 4 columns]
```

##### 规模以上工业增加值年率

接口: macro_china_industrial_production_yoy

目标地址: https://datacenter.jin10.com/reportType/dc_chinese_industrial_production_yoy

描述: 中国规模以上工业增加值年率报告, 数据区间从 19900301-至今

限量: 单次返回所有历史数据

输入参数

| 名称 | 类型 | 描述 |
|----|----|----|
| -  | -  | -  |

输出参数

| 名称  | 类型      | 描述      |
|-----|---------|---------|
| 商品  | object  | -       |
| 日期  | object  | -       |
| 今值  | float64 | 注意单位: % |
| 预测值 | float64 | 注意单位: % |
| 前值  | float64 | 注意单位: % |

接口示例

```python
import akshare as ak

macro_china_industrial_production_yoy_df = ak.macro_china_industrial_production_yoy()
print(macro_china_industrial_production_yoy_df)
```

数据示例

```
                  商品          日期   今值  预测值   前值
0    中国规模以上工业增加值年率报告  1990-03-01  5.0  NaN  NaN
1    中国规模以上工业增加值年率报告  1990-04-01  0.8  NaN  5.0
2    中国规模以上工业增加值年率报告  1990-05-01  1.7  NaN  0.8
3    中国规模以上工业增加值年率报告  1990-06-01  3.3  NaN  1.7
4    中国规模以上工业增加值年率报告  1990-07-01  5.0  NaN  3.3
..               ...         ...  ...  ...  ...
392  中国规模以上工业增加值年率报告  2023-11-15  4.6  4.4  4.5
393  中国规模以上工业增加值年率报告  2023-12-15  6.6  5.6  4.6
394  中国规模以上工业增加值年率报告  2024-01-17  6.8  6.6  6.6
395  中国规模以上工业增加值年率报告  2024-03-18  7.0  5.3  6.8
396  中国规模以上工业增加值年率报告  2024-04-16  NaN  NaN  7.0
[397 rows x 5 columns]
```

##### 官方制造业 PMI

接口: macro_china_pmi_yearly

目标地址: https://datacenter.jin10.com/reportType/dc_chinese_manufacturing_pmi

描述: 中国年度PMI数据, 数据区间从 20050201-至今

限量: 单次返回所有历史数据

输入参数

| 名称 | 类型 | 描述 |
|----|----|----|
| -  | -  | -  |

输出参数

| 名称  | 类型      | 描述 |
|-----|---------|----|
| 商品  | object  | -  |
| 日期  | object  | -  |
| 今值  | float64 | -  |
| 预测值 | float64 | -  |
| 前值  | float64 | -  |

接口示例

```python
import akshare as ak

macro_china_pmi_yearly_df = ak.macro_china_pmi_yearly()
print(macro_china_pmi_yearly_df)
```

数据示例

```
             商品        日期      今值   预测值 前值
0    中国官方制造业PMI  2005-02-01  54.7   NaN   NaN
1    中国官方制造业PMI  2005-03-01  54.5   NaN  54.7
2    中国官方制造业PMI  2005-04-01  57.9   NaN  54.5
3    中国官方制造业PMI  2005-05-01  56.7   NaN  57.9
4    中国官方制造业PMI  2005-06-01  52.9   NaN  56.7
..          ...         ...   ...   ...   ...
228  中国官方制造业PMI  2023-11-30  49.4  49.7  49.5
229  中国官方制造业PMI  2023-12-31  49.0  49.5  49.4
230  中国官方制造业PMI  2024-01-31  49.2  49.2  49.0
231  中国官方制造业PMI  2024-03-01  49.1  49.1  49.2
232  中国官方制造业PMI  2024-03-31  50.8  50.1  49.1
[233 rows x 5 columns]
```

##### 财新制造业PMI终值

接口: macro_china_cx_pmi_yearly

目标地址: https://datacenter.jin10.com/reportType/dc_chinese_caixin_manufacturing_pmi

描述: 中国年度财新 PMI 数据, 数据区间从 20120120-至今

限量: 单次返回所有历史数据

输入参数

| 名称 | 类型 | 描述 |
|----|----|----|
| -  | -  | -  |

输出参数

| 名称  | 类型      | 描述 |
|-----|---------|----|
| 商品  | object  | -  |
| 日期  | object  | -  |
| 今值  | float64 | -  |
| 预测值 | float64 | -  |
| 前值  | float64 | -  |

接口示例

```python
import akshare as ak

macro_china_cx_pmi_yearly_df = ak.macro_china_cx_pmi_yearly()
print(macro_china_cx_pmi_yearly_df)
```

数据示例

```
                 商品          日期    今值   预测值    前值
0    中国财新制造业PMI终值报告  2012-01-20  48.8   NaN  48.7
1    中国财新制造业PMI终值报告  2012-02-22  49.7   NaN  48.8
2    中国财新制造业PMI终值报告  2012-03-22  48.1   NaN  49.6
3    中国财新制造业PMI终值报告  2012-04-23  49.1   NaN  48.3
4    中国财新制造业PMI终值报告  2012-05-02  49.3   NaN  49.1
..              ...         ...   ...   ...   ...
196  中国财新制造业PMI终值报告  2023-12-01  50.7  49.3  49.5
197  中国财新制造业PMI终值报告  2024-01-02  50.8  50.4  50.7
198  中国财新制造业PMI终值报告  2024-02-01  50.8  50.8  50.8
199  中国财新制造业PMI终值报告  2024-03-01  50.9  50.7  50.8
200  中国财新制造业PMI终值报告  2024-04-01  51.1  51.0  50.9
[201 rows x 5 columns]
```

##### 财新服务业PMI

接口: macro_china_cx_services_pmi_yearly

目标地址: https://datacenter.jin10.com/reportType/dc_chinese_caixin_services_pmi

描述: 中国财新服务业 PMI 报告, 数据区间从 20120405-至今

限量: 单次返回所有历史数据

输入参数

| 名称 | 类型 | 描述 |
|----|----|----|
| -  | -  | -  |

输出参数

| 名称  | 类型      | 描述 |
|-----|---------|----|
| 商品  | object  | -  |
| 日期  | object  | -  |
| 今值  | float64 | -  |
| 预测值 | float64 | -  |
| 前值  | float64 | -  |

接口示例

```python
import akshare as ak

macro_china_cx_services_pmi_yearly_df = ak.macro_china_cx_services_pmi_yearly()
print(macro_china_cx_services_pmi_yearly_df)
```

数据示例

```
               商品          日期    今值   预测值    前值
0    中国财新服务业PMI报告  2012-04-05  53.3   NaN  53.9
1    中国财新服务业PMI报告  2012-05-04  54.1   NaN  53.3
2    中国财新服务业PMI报告  2012-06-05  54.7   NaN  54.1
3    中国财新服务业PMI报告  2012-07-04  52.3   NaN  54.7
4    中国财新服务业PMI报告  2012-08-03  53.1   NaN  52.3
..            ...         ...   ...   ...   ...
143  中国财新服务业PMI报告  2023-12-05  51.5  50.7  50.4
144  中国财新服务业PMI报告  2024-01-04  52.9  51.6  51.5
145  中国财新服务业PMI报告  2024-02-05  52.7  53.0  52.9
146  中国财新服务业PMI报告  2024-03-05  52.5  52.9  52.7
147  中国财新服务业PMI报告  2024-04-03  52.7  52.7  52.5
[148 rows x 5 columns]
```

##### 中国官方非制造业PMI

接口: macro_china_non_man_pmi

目标地址: https://datacenter.jin10.com/reportType/dc_chinese_non_manufacturing_pmi

描述: 中国官方非制造业 PMI, 数据区间从 20160101-至今

限量: 单次返回所有历史数据

输入参数

| 名称 | 类型 | 描述 |
|----|----|----|
| -  | -  | -  |

输出参数

| 名称  | 类型      | 描述      |
|-----|---------|---------|
| 商品  | object  | -       |
| 日期  | object  | -       |
| 今值  | float64 | 注意单位: % |
| 预测值 | float64 | 注意单位: % |
| 前值  | float64 | 注意单位: % |

接口示例

```python
import akshare as ak

macro_china_non_man_pmi_df = ak.macro_china_non_man_pmi()
print(macro_china_non_man_pmi_df)
```

数据示例

```
                商品          日期    今值   预测值    前值
0    中国官方非制造业PMI报告  2007-02-01  60.4   NaN   NaN
1    中国官方非制造业PMI报告  2007-03-01  60.6   NaN  60.4
2    中国官方非制造业PMI报告  2007-04-01  58.2   NaN  60.6
3    中国官方非制造业PMI报告  2007-05-01  60.4   NaN  58.2
4    中国官方非制造业PMI报告  2007-06-01  62.2   NaN  60.4
..             ...         ...   ...   ...   ...
204  中国官方非制造业PMI报告  2023-11-30  50.2  51.1  50.6
205  中国官方非制造业PMI报告  2023-12-31  50.4  50.5  50.2
206  中国官方非制造业PMI报告  2024-01-31  50.7  50.6  50.4
207  中国官方非制造业PMI报告  2024-03-01  51.4  50.9  50.7
208  中国官方非制造业PMI报告  2024-03-31  53.0  51.3  51.4
[209 rows x 5 columns]
```

#### 金融指标

##### 外汇储备(亿美元)

接口: macro_china_fx_reserves_yearly

目标地址: https://datacenter.jin10.com/reportType/dc_chinese_fx_reserves

描述: 中国年度外汇储备数据, 数据区间从 20140115-至今

限量: 单次返回所有历史数据

输入参数

| 名称 | 类型 | 描述 |
|----|----|----|
| -  | -  | -  |

输出参数

| 名称  | 类型      | 描述        |
|-----|---------|-----------|
| 商品  | object  | -         |
| 日期  | object  | -         |
| 今值  | float64 | 注意单位: 亿美元 |
| 预测值 | float64 | 注意单位: 亿美元 |
| 前值  | float64 | 注意单位: 亿美元 |

接口示例

```python
import akshare as ak

macro_china_fx_reserves_yearly_df = ak.macro_china_fx_reserves_yearly()
print(macro_china_fx_reserves_yearly_df)
```

数据示例

```
           商品          日期       今值      预测值     前值
0    中国外汇储备报告  2014-01-15  38200.0  38000.0  36600
1    中国外汇储备报告  2014-07-15  39900.0  39800.0  39500
2    中国外汇储备报告  2015-01-15  38400.0  39000.0  39900
3    中国外汇储备报告  2016-03-07  32000.0  31900.0  32300
4    中国外汇储备报告  2016-04-07  32100.0  32100.0  32000
..        ...         ...      ...      ...    ...
106  中国外汇储备报告  2023-12-07  31720.0  31200.0  31010
107  中国外汇储备报告  2024-01-07  32380.0  32000.0  31720
108  中国外汇储备报告  2024-02-07  32190.0  32170.0  32380
109  中国外汇储备报告  2024-03-07  32260.0  32050.0  32190
110  中国外汇储备报告  2024-04-07      NaN      NaN  32260
[111 rows x 5 columns]
```

##### M2货币供应年率

接口: macro_china_m2_yearly

目标地址: https://datacenter.jin10.com/reportType/dc_chinese_m2_money_supply_yoy

描述: 中国年度 M2 数据, 数据区间从 19980201-至今

限量: 单次返回所有历史数据

输入参数

| 名称 | 类型 | 描述 |
|----|----|----|
| -  | -  | -  |

输出参数

| 名称  | 类型      | 描述      |
|-----|---------|---------|
| 商品  | object  | -       |
| 日期  | object  | -       |
| 今值  | float64 | 注意单位: % |
| 预测值 | float64 | 注意单位: % |
| 前值  | float64 | 注意单位: % |

接口示例

```python
import akshare as ak

macro_china_m2_yearly_df = ak.macro_china_m2_yearly()
print(macro_china_m2_yearly_df)
```

数据示例

```
               商品          日期    今值  预测值    前值
0    中国M2货币供应年率报告  1998-02-01  17.4  NaN   NaN
1    中国M2货币供应年率报告  1998-03-01  16.7  NaN  17.4
2    中国M2货币供应年率报告  1998-04-01  15.4  NaN  16.7
3    中国M2货币供应年率报告  1998-05-01  14.6  NaN  15.4
4    中国M2货币供应年率报告  1998-06-01  15.5  NaN  14.6
..            ...         ...   ...  ...   ...
349  中国M2货币供应年率报告  2024-03-12   NaN  8.8   8.7
350  中国M2货币供应年率报告  2024-03-13   NaN  8.8   8.7
351  中国M2货币供应年率报告  2024-03-14   NaN  8.8   8.7
352  中国M2货币供应年率报告  2024-03-15   8.7  8.8   8.7
353  中国M2货币供应年率报告  2024-04-11   NaN  NaN   8.7
[354 rows x 5 columns]
```

##### 新房价指数

接口: macro_china_new_house_price

目标地址: http://data.eastmoney.com/cjsj/newhouse.html

描述: 中国新房价指数月度数据, 数据区间从 201101-至今

限量: 单次返回指定城市的所有历史数据

输入参数

| 名称          | 类型  | 描述                          |
|-------------|-----|-----------------------------|
| city_first  | str | city_first="北京"; 城市列表见目标网站  |
| city_second | str | city_second="上海"; 城市列表见目标网站 |

输出参数

| 名称            | 类型      | 描述  |
|---------------|---------|-----|
| 日期            | object  | 日期  |
| 城市            | object  | -   |
| 新建商品住宅价格指数-环比 | float64 | -   |
| 新建商品住宅价格指数-同比 | float64 | -   |
| 新建商品住宅价格指数-定基 | float64 | -   |
| 二手住宅价格指数-环比   | float64 | -   |
| 二手住宅价格指数-同比   | float64 | -   |
| 二手住宅价格指数-定基   | float64 | -   |

接口示例

```python
import akshare as ak

macro_china_new_house_price_df = ak.macro_china_new_house_price(city_first="北京", city_second="上海")
print(macro_china_new_house_price_df)
```

数据示例

```
           日期  城市  新建商品住宅价格指数-同比  ... 二手住宅价格指数-同比 二手住宅价格指数-环比 二手住宅价格指数-定基
0    2011-01-01  上海          101.8  ...        101.7        100.5        100.6
1    2011-01-01  北京          109.1  ...        102.6        100.3        101.2
2    2011-02-01  北京          108.4  ...        102.9        100.4        101.5
3    2011-02-01  上海          102.8  ...        102.0        100.4        101.0
4    2011-03-01  北京          106.2  ...        101.9         99.9        101.4
..          ...  ..            ...  ...          ...          ...          ...
327  2024-08-01  北京           96.4  ...         91.5         99.0          NaN
328  2024-09-01  上海          104.9  ...         92.4         98.8          NaN
329  2024-09-01  北京           95.4  ...         89.7         98.7          NaN
330  2024-10-01  上海          105.0  ...         93.3        100.2          NaN
331  2024-10-01  北京           95.1  ...         91.6        101.0          NaN
[332 rows x 8 columns]
```

##### 企业景气及企业家信心指数

接口: macro_china_enterprise_boom_index

目标地址: http://data.eastmoney.com/cjsj/qyjqzs.html

描述: 中国企业景气及企业家信心指数数据, 数据区间从 2005 一季度-至今

限量: 单次返回所有历史数据

输入参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| -   | -   | -   |

输出参数

| 名称         | 类型      | 描述      |
|------------|---------|---------|
| 季度         | object  | 日期      |
| 企业景气指数-指数  | float64 | -       |
| 企业景气指数-同比  | float64 | 注意单位: % |
| 企业景气指数-环比  | float64 | 注意单位: % |
| 企业家信心指数-指数 | float64 | -       |
| 企业家信心指数-同比 | float64 | 注意单位: % |
| 企业家信心指数-环比 | float64 | 注意单位: % |

接口示例

```python
import akshare as ak

macro_china_enterprise_boom_index_df = ak.macro_china_enterprise_boom_index()
print(macro_china_enterprise_boom_index_df)
```

数据示例

```
           季度  企业景气指数-指数  企业景气指数-同比  ...  企业家信心指数-指数  企业家信心指数-同比  企业家信心指数-环比
0   2022年第2季度     101.80       1.80  ...         NaN         NaN         NaN
1   2022年第1季度     112.70      12.70  ...         NaN         NaN         NaN
2   2021年第4季度     119.20      19.20  ...         NaN         NaN         NaN
3   2021年第3季度     119.20      19.20  ...      120.90       20.90       -5.10
4   2021年第2季度     123.80      23.80  ...      126.00       26.00       -1.80
..        ...        ...        ...  ...         ...         ...         ...
65  2006年第1季度     131.50      31.50  ...      133.10       33.10        7.70
66  2005年第4季度     131.70      31.70  ...      125.40       25.40       -2.20
67  2005年第3季度     132.00      32.00  ...      127.60       27.60       -0.90
68  2005年第2季度     131.71      31.71  ...      128.50       28.50       -7.35
69  2005年第1季度     132.46      32.46  ...      135.85       35.85        5.04
```

##### 全国税收收入

接口: macro_china_national_tax_receipts

目标地址: http://data.eastmoney.com/cjsj/nationaltaxreceipts.aspx

描述: 中国全国税收收入数据, 数据区间从 2005 一季度-至今

限量: 单次返回所有历史数据

输入参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| -   | -   | -   |

输出参数

| 名称     | 类型      | 描述       |
|--------|---------|----------|
| 季度     | object  | 日期       |
| 税收收入合计 | float64 | 注意单位: 亿元 |
| 较上年同期  | float64 | 注意单位: %  |
| 季度环比   | float64 | -        |

接口示例

```python
import akshare as ak

macro_china_national_tax_receipts_df = ak.macro_china_national_tax_receipts()
print(macro_china_national_tax_receipts_df)
```

数据示例

```
             季度     税收收入合计  较上年同期      季度环比
0   2022年第1-3季度  124365.00  -11.6  0.171811
1   2022年第1-2季度   85564.00  -14.8 -0.368718
2     2022年第1季度   52452.00    7.7  0.637641
3   2021年第1-4季度  172731.00   11.9 -0.204070
4   2021年第1-3季度  140702.00   18.4 -0.222216
..          ...        ...    ...       ...
64  2006年第1-2季度   18484.80   22.0  0.142837
65    2006年第1季度    8626.32   19.0       NaN
66  2005年第1-3季度   21855.35   15.9 -0.151249
67  2005年第1-2季度   15149.73   13.4  0.089860
68    2005年第1季度    7249.16   10.7       NaN
```

##### 银行理财产品发行数量

接口: macro_china_bank_financing

目标地址: https://data.eastmoney.com/cjsj/hyzs_list_EMI01516267.html

描述: 银行理财产品发行数量, 数据区间从 2000 一月-至今

限量: 单次返回所有历史数据

输入参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| -   | -   | -   |

输出参数

| 名称     | 类型      | 描述      |
|--------|---------|---------|
| 日期     | object  | -       |
| 最新值    | int64   | -       |
| 涨跌幅    | float64 | 注意单位: % |
| 近3月涨跌幅 | float64 | 注意单位: % |
| 近6月涨跌幅 | float64 | 注意单位: % |
| 近1年涨跌幅 | float64 | 注意单位: % |
| 近2年涨跌幅 | float64 | 注意单位: % |
| 近3年涨跌幅 | float64 | 注意单位: % |

接口示例

```python
import akshare as ak

macro_china_bank_financing_df = ak.macro_china_bank_financing()
print(macro_china_bank_financing_df)
```

数据示例

```
        日期   最新值         涨跌幅  ...   近1年涨跌幅   近2年涨跌幅  近3年涨跌幅
0    2000-03-01     4         NaN  ...         NaN         NaN         NaN
1    2004-02-01     2  -50.000000  ...  -50.000000  -50.000000  -50.000000
2    2004-03-01     8  300.000000  ...  100.000000  100.000000  100.000000
3    2004-04-01     3  -62.500000  ...  -25.000000  -25.000000  -25.000000
4    2004-05-01     6  100.000000  ...   50.000000   50.000000   50.000000
..          ...   ...         ...  ...         ...         ...         ...
213  2021-10-01  2640  -27.829415  ...  -51.710262  -70.054446  -74.346516
214  2021-11-01  3424   29.696970  ...  -45.347167  -59.541534  -70.644719
215  2021-12-01  3876   13.200935  ...  -44.422139  -44.117647  -64.904020
216  2022-01-01  2793  -27.941176  ...  -45.406568  -50.478723  -77.035027
217  2022-02-01  1779  -36.305048  ...  -54.893509  -66.204407  -78.496313
```

##### 原保险保费收入

接口: macro_china_insurance_income

目标地址: https://data.eastmoney.com/cjsj/hyzs_list_EMM00088870.html

描述: 原保险保费收入, 数据区间从 200407-至今

限量: 单次返回所有历史数据

输入参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| -   | -   | -   |

输出参数

| 名称     | 类型      | 描述      |
|--------|---------|---------|
| 日期     | object  | -       |
| 最新值    | int64   | -       |
| 涨跌幅    | float64 | 注意单位: % |
| 近3月涨跌幅 | float64 | 注意单位: % |
| 近6月涨跌幅 | float64 | 注意单位: % |
| 近1年涨跌幅 | float64 | 注意单位: % |
| 近2年涨跌幅 | float64 | 注意单位: % |
| 近3年涨跌幅 | float64 | 注意单位: % |

接口示例

```python
import akshare as ak

macro_china_insurance_income_df = ak.macro_china_insurance_income()
print(macro_china_insurance_income_df)
```

数据示例

```
      日期        最新值        涨跌幅  ...    近1年涨跌幅    近2年涨跌幅     近3年涨跌幅
0    2004-07-01  0.000000e+00        NaN  ...       NaN       NaN        NaN
1    2004-09-01  2.453616e+07        NaN  ...       NaN       NaN        NaN
2    2004-10-01  2.704315e+07  10.217507  ...       NaN       NaN        NaN
3    2004-11-01  2.956397e+07   9.321471  ...       NaN       NaN        NaN
4    2004-12-01  3.193586e+07   8.022916  ...       NaN       NaN        NaN
..          ...           ...        ...  ...       ...       ...        ...
205  2021-09-01  1.979124e+08   9.327015  ... -1.959838  2.790277  -9.435956
206  2021-10-01  2.094363e+08   5.822728  ... -2.107731  2.866552 -10.337719
207  2021-11-01  2.205047e+08   5.284853  ... -2.053675  2.876131 -11.053454
208  2021-12-01  2.357184e+08   6.899490  ... -1.710283  3.594269 -10.239691
209  2022-01-01  7.049640e+07 -70.092958  ... -5.768089  5.429366  12.398597
```

##### 手机出货量

接口: macro_china_mobile_number

目标地址: https://data.eastmoney.com/cjsj/hyzs_list_EMI00225823.html

描述: 手机出货量, 数据区间从 201201-至今

限量: 单次返回所有历史数据

输入参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| -   | -   | -   |

输出参数

| 名称     | 类型      | 描述      |
|--------|---------|---------|
| 日期     | object  | -       |
| 最新值    | int64   | -       |
| 涨跌幅    | float64 | 注意单位: % |
| 近3月涨跌幅 | float64 | 注意单位: % |
| 近6月涨跌幅 | float64 | 注意单位: % |
| 近1年涨跌幅 | float64 | 注意单位: % |
| 近2年涨跌幅 | float64 | 注意单位: % |
| 近3年涨跌幅 | float64 | 注意单位: % |

接口示例

```python
import akshare as ak

macro_china_mobile_number_df = ak.macro_china_mobile_number()
print(macro_china_mobile_number_df)
```

数据示例

```
        日期     最新值     涨跌幅  ...     近1年涨跌幅      近2年涨跌幅     近3年涨跌幅
0    2012-01-01  2400.6        NaN  ...        NaN         NaN        NaN
1    2012-02-01  2998.9  24.922936  ...        NaN         NaN        NaN
2    2012-03-01  3958.6  32.001734  ...        NaN         NaN        NaN
3    2012-04-01  3528.7 -10.859900  ...        NaN         NaN        NaN
4    2012-05-01  3586.8   1.646499  ...        NaN         NaN        NaN
..          ...     ...        ...  ...        ...         ...        ...
117  2021-10-01  3357.5  56.599813  ...  28.379153   -6.655731 -12.866893
118  2021-11-01  3525.2   4.994788  ...  19.159005    1.176741  -0.333616
119  2021-12-01  3340.1  -5.250766  ...  25.591277    9.712916  -6.371587
120  2022-01-01  3302.2  -1.134697  ... -17.691924   58.660453  -3.013393
121  2022-02-01  1486.4 -54.987584  ... -31.688037  132.832080   2.432637
```

##### 菜篮子产品批发价格指数

接口: macro_china_vegetable_basket

目标地址: https://data.eastmoney.com/cjsj/hyzs_list_EMI00009275.html

描述: 菜篮子产品批发价格指数, 数据区间从 20050927-至今

限量: 单次返回所有历史数据

输入参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| -   | -   | -   |

输出参数

| 名称     | 类型      | 描述      |
|--------|---------|---------|
| 日期     | object  | -       |
| 最新值    | int64   | -       |
| 涨跌幅    | float64 | 注意单位: % |
| 近3月涨跌幅 | float64 | 注意单位: % |
| 近6月涨跌幅 | float64 | 注意单位: % |
| 近1年涨跌幅 | float64 | 注意单位: % |
| 近2年涨跌幅 | float64 | 注意单位: % |
| 近3年涨跌幅 | float64 | 注意单位: % |

接口示例

```python
import akshare as ak

macro_china_vegetable_basket_df = ak.macro_china_vegetable_basket()
print(macro_china_vegetable_basket_df)
```

数据示例

```
        日期     最新值       涨跌幅  ...    近1年涨跌幅  近2年涨跌幅  近3年涨跌幅
0     2005-09-27  123.80       NaN  ...       NaN       NaN        NaN
1     2005-09-28  123.00 -0.646204  ...       NaN       NaN        NaN
2     2005-09-29  123.10  0.081301  ...       NaN       NaN        NaN
3     2005-09-30  124.10  0.812348  ...       NaN       NaN        NaN
4     2005-10-08  122.20 -1.531023  ...       NaN       NaN        NaN
          ...     ...       ...  ...       ...       ...        ...
4100  2022-03-28  137.93  0.561388  ...  6.026597  6.608440  15.470908
4101  2022-03-29  138.45  0.377003  ...  6.952491  7.010357  15.712495
4102  2022-03-30  138.85  0.288913  ...  7.410846  7.978847  16.046803
4103  2022-03-31  139.38  0.381707  ...  8.180689  8.289954  16.489762
4104  2022-04-01  139.70  0.229588  ...  8.775208  8.622969  16.484616
```

##### 农产品批发价格总指数

接口: macro_china_agricultural_product

目标地址: https://data.eastmoney.com/cjsj/hyzs_list_EMI00009274.html

描述: 农产品批发价格总指数, 数据区间从 20050927-至今

限量: 单次返回所有历史数据

输入参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| -   | -   | -   |

输出参数

| 名称     | 类型      | 描述      |
|--------|---------|---------|
| 日期     | object  | -       |
| 最新值    | int64   | -       |
| 涨跌幅    | float64 | 注意单位: % |
| 近3月涨跌幅 | float64 | 注意单位: % |
| 近6月涨跌幅 | float64 | 注意单位: % |
| 近1年涨跌幅 | float64 | 注意单位: % |
| 近2年涨跌幅 | float64 | 注意单位: % |
| 近3年涨跌幅 | float64 | 注意单位: % |

接口示例

```python
import akshare as ak

macro_china_agricultural_product_df = ak.macro_china_agricultural_product()
print(macro_china_agricultural_product_df)
```

数据示例

```
          日期     最新值    涨跌幅  ...    近1年涨跌幅  近2年涨跌幅  近3年涨跌幅
0     2005-09-27  125.50       NaN  ...       NaN       NaN        NaN
1     2005-09-28  125.00 -0.398406  ...       NaN       NaN        NaN
2     2005-09-29  125.00  0.000000  ...       NaN       NaN        NaN
3     2005-09-30  125.80  0.640000  ...       NaN       NaN        NaN
4     2005-10-08  124.20 -1.271860  ...       NaN       NaN        NaN
          ...     ...       ...  ...       ...       ...        ...
4100  2022-03-28  134.83  0.499404  ...  6.349582  7.374373  15.259019
4101  2022-03-29  135.30  0.348587  ...  7.100451  7.748666  15.492958
4102  2022-03-30  135.03 -0.199557  ...  7.098668  8.127803  15.262484
4103  2022-03-31  136.10  0.792416  ...  8.265054  8.862582  16.175843
4104  2022-04-01  136.38  0.205731  ...  8.790683  9.156395  16.176846
```

##### 农副指数

接口: macro_china_agricultural_index

目标地址: https://data.eastmoney.com/cjsj/hyzs_list_EMI00662543.html

描述: 农副指数数据, 数据区间从 20111205-至今

限量: 单次返回所有历史数据

输入参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| -   | -   | -   |

输出参数

| 名称     | 类型      | 描述      |
|--------|---------|---------|
| 日期     | object  | -       |
| 最新值    | int64   | -       |
| 涨跌幅    | float64 | 注意单位: % |
| 近3月涨跌幅 | float64 | 注意单位: % |
| 近6月涨跌幅 | float64 | 注意单位: % |
| 近1年涨跌幅 | float64 | 注意单位: % |
| 近2年涨跌幅 | float64 | 注意单位: % |
| 近3年涨跌幅 | float64 | 注意单位: % |

接口示例

```python
import akshare as ak

macro_china_agricultural_index_df = ak.macro_china_agricultural_index()
print(macro_china_agricultural_index_df)
```

数据示例

```
          日期   最新值   涨跌幅  ...    近1年涨跌幅   近2年涨跌幅   近3年涨跌幅
0     2011-12-05   995       NaN  ...       NaN        NaN        NaN
1     2011-12-12   986 -0.904523  ...       NaN        NaN        NaN
2     2011-12-19   990  0.405680  ...       NaN        NaN        NaN
3     2011-12-26   988 -0.202020  ...       NaN        NaN        NaN
4     2012-01-02   993  0.506073  ...       NaN        NaN        NaN
          ...   ...       ...  ...       ...        ...        ...
2971  2022-03-28  1316 -0.679245  ...  7.428571  24.385633  55.739645
2972  2022-03-29  1311 -0.379939  ...  7.635468  23.913043  55.331754
2973  2022-03-30  1305 -0.457666  ...  7.495881  22.881356  54.620853
2974  2022-03-31  1303 -0.153257  ...  8.222591  22.347418  54.383886
2975  2022-04-01  1289 -1.074444  ...  6.090535  21.489161  52.906287
```

##### 能源指数

接口: macro_china_energy_index

目标地址: https://data.eastmoney.com/cjsj/hyzs_list_EMI00662539.html

描述: 能源指数数据, 数据区间从 20111205-至今

限量: 单次返回所有历史数据

输入参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| -   | -   | -   |

输出参数

| 名称     | 类型      | 描述      |
|--------|---------|---------|
| 日期     | object  | -       |
| 最新值    | int64   | -       |
| 涨跌幅    | float64 | 注意单位: % |
| 近3月涨跌幅 | float64 | 注意单位: % |
| 近6月涨跌幅 | float64 | 注意单位: % |
| 近1年涨跌幅 | float64 | 注意单位: % |
| 近2年涨跌幅 | float64 | 注意单位: % |
| 近3年涨跌幅 | float64 | 注意单位: % |

接口示例

```python
import akshare as ak

macro_china_energy_index_df = ak.macro_china_energy_index()
print(macro_china_energy_index_df)
```

数据示例

```
         日期   最新值    涨跌幅  ...     近1年涨跌幅    近2年涨跌幅  近3年涨跌幅
0     2011-12-05  1003       NaN  ...        NaN        NaN        NaN
1     2011-12-12   995 -0.797607  ...        NaN        NaN        NaN
2     2011-12-19   987 -0.804020  ...        NaN        NaN        NaN
3     2011-12-26   983 -0.405268  ...        NaN        NaN        NaN
4     2012-01-02   984  0.101729  ...        NaN        NaN        NaN
          ...   ...       ...  ...        ...        ...        ...
2972  2022-03-29  1208 -0.247729  ...  48.220859  84.992343  48.768473
2973  2022-03-30  1206 -0.165563  ...  47.252747  88.437500  48.522167
2974  2022-03-31  1207  0.082919  ...  47.735618  90.378549  48.645320
2975  2022-04-01  1207  0.000000  ...  48.098160  91.283677  48.828607
2976  2022-04-02  1208  0.082850  ...  47.858017  91.442155  47.858017
```

##### 大宗商品价格

接口: macro_china_commodity_price_index

目标地址: https://data.eastmoney.com/cjsj/hyzs_list_EMI00662535.html

描述: 大宗商品价格数据, 数据区间从 20111205-至今

限量: 单次返回所有历史数据

输入参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| -   | -   | -   |

输出参数

| 名称     | 类型      | 描述      |
|--------|---------|---------|
| 日期     | object  | -       |
| 最新值    | int64   | -       |
| 涨跌幅    | float64 | 注意单位: % |
| 近3月涨跌幅 | float64 | 注意单位: % |
| 近6月涨跌幅 | float64 | 注意单位: % |
| 近1年涨跌幅 | float64 | 注意单位: % |
| 近2年涨跌幅 | float64 | 注意单位: % |
| 近3年涨跌幅 | float64 | 注意单位: % |

接口示例

```python
import akshare as ak

macro_china_commodity_price_index_df = ak.macro_china_commodity_price_index()
print(macro_china_commodity_price_index_df)
```

数据示例

```
          日期   最新值    涨跌幅  ...     近1年涨跌幅   近2年涨跌幅   近3年涨跌幅
0     2011-12-05   999       NaN  ...        NaN        NaN        NaN
1     2011-12-12   991 -0.800801  ...        NaN        NaN        NaN
2     2011-12-19   990 -0.100908  ...        NaN        NaN        NaN
3     2011-12-26   988 -0.202020  ...        NaN        NaN        NaN
4     2012-01-02   992  0.404858  ...        NaN        NaN        NaN
          ...   ...       ...  ...        ...        ...        ...
2975  2022-03-31  1213  0.000000  ...  21.543086  68.005540  40.556199
2976  2022-04-01  1212 -0.082440  ...  20.958084  69.037657  40.277778
2977  2022-04-02  1212  0.000000  ...  20.717131  70.224719  40.440324
2978  2022-04-03  1212  0.000000  ...  20.837488  70.464135  40.440324
2979  2022-04-04  1212  0.000000  ...  20.837488  70.704225  40.440324
```

##### 费城半导体指数

接口: macro_global_sox_index

目标地址: https://data.eastmoney.com/cjsj/hyzs_list_EMI00055562.html

描述: 费城半导体指数数据, 数据区间从 19940504-至今

限量: 单次返回所有历史数据

输入参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| -   | -   | -   |

输出参数

| 名称     | 类型      | 描述      |
|--------|---------|---------|
| 日期     | object  | -       |
| 最新值    | int64   | -       |
| 涨跌幅    | float64 | 注意单位: % |
| 近3月涨跌幅 | float64 | 注意单位: % |
| 近6月涨跌幅 | float64 | 注意单位: % |
| 近1年涨跌幅 | float64 | 注意单位: % |
| 近2年涨跌幅 | float64 | 注意单位: % |
| 近3年涨跌幅 | float64 | 注意单位: % |

接口示例

```python
import akshare as ak

macro_global_sox_index_df = ak.macro_global_sox_index()
print(macro_global_sox_index_df)
```

数据示例

```
         日期     最新值    涨跌幅  ...     近1年涨跌幅   近2年涨跌幅      近3年涨跌幅
0     1994-05-04   119.0       NaN  ...        NaN         NaN         NaN
1     1994-05-05   118.3 -0.588235  ...        NaN         NaN         NaN
2     1994-05-06   117.7 -0.507185  ...        NaN         NaN         NaN
3     1994-05-09   115.6 -1.784197  ...        NaN         NaN         NaN
4     1994-05-10   117.4  1.557093  ...        NaN         NaN         NaN
          ...     ...       ...  ...        ...         ...         ...
6957  2022-03-25  3525.3 -0.209472  ...  19.102801  139.397788  154.948472
6958  2022-03-28  3547.2  0.621224  ...  14.194471  138.267003  158.203523
6959  2022-03-29  3625.6  2.210194  ...  18.691953  143.533165  159.804659
6960  2022-03-30  3508.6 -3.227052  ...  15.241990  127.048295  151.420628
6961  2022-03-31  3429.0 -2.268711  ...   9.730107  126.997398  145.716620
```

##### 义乌小商品指数-电子元器件

接口: macro_china_yw_electronic_index

目标地址: https://data.eastmoney.com/cjsj/hyzs_list_EMI00055551.html

描述: 义乌小商品指数-电子元器件数据, 数据区间从 20060911-至今

限量: 单次返回所有历史数据

输入参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| -   | -   | -   |

输出参数

| 名称     | 类型      | 描述      |
|--------|---------|---------|
| 日期     | object  | -       |
| 最新值    | int64   | -       |
| 涨跌幅    | float64 | 注意单位: % |
| 近3月涨跌幅 | float64 | 注意单位: % |
| 近6月涨跌幅 | float64 | 注意单位: % |
| 近1年涨跌幅 | float64 | 注意单位: % |
| 近2年涨跌幅 | float64 | 注意单位: % |
| 近3年涨跌幅 | float64 | 注意单位: % |

接口示例

```python
import akshare as ak

macro_china_yw_electronic_index_df = ak.macro_china_yw_electronic_index()
print(macro_china_yw_electronic_index_df)
```

数据示例

```
        日期     最新值   涨跌幅    近3月涨跌幅 近6月涨跌幅 近1年涨跌幅  近2年涨跌幅 近3年涨跌幅
0    2006-09-11  101.34       NaN       NaN     NaN     NaN       NaN     NaN
1    2006-09-18  102.94  1.578844       NaN     NaN     NaN       NaN     NaN
2    2006-09-25  103.34  0.388576       NaN     NaN     NaN       NaN     NaN
3    2006-10-02  103.21 -0.125798       NaN     NaN     NaN       NaN     NaN
4    2006-10-09  103.06 -0.145335       NaN     NaN     NaN       NaN     NaN
..          ...     ...       ...       ...     ...     ...       ...     ...
755  2022-03-07  100.18 -0.169407  0.089919    0.18    0.18 -0.802060    0.18
756  2022-03-14  100.09 -0.089838  0.039980    0.09    0.09 -0.407960    0.09
757  2022-03-21  106.11  6.014587  0.463927    6.11    6.11  5.845387    6.11
758  2022-03-28  103.06 -2.874376  0.243167    3.06    3.06  2.936476    3.06
759  2022-04-04  101.53 -1.484572  0.118332    1.53    1.53  1.469119    1.53
```

##### 建材指数

接口: macro_china_construction_index

目标地址: https://data.eastmoney.com/cjsj/hyzs_list_EMI00662541.html

描述: 建材指数数据, 数据区间从 20111205-至今

限量: 单次返回所有历史数据

输入参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| -   | -   | -   |

输出参数

| 名称     | 类型      | 描述      |
|--------|---------|---------|
| 日期     | object  | -       |
| 最新值    | int64   | -       |
| 涨跌幅    | float64 | 注意单位: % |
| 近3月涨跌幅 | float64 | 注意单位: % |
| 近6月涨跌幅 | float64 | 注意单位: % |
| 近1年涨跌幅 | float64 | 注意单位: % |
| 近2年涨跌幅 | float64 | 注意单位: % |
| 近3年涨跌幅 | float64 | 注意单位: % |

接口示例

```python
import akshare as ak

macro_china_construction_index_df = ak.macro_china_construction_index()
print(macro_china_construction_index_df)
```

数据示例

```
         日期   最新值       涨跌幅  ...    近1年涨跌幅     近2年涨跌幅     近3年涨跌幅
0     2011-12-05  1014       NaN  ...       NaN        NaN        NaN
1     2011-12-12  1013 -0.098619  ...       NaN        NaN        NaN
2     2011-12-19  1005 -0.789733  ...       NaN        NaN        NaN
3     2011-12-26   993 -1.194030  ...       NaN        NaN        NaN
4     2012-01-02   991 -0.201410  ...       NaN        NaN        NaN
          ...   ...       ...  ...       ...        ...        ...
3179  2024-03-31   962  0.000000  ... -8.901515 -25.077882 -23.285486
3180  2024-04-01   958 -0.415800  ... -9.280303 -25.505443 -23.726115
3181  2024-04-02   960  0.208768  ... -9.090909 -25.407925 -24.170616
3182  2024-04-03   960  0.000000  ... -9.176916 -25.407925 -24.170616
3183  2024-04-04   960  0.000000  ... -8.745247 -25.407925 -24.170616
[3184 rows x 8 columns]
```

##### 建材价格指数

接口: macro_china_construction_price_index

目标地址: https://data.eastmoney.com/cjsj/hyzs_list_EMI00237146.html

描述: 建材价格指数数据, 数据区间从 20100615-至今

限量: 单次返回所有历史数据

输入参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| -   | -   | -   |

输出参数

| 名称     | 类型      | 描述      |
|--------|---------|---------|
| 日期     | object  | -       |
| 最新值    | int64   | -       |
| 涨跌幅    | float64 | 注意单位: % |
| 近3月涨跌幅 | float64 | 注意单位: % |
| 近6月涨跌幅 | float64 | 注意单位: % |
| 近1年涨跌幅 | float64 | 注意单位: % |
| 近2年涨跌幅 | float64 | 注意单位: % |
| 近3年涨跌幅 | float64 | 注意单位: % |

接口示例

```python
import akshare as ak

macro_china_construction_price_index_df = ak.macro_china_construction_price_index()
print(macro_china_construction_price_index_df)
```

数据示例

```
       日期        最新值    涨跌幅  ...    近1年涨跌幅   近2年涨跌幅  近3年涨跌幅
0     2010-06-15  1230.40       NaN  ...       NaN       NaN       NaN
1     2010-06-16  1245.10  1.194733  ...       NaN       NaN       NaN
2     2010-08-01  1281.60  2.931491  ...       NaN       NaN       NaN
3     2010-08-02  1214.90 -5.204432  ...       NaN       NaN       NaN
4     2010-08-03  1218.90  0.329245  ...       NaN       NaN       NaN
          ...      ...       ...  ...       ...       ...       ...
3841  2022-02-25  1099.01  1.567395  ...  1.467995  1.493295  2.177410
3842  2022-02-26  1088.05 -0.997261  ...  0.456094  0.481142  1.521824
3843  2022-02-27  1097.41  0.860255  ...  1.120479  1.345536  2.198733
3844  2022-02-28  1088.05 -0.852917  ...  0.977244  0.481142  1.549302
3845  2022-03-01  1097.41  0.860255  ...  1.419528  0.749139  2.051425
```

##### 物流景气指数

接口: macro_china_lpi_index

目标地址: https://data.eastmoney.com/cjsj/hyzs_list_EMI00352262.html

描述: 物流景气指数数据, 数据区间从 20130701-至今

限量: 单次返回所有历史数据

输入参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| -   | -   | -   |

输出参数

| 名称     | 类型      | 描述      |
|--------|---------|---------|
| 日期     | object  | -       |
| 最新值    | int64   | -       |
| 涨跌幅    | float64 | 注意单位: % |
| 近3月涨跌幅 | float64 | 注意单位: % |
| 近6月涨跌幅 | float64 | 注意单位: % |
| 近1年涨跌幅 | float64 | 注意单位: % |
| 近2年涨跌幅 | float64 | 注意单位: % |
| 近3年涨跌幅 | float64 | 注意单位: % |

接口示例

```python
import akshare as ak

macro_china_lpi_index_df = ak.macro_china_lpi_index()
print(macro_china_lpi_index_df)
```

数据示例

```
         日期   最新值   涨跌幅  ...    近1年涨跌幅   近2年涨跌幅 近3年涨跌幅
0    2013-07-01  52.4       NaN  ...       NaN        NaN       NaN
1    2013-08-01  52.9  0.954198  ...       NaN        NaN       NaN
2    2013-09-01  53.3  0.756144  ...       NaN        NaN       NaN
3    2013-10-01  53.6  0.562852  ...       NaN        NaN       NaN
4    2013-11-01  53.0 -1.119403  ...       NaN        NaN       NaN
..          ...   ...       ...  ...       ...        ...       ...
100  2021-11-01  53.6  0.186916  ... -6.782609  -8.998302  7.200000
101  2021-12-01  52.6 -1.865672  ... -7.557118 -10.238908 -3.839122
102  2022-01-01  51.1 -2.851711  ... -6.066176   2.404810 -4.841713
103  2022-02-01  51.2  0.195695  ...  2.811245  95.419847  3.225806
104  2022-03-01  48.7 -4.882812  ... -6.165703  -5.436893 -7.414449
```

##### 原油运输指数

接口: macro_china_bdti_index

目标地址: https://data.eastmoney.com/cjsj/hyzs_list_EMI00107668.html

描述: 原油运输指数数据, 数据区间从 20011227-至今

限量: 单次返回所有历史数据

输入参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| -   | -   | -   |

输出参数

| 名称     | 类型      | 描述      |
|--------|---------|---------|
| 日期     | object  | -       |
| 最新值    | int64   | -       |
| 涨跌幅    | float64 | 注意单位: % |
| 近3月涨跌幅 | float64 | 注意单位: % |
| 近6月涨跌幅 | float64 | 注意单位: % |
| 近1年涨跌幅 | float64 | 注意单位: % |
| 近2年涨跌幅 | float64 | 注意单位: % |
| 近3年涨跌幅 | float64 | 注意单位: % |

接口示例

```python
import akshare as ak

macro_china_bdti_index_df = ak.macro_china_bdti_index()
print(macro_china_bdti_index_df)
```

数据示例

```
           日期   最新值   涨跌幅  ...      近1年涨跌幅     近2年涨跌幅      近3年涨跌幅
0     2001-12-27   849       NaN  ...         NaN        NaN         NaN
1     2001-12-28   850  0.117786  ...         NaN        NaN         NaN
2     2002-01-02   845 -0.588235  ...         NaN        NaN         NaN
3     2002-01-03   826 -2.248521  ...         NaN        NaN         NaN
4     2002-01-04   811 -1.815981  ...         NaN        NaN         NaN
          ...   ...       ...  ...         ...        ...         ...
4921  2022-04-05  1469  7.304602  ...  110.157368   6.218366  138.087520
4922  2022-04-06  1547  5.309735  ...  125.839416  24.758065  150.729335
4923  2022-04-07  1653  6.851972  ...  144.888889  47.326203  167.909238
4924  2022-04-08  1677  1.451906  ...  154.863222  56.582633  174.918033
4925  2022-04-11  1730  3.160405  ...  167.801858  55.296230  172.870662
```

##### 超灵便型船运价指数

接口: macro_china_bsi_index

目标地址: https://data.eastmoney.com/cjsj/hyzs_list_EMI00107667.html

描述: 超灵便型船运价指数数据, 数据区间从 20060103-至今

限量: 单次返回所有历史数据

输入参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| -   | -   | -   |

输出参数

| 名称     | 类型      | 描述      |
|--------|---------|---------|
| 日期     | object  | -       |
| 最新值    | int64   | -       |
| 涨跌幅    | float64 | 注意单位: % |
| 近3月涨跌幅 | float64 | 注意单位: % |
| 近6月涨跌幅 | float64 | 注意单位: % |
| 近1年涨跌幅 | float64 | 注意单位: % |
| 近2年涨跌幅 | float64 | 注意单位: % |
| 近3年涨跌幅 | float64 | 注意单位: % |

接口示例

```python
import akshare as ak

macro_china_bsi_index_df = ak.macro_china_bsi_index()
print(macro_china_bsi_index_df)
```

数据示例

```
          日期   最新值   涨跌幅  ...     近1年涨跌幅    近2年涨跌幅    近3年涨跌幅
0     2006-01-03  1819       NaN  ...        NaN         NaN         NaN
1     2006-01-05  1825  0.329852  ...        NaN         NaN         NaN
2     2006-01-09  1814 -0.602740  ...        NaN         NaN         NaN
3     2006-01-11  1784 -1.653804  ...        NaN         NaN         NaN
4     2006-01-12  1759 -1.401345  ...        NaN         NaN         NaN
          ...   ...       ...  ...        ...         ...         ...
3984  2022-04-06  2605 -2.067669  ...  44.002211  437.113402  251.078167
3985  2022-04-07  2547 -2.226488  ...  42.131696  439.618644  243.261456
3986  2022-04-08  2502 -1.766784  ...  40.089586  451.101322  241.336971
3987  2022-04-11  2473 -1.159073  ...  38.932584  460.770975  245.874126
3988  2022-04-12  2448 -1.010918  ...  37.993236  455.102041  244.303797
```

##### 海岬型运费指数

接口: macro_shipping_bci

目标地址: https://data.eastmoney.com/cjsj/hyzs_list_EMI00107666.html

描述: 海岬型运费指数, 数据区间从 19990430-至今

限量: 单次返回所有历史数据

输入参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| -   | -   | -   |

输出参数

| 名称     | 类型      | 描述      |
|--------|---------|---------|
| 日期     | object  | -       |
| 最新值    | int64   | -       |
| 涨跌幅    | float64 | 注意单位: % |
| 近3月涨跌幅 | float64 | 注意单位: % |
| 近6月涨跌幅 | float64 | 注意单位: % |
| 近1年涨跌幅 | float64 | 注意单位: % |
| 近2年涨跌幅 | float64 | 注意单位: % |
| 近3年涨跌幅 | float64 | 注意单位: % |

接口示例

```python
import akshare as ak

macro_shipping_bci_df = ak.macro_shipping_bci()
print(macro_shipping_bci_df)
```

数据示例

```
      日期   最新值   涨跌幅  ...     近1年涨跌幅     近2年涨跌幅      近3年涨跌幅
0     1999-04-30   940        NaN  ...        NaN        NaN         NaN
1     1999-05-04   947   0.744681  ...        NaN        NaN         NaN
2     1999-05-05   960   1.372756  ...        NaN        NaN         NaN
3     1999-05-06   969   0.937500  ...        NaN        NaN         NaN
4     1999-05-07   981   1.238390  ...        NaN        NaN         NaN
          ...   ...        ...  ...        ...        ...         ...
5988  2023-03-03  1195  19.500000  ... -27.089689 -28.741801  445.375723
5989  2023-03-06  1329  11.213389  ... -18.715596 -25.504484  525.961538
5990  2023-03-07  1471  10.684725  ... -15.894797 -17.544843  571.474359
5991  2023-03-08  1550   5.370496  ... -18.248945 -15.068493  596.794872
5992  2023-03-09  1662   7.225806  ... -27.169150 -14.769231  546.774194
```

##### 波罗的海干散货指数

接口: macro_shipping_bdi

目标地址: https://data.eastmoney.com/cjsj/hyzs_list_EMI00107664.html

描述: 波罗的海干散货指数, 数据区间从 19881019-至今

限量: 单次返回所有历史数据

输入参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| -   | -   | -   |

输出参数

| 名称     | 类型      | 描述      |
|--------|---------|---------|
| 日期     | object  | -       |
| 最新值    | int64   | -       |
| 涨跌幅    | float64 | 注意单位: % |
| 近3月涨跌幅 | float64 | 注意单位: % |
| 近6月涨跌幅 | float64 | 注意单位: % |
| 近1年涨跌幅 | float64 | 注意单位: % |
| 近2年涨跌幅 | float64 | 注意单位: % |
| 近3年涨跌幅 | float64 | 注意单位: % |

接口示例

```python
import akshare as ak

macro_shipping_bdi_df = ak.macro_shipping_bdi()
print(macro_shipping_bdi_df)
```

数据示例

```
      日期   最新值    涨跌幅  ...     近1年涨跌幅     近2年涨跌幅      近3年涨跌幅
0     1988-10-19  1317       NaN  ...        NaN        NaN         NaN
1     1988-10-20  1316 -0.075930  ...        NaN        NaN         NaN
2     1988-10-21  1328  0.911854  ...        NaN        NaN         NaN
3     1988-10-24  1361  2.484940  ...        NaN        NaN         NaN
4     1988-10-25  1363  0.146951  ...        NaN        NaN         NaN
          ...   ...       ...  ...        ...        ...         ...
8577  2023-03-03  1211  5.764192  ... -42.442966 -31.310267  120.582878
8578  2023-03-06  1258  3.881090  ... -41.433892 -31.219245  103.889789
8579  2023-03-07  1298  3.179650  ... -41.923937 -29.032258  110.372771
8580  2023-03-08  1327  2.234206  ... -43.579932 -28.386400  115.072934
8581  2023-03-09  1379  3.918613  ... -46.090696 -27.459232  123.863636
```

##### 巴拿马型运费指数

接口: macro_shipping_bpi

目标地址: https://data.eastmoney.com/cjsj/hyzs_list_EMI00107665.html

描述: 巴拿马型运费指数, 数据区间从 19981231-至今

限量: 单次返回所有历史数据

输入参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| -   | -   | -   |

输出参数

| 名称     | 类型      | 描述      |
|--------|---------|---------|
| 日期     | object  | -       |
| 最新值    | int64   | -       |
| 涨跌幅    | float64 | 注意单位: % |
| 近3月涨跌幅 | float64 | 注意单位: % |
| 近6月涨跌幅 | float64 | 注意单位: % |
| 近1年涨跌幅 | float64 | 注意单位: % |
| 近2年涨跌幅 | float64 | 注意单位: % |
| 近3年涨跌幅 | float64 | 注意单位: % |

接口示例

```python
import akshare as ak

macro_shipping_bpi_df = ak.macro_shipping_bpi()
print(macro_shipping_bpi_df)
```

数据示例

```
     日期   最新值       涨跌幅  ...     近1年涨跌幅     近2年涨跌幅     近3年涨跌幅
0     1998-12-31   732       NaN  ...        NaN        NaN        NaN
1     1999-01-04   717 -2.049180  ...        NaN        NaN        NaN
2     1999-01-05   709 -1.115760  ...        NaN        NaN        NaN
3     1999-01-06   729  2.820874  ...        NaN        NaN        NaN
4     1999-01-07   734  0.685871  ...        NaN        NaN        NaN
          ...   ...       ...  ...        ...        ...        ...
5951  2023-03-03  1565  0.578406  ... -42.015561 -27.579824  67.379679
5952  2023-03-06  1582  1.086262  ... -43.195691 -29.406515  48.127341
5953  2023-03-07  1580 -0.126422  ... -45.536022 -29.495761  47.940075
5954  2023-03-08  1592  0.759494  ... -47.648800 -29.495128  49.063670
5955  2023-03-09  1624  2.010050  ... -49.154665 -27.435210  52.202437
```

##### 成品油运输指数

接口: macro_shipping_bcti

目标地址: https://data.eastmoney.com/cjsj/hyzs_list_EMI00107669.html

描述: 成品油运输指数, 数据区间从 20011217-至今

限量: 单次返回所有历史数据

输入参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| -   | -   | -   |

输出参数

| 名称     | 类型      | 描述      |
|--------|---------|---------|
| 日期     | object  | -       |
| 最新值    | int64   | -       |
| 涨跌幅    | float64 | 注意单位: % |
| 近3月涨跌幅 | float64 | 注意单位: % |
| 近6月涨跌幅 | float64 | 注意单位: % |
| 近1年涨跌幅 | float64 | 注意单位: % |
| 近2年涨跌幅 | float64 | 注意单位: % |
| 近3年涨跌幅 | float64 | 注意单位: % |

接口示例

```python
import akshare as ak

macro_shipping_bcti_df = ak.macro_shipping_bcti()
print(macro_shipping_bcti_df)
```

数据示例

```
    日期  最新值       涨跌幅  ...     近1年涨跌幅     近2年涨跌幅     近3年涨跌幅
0     2001-12-27  693       NaN  ...        NaN        NaN        NaN
1     2001-12-28  691 -0.288600  ...        NaN        NaN        NaN
2     2002-01-02  688 -0.434153  ...        NaN        NaN        NaN
3     2002-01-03  687 -0.145349  ...        NaN        NaN        NaN
4     2002-01-04  687  0.000000  ...        NaN        NaN        NaN
          ...  ...       ...  ...        ...        ...        ...
5148  2023-03-03  789 -1.743462  ... -20.383451  62.012320  22.136223
5149  2023-03-06  782 -0.887199  ... -20.930233  60.245902  14.160584
5150  2023-03-07  784  0.255754  ... -26.797386  60.655738  14.452555
5151  2023-03-08  827  5.484694  ... -21.163012  67.748479  20.729927
5152  2023-03-09  871  5.320435  ... -15.682478  73.161034  21.478382
```

##### 新增信贷数据

接口: macro_china_new_financial_credit

目标地址: http://data.eastmoney.com/cjsj/xzxd.html

描述: 中国新增信贷数据数据, 数据区间从 200801 至今, 月度数据

限量: 单次返回所有历史数据

输入参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| -   | -   | -   |

输出参数

| 名称      | 类型      | 描述       |
|---------|---------|----------|
| 月份      | object  | -        |
| 当月      | float64 | 注意单位: 亿元 |
| 当月-同比增长 | float64 | 注意单位: %  |
| 当月-环比增长 | float64 | 注意单位: %  |
| 累计      | float64 | 注意单位: 亿元 |
| 累计-同比增长 | float64 | 注意单位: %  |

接口示例

```python
import akshare as ak

macro_china_new_financial_credit_df = ak.macro_china_new_financial_credit()
print(macro_china_new_financial_credit_df)
```

数据示例

```
     月份       当月    当月-同比增长      当月-环比增长        累计    累计-同比增长
0    2022年10月份   4431.0 -42.840557   -82.749358  183298.0   4.127067
1    2022年09月份  25686.0  44.669107    92.491007  178867.0   6.290669
2    2022年08月份  13344.0   4.963423   226.418787  153181.0   1.763815
3    2022年07月份   4088.0 -51.281135   -86.614276  139837.0   1.468657
4    2022年06月份  30540.0  31.740143    67.526056  135749.0   4.888659
..         ...      ...        ...          ...       ...        ...
173  2008年05月份   3185.0  28.790942   -32.089552   21201.0   1.386830
174  2008年04月份   4690.0  11.137441    65.490473   18016.0  -2.288751
175  2008年03月份   2834.0 -35.838805    16.433854   13326.0  -6.273738
176  2008年02月份   2434.0 -41.179314   -69.793994   10492.0   7.050301
177  2008年01月份   8058.0  42.292071  1561.443299    8058.0  42.292071
```

##### 居民消费价格指数

接口: macro_china_cpi

目标地址: http://data.eastmoney.com/cjsj/cpi.html

描述: 中国居民消费价格指数, 数据区间从 200801 至今, 月度数据

限量: 单次返回所有历史数据

输入参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| -   | -   | -   |

输出参数

| 名称      | 类型      | 描述      |
|---------|---------|---------|
| 月份      | object  | -       |
| 全国-当月   | float64 | -       |
| 全国-同比增长 | float64 | 注意单位: % |
| 全国-环比增长 | float64 | 注意单位: % |
| 全国-累计   | float64 | -       |
| 城市-当月   | float64 | -       |
| 城市-同比增长 | float64 | 注意单位: % |
| 城市-环比增长 | float64 | 注意单位: % |
| 城市-累计   | float64 | -       |
| 农村-当月   | float64 | -       |
| 农村-同比增长 | float64 | 注意单位: % |
| 农村-环比增长 | float64 | 注意单位: % |
| 农村-累计   | float64 | -       |

接口示例

```python
import akshare as ak

macro_china_cpi_df = ak.macro_china_cpi()
print(macro_china_cpi_df)
```

数据示例

```
     月份     全国-当月  全国-同比增长  ...  农村-同比增长  农村-环比增长     农村-累计
0    2022年10月份  102.1000   2.1000  ...   2.5000      0.1  102.0000
1    2022年09月份  102.8000   2.8000  ...   3.1000      0.4  102.0000
2    2022年08月份  102.5000   2.5000  ...   2.7000     -0.1  101.8000
3    2022年07月份  102.7000   2.7000  ...   3.0000      0.5  101.7000
4    2022年06月份  102.5000   2.5000  ...   2.6000      0.0  101.5000
..         ...       ...      ...  ...      ...      ...       ...
173  2008年05月份  107.7163   7.7163  ...   8.5481     -0.3  108.7612
174  2008年04月份  108.4829   8.4829  ...   9.2737      0.1  108.8147
175  2008年03月份  108.3097   8.3097  ...   9.0330     -0.5  108.6618
176  2008年02月份  108.7443   8.7443  ...   9.2344      2.4  108.4812
177  2008年01月份  107.0781   7.0781  ...   7.7209      1.2  107.7209
```

##### 国内生产总值

接口: macro_china_gdp

目标地址: http://data.eastmoney.com/cjsj/gdp.html

描述: 中国国内生产总值, 数据区间从 200601 至今, 月度数据

限量: 单次返回所有历史数据

输入参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| -   | -   | -   |

输出参数

| 名称          | 类型      | 描述       |
|-------------|---------|----------|
| 季度          | object  | -        |
| 国内生产总值-绝对值  | float64 | 注意单位: 亿元 |
| 国内生产总值-同比增长 | float64 | 注意单位: %  |
| 第一产业-绝对值    | float64 | 注意单位: 亿元 |
| 第一产业-同比增长   | float64 | 注意单位: %  |
| 第二产业-绝对值    | float64 | 注意单位: 亿元 |
| 第二产业-同比增长   | float64 | 注意单位: %  |
| 第三产业-绝对值    | float64 | 注意单位: 亿元 |
| 第三产业-同比增长   | float64 | 注意单位: %  |

接口示例

```python
import akshare as ak

macro_china_gdp_df = ak.macro_china_gdp()
print(macro_china_gdp_df)
```

数据示例

```
   季度  国内生产总值-绝对值  国内生产总值-同比增长  ...  第二产业-同比增长  第三产业-绝对值  第三产业-同比增长
0   2021-03-01    249310.0         18.3  ...       24.4  145355.0       15.6
1   2020-12-01   1015986.2          2.3  ...        2.6  553976.8        2.1
2   2020-09-01    719688.4          0.7  ...        0.9  401249.1        0.4
3   2020-06-01    454712.1         -1.6  ...       -1.9  258427.4       -1.6
4   2020-03-01    205727.0         -6.8  ...       -9.6  123008.5       -5.2
..         ...         ...          ...  ...        ...       ...        ...
56  2007-03-01     57159.3         13.8  ...       14.8   27703.2       14.1
57  2006-12-01    219438.5         12.7  ...       13.5   91762.2       14.1
58  2006-09-01    155816.8         12.8  ...       13.7   67187.0       13.7
59  2006-06-01     99752.2         13.1  ...       14.2   44996.5       13.6
60  2006-03-01     47078.9         12.5  ...       13.1   22648.0       13.1
```

##### 工业品出厂价格指数

接口: macro_china_ppi

目标地址: http://data.eastmoney.com/cjsj/ppi.html

描述: 工业品出厂价格指数, 数据区间从 200601 至今, 月度数据

限量: 单次返回所有历史数据

输入参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| -   | -   | -   |

输出参数

| 名称     | 类型      | 描述      |
|--------|---------|---------|
| 月份     | object  | -       |
| 当月     | float64 | -       |
| 当月同比增长 | float64 | 注意单位: % |
| 累计     | float64 | -       |

接口示例

```python
import akshare as ak

macro_china_ppi_df = ak.macro_china_ppi()
print(macro_china_ppi_df)
```

数据示例

```
            月份       当月  当月同比增长        累计
0    2022年10月份   98.700   -1.30  105.2000
1    2022年09月份  100.948    0.90  105.9397
2    2022年08月份  102.300    2.30  106.6000
3    2022年07月份  104.200    4.20  107.2000
4    2022年06月份  106.100    6.10  107.7000
..         ...      ...     ...       ...
197  2006年05月份  102.430    2.43  102.5700
198  2006年04月份  101.870    1.87  102.6000
199  2006年03月份  102.490    2.49  102.9000
200  2006年02月份  103.010    3.01  103.0000
201  2006年01月份  103.050    3.05  103.0500
```

##### 采购经理人指数

接口: macro_china_pmi

目标地址: http://data.eastmoney.com/cjsj/pmi.html

描述: 采购经理人指数, 数据区间从 200801 至今, 月度数据

限量: 单次返回所有历史数据

输入参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| -   | -   | -   |

输出参数

| 名称        | 类型      | 描述      |
|-----------|---------|---------|
| 月份        | object  | -       |
| 制造业-指数    | float64 | -       |
| 制造业-同比增长  | float64 | 注意单位: % |
| 非制造业-指数   | float64 | -       |
| 非制造业-同比增长 | float64 | 注意单位: % |

接口示例

```python
import akshare as ak

macro_china_pmi_df = ak.macro_china_pmi()
print(macro_china_pmi_df)
```

数据示例

```
    月份  制造业-指数  制造业-同比增长  非制造业-指数  非制造业-同比增长
0    2022年10月份    49.2  0.000000     48.7  -7.061069
1    2022年09月份    50.1  1.008065     50.6  -4.887218
2    2022年08月份    49.4 -1.397206     52.6  10.736842
3    2022年07月份    49.0 -2.777778     53.8   0.938086
4    2022年06月份    50.2 -1.375246     54.7   2.242991
..         ...     ...       ...      ...        ...
173  2008年05月份    53.3 -4.308797     57.4  -7.717042
174  2008年04月份    59.2  1.023891     58.4  -3.311258
175  2008年03月份    58.4  4.099822     58.9   1.202749
176  2008年02月份    53.4  0.564972     59.3  -2.145215
177  2008年01月份    53.0 -3.811252     60.2  -0.331126
```

##### 中国城镇固定资产投资

接口: macro_china_gdzctz

目标地址: http://data.eastmoney.com/cjsj/gdzctz.html

描述: 中国城镇固定资产投资, 数据区间从 200802 至今, 月度数据

限量: 单次返回所有历史数据

输入参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| -   | -   | -   |

输出参数

| 名称    | 类型      | 描述       |
|-------|---------|----------|
| 月份    | object  | -        |
| 当月    | float64 | 注意单位: 亿元 |
| 同比增长  | float64 | 注意单位: %  |
| 环比增长  | float64 | 注意单位: %  |
| 自年初累计 | float64 | 注意单位: 亿元 |

接口示例

```python
import akshare as ak

macro_china_gdzctz_df = ak.macro_china_gdzctz()
print(macro_china_gdzctz_df)
```

数据示例

```
        月份        当月   同比增长   环比增长      自年初累计
0    2022年10月份  50047.00   4.27  -7.84  471459.00
1    2022年09月份  54306.00   6.66  14.83  421412.00
2    2022年08月份  47294.00   6.57  -2.25  367106.00
3    2022年07月份  48382.00   3.75 -26.10  319812.00
4    2022年06月份  65466.00   5.62  24.89  271430.00
..         ...       ...    ...    ...        ...
158  2008年06月份  18171.78  29.49  53.29   58435.98
159  2008年05月份  11854.13  25.44  17.45   40264.20
160  2008年04月份  10093.14  25.37  -1.01   28410.07
161  2008年03月份  10195.65  27.31    NaN   18316.94
162  2008年02月份       NaN    NaN    NaN    8121.29
```

##### 海关进出口增减情况

接口: macro_china_hgjck

目标地址: https://data.eastmoney.com/cjsj/hgjck.html

描述: 中国海关进出口增减情况一览表, 数据区间从 200801 至今, 月度数据

限量: 单次返回所有历史数据

输入参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| -   | -   | -   |

输出参数

| 名称         | 类型      | 描述        |
|------------|---------|-----------|
| 月份         | object  | -         |
| 当月出口额-金额   | float64 | 注意单位: 亿美元 |
| 当月出口额-同比增长 | float64 | 注意单位: %   |
| 当月出口额-环比增长 | float64 | 注意单位: %   |
| 当月进口额-金额   | float64 | 注意单位: 亿美元 |
| 当月进口额-同比增长 | float64 | 注意单位: %   |
| 当月进口额-环比增长 | float64 | 注意单位: %   |
| 累计出口额-金额   | float64 | 注意单位: 亿美元 |
| 累计出口额-同比增长 | float64 | 注意单位: %   |
| 累计进口额-金额   | float64 | 注意单位: 亿美元 |
| 累计进口额-同比增长 | float64 | 注意单位: %   |

接口示例

```python
import akshare as ak

macro_china_hgjck_df = ak.macro_china_hgjck()
print(macro_china_hgjck_df)
```

数据示例

```
     月份      当月出口额-金额  当月出口额-同比增长  ...  累计出口额-同比增长      累计进口额-金额  累计进口额-同比增长
0    2022年10月份  2.983717e+08        -0.3  ...        11.1  2.264551e+09         3.5
1    2022年09月份  3.226903e+08         5.9  ...        12.5  2.051334e+09         4.0
2    2022年08月份  3.143794e+08         7.2  ...        13.5  1.813451e+09         4.5
3    2022年07月份  3.318411e+08        18.0  ...        14.5  1.578288e+09         5.2
4    2022年06月份  3.281211e+08        17.1  ...        13.9  1.347358e+09         5.7
..         ...           ...         ...  ...         ...           ...         ...
173  2008年05月份  1.204965e+08        28.1  ...        22.9  4.670271e+08        30.4
174  2008年04月份  1.187067e+08        21.8  ...        21.5  3.665725e+08        27.9
175  2008年03月份  1.089629e+08        30.6  ...        21.4  2.644787e+08        28.6
176  2008年02月份  8.736780e+07         6.5  ...        16.8  1.689377e+08        30.9
177  2008年01月份  1.096400e+08        26.6  ...        26.6  9.017445e+07        27.6
```

##### 财政收入

接口: macro_china_czsr

目标地址: http://data.eastmoney.com/cjsj/czsr.html

描述: 中国财政收入, 数据区间从 200801 至今, 月度数据

限量: 单次返回所有历史数据

输入参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| -   | -   | -   |

输出参数

| 名称      | 类型      | 描述       |
|---------|---------|----------|
| 月份      | object  | -        |
| 当月      | float64 | 注意单位: 亿元 |
| 当月-同比增长 | float64 | 注意单位: %  |
| 当月-环比增长 | float64 | 注意单位: %  |
| 累计      | float64 | 注意单位: 亿元 |
| 累计-同比增长 | float64 | 注意单位: %  |

接口示例

```python
import akshare as ak

macro_china_czsr_df = ak.macro_china_czsr()
print(macro_china_czsr_df)
```

数据示例

```
     月份        当月    当月-同比增长     当月-环比增长         累计  累计-同比增长
0    2022年10月份  20246.00  15.651777   34.008472  173397.00     -4.5
1    2022年09月份  15108.00   8.440999   15.663757  153151.00     -6.6
2    2022年08月份  13062.00   5.577110  -33.896761  138043.00     -8.0
3    2022年07月份  19760.00  -4.077670    6.914836  124981.00     -9.2
4    2022年06月份  18482.00 -10.550770   48.497509  105221.00    -10.2
..         ...       ...        ...         ...        ...      ...
169  2008年05月份   6268.15  52.602556   -8.157360   29064.37     33.8
170  2008年04月份   6824.88  17.016465   54.550663   22796.22     29.4
171  2008年03月份   4415.95  24.671449    6.184551   15971.34     35.5
172  2008年02月份   4158.75  36.552139  -43.775147   11555.39     40.2
173  2008年01月份   7396.64  42.352305  135.212710    7396.64     42.4
```

##### 外汇贷款数据

接口: macro_china_whxd

目标地址: http://data.eastmoney.com/cjsj/whxd.html

描述: 外汇贷款数据, 数据区间从 200802 至今, 月度数据

限量: 单次返回所有历史数据

输入参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| -   | -   | -   |

输出参数

| 名称   | 类型      | 描述        |
|------|---------|-----------|
| 月份   | object  | -         |
| 当月   | float64 | 注意单位: 亿美元 |
| 同比增长 | float64 | 注意单位: %   |
| 环比增长 | float64 | 注意单位: %   |
| 累计   | float64 | 注意单位: 亿美元 |

接口示例

```python
import akshare as ak

macro_china_whxd_df = ak.macro_china_whxd()
print(macro_china_whxd_df)
```

数据示例

```
       月份      当月     同比增长     环比增长       累计
0    2022年10月份 -264.00  -303.08  -142.20  7895.00
1    2022年09月份 -109.00  -581.25    68.59  8159.00
2    2022年08月份 -347.00 -2378.57   -11.58  8268.00
3    2022年07月份 -311.00  -972.41   -79.77  8615.00
4    2022年06月份 -173.00  -269.61   -50.43  8926.00
..         ...     ...      ...      ...      ...
173  2008年05月份   30.04   -13.15    40.70  2739.34
174  2008年04月份   21.35    39.82   -79.40  2709.30
175  2008年03月份  103.62   279.28   -52.25  2687.95
176  2008年02月份  217.02   754.75    28.20  2584.33
177  2008年01月份  169.28  4508.33  1789.29  2367.31
```

##### 本外币存款

接口: macro_china_wbck

目标地址: http://data.eastmoney.com/cjsj/wbck.html

描述: 本外币存款, 数据区间从 200802 至今, 月度数据

限量: 单次返回所有历史数据

输入参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| -   | -   | -   |

输出参数

| 名称   | 类型      | 描述       |
|------|---------|----------|
| 月份   | object  | -        |
| 当月   | float64 | 注意单位: 亿元 |
| 同比增长 | float64 | 注意单位: %  |
| 环比增长 | float64 | 注意单位: %  |
| 累计   | float64 | 注意单位: 亿元 |

接口示例

```python
import akshare as ak

macro_china_wbck_df = ak.macro_china_wbck()
print(macro_china_wbck_df)
```

数据示例

```
      月份        当月        同比增长        环比增长          累计
0    2022年10月份  -2650.78 -132.917535 -110.068013  2610227.88
1    2022年09月份  26328.73   14.546846  134.206364  2612878.66
2    2022年08月份  11241.68  -19.737487  871.473473  2586549.93
3    2022年07月份  -1457.17   88.621966 -102.976736  2575308.25
4    2022年06月份  48951.94   22.914621   63.520287  2576765.42
..         ...       ...         ...         ...         ...
173  2008年05月份   8763.12  370.965835   22.035258   442540.86
174  2008年04月份   7180.81   69.117415  -33.125126   433777.74
175  2008年03月份  10737.68   25.845804  -19.689339   426596.93
176  2008年02月份  13370.18  152.665137  829.976560   415859.25
177  2008年01月份   1437.69  -72.142704  -59.893939   402489.07
```

##### 新债发行

接口: macro_china_bond_public

目标地址: https://www.chinamoney.com.cn/chinese/xzjfx/

描述: 中国外汇交易中心暨全国银行间同业拆借中心-债券信息披露-新债发行; 近期债券发行数据

限量: 单次返回所有历史数据

输入参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| -   | -   | -   |

输出参数

| 名称    | 类型      | 描述       |
|-------|---------|----------|
| 债券全称  | object  | -        |
| 债券类型  | object  | -        |
| 发行日期  | object  | -        |
| 计息方式  | object  | -        |
| 价格    | float64 | 注意单位: 元  |
| 债券期限  | object  | -        |
| 计划发行量 | float64 | 注意单位: 亿元 |
| 债券评级  | object  | -        |

接口示例

```python
import akshare as ak

macro_china_bond_public_df = ak.macro_china_bond_public()
print(macro_china_bond_public_df)
```

数据示例

```
                                   债券全称    债券类型   发行日期  ...  债券期限  计划发行量  债券评级
0          江苏金融租赁股份有限公司2024年绿色金融债券(第一期)   普通金融债  01-16  ...    3年   15.0   AAA
1            杭银消费金融股份有限公司2024年金融债券(第一期)   普通金融债  01-12  ...    3年   10.0   AAA
2                      2024年记账式附息(一期)国债      国债  01-12  ...    5年  990.0  None
3              湖南银行股份有限公司2024年第006期同业存单    同业存单  01-12  ...    6月   11.0  None
4          北京农村商业银行股份有限公司2024年第012期同业存单    同业存单  01-12  ...    6月   19.0  None
..                                  ...     ...    ...  ...   ...    ...   ...
376    江西省国有资本运营控股集团有限公司2024年度第一期超短期融资券  超短期融资券  01-10  ...  180日   30.0  None
377  中国建筑第八工程局有限公司2024年度第二期超短期融资券(科创票据)  超短期融资券  01-10  ...   44日   30.0  None
378         厦门国贸集团股份有限公司2024年度第二期超短期融资券  超短期融资券  01-10  ...  120日   20.0  None
379          广东省环保集团有限公司2024年度第一期超短期融资券  超短期融资券  01-10  ...  260日    5.0  None
380  台州市路桥公共资产投资管理集团有限公司2024年度第一期超短期融资券  超短期融资券  01-10  ...  252日    6.3  None
[381 rows x 8 columns]
```

##### 消费者信心指数

接口: macro_china_xfzxx

目标地址: https://data.eastmoney.com/cjsj/xfzxx.html

描述: 东方财富网-消费者信心指数

限量: 单次返回所有历史数据

输入参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| -   | -   | -   |

输出参数

| 名称           | 类型      | 描述      |
|--------------|---------|---------|
| 月份           | object  | -       |
| 消费者信心指数-指数值  | float64 | -       |
| 消费者信心指数-同比增长 | float64 | 注意单位: % |
| 消费者信心指数-环比增长 | float64 | 注意单位: % |
| 消费者满意指数-指数值  | float64 | -       |
| 消费者满意指数-同比增长 | float64 | 注意单位: % |
| 消费者满意指数-环比增长 | float64 | 注意单位: % |
| 消费者预期指数-指数值  | float64 | -       |
| 消费者预期指数-同比增长 | float64 | 注意单位: % |
| 消费者预期指数-环比增长 | float64 | 注意单位: % |

接口示例

```python
import akshare as ak

macro_china_xfzxx_df = ak.macro_china_xfzxx()
print(macro_china_xfzxx_df)
```

数据示例

```
      月份  消费者信心指数-指数值  ...  消费者预期指数-同比增长  消费者预期指数-环比增长
0    2022年09月份         87.2  ...    -28.995984      0.568828
1    2022年08月份         87.0  ...    -26.134454     -1.897321
2    2022年07月份         87.9  ...    -25.581395     -1.538462
3    2022年06月份         88.9  ...    -27.949327      3.762828
4    2022年05月份         86.8  ...    -30.063796      1.036866
..         ...          ...  ...           ...           ...
184  2007年05月份        112.8  ...      2.975654      0.351494
185  2007年04月份        112.3  ...      2.430243      1.426025
186  2007年03月份        111.0  ...      0.718133     -1.232394
187  2007年02月份        111.8  ...      2.805430     -0.525394
188  2007年01月份        112.4  ...      3.442029     -0.609225
```

##### 存款准备金率

接口: macro_china_reserve_requirement_ratio

目标地址: https://data.eastmoney.com/cjsj/ckzbj.html

描述: 国家统计局-存款准备金率

限量: 单次返回所有历史数据

输入参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| -   | -   | -   |

输出参数

| 名称            | 类型      | 描述      |
|---------------|---------|---------|
| 公布时间          | object  | XXXX年X月 |
| 生效时间          | object  | XXXX年X月 |
| 大型金融机构-调整前    | float64 | 注意单位: % |
| 大型金融机构-调整后    | float64 | 注意单位: % |
| 大型金融机构-调整幅度   | float64 | 注意单位: % |
| 中小金融机构-调整前    | float64 | 注意单位: % |
| 中小金融机构-调整后    | float64 | 注意单位: % |
| 中小金融机构-调整幅度   | float64 | 注意单位: % |
| 消息公布次日指数涨跌-上证 | float64 | 注意单位: % |
| 消息公布次日指数涨跌-深证 | float64 | 注意单位: % |
| 备注            | object  | -       |

接口示例

```python
import akshare as ak

macro_china_reserve_requirement_ratio_df = ak.macro_china_reserve_requirement_ratio()
print(macro_china_reserve_requirement_ratio_df)
```

数据示例

```
           公布时间  ...                                                 备注
0   2022年11月25日  ...  为保持流动性合理充裕,促进综合融资成本稳中有降,落实稳经济一揽子政策措施,巩固经济回稳向上基...
1   2022年04月15日  ...  为支持实体经济发展,促进综合融资成本稳中有降,中国人民银行决定于2022年4月25日下调金融...
2   2021年12月06日  ...  为支持实体经济发展,促进综合融资成本稳中有降,中国人民银行决定于2021年12月15日下调金...
3   2021年07月09日  ...  为支持实体经济发展,促进综合融资成本稳中有降,中国人民银行决定于2021年7月15日下调金融...
4   2020年04月03日  ...  为支持实体经济发展,促进加大对中小微企业的支持力度,降低社会融资实际成本,中国人民银行决定对...
5   2020年04月03日  ...  为支持实体经济发展,促进加大对中小微企业的支持力度,降低社会融资实际成本,中国人民银行决定对...
6   2020年01月01日  ...  为支持实体经济发展,降低社会融资实际成本,中国人民银行决定于2020年1月6日下调金融机构存...
7   2019年09月06日  ...  为支持实体经济发展,降低社会融资实际成本,中国人民银行决定于2019年9月16日全面下调金融...
8   2019年01月04日  ...  为进一步支持实体经济发展,优化流动性结构,降低融资成本,中国人民银行决定下调金融机构存款准备...
9   2019年01月04日  ...  为进一步支持实体经济发展,优化流动性结构,降低融资成本,中国人民银行决定下调金融机构存款准备...
10  2018年10月07日  ...  从2018年10月15日起,下调大型商业银行、股份制商业银行、城市商业银行、非县域农村商业银...
11  2018年06月24日  ...  从2018年7月5日起,下调大型商业银行、股份制商业银行、邮政储蓄银行、城市商业银行、非县域...
12  2018年04月17日  ...  从2018年4月25日起,下调大型商业银行、股份制商业银行、城市商业银行、非县域农村商业银行...
13  2016年02月29日  ...  自2016年3月1日起,普遍下调金融机构人民币存款准备金率0.5个百分点,以保持金融体系流动...
14  2015年10月23日  ...  2015-10-24,自同日起,下调金融机构人民币存款准备金率0.5个百分点,以保持银行体系...
15  2015年08月25日  ...  自2015年9月6日起,为进一步增强金融机构支持“三农”和小微企业的能力,额外降低县域农村商...
16  2015年04月19日  ...  自4月20日起对农信社、村镇银行等农村金融机构额外降低人民币存款准备金率1个百分点,并统一下...
17  2015年02月04日  ...  同时,为进一步增强金融机构支持结构调整的能力,加大对小微企业、“三农”以及重大水利工程建设的...
18  2012年05月12日  ...                                               None
19  2012年02月18日  ...                                               None
20  2011年11月30日  ...                                               None
21  2011年06月14日  ...                                               None
22  2011年05月12日  ...                                               None
23  2011年04月17日  ...                                               None
24  2011年03月18日  ...                                               None
25  2011年02月18日  ...                                               None
26  2011年01月14日  ...                                               None
27  2010年12月10日  ...                                               None
28  2010年11月19日  ...                                               None
29  2010年11月10日  ...                                               None
30  2010年05月02日  ...  从2010年5月10日起,上调存款类金融机构人民币存款准备金率0.5个百分点,农村信用社、村...
31  2010年02月12日  ...  从2010年2月25日起,上调存款类金融机构人民币存款准备金率0.5个百分点。为加大对“三农...
32  2010年01月12日  ...  从2010年1月18日起,上调存款类金融机构人民币存款准备金率0.5个百分点。为增强支农资金...
33  2008年12月22日  ...                                               None
34  2008年11月26日  ...                                               None
35  2008年10月08日  ...                                               None
36  2008年09月15日  ...  从2008年9月25日起,除工商银行、农业银行、中国银行、建设银行、交通银行、邮政储蓄银行暂...
37  2008年06月07日  ...                                               None
38  2008年06月07日  ...  中国人民银行决定上调存款类金融机构人民币存款准备金率1个百分点,于2008年6月15日和25...
39  2008年05月12日  ...                                               None
40  2008年04月16日  ...                                               None
41  2008年03月18日  ...                                               None
42  2008年01月16日  ...                                               None
43  2007年12月08日  ...                                               None
44  2007年11月10日  ...                                               None
45  2007年10月13日  ...                                               None
46  2007年09月06日  ...                                               None
47  2007年07月30日  ...                                               None
48  2007年05月18日  ...                                               None
49  2007年04月29日  ...                                               None
50  2007年04月05日  ...                                               None
51  2007年02月16日  ...                                               None
52  2007年01月05日  ...                                               None
```

##### 社会消费品零售总额

接口: macro_china_consumer_goods_retail

目标地址: http://data.eastmoney.com/cjsj/xfp.html

描述: 东方财富-经济数据-社会消费品零售总额

限量: 单次返回所有历史数据

输入参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| -   | -   | -   |

输出参数

| 名称      | 类型      | 描述       |
|---------|---------|----------|
| 月份      | object  | -        |
| 当月      | float64 | 注意单位: 亿元 |
| 同比增长    | float64 | 注意单位: %  |
| 环比增长    | float64 | 注意单位: %  |
| 累计      | float64 | 注意单位: 亿元 |
| 累计-同比增长 | float64 | 注意单位: %  |

接口示例

```python
import akshare as ak

macro_china_consumer_goods_retail_df = ak.macro_china_consumer_goods_retail()
print(macro_china_consumer_goods_retail_df)
```

数据示例

```
     月份       当月  同比增长       环比增长        累计  累计-同比增长
0    2022年10月份  40271.0  -0.5   6.692277  360575.0      0.6
1    2022年09月份  37745.0   2.5   4.101164  320305.0      0.7
2    2022年08月份  36258.0   5.4   1.081684  282560.0      0.5
3    2022年07月份  35870.0   2.7  -7.413143  246302.0     -0.2
4    2022年06月份  38742.0   3.1  15.485736  210432.0     -0.7
..         ...      ...   ...        ...       ...      ...
162  2008年05月份   8703.5  21.6   6.896340   42400.7     21.1
163  2008年04月份   8142.0  22.0   0.231436   33697.2     21.0
164  2008年03月份   8123.2  21.5  -2.770895   25555.2     20.6
165  2008年02月份   8354.7  19.1  -7.960517   17432.0     20.2
166  2008年01月份   9077.3  21.2   0.687720    9077.3     21.2
```

##### 全社会用电分类情况表

接口: macro_china_society_electricity

目标地址: http://finance.sina.com.cn/mac/#industry-6-0-31-1

描述: 国家统计局-全社会用电分类情况表

限量: 单次返回所有历史数据

输入参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| -   | -   | -   |

输出参数

| 名称            | 类型      | 描述         |
|---------------|---------|------------|
| 统计时间          | object  | -          |
| 全社会用电量        | float64 | 注意单位: 万千瓦时 |
| 全社会用电量同比      | float64 | 注意单位: %    |
| 各行业用电量合计      | float64 | 注意单位: 万千瓦时 |
| 各行业用电量合计同比    | float64 | 注意单位: %    |
| 第一产业用电量       | float64 | 注意单位: 万千瓦时 |
| 第一产业用电量同比     | float64 | 注意单位: %    |
| 第二产业用电量       | float64 | 注意单位: 万千瓦时 |
| 第二产业用电量同比     | float64 | 注意单位: %    |
| 第三产业用电量       | float64 | 注意单位: 万千瓦时 |
| 第三产业用电量同比     | float64 | 注意单位: %    |
| 城乡居民生活用电量合计   | float64 | 注意单位: 万千瓦时 |
| 城乡居民生活用电量合计同比 | float64 | 注意单位: %    |
| 城镇居民用电量       | float64 | 注意单位: 万千瓦时 |
| 城镇居民用电量同比     | float64 | 注意单位: %    |
| 乡村居民用电量       | float64 | 注意单位: 万千瓦时 |
| 乡村居民用电量同比     | float64 | 注意单位: %    |

接口示例

```python
import akshare as ak

macro_china_society_electricity_df = ak.macro_china_society_electricity()
print(macro_china_society_electricity_df)
```

数据示例

```
      统计时间   全社会用电量  全社会用电量同比  ...  城镇居民用电量同比    乡村居民用电量  乡村居民用电量同比
0    2003.12  188912117.0     15.29  ...      16.12  8806708.0       5.79
1    2004.10  175828690.0     15.17  ...       8.74  7652223.0       8.99
2    2004.11  194584023.0     15.13  ...       8.95  8408040.0      10.48
3     2004.3   48045510.0     15.70  ...       9.85  2187609.0      11.68
4     2004.9  157131146.0     14.92  ...       8.58  6814158.0       9.80
..       ...          ...       ...  ...        ...        ...        ...
209   2023.6  430760000.0      5.00  ...        NaN        NaN        NaN
210   2023.7  519650000.0      5.20  ...        NaN        NaN        NaN
211   2023.8  608260000.0      5.00  ...        NaN        NaN        NaN
212   2023.9  686370000.0      5.60  ...        NaN        NaN        NaN
213   2024.2  153160000.0     11.00  ...        NaN        NaN        NaN
[214 rows x 17 columns]
```

##### 全社会客货运输量

接口: macro_china_society_traffic_volume

目标地址: http://finance.sina.com.cn/mac/#industry-10-0-31-1

描述: 国家统计局-全社会客货运输量-非累计

限量: 单次返回所有历史数据

输入参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| -   | -   | -   |

输出参数

| 名称              | 类型      | 描述       |
|-----------------|---------|----------|
| 统计时间            | object  | 年月       |
| 统计对象            | object  | -        |
| 货运量             | float64 | 注意单位: 亿吨 |
| 货运量同比增长         | float64 | 注意单位: %  |
| 货物周转量           | float64 | 注意单位: 亿  |
| 公里货物周转量同比增长     | float64 | 注意单位: %  |
| 客运量             | float64 | 注意单位: 亿人 |
| 客运量同比增长         | float64 | 注意单位: %  |
| 旅客周转量           | float64 | 注意单位: 亿  |
| 公里旅客周转量同比增长     | float64 | 注意单位: %  |
| 沿海主要港口货物吞吐量     | float64 | 注意单位: 亿吨 |
| 沿海主要港口货物吞吐量同比增长 | float64 | 注意单位: %  |
| 其中:外贸货物吞吐量      | float64 | 注意单位: 亿吨 |
| 其中:外贸货物吞吐量同比增长  | float64 | 注意单位: %  |
| 民航总周转量          | float64 | 注意单位: 亿  |
| 公里民航总周转         | float64 | 注意单位: %  |

接口示例

```python
import akshare as ak

macro_china_society_traffic_volume_df = ak.macro_china_society_traffic_volume()
print(macro_china_society_traffic_volume_df)
```

数据示例

```
         统计时间    统计对象    货运量  ...  其中:外贸货物吞吐量同比增长  民航总周转量  公里民航总周转
0      2023.7    国际航线  23.60  ...             NaN    29.9     74.9
1      2023.7  港澳地区航线   1.40  ...             NaN     1.0    488.6
2      2023.7    国内航线  36.50  ...             NaN    83.8     64.6
3      2023.7      民航  60.11  ...             NaN   113.7     67.1
4      2023.7      水运   7.90  ...             NaN     NaN      NaN
       ...     ...    ...  ...             ...     ...      ...
2403  1952.12      水运    NaN  ...             NaN     NaN      NaN
2404  1952.12      公路    NaN  ...             NaN     NaN      NaN
2405  1952.12      铁路    NaN  ...             NaN     NaN      NaN
2406  1952.12      合计    NaN  ...             NaN     NaN      NaN
2407  1952.12      民航    NaN  ...             NaN     NaN      NaN
[2408 rows x 16 columns]
```

##### 邮电业务基本情况

接口: macro_china_postal_telecommunicational

目标地址: http://finance.sina.com.cn/mac/#industry-11-0-31-1

描述: 国家统计局-邮电业务基本情况-非累计

限量: 单次返回所有历史数据

输入参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| -   | -   | -   |

输出参数

| 名称            | 类型    | 描述       |
|---------------|-------|----------|
| 统计时间          | str   | 年月       |
| 邮电业务总量        | float | 注意单位: 亿元 |
| 邮电业务总量同比增长    | float | 注意单位: %  |
| 邮政业务总量        | float | 注意单位: 亿元 |
| 邮政业务总量同比增长    | float | 注意单位: %  |
| 电信业务总量        | float | 注意单位: 亿元 |
| 电信业务总量同比增长    | float | 注意单位: %  |
| 函件总数          | float | 注意单位: 万件 |
| 函件总数同比增长      | float | 注意单位: %  |
| 包件            | float | 注意单位: 万件 |
| 包件同比增长        | float | 注意单位: %  |
| 特快专递          | float | 注意单位: 万件 |
| 特快专递同比增长      | float | 注意单位: %  |
| 汇票            | float | 注意单位: 万张 |
| 汇票同比增长        | float | 注意单位: %  |
| 订销报纸累计数       | float | 注意单位: 万份 |
| 订销报纸累计数同比增长   | float | 注意单位: %  |
| 订销杂志累计数       | float | 注意单位: 万份 |
| 订销杂志累计数同比增长   | float | 注意单位: %  |
| 集邮业务          | float | 注意单位: 万枚 |
| 集邮业务同比增长      | float | 注意单位: %  |
| 邮政储蓄期末余额      | float | 注意单位: 亿元 |
| 邮政储蓄期末余额同比增长  | float | 注意单位: %  |
| 长途电话通话时长      | float | 注意单位: 万  |
| 钟长途电话通话时长同比增长 | float | 注意单位: %  |
| 本地电话期末用户数     | float | 注意单位: %  |
| 本地电话期末用户数同比增长 | float | 注意单位: %  |
| 城市电话用户数       | float | 注意单位: 万户 |
| 城市电话用户数同比增长   | float | 注意单位: %  |
| 乡村电话用户数       | float | 注意单位: 万户 |
| 乡村电话用户数同比增长   | float | 注意单位: %  |
| 无线寻呼用户数       | float | 注意单位: 万户 |
| 无线寻呼用户数同比增长   | float | 注意单位: %  |
| 移动电话用户数       | float | 注意单位: 万户 |
| 移动电话用户数同比增长   | float | 注意单位: %  |
| 固定电话用         | float | 注意单位: 万户 |
| 固定电话用户数同比增长   | float | 注意单位: %  |
| 城市住宅电话用户      | float | 注意单位: 万户 |
| 城市住宅电话用户同比增长  | float | 注意单位: %  |
| 乡村住宅电话用户      | float | 注意单位: 万户 |
| 乡村住宅电话用户同比增长  | float | 注意单位: %  |

接口示例

```python
import akshare as ak

macro_china_postal_telecommunicational_df = ak.macro_china_postal_telecommunicational()
print(macro_china_postal_telecommunicational_df)
```

数据示例

```
        统计时间  邮电业务总量  邮电业务总量同比增长  ...  城市住宅电话用户同比增长  乡村住宅电话用户  乡村住宅电话用户同比增长
0     2023.7     NaN         NaN  ...           NaN       NaN           NaN
1     2023.6     NaN         NaN  ...           NaN       NaN           NaN
2     2023.5     NaN         NaN  ...           NaN       NaN           NaN
3     2023.4     NaN         NaN  ...           NaN       NaN           NaN
4     2023.3     NaN         NaN  ...           NaN       NaN           NaN
..       ...     ...         ...  ...           ...       ...           ...
299  1970.12     NaN         NaN  ...           NaN       NaN           NaN
300  1965.12     NaN         NaN  ...           NaN       NaN           NaN
301  1962.12     NaN         NaN  ...           NaN       NaN           NaN
302  1957.12     NaN         NaN  ...           NaN       NaN           NaN
303  1952.12     NaN         NaN  ...           NaN       NaN           NaN
[304 rows x 41 columns]
```

##### 国际旅游外汇收入构成

接口: macro_china_international_tourism_fx

目标地址: http://finance.sina.com.cn/mac/#industry-15-0-31-3

描述: 国家统计局-国际旅游外汇收入构成

限量: 单次返回所有历史数据

输入参数

| 名称 | 类型 | 描述 |
|----|----|----|
| -  | -  | -  |

输出参数

| 名称   | 类型      | 描述         |
|------|---------|------------|
| 统计年度 | object  | 年          |
| 指标   | object  | -          |
| 数量   | float64 | 注意单位: 百万美元 |
| 比重   | float64 | 注意单位: %    |

接口示例

```python
import akshare as ak

macro_china_international_tourism_fx_df = ak.macro_china_international_tourism_fx()
print(macro_china_international_tourism_fx_df)
```

数据示例

```
     统计年度    指标        数量     比重
0    2019    餐饮   16041.0   12.2
1    2019    总计  131254.0  100.0
2    2019  市内交通    3453.0    2.6
3    2019  其他服务   10189.0    7.8
4    2019    汽车    1593.0    1.2
..    ...   ...       ...    ...
251  1996    餐饮    1376.0   13.5
252  1995    总计    8733.0  100.0
253  1994    总计    7323.0  100.0
254  1993    总计    4683.0  100.0
255  1992    总计    3947.0  100.0
[256 rows x 4 columns]
```

##### 民航客座率及载运率

接口: macro_china_passenger_load_factor

目标地址: http://finance.sina.com.cn/mac/#industry-20-0-31-1

描述: 国家统计局-民航客座率及载运率

限量: 单次返回所有历史数据

输入参数

| 名称 | 类型 | 描述 |
|----|----|----|
| -  | -  | -  |

输出参数

| 名称   | 类型      | 描述      |
|------|---------|---------|
| 统计年度 | object  | 年月      |
| 客座率  | float64 | 注意单位: % |
| 载运率  | float64 | 注意单位: % |

接口示例

```python
import akshare as ak

macro_china_passenger_load_factor_df = ak.macro_china_passenger_load_factor()
print(macro_china_passenger_load_factor_df)
```

数据示例

```
     统计时间    客座率    载运率
0    2023.7  81.20  68.50
1    2023.6  78.60  69.30
2    2023.5  74.30  65.90
3    2023.4  75.90  66.10
4    2023.3  74.70  66.20
..      ...    ...    ...
202  2006.6  72.00  64.60
203  2006.5  71.30  64.40
204  2006.4  76.30  69.00
205  2006.3  72.40  67.50
206  2006.2  72.70  64.60
[207 rows x 3 columns]
```

##### 航贸运价指数

接口: macro_china_freight_index

目标地址: http://finance.sina.com.cn/mac/#industry-22-0-31-2

描述: 新浪财经-中国宏观经济数据-航贸运价指数

限量: 单次返回所有历史数据

输入参数

| 名称 | 类型 | 描述 |
|----|----|----|
| -  | -  | -  |

输出参数

| 名称                | 类型      | 描述 |
|-------------------|---------|----|
| 截止日期              | object  | 年月 |
| 波罗的海好望角型船运价指数BCI  | float64 | -  |
| 灵便型船综合运价指数BHMI    | float64 | -  |
| 波罗的海超级大灵便型船BSI指数  | float64 | -  |
| 波罗的海综合运价指数BDI     | float64 | -  |
| HRCI国际集装箱租船指数     | float64 | -  |
| 油轮运价指数成品油运价指数BCTI | float64 | -  |
| 油轮运价指数原油运价指数BDTI  | float64 | -  |

接口示例

```python
import akshare as ak

macro_china_freight_index_df = ak.macro_china_freight_index()
print(macro_china_freight_index_df)
```

数据示例

```
      截止日期  波罗的海好望角型船运价指数BCI  ...  油轮运价指数成品油运价指数BCTI  油轮运价指数原油运价指数BDTI
0     2021-08-10            4328.0  ...                NaN               NaN
1     2021-08-09            4342.0  ...                NaN               NaN
2     2021-08-06            4359.0  ...                NaN               NaN
3     2021-08-05            4414.0  ...                NaN               NaN
4     2021-08-04            4302.0  ...                NaN               NaN
          ...               ...  ...                ...               ...
3845  2005-12-13            3459.0  ...             1326.0            2063.0
3846  2005-12-12            3476.0  ...             1286.0            2051.0
3847  2005-12-09            3509.0  ...             1261.0            2061.0
3848  2005-12-08            3553.0  ...                NaN               NaN
3849  2005-12-07            3709.0  ...                NaN               NaN
```

##### 央行货币当局资产负债

接口: macro_china_central_bank_balance

目标地址: http://finance.sina.com.cn/mac/#fininfo-8-0-31-2

描述: 新浪财经-中国宏观经济数据-央行货币当局资产负债

限量: 单次返回所有历史数据

输入参数

| 名称 | 类型 | 描述 |
|----|----|----|
| -  | -  | -  |

输出参数

| 名称         | 类型      | 描述       |
|------------|---------|----------|
| 统计时间       | object  | 年月       |
| 国外资产       | float64 | 注意单位: 亿元 |
| 外汇         | float64 | 注意单位: 亿元 |
| 货币黄金       | float64 | 注意单位: 亿元 |
| 其他国外资产     | float64 | 注意单位: 亿元 |
| 对政府债权      | float64 | 注意单位: 亿元 |
| 其中:中央政府    | float64 | 注意单位: 亿元 |
| 对其他存款性公司债权 | float64 | 注意单位: 亿元 |
| 对其他金融性公司债权 | float64 | 注意单位: 亿元 |
| 对非货币金融机构债权 | float64 | 注意单位: 亿元 |
| 对非金融性公司债权  | float64 | 注意单位: 亿元 |
| 其他资产       | float64 | 注意单位: 亿元 |
| 总资产        | float64 | 注意单位: 亿元 |
| 储备货币       | float64 | 注意单位: 亿元 |
| 发行货币       | float64 | 注意单位: 亿元 |
| 金融性公司存款    | float64 | 注意单位: 亿元 |
| 其他存款性公司    | float64 | 注意单位: 亿元 |
| 其他金融性公司    | float64 | 注意单位: 亿元 |
| 对金融机构负债    | float64 | 注意单位: 亿元 |
| 准备金存款      | float64 | 注意单位: 亿元 |
| 非金融性公司存款   | float64 | 注意单位: 亿元 |
| 活期存款       | float64 | 注意单位: 亿元 |
| 债券         | float64 | 注意单位: 亿元 |
| 国外负债       | float64 | 注意单位: 亿元 |
| 政府存款       | float64 | 注意单位: 亿元 |
| 自有资金       | float64 | 注意单位: 亿元 |
| 其他负债       | float64 | 注意单位: 亿元 |
| 总负债        | float64 | 注意单位: 亿元 |

接口示例

```python
import akshare as ak

macro_china_central_bank_balance_df = ak.macro_china_central_bank_balance()
print(macro_china_central_bank_balance_df)
```

数据示例

```
        统计时间  国外资产         外汇  ...    自有资金     其他负债        总负债
0     2023.7  231736.57  217824.72  ...  219.75  6064.21  408091.68
1     2023.6  231574.38  217733.71  ...  219.75  6544.37  418062.84
2     2023.5  231469.31  217822.22  ...  219.75  6674.58  406420.84
3     2023.4  231338.37  217895.06  ...  219.75  6784.49  412654.20
4     2023.3  230941.18  217893.63  ...  219.75  6690.57  421007.27
..       ...        ...        ...  ...     ...      ...        ...
314   1994.3    2437.60    2253.80  ...  280.50  -298.60        NaN
315  1993.12    1549.50    1431.80  ...  310.30  -255.00        NaN
316   1993.9    1451.00    1332.20  ...  310.30  -286.50        NaN
317   1993.6    1286.50    1183.00  ...  310.30  -411.60        NaN
318   1993.3    1324.00    1222.00  ...  310.30  -384.10        NaN
[319 rows x 28 columns]
```

##### 保险业经营情况

接口: macro_china_insurance

目标地址: http://finance.sina.com.cn/mac/#fininfo-19-0-31-3

描述: 新浪财经-中国宏观经济数据-保险业经营情况

限量: 单次返回所有历史数据

输入参数

| 名称 | 类型 | 描述 |
|----|----|----|
| -  | -  | -  |

输出参数

| 名称               | 类型      | 描述       |
|------------------|---------|----------|
| 统计时间             | object  | 年月       |
| 省市地区             | object  | 地区       |
| 原保险保费收入          | float64 | 注意单位: 万元 |
| 财产险保费收入          | float64 | 注意单位: 万元 |
| 人身险保费收入          | float64 | 注意单位: 万元 |
| 人身险-寿险保费收入       | float64 | 注意单位: 万元 |
| 人身险-健康险保费收入      | float64 | 注意单位: 万元 |
| 人身险-意外险保费收入      | float64 | 注意单位: 万元 |
| 养老保险公司企业年金缴费     | float64 | 注意单位: 万元 |
| 原保险赔付支出          | float64 | 注意单位: 万元 |
| 财产险保费赔付支出        | float64 | 注意单位: 万元 |
| 人身险保费赔付支出        | float64 | 注意单位: 万元 |
| 人身险-寿险赔付支出       | float64 | 注意单位: 万元 |
| 人身险-健康险赔付支出      | float64 | 注意单位: 万元 |
| 人身险-意外险赔付支出      | float64 | 注意单位: 万元 |
| 业务及管理费           | float64 | 注意单位: 万元 |
| 银行存款             | float64 | 注意单位: 万元 |
| 投资               | float64 | 注意单位: 万元 |
| 资产总额             | float64 | 注意单位: 万元 |
| 养老保险公司企业年金受托管理资产 | float64 | 注意单位: 万元 |
| 养老保险公司企业年金投资管理资产 | float64 | 注意单位: 万元 |

接口示例

```python
import akshare as ak

macro_china_insurance_df = ak.macro_china_insurance()
print(macro_china_insurance_df)
```

数据示例

```
        统计时间 省市地区     原保险保费收入  ...  资产总额  养老保险公司企业年金受托管理资产  养老保险公司企业年金投资管理资产
0     2023.7   新疆   5361100.0  ...   NaN               NaN               NaN
1     2023.7   宁夏   1718300.0  ...   NaN               NaN               NaN
2     2023.7   青海    806100.0  ...   NaN               NaN               NaN
3     2023.7   甘肃   3886700.0  ...   NaN               NaN               NaN
4     2023.7   陕西   8311200.0  ...   NaN               NaN               NaN
..       ...  ...         ...  ...   ...               ...               ...
305  2022.11   宁夏   2020200.0  ...   NaN               NaN               NaN
306  2022.11   青海    978500.0  ...   NaN               NaN               NaN
307  2022.11   甘肃   4591900.0  ...   NaN               NaN               NaN
308  2022.11   陕西  10225900.0  ...   NaN               NaN               NaN
309  2022.11   西藏    370100.0  ...   NaN               NaN               NaN
[310 rows x 21 columns]
```

##### 货币供应量

接口: macro_china_supply_of_money

目标地址: http://finance.sina.com.cn/mac/#fininfo-1-0-31-1

描述: 新浪财经-中国宏观经济数据-货币供应量

限量: 单次返回所有历史数据

输入参数

| 名称 | 类型 | 描述 |
|----|----|----|
| -  | -  | -  |

输出参数

| 名称                 | 类型      | 描述       |
|--------------------|---------|----------|
| 统计时间               | object  | 年月       |
| 货币和准货币（广义货币M2）     | float64 | 注意单位: 亿元 |
| 货币和准货币（广义货币M2）同比增长 | float64 | 注意单位: %  |
| 货币(狭义货币M1)         | float64 | 注意单位: 亿元 |
| 货币(狭义货币M1)同比增长     | float64 | 注意单位: %  |
| 流通中现金(M0)          | float64 | 注意单位: 亿元 |
| 流通中现金(M0)同比增长      | float64 | 注意单位: %  |
| 活期存款               | float64 | 注意单位: 亿元 |
| 活期存款同比增长           | float64 | 注意单位: %  |
| 准货币                | float64 | 注意单位: 亿元 |
| 准货币同比增长            | float64 | 注意单位: %  |
| 定期存款               | float64 | 注意单位: 亿元 |
| 定期存款同比增长           | float64 | 注意单位: %  |
| 储蓄存款出              | float64 | 注意单位: 亿元 |
| 储蓄存款同比增长           | float64 | 注意单位: %  |
| 其他存款               | float64 | 注意单位: 亿元 |
| 其他存款同比增长           | float64 | 注意单位: %  |

接口示例

```python
import akshare as ak

macro_china_supply_of_money_df = ak.macro_china_supply_of_money()
print(macro_china_supply_of_money_df)
```

数据示例

```
       统计时间 货币和准货币（广义货币M2） 货币和准货币（广义货币M2）同比增长  ... 储蓄存款同比增长       其他存款 其他存款同比增长
0    2020.8     2136800.00              10.40  ...     None  235344.24     None
1    2020.7     2125458.46              10.70  ...     None  240538.49     None
2    2020.6     2134948.66              11.10  ...     None  228402.91     None
3    2020.5     2100183.74              11.10  ...     None  233222.73     None
4    2020.4     2093533.83              11.10  ...     None  241313.38     None
..      ...            ...                ...  ...      ...        ...      ...
507  1978.5           None               None  ...     None       None     None
508  1978.4           None               None  ...     None       None     None
509  1978.3           None               None  ...     None       None     None
510  1978.2           None               None  ...     None       None     None
511  1978.1           None               None  ...     None       None     None
```

##### FR007利率互换曲线历史数据

接口: macro_china_swap_rate

目标地址: https://www.chinamoney.com.cn/chinese/bkcurvfxhis/?cfgItemType=72&curveType=FR007

描述: 国家统计局-FR007利率互换曲线历史数据

限量: 单次返回所有历史数据, 该接口只能获取近一年的数据的数据，其中每次只能获取一个月的数据

输入参数

| 名称         | 类型  | 描述                           |
|------------|-----|------------------------------|
| start_date | str | start_date="20231128"；注意时间间隔 |
| end_date   | str | end_date="20231130"          |

输出参数

| 名称   | 类型      | 描述  |
|------|---------|-----|
| 日期   | object  | -   |
| 曲线名称 | object  | -   |
| 时刻   | object  | -   |
| 价格类型 | object  | -   |
| 1M   | float64 | -   |
| 3M   | float64 | -   |
| 6M   | float64 | -   |
| 9M   | float64 | -   |
| 1Y   | float64 | -   |
| 2Y   | float64 | -   |
| 3Y   | float64 | -   |
| 4Y   | float64 | -   |
| 5Y   | float64 | -   |
| 7Y   | float64 | -   |
| 10Y  | float64 | -   |

接口示例

```python
import akshare as ak

macro_china_swap_rate_df = ak.macro_china_swap_rate(start_date="20240501", end_date="20240531")
print(macro_china_swap_rate_df)
```

数据示例

```
             日期           曲线名称    时刻 价格类型  ...      4Y      5Y      7Y     10Y
0    2024-05-06  FR007利率互换行情曲线   9:30   报卖  ...  2.1375  2.1652  2.5000  2.5000
1    2024-05-06  FR007利率互换行情曲线  13:30   报买  ...  2.0750  2.1350  2.1650  2.2650
2    2024-05-06  FR007利率互换行情曲线  14:00   报卖  ...  2.0944  2.1543  2.2512  2.3263
3    2024-05-06  FR007利率互换行情曲线  14:00   均值  ...  2.0864  2.1530  2.2369  2.3106
4    2024-05-06  FR007利率互换行情曲线  14:00   报买  ...  2.0784  2.1518  2.2225  2.2950
..          ...            ...    ...  ...  ...     ...     ...     ...     ...
940  2024-05-31  FR007利率互换行情曲线  12:30   报卖  ...  2.0000  2.0650  2.2450  2.3350
941  2024-05-31  FR007利率互换行情曲线  12:30   均值  ...  1.9800  2.0450  2.1450  2.2350
942  2024-05-31  FR007利率互换行情曲线  12:30   报买  ...  1.9600  2.0250  2.0450  2.1350
943  2024-05-31  FR007利率互换行情曲线  11:00   均值  ...  1.9775  2.0476  2.1721  2.2687
944  2024-05-31  FR007利率互换收盘曲线  16:30   报买  ...  1.9853  2.0569  2.1525  2.2425
[945 rows x 15 columns]
```

##### 央行黄金和外汇储备

接口: macro_china_foreign_exchange_gold

目标地址: http://finance.sina.com.cn/mac/#fininfo-5-0-31-2

描述: 国家统计局-央行黄金和外汇储备, 比东财接口数据时间长

限量: 单次返回所有历史数据

输入参数

| 名称 | 类型 | 描述 |
|----|----|----|
| -  | -  | -  |

输出参数

| 名称     | 类型      | 描述        |
|--------|---------|-----------|
| 统计时间   | object  | 年月        |
| 黄金储备   | float64 | 注意单位: 万盎司 |
| 国家外汇储备 | float64 | 注意单位: 亿美元 |

接口示例

```python
import akshare as ak

macro_china_foreign_exchange_gold_df = ak.macro_china_foreign_exchange_gold()
print(macro_china_foreign_exchange_gold_df)
```

数据示例

```
        统计时间     黄金储备    国家外汇储备
0    1978.12  1280.00      1.67
1    1979.12  1280.00      8.40
2    1980.12  1280.00    -12.96
3    1981.12  1267.00     27.08
4    1982.12  1267.00     69.86
..       ...      ...       ...
384   2023.7  6869.00  32042.70
385   2023.8  6962.00  31600.98
386   2023.9  7046.00  31150.70
387   2024.1  7219.00  32193.20
388   2024.2  7258.00  32258.17
[389 rows x 3 columns]
```

##### 商品零售价格指数

接口: macro_china_retail_price_index

目标地址: http://finance.sina.com.cn/mac/#price-12-0-31-1

描述: 国家统计局-商品零售价格指数

限量: 单次返回所有历史数据

输入参数

| 名称 | 类型 | 描述 |
|----|----|----|
| -  | -  | -  |

输出参数

| 名称       | 类型      | 描述 |
|----------|---------|----|
| 统计月份     | object  | 年月 |
| 居民消费项目   | object  | -  |
| 零售商品价格指数 | float64 | -  |

接口示例

```python
import akshare as ak

macro_china_retail_price_index_df = ak.macro_china_retail_price_index()
print(macro_china_retail_price_index_df)
```

数据示例

```
     统计月份       居民消费项目 零售商品价格指数
0     2020.8    建筑材料及五金电料   100.10
1     2020.8           燃料    90.70
2     2020.8   书报杂志及电子出版物   101.70
3     2020.8  中西药品及医疗保健用品   100.30
4     2020.8         金银珠宝   122.90
      ...          ...      ...
3777  2002.1         机电产品    94.10
3778  2002.1    家用电器及音像器材    94.20
3779  2002.1       体育娱乐用品    98.80
3780  2002.1  中西药品及医疗保健用品    97.30
3781  2002.1   书报杂志及电子出版物   101.00
```

##### 国房景气指数

接口: macro_china_real_estate

目标地址: http://data.eastmoney.com/cjsj/hyzs_list_EMM00121987.html

描述: 国家统计局-国房景气指数

限量: 单次返回所有历史数据

输入参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| -   | -   | -   |

输出参数

| 名称     | 类型      | 描述  |
|--------|---------|-----|
| 日期     | object  | -   |
| 最新值    | float64 | -   |
| 涨跌幅    | float64 | -   |
| 近3月涨跌幅 | float64 | -   |
| 近6月涨跌幅 | float64 | -   |
| 近1年涨跌幅 | float64 | -   |
| 近2年涨跌幅 | float64 | -   |
| 近3年涨跌幅 | float64 | -   |

接口示例

```python
import akshare as ak

macro_china_real_estate_df = ak.macro_china_real_estate()
print(macro_china_real_estate_df)
```

数据示例

```
     日期     最新值       涨跌幅  ...    近1年涨跌幅    近2年涨跌幅    近3年涨跌幅
0    1998-01-01   98.60       NaN  ...       NaN       NaN       NaN
1    1998-02-01   98.99  0.395538  ...       NaN       NaN       NaN
2    1998-03-01   99.05  0.060612  ...       NaN       NaN       NaN
3    1998-04-01  100.81  1.776880  ...       NaN       NaN       NaN
4    1998-05-01  101.68  0.863010  ...       NaN       NaN       NaN
..          ...     ...       ...  ...       ...       ...       ...
280  2022-03-01   96.66 -0.278552  ... -4.618117 -1.598290 -4.306504
281  2022-04-01   95.89 -0.796607  ... -5.349916 -3.023867 -5.190825
282  2022-05-01   95.60 -0.302430  ... -5.533597 -3.774534 -5.402731
283  2022-06-01   95.40 -0.209205  ... -5.619311 -4.456685 -5.628648
284  2022-07-01   95.26 -0.146751  ... -5.683168 -4.825657 -5.757816
```

##### 外汇和黄金储备

接口: macro_china_fx_gold

目标地址: http://data.eastmoney.com/cjsj/hjwh.html

描述: 中国外汇和黄金储备, 数据区间从 200801 至今, 月度数据

限量: 单次返回所有历史数据

输入参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| -   | -   | -   |

输出参数

| 名称        | 类型      | 描述        |
|-----------|---------|-----------|
| 月份        | object  | 年度和月份     |
| 黄金储备-数值   | float64 | 注意单位: 万盎司 |
| 黄金储备-同比   | float64 | 注意单位: 万盎司 |
| 黄金储备-环比   | float64 | 注意单位: 万盎司 |
| 国家外汇储备-数值 | float64 | 注意单位: 亿美元 |
| 国家外汇储备-同比 | float64 | 注意单位: 亿美元 |
| 国家外汇储备-环比 | float64 | 注意单位: 亿美元 |

接口示例

```python
import akshare as ak

macro_china_fx_gold_df = ak.macro_china_fx_gold()
print(macro_china_fx_gold_df)
```

数据示例

```
       月份  黄金储备-数值    黄金储备-同比   黄金储备-环比   国家外汇储备-数值  国家外汇储备-同比  国家外汇储备-环比
0    2008年01月份      NaN        NaN       NaN  15898.1040  43.914387   4.028224
1    2008年02月份      NaN        NaN       NaN  16471.3371  42.316677   3.605670
2    2008年03月份      NaN        NaN       NaN  16821.7700  39.944557   2.127532
3    2008年04月份      NaN        NaN       NaN  17566.5514  40.919594   4.427485
4    2008年05月份      NaN        NaN       NaN  17969.6074  39.011455   2.294451
..         ...      ...        ...       ...         ...        ...        ...
170  2022年03月份  1216.63  14.849008  1.690906  31879.9400   0.566714  -0.803808
171  2022年04月份  1197.31   8.126829 -1.587993  31197.2000  -2.453270  -2.141598
172  2022年05月份  1151.83  -3.223828 -3.798515  31277.8000  -2.918335   0.258357
173  2022年06月份  1138.23   3.053871 -1.180730  30712.7200  -4.441119  -1.806649
174  2022年07月份  1098.39  -3.963383 -3.500171  31040.7100  -4.073655   1.067929
```

##### 中国货币供应量

接口: macro_china_money_supply

目标地址: http://data.eastmoney.com/cjsj/hbgyl.html

描述: 东方财富-经济数据-中国宏观-中国货币供应量; 数据区间从 200801 至今, 月度数据

限量: 单次返回所有历史数据

输入参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| -   | -   | -   |

输出参数

| 名称                | 类型      | 描述    |
|-------------------|---------|-------|
| 月份                | object  | 年度和月份 |
| 货币和准货币(M2)-数量(亿元) | float64 | -     |
| 货币和准货币(M2)-同比增长   | float64 | -     |
| 货币和准货币(M2)-环比增长   | float64 | -     |
| 货币(M1)-数量(亿元)     | float64 | -     |
| 货币(M1)-同比增长       | float64 | -     |
| 货币(M1)-环比增长       | float64 | -     |
| 流通中的现金(M0)-数量(亿元) | float64 | -     |
| 流通中的现金(M0)-同比增长   | float64 | -     |
| 流通中的现金(M0)-环比增长   | float64 | -     |

接口示例

```python
import akshare as ak

macro_china_money_supply_df = ak.macro_china_money_supply()
print(macro_china_money_supply_df)
```

数据示例

```
            月份  货币和准货币(M2)-数量(亿元)  ...  流通中的现金(M0)-同比增长  流通中的现金(M0)-环比增长
0    2022年10月份         2612914.57  ...            14.30        -0.258787
1    2022年09月份         2626600.92  ...            13.60         1.482068
2    2022年08月份         2595068.27  ...            14.30         0.747950
3    2022年07月份         2578078.57  ...            13.90         0.518710
4    2022年06月份         2581451.20  ...            13.80         0.485950
..         ...                ...  ...              ...              ...
173  2008年05月份          436221.60  ...            12.88        -2.014673
174  2008年04月份          429313.72  ...            10.70         1.171555
175  2008年03月份          423054.53  ...            11.12        -6.228418
176  2008年02月份          421037.84  ...             5.96       -11.503457
177  2008年01月份          417846.17  ...            31.21        20.896562
```

##### 全国股票交易统计表

接口: macro_china_stock_market_cap

目标地址: http://data.eastmoney.com/cjsj/gpjytj.html

描述: 全国股票交易统计表, 数据区间从 200801 至今, 月度数据

限量: 单次返回所有历史数据

输入参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| -   | -   | -   |

输出参数

| 名称            | 类型      | 描述       |
|---------------|---------|----------|
| 数据日期          | object  | 年度和月份    |
| 发行总股本-上海      | float64 | 注意单位: 亿元 |
| 发行总股本-深圳      | float64 | 注意单位: 亿元 |
| 市价总值-上海       | float64 | 注意单位: 亿元 |
| 市价总值-深圳       | float64 | 注意单位: 亿元 |
| 成交金额-上海       | float64 | 注意单位: 亿元 |
| 成交金额-深圳       | float64 | 注意单位: 亿元 |
| 成交量-上海        | float64 | -        |
| 成交量-深圳        | float64 | -        |
| A股最高综合股价指数-上海 | float64 | -        |
| A股最高综合股价指数-深圳 | float64 | -        |
| A股最低综合股价指数-上海 | float64 | -        |
| A股最低综合股价指数-深圳 | float64 | -        |

接口示例

```python
import akshare as ak

macro_china_stock_market_cap_df = ak.macro_china_stock_market_cap()
print(macro_china_stock_market_cap_df)
```

数据示例

```
          数据日期  发行总股本-上海  ...  A股最低综合股价指数-上海  A股最低综合股价指数-深圳
0    2022年11月份       NaN  ...      2896.7572      2032.0920
1    2022年10月份  47240.00  ...      2885.0894      1974.9663
2    2022年09月份  47205.00  ...      3021.9309      2032.3677
3    2022年08月份  47038.00  ...      3155.1866      2231.3209
4    2022年07月份  46908.00  ...      3226.2315      2284.0597
..         ...       ...  ...            ...            ...
174  2008年05月份  14980.19  ...      3333.9510      1086.0100
175  2008年04月份  14785.95  ...      2990.7880       963.8400
176  2008年03月份  14476.47  ...      3357.2290      1186.6000
177  2008年02月份  14300.84  ...      4123.3050      1385.0400
178  2008年01月份  14198.16  ...      4330.6970      1424.3500
```

##### 上海银行业同业拆借报告

接口: macro_china_shibor_all

目标地址: https://datacenter.jin10.com/reportType/dc_shibor

描述: 上海银行业同业拆借报告, 数据区间从 20170317-至今

限量: 单次返回所有历史数据

输入参数

| 名称 | 类型 | 描述 |
|----|----|----|
| -  | -  | -  |

输出参数

| 名称      | 类型      | 默 描述  |
|---------|---------|-------|
| 日期      | object  | -     |
| O/N-定价  | float64 | -     |
| O/N-涨跌幅 | float64 | 单位: 点 |
| 1W-定价   | float64 | -     |
| 1W-涨跌幅  | float64 | 单位: 点 |
| 2W-定价   | float64 | -     |
| 2W-涨跌幅  | float64 | 单位: 点 |
| 1M-定价   | float64 | -     |
| 1M-涨跌幅  | float64 | 单位: 点 |
| 3M-定价   | float64 | -     |
| 3M-涨跌幅  | float64 | 单位: 点 |
| 6M-定价   | float64 | -     |
| 6M-涨跌幅  | float64 | 单位: 点 |
| 9M-定价   | float64 | -     |
| 9M-涨跌幅  | float64 | 单位: 点 |
| 1Y-定价   | float64 | -     |
| 1Y-涨跌幅  | float   | 单位: 点 |
| ON-定价   | float64 | -     |
| ON-涨跌幅  | float64 | 单位: 点 |
| 2M-定价   | float64 | -     |
| 2M-涨跌幅  | float64 | 单位: 点 |

接口示例

```python
import akshare as ak

macro_china_shibor_all_df = ak.macro_china_shibor_all()
print(macro_china_shibor_all_df)
```

数据示例

```
              日期  O/N-定价  O/N-涨跌幅   1W-定价  ...   9M-定价  9M-涨跌幅   1Y-定价  1Y-涨跌幅
0     2015-05-08  1.4430      NaN  2.3340  ...  4.3960     NaN  4.5000     NaN
1     2017-03-17  2.6330   119.00  2.7250  ...  4.1340  -26.20  4.1246  -37.54
2     2017-03-20  2.6325    -0.05  2.7471  ...  4.1504    1.64  4.1435    1.89
3     2017-03-21  2.6477     1.52  2.7680  ...  4.1621    1.17  4.1559    1.24
4     2017-03-22  2.6507     0.30  2.7910  ...  4.1713    0.92  4.1649    0.90
          ...     ...      ...     ...  ...     ...     ...     ...     ...
1753  2024-03-28  1.7340    -3.60  1.9480  ...  2.2380    0.00  2.2670    0.10
1754  2024-03-29  1.7180    -1.60  1.9670  ...  2.2380    0.00  2.2680    0.10
1755  2024-04-01  1.7280     1.00  1.8570  ...  2.2350   -0.30  2.2650   -0.30
1756  2024-04-02  1.7040    -2.40  1.8330  ...  2.2330   -0.20  2.2620   -0.30
1757  2024-04-03  1.6850    -1.90  1.8210  ...  2.2270   -0.60  2.2560   -0.60
[1758 rows x 17 columns]
```

##### 人民币香港银行同业拆息

接口: macro_china_hk_market_info

目标地址: https://datacenter.jin10.com/reportType/dc_hk_market_info

描述: 香港同业拆借报告, 数据区间从 20170320-至今

限量: 单次返回所有历史数据

输入参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| -   | -   | -   |

输出参数

| 名称      | 类型      | 描述    |
|---------|---------|-------|
| 日期      | object  | 日期    |
| O/N-定价  | float64 | -     |
| O/N-涨跌幅 | float64 | 单位: 点 |
| 1W-定价   | float64 | -     |
| 1W-涨跌幅  | float64 | 单位: 点 |
| 2W-定价   | float64 | -     |
| 2W-涨跌幅  | float64 | 单位: 点 |
| 1M-定价   | float64 | -     |
| 1M-涨跌幅  | float64 | 单位: 点 |
| 3M-定价   | float64 | -     |
| 3M-涨跌幅  | float64 | 单位: 点 |
| 6M-定价   | float64 | -     |
| 6M-涨跌幅  | float64 | 单位: 点 |
| 9M-定价   | float64 | -     |
| 9M-涨跌幅  | float64 | 单位: 点 |
| 1Y-定价   | float64 | -     |
| 1Y-涨跌幅  | float64 | 单位: 点 |
| ON-定价   | float64 | -     |
| ON-涨跌幅  | float64 | 单位: 点 |
| 2M-定价   | float64 | -     |
| 2M-涨跌幅  | float64 | 单位: 点 |

接口示例

```python
import akshare as ak

macro_china_hk_market_info_df = ak.macro_china_hk_market_info()
print(macro_china_hk_market_info_df)
```

数据示例

```
         日期   1W-定价  1W-涨跌幅   2W-定价  ...   ON-定价  ON-涨跌幅   2M-定价  2M-涨跌幅
0     2017-03-20  3.9428     NaN  4.2417  ...  2.0283     NaN  4.5915     NaN
1     2017-03-21  3.9094   -3.34  4.1647  ...  2.3883   36.00  4.6113    1.98
2     2017-03-22  4.3795   47.01  4.5853  ...  2.3100   -7.83  4.8365   22.52
3     2017-03-23  4.3538   -2.57  4.3930  ...  2.2263   -8.37  4.5410  -29.55
4     2017-03-24  4.3542    0.03  4.4410  ...  2.2438    1.75  4.6405    9.95
          ...     ...     ...     ...  ...     ...     ...     ...     ...
1729  2024-03-27  3.9303   26.59  3.9194  ...  5.9838  170.12  3.7000    7.86
1730  2024-03-28  4.6167   68.64  4.4297  ...  5.4012  -58.26  3.7000    0.00
1731  2024-04-02  4.7386   12.20  4.5751  ...  5.1127  -28.85  3.9600   26.00
1732  2024-04-03  4.7218   -1.68  4.5127  ...  5.0232   -8.96  4.0177    5.77
1733  2024-04-05  4.4933  -22.85  4.3118  ...  3.1500 -187.32  3.9300   -8.77
[1734 rows x 17 columns]
```

#### 其他指标

##### 中国日度沿海六大电库存

接口: macro_china_daily_energy

目标地址: https://datacenter.jin10.com/reportType/dc_qihuo_energy_report

描述: 中国日度沿海六大电库存数据, 数据区间从20160101-至今, 不再更新, 只能获得历史数据

限量: 单次返回所有历史数据

输入参数

| 名称 | 类型 | 描述 |
|----|----|----|
| -  | -  | -  |

输出参数

| 名称      | 类型      | 描述 |
|---------|---------|----|
| 日期      | object  | -  |
| 沿海六大电库存 | float64 | -  |
| 日耗      | float64 | -  |
| 存煤可用天数  | float64 | -  |

接口示例

```python
import akshare as ak

macro_china_daily_energy_df = ak.macro_china_daily_energy()
print(macro_china_daily_energy_df)
```

数据示例

```
              日期  沿海六大电库存     日耗  存煤可用天数
0     2016-01-01  1167.60  64.20   18.19
1     2016-01-02  1162.90  63.40   18.34
2     2016-01-03  1160.80  62.60   18.54
3     2016-01-04  1185.30  57.60   20.58
4     2016-01-05  1150.20  57.20   20.11
          ...      ...    ...     ...
1202  2019-05-17  1639.47  61.71   26.56
1203  2019-05-21  1591.92  62.67   25.40
1204  2019-05-22  1578.63  59.54   26.51
1205  2019-05-24  1671.83  60.65   27.56
1206  2019-06-21  1786.64  66.57   26.84
[1207 rows x 4 columns]
```

##### 人民币汇率中间价报告

接口: macro_china_rmb

目标地址: https://datacenter.jin10.com/reportType/dc_rmb_data

描述: 中国人民币汇率中间价报告, 数据区间从 20170103-20210513

限量: 单次返回所有历史数据

输入参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| -   | -   | -   |

输出参数

| 名称              | 类型      | 描述     |
|-----------------|---------|--------|
| 日期              | object  | 日期     |
| 美元/人民币_中间价      | float64 | -      |
| 美元/人民币_涨跌幅      | float64 | 单位: 点  |
| 欧元/人民币_中间价      | float64 | -      |
| 欧元/人民币_涨跌幅      | float64 | 单位: 点  |
| 100日元/人民币_中间价   | float64 | -      |
| 100日元/人民币_涨跌幅   | float64 | 单位: 点对 |
| 港元/人民币_中间价      | float64 | -      |
| 港元/人民币_涨跌幅      | float64 | 单位: 点  |
| 英镑/人民币_中间价      | float64 | -      |
| 英镑/人民币_涨跌幅      | float64 | 单位: 点  |
| 澳元/人民币_中间价      | float64 | -      |
| 澳元/人民币_涨跌幅      | float64 | 单位: 点  |
| 新西兰元/人民币_中间价    | float64 | -      |
| 新西兰元/人民币_涨跌幅    | float64 | 单位: 点  |
| 新加坡元/人民币_中间价    | float64 | -      |
| 新加坡元/人民币_涨跌幅    | float64 | 单位: 点  |
| 瑞郎/人民币_中间价      | float64 | -      |
| 瑞郎/人民币_涨跌幅      | float64 | 单位: 点  |
| 加元/人民币_中间价      | float64 | -      |
| 加元/人民币_涨跌幅      | float64 | 单位: 点  |
| 人民币/马来西亚林吉特_中间价 | float64 | -      |
| 人民币/马来西亚林吉特_涨跌幅 | float64 | 单位: 点  |
| 人民币/俄罗斯卢布_中间价   | float64 | -      |
| 人民币/俄罗斯卢布_涨跌幅   | float64 | 单位: 点  |
| 人民币/南非兰特_中间价    | float64 | -      |
| 人民币/南非兰特_涨跌幅    | float64 | 单位: 点  |
| 人民币/韩元_中间价      | float64 | -      |
| 人民币/韩元_涨跌幅      | float64 | 单位: 点  |
| 人民币/阿联酋迪拉姆_中间价  | float64 | -      |
| 人民币/阿联酋迪拉姆_涨跌幅  | float64 | 单位: 点  |
| 人民币/沙特里亚尔_中间价   | float64 | -      |
| 人民币/沙特里亚尔_涨跌幅   | float64 | 单位: 点  |
| 人民币/匈牙利福林_中间价   | float64 | -      |
| 人民币/匈牙利福林_涨跌幅   | float64 | 单位: 点  |
| 人民币/波兰兹罗提_中间价   | float64 | -      |
| 人民币/波兰兹罗提_涨跌幅   | float64 | 单位: 点  |
| 人民币/丹麦克朗_中间价    | float64 | -      |
| 人民币/丹麦克朗_涨跌幅    | float64 | 单位: 点  |
| 人民币/瑞典克朗_中间价    | float64 | -      |
| 人民币/瑞典克朗_涨跌幅    | float64 | 单位: 点  |
| 人民币/丹麦克朗_中间价    | float64 | -      |
| 人民币/丹麦克朗_涨跌幅    | float64 | 单位: 点  |
| 人民币/挪威克朗_中间价    | float64 | -      |
| 人民币/挪威克朗_涨跌幅    | float64 | 单位: 点  |
| 人民币/土耳其里拉_中间价   | float64 | -      |
| 人民币/土耳其里拉_涨跌幅   | float64 | 单位: 点  |
| 人民币/墨西哥比索_中间价   | float64 | -      |
| 人民币/墨西哥比索_涨跌幅   | float64 | 单位: 点  |
| 人民币/泰铢_中间价      | float64 | -      |
| 人民币/泰铢_涨跌幅      | float64 | 单位: 点  |

接口示例

```python
import akshare as ak

macro_china_rmb_df = ak.macro_china_rmb()
print(macro_china_rmb_df)
```

数据示例

```
     日期  美元/人民币_中间价  美元/人民币_涨跌幅  ...  人民币/墨西哥比索_涨跌幅  人民币/泰铢_定价  人民币/泰铢_涨跌幅
0    2018-02-06      6.3072         0.0  ...            0.0     5.0191         0.0
1    2018-02-07      6.2882      -190.0  ...         -184.0     5.0178       -13.0
2    2018-02-08      6.2822       -60.0  ...          286.0     5.0429       251.0
3    2018-02-09      6.3194       372.0  ...          -64.0     5.0406       -23.0
4    2018-02-12      6.3001      -193.0  ...         -146.0     5.0310       -96.0
..          ...         ...         ...  ...            ...        ...         ...
786  2021-05-07      6.4678      -217.0  ...         -118.0     4.8283       310.0
787  2021-05-10      6.4425      -253.0  ...         -171.0     4.8333        50.0
788  2021-05-11      6.4254      -171.0  ...           85.0     4.8434       101.0
789  2021-05-12      6.4258         4.0  ...           65.0     4.8503        69.0
790  2021-05-13      6.4612       354.0  ...          107.0     4.8427       -76.0
```

##### 深圳融资融券报告

接口: macro_china_market_margin_sz

目标地址: https://datacenter.jin10.com/reportType/dc_market_margin_sz

描述: 深圳融资融券报告, 数据区间从 20100331-至今

限量: 单次返回所有历史数据

输入参数

| 名称 | 类型 | 描述 |
|----|----|----|
| -  | -  | -  |

输出参数

| 名称     | 类型      | 描述      |
|--------|---------|---------|
| 日期     | object  | -       |
| 融资买入额  | float64 | 注意单位: 元 |
| 融资余额   | float64 | 注意单位: 元 |
| 融券卖出量  | float64 | 注意单位: 股 |
| 融券余量   | float64 | 注意单位: 股 |
| 融券余额   | float64 | 注意单位: 元 |
| 融资融券余额 | float64 | 注意单位: 元 |

接口示例

```python
import akshare as ak

macro_china_market_margin_sz_df = ak.macro_china_market_margin_sz()
print(macro_china_market_margin_sz_df)
```

数据示例

```
         日期       融资买入额  ...          融券余额        融资融券余额
0     2010-03-31  6.845690e+05  ...  7.089500e+04  7.416910e+05
1     2010-04-08  6.713260e+06  ...  5.602300e+04  1.452378e+07
2     2010-04-09  9.357095e+06  ...  1.083620e+05  1.984136e+07
3     2010-04-12  1.040656e+07  ...  8.100000e+03  2.482113e+07
4     2010-04-15  1.660717e+07  ...  9.767600e+04  4.807796e+07
          ...           ...  ...           ...           ...
3374  2024-03-27  3.589356e+10  ...  1.500044e+10  7.222717e+11
3375  2024-03-28  4.240178e+10  ...  1.527372e+10  7.247246e+11
3376  2024-03-29  3.843272e+10  ...  1.540008e+10  7.220648e+11
3377  2024-04-01  5.034045e+10  ...  1.579884e+10  7.264701e+11
3378  2024-04-02  4.460770e+10  ...  1.577416e+10  7.271902e+11
[3379 rows x 7 columns]
```

##### 上海融资融券报告

接口: macro_china_market_margin_sh

目标地址: https://datacenter.jin10.com/reportType/dc_market_margin_sse

描述: 上海融资融券报告, 数据区间从 20100331-至今

限量: 单次返回所有历史数据

输入参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| -   | -   | -   |

输出参数

| 名称     | 类型     | 描述     |
|--------|--------|--------|
| 日期     | object | 日期     |
| 融资余额   | int64  | 注意单位：元 |
| 融资买入额  | int64  | 注意单位：元 |
| 融券余量   | int64  | 注意单位：股 |
| 融券余额   | int64  | 注意单位：元 |
| 融券卖出量  | int64  | 注意单位：股 |
| 融资融券余额 | int64  | 注意单位：元 |

接口示例

```python
import akshare as ak

macro_china_market_margin_sh_df = ak.macro_china_market_margin_sh()
print(macro_china_market_margin_sh_df)
```

数据示例

```
       日期        融资买入额  ...         融券余额        融资融券余额
0     2010-03-31      5866316  ...        24142       5848955
1     2010-04-01      1054024  ...        17325       6859439
2     2010-04-02       207516  ...        11929       6774710
3     2010-04-06      3329461  ...        10267      10101510
4     2010-04-07     15141395  ...        38418      25125244
          ...          ...  ...          ...           ...
3352  2024-01-18  31033922602  ...  43743457679  862765876243
3353  2024-01-19  21406253320  ...  43641546854  859359188408
3354  2024-01-22  25940316396  ...  42027262985  851323826307
3355  2024-01-23  24325476838  ...  42650470550  849682914607
3356  2024-01-24  30879941897  ...  43796442043  849741276211
[3357 rows x 7 columns]
```

##### 上海黄金交易所报告

接口: macro_china_au_report

目标地址: https://datacenter.jin10.com/reportType/dc_sge_report

描述: 上海黄金交易所报告, 数据区间从 20140905-至今

限量: 单次返回所有历史数据

输入参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| -   | -   | -   |

输出参数

| 名称    | 类型      | 描述  |
|-------|---------|-----|
| 日期    | object  | -   |
| 商品    | object  | -   |
| 开盘价   | float64 | -   |
| 最高价   | float64 | -   |
| 最低价   | float64 | -   |
| 收盘价   | float64 | -   |
| 涨跌    | float64 | -   |
| 涨跌幅   | float64 | -   |
| 加权平均价 | float64 | -   |
| 成交量   | float64 | -   |
| 成交金额  | float64 | -   |
| 持仓量   | float64 | -   |
| 交收方向  | object  | -   |
| 交收量   | float64 | -   |

接口示例

```python
import akshare as ak

macro_china_au_report_df = ak.macro_china_au_report()
print(macro_china_au_report_df)
```

数据示例

```
               日期        商品      开盘价  ...        持仓量   交收方向      交收量
0      2014-09-05    Pt9995   293.50  ...        NaN             NaN
1      2014-09-05   Ag(T+D)  4159.00  ...  5252236.0   空支付多      NaN
2      2014-09-05   Ag99.99  4218.00  ...      322.0             NaN
3      2014-09-05   Au(T+D)   250.52  ...   144574.0   多支付空  11848.0
4      2014-09-05  mAu(T+D)   250.87  ...   130808.0   多支付空  23644.0
           ...       ...      ...  ...        ...    ...      ...
24389  2023-03-23  NYAuTN12   437.60  ...    28328.0             NaN
24390  2023-03-23    PGC30g   444.00  ...        NaN             NaN
24391  2023-03-23   Pt99.95   227.20  ...        NaN             NaN
24392  2023-03-23   Au99.99   434.00  ...        NaN             NaN
24393  2023-03-23   Ag(T+D)  5108.00  ...  4965526.0  空支付给多  17670.0
```

#### 国家统计局通用接口

##### 国家统计局全国数据

接口: macro_china_nbs_nation

目标地址: https://data.stats.gov.cn/easyquery.htm

描述: 国家统计局全国数据通用接口，包括月度数据、季度数据、年度数据，具体指标见数据官网。

限量: 根据参数返回指定数据

输入参数

| 名称     | 类型  | 描述                                                                                                                                                 |
|--------|-----|----------------------------------------------------------------------------------------------------------------------------------------------------|
| kind   | str | 数据类别，包括：月度数据、季度数据、年度数据。                                                                                                                            |
| path   | str | 数据路径， 需与kind参数匹配，具体见官网，多层级之间使用  > 连接 。<br> 示例：<br>    国民经济核算 > 支出法国内生产总值<br>   人口 > 总人口<br>   金融业 > 保险系统机构、人员数 > 保险系统机构数                           |
| period | str | 时间区间 <br/>参考格式如下(英文逗号分割，且不能有多余空格)：<br/>    月：201201,201205<br/>    季：2012A,2012B,2012C,2012D<br/>    年：2012,2013 <br>    至今：2013-<br>    最近：last10 |

输出参数

| 名称 | 类型 | 描述 |
|----|----|----|
| -  | -  | -  |

接口示例

```python
import akshare as ak

macro_china_nbs_nation_df = ak.macro_china_nbs_nation(kind="年度数据", path="人口 > 总人口", period="LAST5")
print(macro_china_nbs_nation_df)
```

数据示例

```
                  2022年    2021年    2020年      2019年        2018年
年末总人口(万人)  141175.0  141260.0  141212.0  141008.0000  140541.000000
男性人口(万人)    72206.0   72311.0   72357.0   72039.0000   71863.871426
女性人口(万人)    68969.0   68949.0   68855.0   68969.0000   68677.128574
城镇人口(万人)    92071.0   91425.0   90220.0   88426.1168   86432.715000
乡村人口(万人)    49104.0   49835.0   50992.0   52581.8832   54108.285000
```

##### 国家统计局地区数据

接口: macro_china_nbs_region

目标地址: https://data.stats.gov.cn/easyquery.htm

描述: 国家统计局地区数据通用接口，包括分省月度数据、分省季度数据、分省年度数据、主要城市月度价格、主要城市年度数据、港澳台月度数据、港澳台年度数据，具体指标见数据官网。

限量: 根据参数返回指定数据

输入参数

| 名称        | 类型               | 描述                                                                                                                                                |
|-----------|------------------|---------------------------------------------------------------------------------------------------------------------------------------------------|
| kind      | str              | 数据类别，包括：分省月度数据、分省季度数据、分省年度数据、主要城市月度价格、主要城市年度数据、港澳台月度数据、港澳台年度数据。                                                                                   |
| path      | str              | 数据路径， 需与kind匹配，具体见官网，多层级之间使用  > 连接 。<br> 示例：<br>   国民经济核算 > 地区生产总值<br>   财政 > 地方财政收入                                                              |
| indicator | Union[str, None] | 指定指标，表示在当前path下可选择的指标。在指定region参数的情况下，此参数可以设置为None，此时将获取指定地区下所有可选指标的值。indicator和region参数不能同时为None。                                                |
| region    | Union[str, None] | 指定地区，为可选指标。指定时表示仅获取当前地区下的数据。                                                                                                                      |
| period    | str              | 时间区间<br/>参考格式如下(英文逗号分割，且不能有多余空格)：<br/>    月：201201,201205<br/>    季：2012A,2012B,2012C,2012D<br/>    年：2012,2013 <br>    至今：2013-<br>    最近：last10 |

输出参数

| 名称 | 类型 | 描述 |
|----|----|----|
| -  | -  | -  |

接口示例

```python
import akshare as ak

macro_china_nbs_region_df = ak.macro_china_nbs_region(kind="分省季度数据", path="国民经济核算 > 地区生产总值", period="last3", indicator=None, region="河北省")
print(macro_china_nbs_region_df)

macro_china_nbs_region_df = ak.macro_china_nbs_region(kind="分省季度数据", path="人民生活 > 居民人均可支配收入", indicator='居民人均可支配收入_累计值(元)', period="2022")
print(macro_china_nbs_region_df)
```

数据示例

```
region_gdp_df
河北省                                2023年第二季度   2023年第一季度  2022年第四季度
地区生产总值_累计值(亿元)                 20779.0        10041.0        42370.0
地区生产总值指数(上年同期=100)_累计值(%)    106.1         105.1          103.8

region_income_df
居民人均可支配收入_累计值(元)      2022年第四季度     2022年第三季度     2022年第二季度  2022年第一季度
北京市                            77414.548842      58596.590041     39391.260057    20630.0
天津市                            48976.122565      38934.695987     25453.536929    14082.0
河北省                            30867.016699      22783.082852     14998.243977     7863.0
...                               ...               ...              ...              ...
新疆维吾尔自治区                   27062.738782      16553.955250     10747.883634     5713.0
```

#### 金融市场

##### 股票筹资

接口: macro_stock_finance

目标地址: https://data.10jqka.com.cn/macro/finance/

描述: 同花顺-数据中心-宏观数据-股票筹资

限量: 单次返回所有历史数据

输入参数

| 名称 | 类型 | 描述 |
|----|----|----|
| -  | -  | -  |

输出参数

| 名称     | 类型      | 描述 |
|--------|---------|----|
| 月份     | object  | -  |
| 募集资金   | float64 | -  |
| 首发募集资金 | float64 | -  |
| 增发募集资金 | float64 | -  |
| 配股募集资金 | float64 | -  |

接口示例

```python
import akshare as ak

macro_stock_finance_df = ak.macro_stock_finance()
print(macro_stock_finance_df)
```

数据示例

```
      月份    募集资金  首发募集资金 增发募集资金 配股募集资金
0   2024-07    53.67   21.00    32.67     NaN
1   2024-06   109.70   38.76    70.93     NaN
2   2024-05    84.48   21.49    62.99     NaN
3   2024-04   182.98    9.31   173.67     NaN
4   2024-03   306.71   53.24   253.47     NaN
5   2024-02    44.83   18.00    26.83     NaN
6   2024-01   497.90  116.16   381.74     NaN
7   2023-12   439.66  110.33   329.33     NaN
8   2023-11   416.33   81.24   317.10   17.98
9   2023-10   318.09   57.45   260.64     NaN
10  2023-09   310.89  142.20   168.69     NaN
11  2023-08   889.95  244.96   632.48   12.51
12  2023-07  1145.18  602.19   542.99     NaN
13  2023-06   600.35  313.84   189.29   97.22
14  2023-05   566.78  368.03   198.75     NaN
15  2023-04  1532.55  416.53  1116.01     NaN
16  2023-03  1174.06  482.81   691.25     NaN
17  2023-02   888.74  165.32   719.69    3.74
18  2023-01  1246.71  192.72  1053.99     NaN
19  2022-12  1391.35  252.57  1120.48   18.30
20  2022-11  1014.84  271.62   695.28   47.94
21  2022-10   621.47  186.86   434.62     NaN
22  2022-09  1239.08  541.32   697.75     NaN
23  2022-08  1856.48  669.71  1086.50  100.27
24  2022-07  1620.69  382.63  1238.06     NaN
25  2022-06   493.75  354.55   139.20     NaN
26  2022-05   344.12  181.95   162.17     NaN
27  2022-04  1194.55  638.77   418.45  137.34
28  2022-03  1101.39  566.20   463.71   71.47
29  2022-02   438.57  172.34   259.57    6.66
```

##### 新增人民币贷款

接口: macro_rmb_loan

目标地址: https://data.10jqka.com.cn/macro/loan/

描述: 同花顺-数据中心-宏观数据-新增人民币贷款

限量: 单次返回所有历史数据

输入参数

| 名称 | 类型 | 描述 |
|----|----|----|
| -  | -  | -  |

输出参数

| 名称         | 类型      | 描述 |
|------------|---------|----|
| 月份         | object  | -  |
| 新增人民币贷款-总额 | float64 | -  |
| 新增人民币贷款-同比 | object  | -  |
| 新增人民币贷款-环比 | object  | -  |
| 累计人民币贷款-总额 | float64 | -  |
| 累计人民币贷款-同比 | object  | -  |

接口示例

```python
import akshare as ak

macro_rmb_loan_df = ak.macro_rmb_loan()
print(macro_rmb_loan_df)
```

数据示例

```
    月份  新增人民币贷款-总额 新增人民币贷款-同比 新增人民币贷款-环比  累计人民币贷款-总额 累计人民币贷款-同比
0   2024-06     21300.0    -30.16%    124.21%  2508526.52      8.79%
1   2024-05      9500.0    -30.15%     30.14%  2487269.61      9.32%
2   2024-04      7300.0      1.56%    -76.38%  2477817.72      9.56%
3   2024-03     30900.0    -20.57%    113.10%  2470492.81      9.58%
4   2024-02     14500.0    -19.89%    -70.53%  2439604.04     10.11%
5   2024-01     49200.0      0.41%    320.51%  2425047.89     10.36%
6   2023-12     11700.0    -16.43%      7.34%  2375905.37     11.03%
7   2023-11     10900.0     -9.92%     47.62%  2364196.44     11.21%
8   2023-10      7384.0     20.03%    -68.03%  2353309.12     11.33%
9   2023-09     23100.0     -6.48%     69.85%  2345924.92     11.31%
10  2023-08     13600.0      8.80%    293.18%  2322806.64     11.52%
11  2023-07      3459.0    -49.06%    -88.66%  2309226.18     11.54%
12  2023-06     30500.0      8.54%    124.26%  2305766.69     11.74%
13  2023-05     13600.0    -28.04%     89.20%  2275271.41     11.78%
14  2023-04      7188.0     11.37%    -81.52%  2261643.47     12.15%
15  2023-03     38900.0     24.28%    114.92%  2254455.10     12.16%
16  2023-02     18100.0     47.15%    -63.06%  2215575.67     11.96%
17  2023-01     49000.0     23.12%    250.00%  2197455.14     11.74%
18  2022-12     14000.0     23.89%     15.70%  2139852.67     11.05%
19  2022-11     12100.0     -4.72%     96.68%  2125869.63     10.98%
20  2022-10      6152.0    -25.54%    -75.09%  2113733.70     11.08%
21  2022-09     24700.0     48.80%     97.60%  2107581.92     11.24%
22  2022-08     12500.0      2.46%     84.09%  2082843.52     10.91%
23  2022-07      6790.0    -37.13%    -75.84%  2070301.63     10.96%
24  2022-06     28100.0     32.55%     48.68%  2063511.81     11.24%
25  2022-05     18900.0     26.00%    192.84%  2035448.45     11.00%
26  2022-04      6454.0    -56.10%    -79.38%  2016564.32     10.87%
27  2022-03     31300.0     14.65%    154.47%  2010110.55     11.42%
28  2022-02     12300.0     -9.56%    -69.10%  1978857.03     11.37%
29  2022-01     39800.0     11.17%    252.21%  1966521.17     11.53%
```

##### 人民币存款余额

接口: macro_rmb_deposit

目标地址: https://data.10jqka.com.cn/macro/rmb/

描述: 同花顺-数据中心-宏观数据-人民币存款余额

限量: 单次返回所有历史数据

输入参数

| 名称 | 类型 | 描述 |
|----|----|----|
| -  | -  | -  |

输出参数

| 名称        | 类型      | 描述 |
|-----------|---------|----|
| 月份        | object  | -  |
| 新增存款-数量   | float64 | -  |
| 新增存款-同比   | object  | -  |
| 新增存款-环比   | object  | -  |
| 新增企业存款-数量 | float64 | -  |
| 新增企业存款-同比 | object  | -  |
| 新增企业存款-环比 | object  | -  |
| 新增储蓄存款-数量 | float64 | -  |
| 新增储蓄存款-同比 | object  | -  |
| 新增储蓄存款-环比 | object  | -  |
| 新增其他存款-数量 | float64 | -  |
| 新增其他存款-同比 | object  | -  |
| 新增其他存款-环比 | object  | -  |

接口示例

```python
import akshare as ak

macro_rmb_deposit_df = ak.macro_rmb_deposit()
print(macro_rmb_deposit_df)
```

数据示例

```
    月份     新增存款-数量 新增存款-同比  ... 新增其他存款-数量  新增其他存款-同比 新增其他存款-环比
0   2024-06  2957172.45   6.14%  ...  55502.91     11.25%   -12.86%
1   2024-05  2932599.27   6.68%  ...  63696.00      5.48%    13.62%
2   2024-04  2915852.23   6.63%  ...  56062.80     -3.36%     1.78%
3   2024-03  2955054.17   7.89%  ...  55082.19      3.95%   -12.21%
4   2024-02  2906999.38   8.39%  ...  62743.55      2.19%    -5.71%
5   2024-01  2897428.50   9.18%  ...  66540.81     17.07%    14.85%
6   2023-12  2842623.30   9.97%  ...  57937.13     15.84%   -13.68%
7   2023-11  2841754.85  10.24%  ...  67118.35     10.21%    -4.68%
8   2023-10  2816483.21  10.53%  ...  70411.12      9.02%    24.07%
9   2023-09  2810037.37  10.20%  ...  56750.59      6.77%    -3.61%
10  2023-08  2787610.76  10.45%  ...  58878.32      1.60%    -0.15%
11  2023-07  2774993.33  10.51%  ...  58965.86     -2.57%    18.20%
12  2023-06  2786204.53  10.98%  ...  49888.25    -10.37%   -17.38%
13  2023-05  2749085.32  11.65%  ...  60384.07      0.59%     4.08%
14  2023-04  2734467.18  12.44%  ...  58014.72      6.58%     9.49%
15  2023-03  2739076.14  12.67%  ...  52986.64     -1.92%   -13.70%
16  2023-02  2681999.00  12.40%  ...  61398.30     -1.68%     8.02%
17  2023-01  2653915.86  12.42%  ...  56840.80      0.70%    13.65%
18  2022-12  2584998.23  11.30%  ...  50012.92     -0.75%   -17.88%
19  2022-11  2577756.26  11.55%  ...  60902.84      0.31%    -5.70%
20  2022-10  2548210.07  10.82%  ...  64583.53     -5.02%    21.51%
21  2022-09  2550054.11  11.27%  ...  53151.50     -6.56%    -8.28%
22  2022-08  2523760.96  11.25%  ...  57952.00     -6.38%    -4.25%
23  2022-07  2510993.90  11.36%  ...  60523.52      0.57%     8.74%
24  2022-06  2510546.99  10.78%  ...  55660.03      2.75%    -7.28%
25  2022-05  2462241.02  10.53%  ...  60027.16      3.19%    10.27%
26  2022-04  2431864.69  10.44%  ...  54435.00     11.28%     0.76%
27  2022-03  2430955.93  10.04%  ...  54024.79     25.24%   -13.49%
28  2022-02  2386080.47   9.81%  ...  62449.64     30.08%    10.63%
29  2022-01  2360690.54   9.22%  ...  56447.88     -0.07%    12.02%
[30 rows x 13 columns]
```

### 中国香港宏观

#### 消费者物价指数

接口: macro_china_hk_cpi

目标地址: https://data.eastmoney.com/cjsj/foreign_8_0.html

描述: 东方财富-经济数据一览-中国香港-消费者物价指数

限量: 单次返回所有历史数据

输入参数

| 名称 | 类型 | 描述 |
|----|----|----|
| -  | -  | -  |

输出参数

| 名称   | 类型      | 描述  |
|------|---------|-----|
| 时间   | object  | -   |
| 前值   | float64 | -   |
| 现值   | float64 | -   |
| 发布日期 | object  | -   |

接口示例

```python
import akshare as ak

macro_china_hk_cpi_df = ak.macro_china_hk_cpi()
print(macro_china_hk_cpi_df)
```

数据示例

```
      时间        前值  现值        发布日期
0    2008年01月   78.4   77.9  2008-02-21
1    2008年02月   77.9   79.0  2008-03-23
2    2008年03月   79.0   79.0  2008-04-21
3    2008年04月   79.0   79.5  2008-05-22
4    2008年05月   79.5   79.7  2008-06-21
..        ...    ...    ...         ...
167  2021年12月  102.0  102.2  2022-01-20
168  2022年01月  102.2  102.3  2022-02-22
169  2022年02月  102.3  103.1  2022-03-21
170  2022年03月  103.1  103.3  2022-04-22
171  2022年04月  103.3  103.1  2022-05-23
[172 rows x 4 columns]
```

#### 消费者物价指数年率

接口: macro_china_hk_cpi_ratio

目标地址: https://data.eastmoney.com/cjsj/foreign_8_1.html

描述: 东方财富-经济数据一览-中国香港-消费者物价指数年率

限量: 单次返回所有历史数据

输入参数

| 名称 | 类型 | 描述 |
|----|----|----|
| -  | -  | -  |

输出参数

| 名称   | 类型      | 描述      |
|------|---------|---------|
| 时间   | object  | -       |
| 前值   | float64 | 注意单位: % |
| 现值   | float64 | 注意单位: % |
| 发布日期 | object  | -       |

接口示例

```python
import akshare as ak

macro_china_hk_cpi_ratio_df = ak.macro_china_hk_cpi_ratio()
print(macro_china_hk_cpi_ratio_df)
```

数据示例

```
           时间   前值   现值        发布日期
0    2008年01月  3.8  3.2  2008-02-21
1    2008年02月  3.2  6.3  2008-03-23
2    2008年03月  6.3  4.2  2008-04-21
3    2008年04月  4.2  5.4  2008-05-22
4    2008年05月  5.4  5.7  2008-06-21
..        ...  ...  ...         ...
167  2021年12月  1.8  2.4  2022-01-20
168  2022年01月  2.4  1.2  2022-02-22
169  2022年02月  1.2  1.6  2022-03-21
170  2022年03月  1.6  1.7  2022-04-22
171  2022年04月  1.7  1.3  2022-05-23
[172 rows x 4 columns]
```

#### 失业率

接口: macro_china_hk_rate_of_unemployment

目标地址: https://data.eastmoney.com/cjsj/foreign_8_2.html

描述: 东方财富-经济数据一览-中国香港-失业率

限量: 单次返回所有历史数据

输入参数

| 名称 | 类型 | 描述 |
|----|----|----|
| -  | -  | -  |

输出参数

| 名称   | 类型      | 描述      |
|------|---------|---------|
| 时间   | object  | -       |
| 前值   | float64 | 注意单位: % |
| 现值   | float64 | 注意单位: % |
| 发布日期 | object  | -       |

接口示例

```python
import akshare as ak

macro_china_hk_rate_of_unemployment_df = ak.macro_china_hk_rate_of_unemployment()
print(macro_china_hk_rate_of_unemployment_df)
```

数据示例

```
           时间   前值   现值        发布日期
0    2008年01月  3.4  3.4  2008-02-18
1    2008年02月  3.4  3.3  2008-03-20
2    2008年03月  3.3  3.3  2008-04-18
3    2008年04月  3.3  3.3  2008-05-19
4    2008年05月  3.3  3.3  2008-06-18
..        ...  ...  ...         ...
190  2023年11月  2.9  2.9  2023-12-19
191  2023年12月  2.9  2.9  2024-01-18
192  2024年01月  2.9  2.9  2024-02-20
193  2024年02月  2.9  2.9  2024-03-18
194  2024年03月  2.9  NaN  2024-04-18
[195 rows x 4 columns]
```

#### GDP

接口: macro_china_hk_gbp

目标地址: https://data.eastmoney.com/cjsj/foreign_8_3.html

描述: 东方财富-经济数据一览-中国香港-香港 GDP

限量: 单次返回所有历史数据

输入参数

| 名称 | 类型 | 描述 |
|----|----|----|
| -  | -  | -  |

输出参数

| 名称   | 类型      | 描述        |
|------|---------|-----------|
| 时间   | object  | -         |
| 前值   | float64 | 注意单位: 亿港元 |
| 现值   | float64 | 注意单位: 亿港元 |
| 发布日期 | object  | -         |

接口示例

```python
import akshare as ak

macro_china_hk_gbp_df = ak.macro_china_hk_gbp()
print(macro_china_hk_gbp_df)
```

数据示例

```
          时间    前值      现值       发布日期
0   2008第1季度  458870  418762.0  2008-05-15
1   2008第2季度  418762  409388.0  2008-08-15
2   2008第3季度  409388  436152.0  2008-11-15
3   2008第4季度  436152  443186.0  2009-02-14
4   2009第1季度  443186  389033.0  2009-05-15
..       ...     ...       ...         ...
60  2023第1季度  732062  720760.0  2023-05-12
61  2023第2季度  720760  707502.0  2023-08-11
62  2023第3季度  707502  768426.0  2023-11-10
63  2023第4季度  768426  794640.0  2024-02-28
64  2024第1季度  794640       NaN  2024-05-17
```

#### GDP 同比

接口: macro_china_hk_gbp_ratio

目标地址: https://data.eastmoney.com/cjsj/foreign_8_4.html

描述: 东方财富-经济数据一览-中国香港-香港 GDP 同比

限量: 单次返回所有历史数据

输入参数

| 名称 | 类型 | 描述 |
|----|----|----|
| -  | -  | -  |

输出参数

| 名称   | 类型      | 描述      |
|------|---------|---------|
| 时间   | object  | -       |
| 前值   | float64 | 注意单位: % |
| 现值   | float64 | 注意单位: % |
| 发布日期 | object  | -       |

接口示例

```python
import akshare as ak

macro_china_hk_gbp_ratio_df = ak.macro_china_hk_gbp_ratio()
print(macro_china_hk_gbp_ratio_df)
```

数据示例

```
        时间    前值   现值      发布日期
0   2008第1季度  12.3  9.7  2008-05-15
1   2008第2季度   9.7  6.0  2008-08-15
2   2008第3季度   6.0  2.9  2008-11-15
3   2008第4季度   2.9 -3.4  2009-02-14
4   2009第1季度  -3.4 -7.1  2009-05-15
..       ...   ...  ...         ...
60  2023第1季度  -2.9  5.2  2023-05-12
61  2023第2季度   5.2  4.9  2023-08-11
62  2023第3季度   4.9  7.1  2023-11-10
63  2023第4季度   7.1  8.5  2024-02-28
64  2024第1季度   8.5  NaN  2024-05-17
[65 rows x 4 columns]
```

#### 香港楼宇买卖合约数量

接口: macro_china_hk_building_volume

目标地址: https://data.eastmoney.com/cjsj/foreign_8_5.html

描述: 东方财富-经济数据一览-中国香港-香港楼宇买卖合约数量

限量: 单次返回所有历史数据

输入参数

| 名称 | 类型 | 描述 |
|----|----|----|
| -  | -  | -  |

输出参数

| 名称   | 类型      | 描述  |
|------|---------|-----|
| 时间   | object  | -   |
| 前值   | float64 | -   |
| 现值   | float64 | -   |
| 发布日期 | object  | -   |

接口示例

```python
import akshare as ak

macro_china_hk_building_volume_df = ak.macro_china_hk_building_volume()
print(macro_china_hk_building_volume_df)
```

数据示例

```
          时间    前值     现值      发布日期
0    2008年01月  15592  16984.0  2008-02-04
1    2008年02月  16984  14384.0  2008-03-06
2    2008年03月  14384  10993.0  2008-04-04
3    2008年04月  10993  10945.0  2008-05-05
4    2008年05月  10945  10138.0  2008-06-04
..        ...    ...      ...         ...
190  2023年11月   2938   3532.0  2023-12-04
191  2023年12月   3532   3764.0  2024-01-04
192  2024年01月   3764   4401.0  2024-02-02
193  2024年02月   4401   3189.0  2024-03-04
194  2024年03月   3189      NaN  2024-04-04
[195 rows x 4 columns]
```

#### 香港楼宇买卖合约成交金额

接口: macro_china_hk_building_amount

目标地址: https://data.eastmoney.com/cjsj/foreign_8_6.html

描述: 东方财富-经济数据一览-中国香港-香港楼宇买卖合约成交金额

限量: 单次返回所有历史数据

输入参数

| 名称 | 类型 | 描述 |
|----|----|----|
| -  | -  | -  |

输出参数

| 名称   | 类型      | 描述        |
|------|---------|-----------|
| 时间   | object  | -         |
| 前值   | float64 | 注意单位: 亿港元 |
| 现值   | float64 | 注意单位: 亿港元 |
| 发布日期 | object  | -         |

接口示例

```python
import akshare as ak

macro_china_hk_building_amount_df = ak.macro_china_hk_building_amount()
print(macro_china_hk_building_amount_df)
```

数据示例

```
           时间   前值     现值       发布日期
0    2008年01月  64449  67774.0  2008-02-04
1    2008年02月  67774  51974.0  2008-03-06
2    2008年03月  51974  44017.0  2008-04-04
3    2008年04月  44017  33509.0  2008-05-05
4    2008年05月  33509  33972.0  2008-06-04
..        ...    ...      ...         ...
190  2023年11月  29008  24537.0  2023-12-04
191  2023年12月  24537  33585.0  2024-01-04
192  2024年01月  33585  33669.0  2024-02-02
193  2024年02月  33669  22579.0  2024-03-04
194  2024年03月  22579      NaN  2024-04-04
[195 rows x 4 columns]
```

#### 香港商品贸易差额年率

接口: macro_china_hk_trade_diff_ratio

目标地址: https://data.eastmoney.com/cjsj/foreign_8_7.html

描述: 东方财富-经济数据一览-中国香港-香港商品贸易差额年率

限量: 单次返回所有历史数据

输入参数

| 名称 | 类型 | 描述 |
|----|----|----|
| -  | -  | -  |

输出参数

| 名称   | 类型      | 描述      |
|------|---------|---------|
| 时间   | object  | -       |
| 前值   | float64 | 注意单位: % |
| 现值   | float64 | 注意单位: % |
| 发布日期 | object  | -       |

接口示例

```python
import akshare as ak

macro_china_hk_trade_diff_ratio_df = ak.macro_china_hk_trade_diff_ratio()
print(macro_china_hk_trade_diff_ratio_df)
```

数据示例

```
           时间      前值         现值        发布日期
0    2008年01月  -31.654573  -69.070295  2008-02-27
1    2008年02月  -69.070295 -109.659467  2008-03-29
2    2008年03月 -109.659467    0.699128  2008-04-27
3    2008年04月    0.699128   21.249636  2008-05-28
4    2008年05月   21.249636  -92.302304  2008-06-27
..        ...         ...         ...         ...
189  2023年10月  -43.766696  -23.398342  2023-11-27
190  2023年11月  -23.398342   -3.056591  2023-12-28
191  2023年12月   -3.056591  -15.908299  2024-01-25
192  2024年01月  -15.908299  114.357782  2024-02-27
193  2024年02月  114.357782    8.186928  2024-03-26
[194 rows x 4 columns]
```

#### 香港制造业 PPI 年率

接口: macro_china_hk_ppi

目标地址: https://data.eastmoney.com/cjsj/foreign_8_8.html

描述: 东方财富-经济数据一览-中国香港-香港制造业PPI年率

限量: 单次返回所有历史数据

输入参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| -   | -   | -   |

输出参数

| 名称   | 类型      | 描述      |
|------|---------|---------|
| 时间   | object  | -       |
| 前值   | float64 | 注意单位: % |
| 现值   | float64 | 注意单位: % |
| 发布日期 | object  | -       |

接口示例

```python
import akshare as ak

macro_china_hk_ppi_df = ak.macro_china_hk_ppi()
print(macro_china_hk_ppi_df)
```

数据示例

```
       时间   前值   现值     发布日期
0   2008第1季度  4.4  5.9  2008-06-13
1   2008第2季度  5.9  6.9  2008-09-12
2   2008第3季度  6.9  5.8  2008-12-12
3   2008第4季度  5.8  3.9  2009-03-13
4   2009第1季度  3.9 -1.4  2009-06-12
..       ...  ...  ...         ...
60  2023第1季度 -0.3  0.9  2023-06-15
61  2023第2季度  0.9 -0.2  2023-09-13
62  2023第3季度 -0.2  3.0  2023-12-14
63  2023第4季度  3.0  2.7  2024-03-14
64  2024第1季度  2.7  NaN  2024-06-14
[65 rows x 4 columns]
```

### 美国宏观

#### 经济状况

##### 美国GDP

接口: macro_usa_gdp_monthly

目标地址: https://datacenter.jin10.com/reportType/dc_usa_gdp

描述: 美国国内生产总值(GDP)报告, 数据区间从 20080228-至今

限量: 单次返回所有历史数据

输入参数

| 名称 | 类型 | 描述 |
|----|----|----|
| -  | -  | -  |

输出参数

| 名称  | 类型      | 描述      |
|-----|---------|---------|
| 商品  | object  | -       |
| 日期  | object  | -       |
| 今值  | float64 | 注意单位: % |
| 预测值 | float64 | 注意单位: % |
| 前值  | float64 | 注意单位: % |

接口示例

```python
import akshare as ak

macro_usa_gdp_monthly_df = ak.macro_usa_gdp_monthly()
print(macro_usa_gdp_monthly_df)
```

数据示例

```
                商品          日期   今值  预测值   前值
0    美国国内生产总值(GDP)  2008-02-28  0.6  0.7  0.6
1    美国国内生产总值(GDP)  2008-03-27  0.6  0.6  0.6
2    美国国内生产总值(GDP)  2008-04-30  0.6  0.2  0.6
3    美国国内生产总值(GDP)  2008-06-26  2.7  1.0  0.9
4    美国国内生产总值(GDP)  2008-07-31  1.9  2.0  1.0
..             ...         ...  ...  ...  ...
188  美国国内生产总值(GDP)  2023-12-21  4.9  5.2  2.1
189  美国国内生产总值(GDP)  2024-01-25  3.3  2.0  4.9
190  美国国内生产总值(GDP)  2024-02-28  3.2  3.3  4.9
191  美国国内生产总值(GDP)  2024-03-28  3.4  3.2  4.9
192  美国国内生产总值(GDP)  2024-04-25  NaN  NaN  3.4
[193 rows x 5 columns]
```

#### 物价水平

##### 美国CPI月率报告

接口: macro_usa_cpi_monthly

目标地址: https://datacenter.jin10.com/reportType/dc_usa_cpi

描述: 美国 CPI 月率报告, 数据区间从 19700101-至今

限量: 单次返回所有历史数据

输入参数

| 名称 | 类型 | 描述 |
|----|----|----|
| -  | -  | -  |

输出参数

| 名称  | 类型      | 描述      |
|-----|---------|---------|
| 商品  | object  | -       |
| 日期  | object  | -       |
| 今值  | float64 | 注意单位: % |
| 预测值 | float64 | 注意单位: % |
| 前值  | float64 | 注意单位: % |

接口示例

```python
import akshare as ak

macro_usa_cpi_monthly_df = ak.macro_usa_cpi_monthly()
print(macro_usa_cpi_monthly_df)
```

数据示例

```
          商品        日期   今值  预测值   前值
0    美国CPI月率  1970-01-01  0.5  NaN  NaN
1    美国CPI月率  1970-02-01  0.5  NaN  0.5
2    美国CPI月率  1970-03-01  0.5  NaN  0.5
3    美国CPI月率  1970-04-01  0.5  NaN  0.5
4    美国CPI月率  1970-05-01  0.5  NaN  0.5
..       ...         ...  ...  ...  ...
647  美国CPI月率  2023-12-12  0.1  0.0  0.1
648  美国CPI月率  2024-01-11  0.3  0.2  0.2
649  美国CPI月率  2024-02-13  0.3  0.2  0.2
650  美国CPI月率  2024-03-12  0.4  0.4  0.3
651  美国CPI月率  2024-04-10  NaN  0.3  0.4
[652 rows x 5 columns]
```

##### 美国CPI年率报告

接口: macro_usa_cpi_yoy

目标地址: https://data.eastmoney.com/cjsj/foreign_0_12.html

描述: 东方财富-经济数据一览-美国-CPI年率, 数据区间从2008-至今

限量: 单次返回所有历史数据

输入参数

| 名称 | 类型 | 描述 |
|----|----|----|
| -  | -  | -  |

输出参数

| 名称   | 类型      | 描述      |
|------|---------|---------|
| 时间   | object  | -       |
| 发布日期 | object  | -       |
| 现值   | float64 | 注意单位: % |
| 前值   | float64 | 注意单位: % |

接口示例

```python
import akshare as ak

macro_usa_cpi_yoy_df = ak.macro_usa_cpi_yoy()
print(macro_usa_cpi_yoy_df)
```

数据示例

```
       时间        发布日期   现值   前值
0    2008-01-01  2008-02-14  4.3  4.1
1    2008-02-01  2008-03-16  4.0  4.3
2    2008-03-01  2008-04-14  4.0  4.0
3    2008-04-01  2008-05-15  3.9  4.0
4    2008-05-01  2008-06-14  4.2  3.9
..          ...         ...  ...  ...
181  2023-03-01  2023-04-12  5.0  6.0
182  2023-04-01  2023-05-10  4.9  5.0
183  2023-05-01  2023-06-13  4.0  4.9
184  2023-06-01  2023-07-12  3.0  4.0
185  2023-07-01  2023-08-10  NaN  3.0
[186 rows x 4 columns]
```

##### 美国核心CPI月率报告

接口: macro_usa_core_cpi_monthly

目标地址: https://datacenter.jin10.com/reportType/dc_usa_core_cpi

描述: 美国核心 CPI 月率报告, 数据区间从 19700101-至今

限量: 单次返回所有历史数据

输入参数

| 名称 | 类型 | 描述 |
|----|----|----|
| -  | -  | -  |

输出参数

| 名称  | 类型      | 描述      |
|-----|---------|---------|
| 商品  | object  | -       |
| 日期  | object  | -       |
| 今值  | float64 | 注意单位: % |
| 预测值 | float64 | 注意单位: % |
| 前值  | float64 | 注意单位: % |

接口示例

```python
import akshare as ak

macro_usa_core_cpi_monthly_df = ak.macro_usa_core_cpi_monthly()
print(macro_usa_core_cpi_monthly_df)
```

数据示例

```
            商品          日期   今值  预测值   前值
0    美国核心CPI月率  1970-01-01  0.5  NaN  NaN
1    美国核心CPI月率  1970-02-01  0.5  NaN  0.5
2    美国核心CPI月率  1970-03-01  0.5  NaN  0.5
3    美国核心CPI月率  1970-04-01  0.8  NaN  0.5
4    美国核心CPI月率  1970-05-01  0.7  NaN  0.8
..         ...         ...  ...  ...  ...
647  美国核心CPI月率  2023-12-12  0.3  0.3  0.2
648  美国核心CPI月率  2024-01-11  0.3  0.3  0.3
649  美国核心CPI月率  2024-02-13  0.4  0.3  0.3
650  美国核心CPI月率  2024-03-12  0.4  0.3  0.4
651  美国核心CPI月率  2024-04-10  NaN  0.3  0.4
[652 rows x 5 columns]
```

##### 美国个人支出月率报告

接口: macro_usa_personal_spending

目标地址: https://datacenter.jin10.com/reportType/dc_usa_personal_spending

描述: 美国个人支出月率报告, 数据区间从 19700101-至今

限量: 单次返回所有历史数据

输入参数

| 名称 | 类型 | 描述 |
|----|----|----|
| -  | -  | -  |

输出参数

| 名称  | 类型      | 描述      |
|-----|---------|---------|
| 商品  | object  | -       |
| 日期  | object  | -       |
| 今值  | float64 | 注意单位: % |
| 预测值 | float64 | 注意单位: % |
| 前值  | float64 | 注意单位: % |

接口示例

```python
import akshare as ak

macro_usa_personal_spending_df = ak.macro_usa_personal_spending()
print(macro_usa_personal_spending_df)
```

数据示例

```
           商品          日期   今值  预测值   前值
0    美国个人支出月率  1970-01-01  0.4  NaN  NaN
1    美国个人支出月率  1970-02-01  1.0  NaN  0.4
2    美国个人支出月率  1970-03-01  0.8  NaN  1.0
3    美国个人支出月率  1970-04-01 -0.3  NaN  0.8
4    美国个人支出月率  1970-05-01  0.6  NaN -0.3
..        ...         ...  ...  ...  ...
645  美国个人支出月率  2023-11-30  0.2  0.2  0.7
646  美国个人支出月率  2023-12-22  0.2  0.2  0.1
647  美国个人支出月率  2024-01-26  0.7  0.4  0.4
648  美国个人支出月率  2024-02-29  0.2  0.2  0.7
649  美国个人支出月率  2024-03-29  0.8  0.5  0.2
[650 rows x 5 columns]
```

##### 美国零售销售月率报告

接口: macro_usa_retail_sales

目标地址: https://datacenter.jin10.com/reportType/dc_usa_retail_sales

描述: 美国零售销售月率报告, 数据区间从 19920301-至今

限量: 单次返回所有历史数据

输入参数

| 名称 | 类型   | 描述 |
|----|------|----|
| -  | -  - |

输出参数

| 名称  | 类型      | 描述      |
|-----|---------|---------|
| 商品  | object  | -       |
| 日期  | object  | -       |
| 今值  | float64 | 注意单位: % |
| 预测值 | float64 | 注意单位: % |
| 前值  | float64 | 注意单位: % |

接口示例

```python
import akshare as ak

macro_usa_retail_sales_df = ak.macro_usa_retail_sales()
print(macro_usa_retail_sales_df)
```

数据示例

```
           商品          日期   今值  预测值   前值
0    美国零售销售月率  1992-03-01  0.1  NaN  NaN
1    美国零售销售月率  1992-04-01 -0.3  NaN  0.1
2    美国零售销售月率  1992-05-01  0.6  NaN -0.3
3    美国零售销售月率  1992-06-01  0.5  NaN  0.6
4    美国零售销售月率  1992-07-01  0.3  NaN  0.5
..        ...         ...  ...  ...  ...
381  美国零售销售月率  2023-12-14  0.3 -0.1 -0.2
382  美国零售销售月率  2024-01-17  0.6  0.4  0.3
383  美国零售销售月率  2024-02-15 -0.8 -0.2  0.4
384  美国零售销售月率  2024-03-14  0.6  0.8 -1.1
385  美国零售销售月率  2024-04-15  NaN  NaN  0.6
[386 rows x 5 columns]
```

##### 美国进口物价指数报告

接口: macro_usa_import_price

目标地址: https://datacenter.jin10.com/reportType/dc_usa_import_price

描述: 美国进口物价指数报告, 数据区间从 19890201-至今

限量: 单次返回所有历史数据

输入参数

| 名称 | 类型   | 描述 |
|----|------|----|
| -  | -  - |

输出参数

| 名称  | 类型      | 描述      |
|-----|---------|---------|
| 商品  | object  | -       |
| 日期  | object  | -       |
| 今值  | float64 | 注意单位: % |
| 预测值 | float64 | 注意单位: % |
| 前值  | float64 | 注意单位: % |

接口示例

```python
import akshare as ak

macro_usa_import_price_df = ak.macro_usa_import_price()
print(macro_usa_import_price_df)
```

数据示例

```
           商品          日期   今值  预测值   前值
0    美国进口物价指数  1989-02-01  2.0  NaN  NaN
1    美国进口物价指数  1989-03-01 -0.5  NaN  2.0
2    美国进口物价指数  1989-04-01  0.8  NaN -0.5
3    美国进口物价指数  1989-05-01  0.8  NaN  0.8
4    美国进口物价指数  1989-06-01  0.7  NaN  0.8
..        ...         ...  ...  ...  ...
420  美国进口物价指数  2023-12-14 -0.4 -0.8 -0.6
421  美国进口物价指数  2024-01-17  0.0 -0.5 -0.5
422  美国进口物价指数  2024-02-15  0.8  0.0 -0.7
423  美国进口物价指数  2024-03-15  0.3  0.3  0.8
424  美国进口物价指数  2024-04-12  NaN  0.2  0.3
[425 rows x 5 columns]
```

##### 美国出口价格指数报告

接口: macro_usa_export_price

目标地址: https://datacenter.jin10.com/reportType/dc_usa_export_price

描述: 美国出口价格指数报告, 数据区间从 19890201-至今

限量: 单次返回所有历史数据

输入参数

| 名称 | 类型   | 描述 |
|----|------|----|
| -  | -  - |

输出参数

| 名称  | 类型      | 描述      |
|-----|---------|---------|
| 商品  | object  | -       |
| 日期  | object  | -       |
| 今值  | float64 | 注意单位: % |
| 预测值 | float64 | 注意单位: % |
| 前值  | float64 | 注意单位: % |

接口示例

```python
import akshare as ak

macro_usa_export_price_df = ak.macro_usa_export_price()
print(macro_usa_export_price_df)
```

数据示例

```
           商品          日期   今值  预测值   前值
0    美国出口价格指数  1989-02-01  1.2  NaN  NaN
1    美国出口价格指数  1989-03-01 -0.3  NaN  1.2
2    美国出口价格指数  1989-04-01  0.6  NaN -0.3
3    美国出口价格指数  1989-05-01 -0.2  NaN  0.6
4    美国出口价格指数  1989-06-01  0.4  NaN -0.2
..        ...         ...  ...  ...  ...
420  美国出口价格指数  2023-12-14 -0.9 -1.0 -0.9
421  美国出口价格指数  2024-01-17 -0.9 -0.6 -0.9
422  美国出口价格指数  2024-02-15  0.8 -0.1 -0.7
423  美国出口价格指数  2024-03-15  0.8  0.2  0.9
424  美国出口价格指数  2024-04-12  NaN  0.1  0.8
[425 rows x 5 columns]
```

#### 劳动力市场

##### LMCI

接口: macro_usa_lmci

目标地址: https://datacenter.jin10.com/reportType/dc_usa_lmci

描述: 美联储劳动力市场状况指数报告, 数据区间从 20141006-至今

限量: 单次返回所有历史数据

输入参数

| 名称 | 类型   | 描述 |
|----|------|----|
| -  | -  - |

输出参数

| 名称  | 类型      | 描述      |
|-----|---------|---------|
| 商品  | object  | -       |
| 日期  | object  | -       |
| 今值  | float64 | 注意单位: % |
| 预测值 | float64 | 注意单位: % |
| 前值  | float64 | 注意单位: % |

接口示例

```python
import akshare as ak

macro_usa_lmci_df = ak.macro_usa_lmci()
print(macro_usa_lmci_df)
```

数据示例

```
              商品          日期   今值  预测值   前值
0   美联储劳动力市场状况指数  2014-10-06  2.5  NaN  NaN
1   美联储劳动力市场状况指数  2014-11-10  4.0  NaN  4.0
2   美联储劳动力市场状况指数  2014-12-08  2.9  NaN  3.9
3   美联储劳动力市场状况指数  2015-01-12  6.1  NaN  5.5
4   美联储劳动力市场状况指数  2015-02-09  4.9  NaN  7.3
5   美联储劳动力市场状况指数  2015-03-09  4.0  NaN  4.9
6   美联储劳动力市场状况指数  2015-04-06 -0.3  NaN  2.0
7   美联储劳动力市场状况指数  2015-05-11 -1.9  NaN -1.8
8   美联储劳动力市场状况指数  2015-06-08  1.3  NaN -0.5
9   美联储劳动力市场状况指数  2015-07-06  0.8  2.0  0.9
10  美联储劳动力市场状况指数  2015-08-10  1.1  1.0  1.4
11  美联储劳动力市场状况指数  2015-09-08  2.1  1.5  1.8
12  美联储劳动力市场状况指数  2015-10-05  0.0  NaN  1.2
13  美联储劳动力市场状况指数  2015-11-09  1.6  0.9  1.3
14  美联储劳动力市场状况指数  2015-12-07  0.5  1.6  2.2
15  美联储劳动力市场状况指数  2016-01-11  2.9  0.0  2.7
16  美联储劳动力市场状况指数  2016-02-08  0.4  2.5  2.3
17  美联储劳动力市场状况指数  2016-03-07 -2.4  1.0 -0.8
18  美联储劳动力市场状况指数  2016-04-04 -2.1  1.5 -2.5
19  美联储劳动力市场状况指数  2016-05-09 -0.9 -1.0 -2.1
20  美联储劳动力市场状况指数  2016-06-06 -4.8 -0.8 -3.4
21  美联储劳动力市场状况指数  2016-07-11 -1.9  0.0 -3.6
22  美联储劳动力市场状况指数  2016-08-08  1.0  NaN -0.1
23  美联储劳动力市场状况指数  2016-09-06 -0.7  0.0  1.0
24  美联储劳动力市场状况指数  2016-10-11 -2.2  NaN -1.3
25  美联储劳动力市场状况指数  2016-11-07  0.7  NaN -0.1
26  美联储劳动力市场状况指数  2016-12-05  1.5  NaN  1.4
27  美联储劳动力市场状况指数  2017-01-09 -0.3  NaN  2.1
28  美联储劳动力市场状况指数  2017-02-06  1.3  NaN  0.6
29  美联储劳动力市场状况指数  2017-03-17  1.3  NaN  1.3
30  美联储劳动力市场状况指数  2017-04-10  0.4  NaN  1.5
31  美联储劳动力市场状况指数  2017-05-08  3.5  NaN  3.6
32  美联储劳动力市场状况指数  2017-06-16  2.3  3.0  3.5
33  美联储劳动力市场状况指数  2017-07-10  1.5  2.5  3.3
```

##### 失业率

###### 美国失业率报告

接口: macro_usa_unemployment_rate

目标地址: https://datacenter.jin10.com/reportType/dc_usa_unemployment_rate

描述: 美国失业率报告, 数据区间从 19700101-至今

限量: 单次返回所有历史数据

输入参数

| 名称 | 类型   | 描述 |
|----|------|----|
| -  | -  - |

输出参数

| 名称  | 类型      | 描述      |
|-----|---------|---------|
| 商品  | object  | -       |
| 日期  | object  | -       |
| 今值  | float64 | 注意单位: % |
| 预测值 | float64 | 注意单位: % |
| 前值  | float64 | 注意单位: % |

接口示例

```python
import akshare as ak

macro_usa_unemployment_rate_df = ak.macro_usa_unemployment_rate()
print(macro_usa_unemployment_rate_df)
```

数据示例

```
        商品          日期   今值  预测值   前值
0    美国失业率  1970-01-01  3.5  NaN  3.5
1    美国失业率  1970-02-01  3.9  NaN  3.5
2    美国失业率  1970-03-01  4.2  NaN  3.9
3    美国失业率  1970-04-01  4.4  NaN  4.2
4    美国失业率  1970-05-01  4.6  NaN  4.4
..     ...         ...  ...  ...  ...
647  美国失业率  2023-12-08  3.7  3.9  3.9
648  美国失业率  2024-01-05  3.7  3.8  3.7
649  美国失业率  2024-02-02  3.7  3.8  3.7
650  美国失业率  2024-03-08  3.9  3.7  3.7
651  美国失业率  2024-04-05  NaN  3.9  3.9
[652 rows x 5 columns]
```

###### 美国挑战者企业裁员人数报告

接口: macro_usa_job_cuts

目标地址: https://datacenter.jin10.com/reportType/dc_usa_job_cuts

描述: 美国挑战者企业裁员人数报告, 数据区间从 19940201-至今

限量: 单次返回所有历史数据

输入参数

| 名称 | 类型   | 描述 |
|----|------|----|
| -  | -  - |

输出参数

| 名称  | 类型      | 描述       |
|-----|---------|----------|
| 商品  | object  | -        |
| 日期  | object  | -        |
| 今值  | float64 | 注意单位: 万人 |
| 预测值 | float64 | 注意单位: 万人 |
| 前值  | float64 | 注意单位: 万人 |

接口示例

```python
import akshare as ak

macro_usa_job_cuts_df = ak.macro_usa_job_cuts()
print(macro_usa_job_cuts_df)
```

数据示例

```
              商品          日期       今值  预测值       前值
0    美国挑战者企业裁员人数  1994-02-01  10.8900  NaN      NaN
1    美国挑战者企业裁员人数  1994-03-01   3.4600  NaN  10.8900
2    美国挑战者企业裁员人数  1994-04-01   4.9000  NaN   3.4600
3    美国挑战者企业裁员人数  1994-05-01   3.6100  NaN   4.9000
4    美国挑战者企业裁员人数  1994-06-01   3.6300  NaN   3.6100
..           ...         ...      ...  ...      ...
360  美国挑战者企业裁员人数  2023-12-07   4.5510  NaN   3.6836
361  美国挑战者企业裁员人数  2024-01-04   3.4817  NaN   4.5510
362  美国挑战者企业裁员人数  2024-02-01   8.2307  NaN   3.4817
363  美国挑战者企业裁员人数  2024-03-07   8.4638  NaN   8.2307
364  美国挑战者企业裁员人数  2024-04-04   9.0309  NaN   8.4638
[365 rows x 5 columns]
```

##### 就业人口

###### 美国非农就业人数报告

接口: macro_usa_non_farm

目标地址: https://datacenter.jin10.com/reportType/dc_nonfarm_payrolls

描述: 美国非农就业人数报告, 数据区间从 19700102-至今

限量: 单次返回所有历史数据

输入参数

| 名称 | 类型   | 描述 |
|----|------|----|
| -  | -  - |

输出参数

| 名称  | 类型      | 描述       |
|-----|---------|----------|
| 商品  | object  | -        |
| 日期  | object  | -        |
| 今值  | float64 | 注意单位: 万人 |
| 预测值 | float64 | 注意单位: 万人 |
| 前值  | float64 | 注意单位: 万人 |

接口示例

```python
import akshare as ak

macro_usa_non_farm_df = ak.macro_usa_non_farm()
print(macro_usa_non_farm_df)
```

数据示例

```
           商品          日期    今值   预测值    前值
0    美国非农就业人数  1970-01-02  15.3   NaN  -3.3
1    美国非农就业人数  1970-02-06  -6.4   NaN  15.3
2    美国非农就业人数  1970-03-06  12.8   NaN  -6.4
3    美国非农就业人数  1970-04-03  14.8   NaN  12.8
4    美国非农就业人数  1970-05-01 -10.4   NaN  14.8
..        ...         ...   ...   ...   ...
647  美国非农就业人数  2023-12-08  19.9  18.0  15.0
648  美国非农就业人数  2024-01-05  21.6  17.0  17.3
649  美国非农就业人数  2024-02-02  35.3  18.7  33.3
650  美国非农就业人数  2024-03-08  27.5  19.8  22.9
651  美国非农就业人数  2024-04-05   NaN  21.2  27.5
[652 rows x 5 columns]
```

###### 美国ADP就业人数报告

接口: macro_usa_adp_employment

目标地址: https://datacenter.jin10.com/reportType/dc_adp_nonfarm_employment

描述: 美国 ADP 就业人数报告, 数据区间从 20010601-至今

限量: 单次返回所有历史数据

输入参数

| 名称 | 类型   | 描述 |
|----|------|----|
| -  | -  - |

输出参数

| 名称  | 类型      | 描述       |
|-----|---------|----------|
| 商品  | object  | -        |
| 日期  | object  | -        |
| 今值  | float64 | 注意单位: 万人 |
| 预测值 | float64 | 注意单位: 万人 |
| 前值  | float64 | 注意单位: 万人 |

接口示例

```python
import akshare as ak

macro_usa_adp_employment_df = ak.macro_usa_adp_employment()
print(macro_usa_adp_employment_df)
```

数据示例

```
            商品          日期    今值   预测值    前值
0    美国ADP就业人数  2001-06-01 -17.5   NaN   NaN
1    美国ADP就业人数  2001-07-01 -23.0   NaN -17.5
2    美国ADP就业人数  2001-08-01 -20.3   NaN -23.0
3    美国ADP就业人数  2001-09-01 -24.6   NaN -20.3
4    美国ADP就业人数  2001-10-01 -26.1   NaN -24.6
..         ...         ...   ...   ...   ...
271  美国ADP就业人数  2023-12-06  10.3  13.0  10.6
272  美国ADP就业人数  2024-01-04  16.4  11.5  10.1
273  美国ADP就业人数  2024-01-31  10.7  14.5  15.8
274  美国ADP就业人数  2024-03-06  14.0  14.9  11.1
275  美国ADP就业人数  2024-04-03  18.4  14.8  15.5
[276 rows x 5 columns]
```

##### 消费者收入与支出

###### 美国核心PCE物价指数年率报告

接口: macro_usa_core_pce_price

目标地址: https://datacenter.jin10.com/reportType/dc_usa_core_pce_price

描述: 美国核心 PCE 物价指数年率报告, 数据区间从 19700101-至今

限量: 单次返回所有历史数据

输入参数

| 名称 | 类型   | 描述 |
|----|------|----|
| -  | -  - |

输出参数

| 名称  | 类型      | 描述      |
|-----|---------|---------|
| 商品  | object  | -       |
| 日期  | object  | -       |
| 今值  | float64 | 注意单位: % |
| 预测值 | float64 | 注意单位: % |
| 前值  | float64 | 注意单位: % |

接口示例

```python
import akshare as ak

macro_usa_core_pce_price_df = ak.macro_usa_core_pce_price()
print(macro_usa_core_pce_price_df)
```

数据示例

```
                商品          日期   今值  预测值   前值
0    美国核心PCE物价指数年率  1970-01-01  4.8  NaN  NaN
1    美国核心PCE物价指数年率  1970-02-01  4.7  NaN  4.8
2    美国核心PCE物价指数年率  1970-03-01  4.8  NaN  4.7
3    美国核心PCE物价指数年率  1970-04-01  4.7  NaN  4.8
4    美国核心PCE物价指数年率  1970-05-01  4.7  NaN  4.7
..             ...         ...  ...  ...  ...
649  美国核心PCE物价指数年率  2023-11-30  3.5  3.5  3.7
650  美国核心PCE物价指数年率  2023-12-22  3.2  3.3  3.4
651  美国核心PCE物价指数年率  2024-01-26  2.9  3.0  3.2
652  美国核心PCE物价指数年率  2024-02-29  2.8  2.8  2.9
653  美国核心PCE物价指数年率  2024-03-29  2.8  2.8  2.9
[654 rows x 5 columns]
```

###### 美国实际个人消费支出季率初值报告

接口: macro_usa_real_consumer_spending

目标地址: https://datacenter.jin10.com/reportType/dc_usa_real_consumer_spending

描述: 美国实际个人消费支出季率初值报告, 数据区间从 20131107-至今

限量: 单次返回所有历史数据

输入参数

| 名称 | 类型   | 描述 |
|----|------|----|
| -  | -  - |

输出参数

| 名称  | 类型      | 描述      |
|-----|---------|---------|
| 商品  | object  | -       |
| 日期  | object  | -       |
| 今值  | float64 | 注意单位: % |
| 预测值 | float64 | 注意单位: % |
| 前值  | float64 | 注意单位: % |

接口示例

```python
import akshare as ak

macro_usa_real_consumer_spending_df = ak.macro_usa_real_consumer_spending()
print(macro_usa_real_consumer_spending_df)
```

数据示例

```
                 商品          日期   今值  预测值   前值
0    美国实际个人消费支出季率初值  2013-11-07  1.5  1.6  1.8
1    美国实际个人消费支出季率初值  2013-12-05  1.4  1.5  1.5
2    美国实际个人消费支出季率初值  2013-12-20  2.0  1.4  1.4
3    美国实际个人消费支出季率初值  2014-01-30  3.3  3.7  2.0
4    美国实际个人消费支出季率初值  2014-02-28  2.6  2.9  3.3
..              ...         ...  ...  ...  ...
124  美国实际个人消费支出季率初值  2023-12-21  3.1  3.6  3.6
125  美国实际个人消费支出季率初值  2024-01-25  2.8  NaN  3.1
126  美国实际个人消费支出季率初值  2024-02-28  3.0  2.8  3.1
127  美国实际个人消费支出季率初值  2024-03-28  3.3  3.0  3.1
128  美国实际个人消费支出季率初值  2024-04-25  NaN  NaN  3.3
[129 rows x 5 columns]
```

#### 贸易状况

##### 美国贸易帐报告

接口: macro_usa_trade_balance

目标地址: https://datacenter.jin10.com/reportType/dc_usa_trade_balance

描述: 美国贸易帐报告, 数据区间从 19700101-至今

限量: 单次返回所有历史数据

输入参数

| 名称 | 类型   | 描述 |
|----|------|----|
| -  | -  - |

输出参数

| 名称  | 类型      | 描述        |
|-----|---------|-----------|
| 商品  | object  | -         |
| 日期  | object  | -         |
| 今值  | float64 | 注意单位: 亿美元 |
| 预测值 | float64 | 注意单位: 亿美元 |
| 前值  | float64 | 注意单位: 亿美元 |

接口示例

```python
import akshare as ak

macro_usa_trade_balance_df = ak.macro_usa_trade_balance()
print(macro_usa_trade_balance_df)
```

数据示例

```
          商品          日期     今值    预测值     前值
0    美国贸易帐报告  1970-01-01    2.0    NaN    NaN
1    美国贸易帐报告  1970-02-01    1.0    NaN    2.0
2    美国贸易帐报告  1970-03-01    2.0    NaN    1.0
3    美国贸易帐报告  1970-04-01    1.0    NaN    2.0
4    美国贸易帐报告  1970-05-01    1.0    NaN    1.0
..       ...         ...    ...    ...    ...
648  美国贸易帐报告  2023-12-06 -643.0 -642.0 -612.0
649  美国贸易帐报告  2024-01-09 -632.0 -650.0 -645.0
650  美国贸易帐报告  2024-02-07 -622.0 -620.0 -619.0
651  美国贸易帐报告  2024-03-07 -674.0 -634.0 -642.0
652  美国贸易帐报告  2024-04-04 -689.0 -669.0 -676.0
[653 rows x 5 columns]
```

##### 美国经常帐报告

接口: macro_usa_current_account

目标地址: https://datacenter.jin10.com/reportType/dc_usa_current_account

描述: 美国经常帐报告, 数据区间从 ********-至今

限量: 单次返回所有历史数据

输入参数

| 名称 | 类型   | 描述 |
|----|------|----|
| -  | -  - |

输出参数

| 名称  | 类型      | 描述        |
|-----|---------|-----------|
| 商品  | object  | -         |
| 日期  | object  | -         |
| 今值  | float64 | 注意单位: 亿美元 |
| 预测值 | float64 | 注意单位: 亿美元 |
| 前值  | float64 | 注意单位: 亿美元 |

接口示例

```python
import akshare as ak

macro_usa_current_account_df = ak.macro_usa_current_account()
print(macro_usa_current_account_df)
```

数据示例

```
         商品          日期    今值     预测值    前值
0   美国经常账报告  2008-03-17 -1730 -1840.0 -1790
1   美国经常账报告  2008-06-17 -1760     NaN -1730
2   美国经常账报告  2008-09-17 -1830 -1800.0 -1760
3   美国经常账报告  2008-12-17 -1740 -1800.0 -1830
4   美国经常账报告  2009-03-18 -1330 -1370.0 -1740
..      ...         ...   ...     ...   ...
60  美国经常账报告  2023-03-23 -2068 -2132.0 -2190
61  美国经常账报告  2023-06-22 -2193 -2169.0 -2162
62  美国经常账报告  2023-09-21 -2121 -2210.0 -2145
63  美国经常账报告  2023-12-20 -2003 -1970.0 -2168
64  美国经常账报告  2024-03-21 -1948 -2090.0 -1964
[65 rows x 5 columns]
```

#### 产业指标

##### 制造业

###### 贝克休斯钻井报告

接口: macro_usa_rig_count

目标地址: https://datacenter.jin10.com/reportType/dc_rig_count_summary

描述: 贝克休斯钻井报告, 数据区间从 19870717-至今

限量: 单次返回所有历史数据

输入参数

| 名称 | 类型 | 描述 |
|----|----|----|
| -  | -  | -  |

输出参数

| 名称          | 类型      | 描述 |
|-------------|---------|----|
| 日期          | object  | -  |
| 钻井总数_钻井数    | float64 | -  |
| 钻井总数_变化     | float64 | -  |
| 美国石油钻井_钻井数  | float64 | -  |
| 美国石油钻井_变化   | float64 | -  |
| 混合钻井_钻井数    | float64 | -  |
| 混合钻井_变化     | float64 | -  |
| 美国天然气钻井_钻井数 | float64 | -  |
| 美国天然气钻井_变化  | float64 | -  |

接口示例

```python
import akshare as ak

macro_usa_rig_count_df = ak.macro_usa_rig_count()
print(macro_usa_rig_count_df)
```

数据示例

```
              日期  钻井总数_钻井数  钻井总数_变化  ...  混合钻井_变化  美国天然气钻井_钻井数  美国天然气钻井_变化
0     1987-07-17     922.0      NaN  ...      NaN        337.0         NaN
1     1987-07-24     917.0     -5.0  ...     -5.0        331.0        -6.0
2     1987-07-31     973.0     56.0  ...      1.0        346.0        15.0
3     1987-08-07     983.0     10.0  ...     -1.0        349.0         3.0
4     1987-08-14     998.0     15.0  ...      5.0        352.0         3.0
          ...       ...      ...  ...      ...          ...         ...
1891  2024-01-26     621.0      1.0  ...      0.0        119.0        -1.0
1892  2024-02-02     619.0     -2.0  ...      0.0        117.0        -2.0
1893  2024-02-09     623.0      4.0  ...      0.0        121.0         4.0
1894  2024-02-16     621.0     -2.0  ...      0.0        121.0         0.0
1895  2024-02-23     626.0      5.0  ...      0.0        120.0        -1.0
[1896 rows x 9 columns]
```

###### 美国生产者物价指数(PPI)报告

接口: macro_usa_ppi

目标地址: https://datacenter.jin10.com/reportType/dc_usa_ppi

描述: 美国生产者物价指数(PPI)报告, 数据区间从 20080226-至今

限量: 单次返回所有历史数据

输入参数

| 名称 | 类型   | 描述 |
|----|------|----|
| -  | -  - |

输出参数

| 名称  | 类型      | 描述      |
|-----|---------|---------|
| 商品  | object  | -       |
| 日期  | object  | -       |
| 今值  | float64 | 注意单位: % |
| 预测值 | float64 | 注意单位: % |
| 前值  | float64 | 注意单位: % |

接口示例

```python
import akshare as ak

macro_usa_ppi_df = ak.macro_usa_ppi()
print(macro_usa_ppi_df)
```

数据示例

```
            商品          日期   今值  预测值   前值
0    美国生产者物价指数  2008-02-26  1.0  0.4 -0.3
1    美国生产者物价指数  2008-03-18  0.3  0.3  1.0
2    美国生产者物价指数  2008-04-15  1.1  0.6  0.3
3    美国生产者物价指数  2008-05-20  0.2  0.4  1.1
4    美国生产者物价指数  2008-06-17  1.4  1.0  0.2
..         ...         ...  ...  ...  ...
191  美国生产者物价指数  2023-12-13  0.0  0.1 -0.4
192  美国生产者物价指数  2024-01-12 -0.1  0.1 -0.1
193  美国生产者物价指数  2024-02-16  0.3  0.1 -0.1
194  美国生产者物价指数  2024-03-14  0.6  0.3  0.3
195  美国生产者物价指数  2024-04-11  NaN  0.3  0.6
[196 rows x 5 columns]
```

###### 美国核心生产者物价指数(PPI)报告

接口: macro_usa_core_ppi

目标地址: https://datacenter.jin10.com/reportType/dc_usa_core_ppi

描述: 美国核心生产者物价指数(PPI)报告, 数据区间从 20080318-至今

限量: 单次返回所有历史数据

输入参数

| 名称 | 类型   | 描述 |
|----|------|----|
| -  | -  - |

输出参数

| 名称  | 类型      | 描述      |
|-----|---------|---------|
| 商品  | object  | -       |
| 日期  | object  | -       |
| 今值  | float64 | 注意单位: % |
| 预测值 | float64 | 注意单位: % |
| 前值  | float64 | 注意单位: % |

接口示例

```python
import akshare as ak

macro_usa_core_ppi_df = ak.macro_usa_core_ppi()
print(macro_usa_core_ppi_df)
```

数据示例

```
              商品          日期   今值  预测值   前值
0    美国核心生产者物价指数  2008-03-18  0.5  0.2  0.4
1    美国核心生产者物价指数  2008-04-15  0.2  0.2  0.5
2    美国核心生产者物价指数  2008-05-20  0.4  0.2  0.2
3    美国核心生产者物价指数  2008-06-17  0.2  0.2  0.4
4    美国核心生产者物价指数  2008-08-19  0.7  0.2  0.2
..           ...         ...  ...  ...  ...
188  美国核心生产者物价指数  2023-12-13  0.0  0.2  0.0
189  美国核心生产者物价指数  2024-01-12  0.0  0.2  0.0
190  美国核心生产者物价指数  2024-02-16  0.5  0.1 -0.1
191  美国核心生产者物价指数  2024-03-14  0.3  0.2  0.5
192  美国核心生产者物价指数  2024-04-11  NaN  0.2  0.3
[193 rows x 5 columns]
```

###### 美国 API 原油库存报告

接口: macro_usa_api_crude_stock

目标地址: https://datacenter.jin10.com/reportType/dc_usa_api_crude_stock

描述: 美国 API 原油库存报告, 数据区间从 20120328-至今

限量: 单次返回所有历史数据

输入参数

| 名称 | 类型 | 描述 |
|----|----|----|
| -  | -  | -  |

输出参数

| 名称  | 类型      | 描述       |
|-----|---------|----------|
| 商品  | object  | -        |
| 日期  | object  | -        |
| 今值  | float64 | 注意单位: 万桶 |
| 预测值 | float64 | 注意单位: 万桶 |
| 前值  | float64 | 注意单位: 万桶 |

接口示例

```python
import akshare as ak

macro_usa_api_crude_stock_df = ak.macro_usa_api_crude_stock()
print(macro_usa_api_crude_stock_df)
```

数据示例

```
            商品          日期     今值    预测值     前值
0    美国API原油库存  2012-03-28  360.0  277.0 -137.0
1    美国API原油库存  2012-04-04  785.0  221.0  360.0
2    美国API原油库存  2012-04-11  658.0  208.0  785.0
3    美国API原油库存  2012-04-18  341.0  157.0  658.0
4    美国API原油库存  2012-04-25  -99.0  293.0  341.0
..         ...         ...    ...    ...    ...
624  美国API原油库存  2024-03-13 -552.1   40.0   42.3
625  美国API原油库存  2024-03-20 -151.9    7.7 -552.1
626  美国API原油库存  2024-03-27  933.7    NaN -151.9
627  美国API原油库存  2024-04-03 -228.6 -200.0  933.7
628  美国API原油库存  2024-04-10    NaN    NaN -228.6
[629 rows x 5 columns]
```

###### 美国Markit制造业PMI初值报告

接口: macro_usa_pmi

目标地址: https://datacenter.jin10.com/reportType/dc_usa_pmi

描述: 美国 Markit 制造业 PMI 初值报告, 数据区间从 20120601-至今

限量: 单次返回所有历史数据

输入参数

| 名称 | 类型   | 描述 |
|----|------|----|
| -  | -  - |

输出参数

| 名称  | 类型      | 描述 |
|-----|---------|----|
| 商品  | object  | -  |
| 日期  | object  | -  |
| 今值  | float64 | -  |
| 预测值 | float64 | -  |
| 前值  | float64 | -  |

接口示例

```python
import akshare as ak

macro_usa_pmi_df = ak.macro_usa_pmi()
print(macro_usa_pmi_df)
```

数据示例

```
                   商品          日期    今值   预测值    前值
0    美国Markit制造业PMI报告  2012-06-01  54.0   NaN  53.9
1    美国Markit制造业PMI报告  2012-07-02  52.5  53.0  52.9
2    美国Markit制造业PMI报告  2012-07-24  51.8  52.1  52.5
3    美国Markit制造业PMI报告  2012-08-01  51.4  51.9  51.8
4    美国Markit制造业PMI报告  2012-08-23  51.9  51.3  51.4
..                ...         ...   ...   ...   ...
280  美国Markit制造业PMI报告  2024-02-22  51.5  50.5  50.7
281  美国Markit制造业PMI报告  2024-03-01  52.2  51.5  50.7
282  美国Markit制造业PMI报告  2024-03-21  52.5  51.8  52.2
283  美国Markit制造业PMI报告  2024-04-01  51.9  52.5  52.2
284  美国Markit制造业PMI报告  2024-04-23   NaN   NaN  51.9
[285 rows x 5 columns]
```

###### 美国ISM制造业PMI报告

接口: macro_usa_ism_pmi

目标地址: https://datacenter.jin10.com/reportType/dc_usa_ism_pmi

描述: 美国 ISM 制造业 PMI 报告, 数据区间从 19700101-至今

限量: 单次返回所有历史数据

输入参数

| 名称 | 类型 | 描述 |
|----|----|----|
| -  | -  | -  |

输出参数

| 名称  | 类型      | 描述 |
|-----|---------|----|
| 商品  | object  | -  |
| 日期  | object  | -  |
| 今值  | float64 | -  |
| 预测值 | float64 | -  |
| 前值  | float64 | -  |

接口示例

```python
import akshare as ak

macro_usa_ism_pmi_df = ak.macro_usa_ism_pmi()
print(macro_usa_ism_pmi_df)
```

数据示例

```
                商品          日期    今值   预测值    前值
0    美国ISM制造业PMI报告  1970-01-01  52.0   NaN   NaN
1    美国ISM制造业PMI报告  1970-02-01  48.7   NaN  52.0
2    美国ISM制造业PMI报告  1970-03-01  47.4   NaN  48.7
3    美国ISM制造业PMI报告  1970-04-01  46.9   NaN  47.4
4    美国ISM制造业PMI报告  1970-05-01  45.0   NaN  46.9
..             ...         ...   ...   ...   ...
648  美国ISM制造业PMI报告  2023-12-01  46.7  47.6  46.7
649  美国ISM制造业PMI报告  2024-01-03  47.4  47.1  46.7
650  美国ISM制造业PMI报告  2024-02-01  49.1  47.2  47.1
651  美国ISM制造业PMI报告  2024-03-01  47.8  49.5  49.1
652  美国ISM制造业PMI报告  2024-04-01  50.3  48.5  47.8
[653 rows x 5 columns]
```

##### 工业

###### 美国工业产出月率报告

接口: macro_usa_industrial_production

目标地址: https://datacenter.jin10.com/reportType/dc_usa_industrial_production

描述: 美国工业产出月率报告, 数据区间从 19700101-至今

限量: 单次返回所有历史数据

输入参数

| 名称 | 类型 | 描述 |
|----|----|----|
| -  | -  | -  |

输出参数

| 名称  | 类型      | 描述      |
|-----|---------|---------|
| 商品  | object  | -       |
| 日期  | object  | -       |
| 今值  | float64 | 注意单位: % |
| 预测值 | float64 | 注意单位: % |
| 前值  | float64 | 注意单位: % |

接口示例

```python
import akshare as ak

macro_usa_industrial_production_df = ak.macro_usa_industrial_production()
print(macro_usa_industrial_production_df)
```

数据示例

```
             商品          日期   今值  预测值   前值
0    美国工业产出月率报告  1970-01-01 -0.3  NaN  NaN
1    美国工业产出月率报告  1970-02-01 -1.9  NaN -0.3
2    美国工业产出月率报告  1970-03-01 -0.1  NaN -1.9
3    美国工业产出月率报告  1970-04-01 -0.1  NaN -0.1
4    美国工业产出月率报告  1970-05-01 -0.3  NaN -0.1
..          ...         ...  ...  ...  ...
647  美国工业产出月率报告  2023-12-15  0.2  0.3 -0.9
648  美国工业产出月率报告  2024-01-17  0.1  0.0  0.0
649  美国工业产出月率报告  2024-02-15 -0.1  0.2  0.1
650  美国工业产出月率报告  2024-03-15  0.1  0.0 -0.5
651  美国工业产出月率报告  2024-04-16  NaN  NaN  0.1
[652 rows x 5 columns]
```

###### 美国耐用品订单月率报告

接口: macro_usa_durable_goods_orders

目标地址: https://datacenter.jin10.com/reportType/dc_usa_durable_goods_orders

描述: 美国耐用品订单月率报告, 数据区间从 20080227-至今

限量: 单次返回所有历史数据

输入参数

| 名称 | 类型 | 描述 |
|----|----|----|
| -  | -  | -  |

输出参数

| 名称  | 类型      | 描述      |
|-----|---------|---------|
| 商品  | object  | -       |
| 日期  | object  | -       |
| 今值  | float64 | 注意单位: % |
| 预测值 | float64 | 注意单位: % |
| 前值  | float64 | 注意单位: % |

接口示例

```python
import akshare as ak

macro_usa_durable_goods_orders_df = ak.macro_usa_durable_goods_orders()
print(macro_usa_durable_goods_orders_df)
```

数据示例

```
              商品          日期   今值  预测值   前值
0    美国耐用品订单月率报告  2008-02-27 -5.3 -4.0  5.0
1    美国耐用品订单月率报告  2008-03-26 -1.7  0.8 -5.3
2    美国耐用品订单月率报告  2008-04-24 -0.3  0.0 -1.1
3    美国耐用品订单月率报告  2008-05-28 -0.5 -1.5 -0.3
4    美国耐用品订单月率报告  2008-06-25  0.0  0.0 -0.6
..           ...         ...  ...  ...  ...
198  美国耐用品订单月率报告  2023-12-22  5.4  1.7 -5.1
199  美国耐用品订单月率报告  2024-01-25  0.0  1.1  5.5
200  美国耐用品订单月率报告  2024-02-27 -6.1 -4.9 -0.3
201  美国耐用品订单月率报告  2024-03-26  1.4  1.2 -6.9
202  美国耐用品订单月率报告  2024-04-24  NaN  NaN  1.3
[203 rows x 5 columns]
```

###### 美国工厂订单月率报告

接口: macro_usa_factory_orders

目标地址: https://datacenter.jin10.com/reportType/dc_usa_factory_orders

描述: 美国工厂订单月率报告, 数据区间从 19920401-至今

限量: 单次返回所有历史数据

输入参数

| 名称 | 类型 | 描述 |
|----|----|----|
| -  | -  | -  |

输出参数

| 名称  | 类型      | 描述      |
|-----|---------|---------|
| 商品  | object  | -       |
| 日期  | object  | -       |
| 今值  | float64 | 注意单位: % |
| 预测值 | float64 | 注意单位: % |
| 前值  | float64 | 注意单位: % |

接口示例

```python
import akshare as ak

macro_usa_factory_orders_df = ak.macro_usa_factory_orders()
print(macro_usa_factory_orders_df)
```

数据示例

```
             商品          日期   今值  预测值   前值
0    美国工厂订单月率报告  1992-04-01  4.6  NaN  NaN
1    美国工厂订单月率报告  1992-05-01  1.9  NaN  4.6
2    美国工厂订单月率报告  1992-06-01  1.6  NaN  1.9
3    美国工厂订单月率报告  1992-07-01 -0.5  NaN  1.6
4    美国工厂订单月率报告  1992-08-01 -0.9  NaN -0.5
..          ...         ...  ...  ...  ...
381  美国工厂订单月率报告  2023-12-04 -3.6 -2.8  2.3
382  美国工厂订单月率报告  2024-01-05  2.6  2.1 -3.4
383  美国工厂订单月率报告  2024-02-02  0.2  0.3  2.6
384  美国工厂订单月率报告  2024-03-05 -3.6 -3.1 -0.3
385  美国工厂订单月率报告  2024-04-02  1.4  1.1 -3.8
[386 rows x 5 columns]
```

##### 服务业

###### 美国Markit服务业PMI初值报告

接口: macro_usa_services_pmi

目标地址: https://datacenter.jin10.com/reportType/dc_usa_services_pmi

描述: 美国Markit服务业PMI初值报告, 数据区间从 20120701-至今

限量: 单次返回所有历史数据

输入参数

| 名称 | 类型 | 描述 |
|----|----|----|
| -  | -  | -  |

输出参数

| 名称  | 类型      | 描述 |
|-----|---------|----|
| 商品  | object  | -  |
| 日期  | object  | -  |
| 今值  | float64 | -  |
| 预测值 | float64 | -  |
| 前值  | float64 | -  |

接口示例

```python
import akshare as ak

macro_usa_services_pmi_df = ak.macro_usa_services_pmi()
print(macro_usa_services_pmi_df)
```

数据示例

```
                     商品          日期    今值   预测值    前值
0    美国Markit服务业PMI初值报告  2012-07-01  53.2   NaN   NaN
1    美国Markit服务业PMI初值报告  2012-08-01  51.2   NaN  53.2
2    美国Markit服务业PMI初值报告  2012-09-01  52.0   NaN  51.2
3    美国Markit服务业PMI初值报告  2012-10-01  50.7   NaN  52.0
4    美国Markit服务业PMI初值报告  2012-11-01  52.7   NaN  50.7
..                  ...         ...   ...   ...   ...
262  美国Markit服务业PMI初值报告  2024-02-22  51.3  52.4  52.5
263  美国Markit服务业PMI初值报告  2024-03-05  52.3  51.3  52.5
264  美国Markit服务业PMI初值报告  2024-03-21  51.7  52.0  52.3
265  美国Markit服务业PMI初值报告  2024-04-03  51.7  51.7  52.3
266  美国Markit服务业PMI初值报告  2024-04-23   NaN   NaN  51.7
[267 rows x 5 columns]
```

###### 美国商业库存月率报告

接口: macro_usa_business_inventories

目标地址: https://datacenter.jin10.com/reportType/dc_usa_business_inventories

描述: 美国商业库存月率报告, 数据区间从 19920301-至今

限量: 单次返回所有历史数据

输入参数

| 名称 | 类型 | 描述 |
|----|----|----|
| -  | -  | -  |

输出参数

| 名称  | 类型      | 描述      |
|-----|---------|---------|
| 商品  | object  | -       |
| 日期  | object  | -       |
| 今值  | float64 | 注意单位: % |
| 预测值 | float64 | 注意单位: % |
| 前值  | float64 | 注意单位: % |

接口示例

```python
import akshare as ak

macro_usa_business_inventories_df = ak.macro_usa_business_inventories()
print(macro_usa_business_inventories_df)
```

数据示例

```
             商品          日期   今值  预测值   前值
0    美国商业库存月率报告  1992-03-01  0.2  NaN  NaN
1    美国商业库存月率报告  1992-04-01  0.4  NaN  0.2
2    美国商业库存月率报告  1992-05-01  0.3  NaN  0.4
3    美国商业库存月率报告  1992-06-01 -0.1  NaN  0.3
4    美国商业库存月率报告  1992-07-01  0.7  NaN -0.1
..          ...         ...  ...  ...  ...
381  美国商业库存月率报告  2023-12-14 -0.1  0.0  0.2
382  美国商业库存月率报告  2024-01-17 -0.1 -0.1 -0.1
383  美国商业库存月率报告  2024-02-15  0.4  0.4 -0.1
384  美国商业库存月率报告  2024-03-14  0.0  0.2  0.3
385  美国商业库存月率报告  2024-04-15  NaN  NaN  0.3
[386 rows x 5 columns]
```

###### 美国ISM非制造业PMI报告

接口: macro_usa_ism_non_pmi

目标地址: https://datacenter.jin10.com/reportType/dc_usa_ism_non_pmi

描述: 美国 ISM 非制造业 PMI 报告, 数据区间从 19970801-至今

限量: 单次返回所有历史数据

输入参数

| 名称 | 类型 | 描述 |
|----|----|----|
| -  | -  | -  |

输出参数

| 名称  | 类型      | 描述 |
|-----|---------|----|
| 商品  | object  | -  |
| 日期  | object  | -  |
| 今值  | float64 | -  |
| 预测值 | float64 | -  |
| 前值  | float64 | -  |

接口示例

```python
import akshare as ak

macro_usa_ism_non_pmi_df = ak.macro_usa_ism_non_pmi()
print(macro_usa_ism_non_pmi_df)
```

数据示例

```
                 商品          日期    今值   预测值    前值
0    美国ISM非制造业PMI报告  1997-08-01  56.7   NaN   NaN
1    美国ISM非制造业PMI报告  1997-09-01  62.0   NaN  56.7
2    美国ISM非制造业PMI报告  1997-10-01  56.2   NaN  62.0
3    美国ISM非制造业PMI报告  1997-11-01  56.6   NaN  56.2
4    美国ISM非制造业PMI报告  1997-12-01  58.5   NaN  56.6
..              ...         ...   ...   ...   ...
319  美国ISM非制造业PMI报告  2023-12-05  52.7  52.0  51.8
320  美国ISM非制造业PMI报告  2024-01-05  50.6  52.6  52.7
321  美国ISM非制造业PMI报告  2024-02-05  53.4  52.0  50.5
322  美国ISM非制造业PMI报告  2024-03-05  52.6  53.0  53.4
323  美国ISM非制造业PMI报告  2024-04-03  51.4  52.8  52.6
[324 rows x 5 columns]
```

##### 房地产

###### 美国NAHB房产市场指数报告

接口: macro_usa_nahb_house_market_index

目标地址: https://datacenter.jin10.com/reportType/dc_usa_nahb_house_market_index

描述: 美国 NAHB 房产市场指数报告, 数据区间从 19850201-至今

限量: 单次返回所有历史数据

输入参数

| 名称 | 类型 | 描述 |
|----|----|----|
| -  | -  | -  |

输出参数

| 名称  | 类型      | 描述 |
|-----|---------|----|
| 商品  | object  | -  |
| 日期  | object  | -  |
| 今值  | float64 | -  |
| 预测值 | float64 | -  |
| 前值  | float64 | -  |

接口示例

```python
import akshare as ak

macro_usa_nahb_house_market_index_df = ak.macro_usa_nahb_house_market_index()
print(macro_usa_nahb_house_market_index_df)
```

数据示例

```
                 商品          日期    今值   预测值    前值
0    美国NAHB房产市场指数报告  1985-02-01  50.0   NaN   NaN
1    美国NAHB房产市场指数报告  1985-03-01  58.0   NaN  50.0
2    美国NAHB房产市场指数报告  1985-04-01  54.0   NaN  58.0
3    美国NAHB房产市场指数报告  1985-05-01  49.0   NaN  54.0
4    美国NAHB房产市场指数报告  1985-06-01  51.0   NaN  49.0
..              ...         ...   ...   ...   ...
467  美国NAHB房产市场指数报告  2023-12-18  37.0  36.0  34.0
468  美国NAHB房产市场指数报告  2024-01-17  44.0  39.0  37.0
469  美国NAHB房产市场指数报告  2024-02-15  48.0  46.0  44.0
470  美国NAHB房产市场指数报告  2024-03-18  51.0  48.0  48.0
471  美国NAHB房产市场指数报告  2024-04-15   NaN   NaN  51.0
[472 rows x 5 columns]
```

###### 美国新屋开工总数年化报告

接口: macro_usa_house_starts

目标地址: https://datacenter.jin10.com/reportType/dc_usa_house_starts

描述: 美国新屋开工总数年化报告, 数据区间从 19700101-至今

限量: 单次返回所有历史数据

输入参数

| 名称 | 类型 | 描述 |
|----|----|----|
| -  | -  | -  |

输出参数

| 名称  | 类型      | 描述       |
|-----|---------|----------|
| 商品  | object  | -        |
| 日期  | object  | -        |
| 今值  | float64 | 注意单位: 万户 |
| 预测值 | float64 | 注意单位: 万户 |
| 前值  | float64 | 注意单位: 万户 |

接口示例

```python
import akshare as ak

macro_usa_house_starts_df = ak.macro_usa_house_starts()
print(macro_usa_house_starts_df)
```

数据示例

```
               商品          日期     今值    预测值     前值
0    美国新屋开工总数年化报告  1970-01-01  133.0    NaN    NaN
1    美国新屋开工总数年化报告  1970-02-01  109.0    NaN  133.0
2    美国新屋开工总数年化报告  1970-03-01  131.0    NaN  109.0
3    美国新屋开工总数年化报告  1970-04-01  132.0    NaN  131.0
4    美国新屋开工总数年化报告  1970-05-01  126.0    NaN  132.0
..            ...         ...    ...    ...    ...
645  美国新屋开工总数年化报告  2023-12-19  156.0  136.0  135.9
646  美国新屋开工总数年化报告  2024-01-18  146.0  142.6  152.5
647  美国新屋开工总数年化报告  2024-02-16  133.1  145.0  156.2
648  美国新屋开工总数年化报告  2024-03-19  152.1  143.0  137.4
649  美国新屋开工总数年化报告  2024-04-16    NaN    NaN  152.1
[650 rows x 5 columns]
```

###### 美国新屋销售总数年化报告

接口: macro_usa_new_home_sales

目标地址: https://datacenter.jin10.com/reportType/dc_usa_new_home_sales

描述: 美国新屋销售总数年化报告, 数据区间从 19700101-至今

限量: 单次返回所有历史数据

输入参数

| 名称 | 类型 | 描述 |
|----|----|----|
| -  | -  | -  |

输出参数

| 名称  | 类型      | 描述       |
|-----|---------|----------|
| 商品  | object  | -        |
| 日期  | object  | -        |
| 今值  | float64 | 注意单位: 万户 |
| 预测值 | float64 | 注意单位: 万户 |
| 前值  | float64 | 注意单位: 万户 |

接口示例

```python
import akshare as ak

macro_usa_new_home_sales_df = ak.macro_usa_new_home_sales()
print(macro_usa_new_home_sales_df)
```

数据示例

```
               商品          日期    今值   预测值    前值
0    美国新屋销售总数年化报告  1970-01-01  45.2   NaN   NaN
1    美国新屋销售总数年化报告  1970-02-01  46.1   NaN  45.2
2    美国新屋销售总数年化报告  1970-03-01  37.3   NaN  46.1
3    美国新屋销售总数年化报告  1970-04-01  38.9   NaN  37.3
4    美国新屋销售总数年化报告  1970-05-01  44.5   NaN  38.9
..            ...         ...   ...   ...   ...
646  美国新屋销售总数年化报告  2023-12-22  59.0  69.5  67.2
647  美国新屋销售总数年化报告  2024-01-25  66.4  64.5  61.5
648  美国新屋销售总数年化报告  2024-02-26  66.1  68.0  65.1
649  美国新屋销售总数年化报告  2024-03-25  66.2  67.5  66.4
650  美国新屋销售总数年化报告  2024-04-23   NaN   NaN  66.2
[651 rows x 5 columns]
```

###### 美国营建许可总数报告

接口: macro_usa_building_permits

目标地址: https://datacenter.jin10.com/reportType/dc_usa_building_permits

描述: 美国营建许可总数报告, 数据区间从 20080220-至今

限量: 单次返回所有历史数据

输入参数

| 名称 | 类型 | 描述 |
|----|----|----|
| -  | -  | -  |

输出参数

| 名称  | 类型      | 描述       |
|-----|---------|----------|
| 商品  | object  | -        |
| 日期  | object  | -        |
| 今值  | float64 | 注意单位: 万户 |
| 预测值 | float64 | 注意单位: 万户 |
| 前值  | float64 | 注意单位: 万户 |

接口示例

```python
import akshare as ak

macro_usa_building_permits_df = ak.macro_usa_building_permits()
print(macro_usa_building_permits_df)
```

数据示例

```
             商品          日期     今值    预测值     前值
0    美国营建许可总数报告  2008-02-20  105.0  105.0  107.0
1    美国营建许可总数报告  2008-03-18   98.0  105.0  106.0
2    美国营建许可总数报告  2008-04-16   93.0   97.0   98.0
3    美国营建许可总数报告  2008-05-16   98.0   92.0   93.0
4    美国营建许可总数报告  2008-06-17   97.0   96.0   98.0
..          ...         ...    ...    ...    ...
206  美国营建许可总数报告  2024-02-16  147.0  150.9  149.3
207  美国营建许可总数报告  2024-02-26  148.9  147.0  149.3
208  美国营建许可总数报告  2024-03-19  151.8  150.0  148.9
209  美国营建许可总数报告  2024-03-25  152.4  151.8  148.9
210  美国营建许可总数报告  2024-04-16    NaN    NaN  152.4
[211 rows x 5 columns]
```

###### 美国成屋销售总数年化报告

接口: macro_usa_exist_home_sales

目标地址: https://datacenter.jin10.com/reportType/dc_usa_exist_home_sales

描述: 美国成屋销售总数年化报告, 数据区间从 19700101-至今

限量: 单次返回所有历史数据

输入参数

| 名称 | 类型 | 描述 |
|----|----|----|
| -  | -  | -  |

输出参数

| 名称  | 类型      | 描述       |
|-----|---------|----------|
| 商品  | object  | -        |
| 日期  | object  | -        |
| 今值  | float64 | 注意单位: 万户 |
| 预测值 | float64 | 注意单位: 万户 |
| 前值  | float64 | 注意单位: 万户 |

接口示例

```python
import akshare as ak

macro_usa_exist_home_sales_df = ak.macro_usa_exist_home_sales()
print(macro_usa_exist_home_sales_df)
```

数据示例

```
               商品          日期     今值    预测值     前值
0    美国成屋销售总数年化报告  1970-01-01  160.0    NaN    NaN
1    美国成屋销售总数年化报告  1970-02-01  153.0    NaN  160.0
2    美国成屋销售总数年化报告  1970-03-01  146.0    NaN  153.0
3    美国成屋销售总数年化报告  1970-04-01  137.0    NaN  146.0
4    美国成屋销售总数年化报告  1970-05-01  151.0    NaN  137.0
..            ...         ...    ...    ...    ...
647  美国成屋销售总数年化报告  2023-12-20  382.0  378.0  379.0
648  美国成屋销售总数年化报告  2024-01-19  378.0  382.0  382.0
649  美国成屋销售总数年化报告  2024-02-22  400.0  396.0  388.0
650  美国成屋销售总数年化报告  2024-03-21  438.0  395.0  400.0
651  美国成屋销售总数年化报告  2024-04-18    NaN    NaN  438.0
[652 rows x 5 columns]
```

###### 美国FHFA房价指数月率报告

接口: macro_usa_house_price_index

目标地址: https://datacenter.jin10.com/reportType/dc_usa_house_price_index

描述: 美国 FHFA 房价指数月率报告, 数据区间从 ********-至今

限量: 单次返回所有历史数据

输入参数

| 名称 | 类型 | 描述 |
|----|----|----|
| -  | -  | -  |

输出参数

| 名称  | 类型      | 描述      |
|-----|---------|---------|
| 商品  | object  | -       |
| 日期  | object  | -       |
| 今值  | float64 | 注意单位: % |
| 预测值 | float64 | 注意单位: % |
| 前值  | float64 | 注意单位: % |

接口示例

```python
import akshare as ak

macro_usa_house_price_index_df = ak.macro_usa_house_price_index()
print(macro_usa_house_price_index_df)
```

数据示例

```
                 商品          日期   今值  预测值   前值
0    美国FHFA房价指数月率报告  1991-03-01  0.5  NaN  NaN
1    美国FHFA房价指数月率报告  1991-04-01  0.0  NaN  0.5
2    美国FHFA房价指数月率报告  1991-05-01 -0.2  NaN  0.0
3    美国FHFA房价指数月率报告  1991-06-01  0.1  NaN -0.2
4    美国FHFA房价指数月率报告  1991-07-01  0.1  NaN  0.1
..              ...         ...  ...  ...  ...
392  美国FHFA房价指数月率报告  2023-11-28  0.6  0.4  0.7
393  美国FHFA房价指数月率报告  2023-12-26  0.3  0.5  0.7
394  美国FHFA房价指数月率报告  2024-01-30  0.3  0.2  0.3
395  美国FHFA房价指数月率报告  2024-02-27  0.1  0.3  0.4
396  美国FHFA房价指数月率报告  2024-03-26 -0.1  0.2  0.1
[397 rows x 5 columns]
```

###### 美国S&P/CS20座大城市房价指数年率报告

接口: macro_usa_spcs20

目标地址: https://datacenter.jin10.com/reportType/dc_usa_spcs20

描述: 美国S&P/CS20座大城市房价指数年率报告, 数据区间从 20010201-至今

限量: 单次返回所有历史数据

输入参数

| 名称 | 类型 | 描述 |
|----|----|----|
| -  | -  | -  |

输出参数

| 名称  | 类型      | 描述      |
|-----|---------|---------|
| 商品  | object  | -       |
| 日期  | object  | -       |
| 今值  | float64 | 注意单位: % |
| 预测值 | float64 | 注意单位: % |
| 前值  | float64 | 注意单位: % |

接口示例

```python
import akshare as ak

macro_usa_spcs20_df = ak.macro_usa_spcs20()
print(macro_usa_spcs20_df)
```

数据示例

```
                       商品          日期    今值  预测值    前值
0    美国S&P/CS20座大城市房价指数年率  2001-02-01  12.4  NaN   NaN
1    美国S&P/CS20座大城市房价指数年率  2001-03-01  12.2  NaN  12.4
2    美国S&P/CS20座大城市房价指数年率  2001-04-01  12.0  NaN  12.2
3    美国S&P/CS20座大城市房价指数年率  2001-05-01  11.4  NaN  12.0
4    美国S&P/CS20座大城市房价指数年率  2001-06-01  10.5  NaN  11.4
..                    ...         ...   ...  ...   ...
275  美国S&P/CS20座大城市房价指数年率  2023-11-28   3.9  4.0   2.1
276  美国S&P/CS20座大城市房价指数年率  2023-12-26   4.9  5.0   3.9
277  美国S&P/CS20座大城市房价指数年率  2024-01-30   5.4  5.8   4.9
278  美国S&P/CS20座大城市房价指数年率  2024-02-27   6.1  6.0   5.4
279  美国S&P/CS20座大城市房价指数年率  2024-03-26   6.6  6.6   6.2
[280 rows x 5 columns]
```

###### 美国成屋签约销售指数月率报告

接口: macro_usa_pending_home_sales

目标地址: https://datacenter.jin10.com/reportType/dc_usa_pending_home_sales

描述: 美国成屋签约销售指数月率报告, 数据区间从 20010301-至今

限量: 单次返回所有历史数据

输入参数

| 名称 | 类型 | 描述 |
|----|----|----|
| -  | -  | -  |

输出参数

| 名称  | 类型      | 描述      |
|-----|---------|---------|
| 商品  | object  | -       |
| 日期  | object  | -       |
| 今值  | float64 | 注意单位: % |
| 预测值 | float64 | 注意单位: % |
| 前值  | float64 | 注意单位: % |

接口示例

```python
import akshare as ak

macro_usa_pending_home_sales_df = ak.macro_usa_pending_home_sales()
print(macro_usa_pending_home_sales_df)
```

数据示例

```
                 商品          日期   今值  预测值   前值
0    美国成屋签约销售指数月率报告  2001-03-01  5.1  NaN  NaN
1    美国成屋签约销售指数月率报告  2001-04-01 -4.7  NaN  5.1
2    美国成屋签约销售指数月率报告  2001-05-01 -2.9  NaN -4.7
3    美国成屋签约销售指数月率报告  2001-06-01  2.7  NaN -2.9
4    美国成屋签约销售指数月率报告  2001-07-01 -3.4  NaN  2.7
..              ...         ...  ...  ...  ...
274  美国成屋签约销售指数月率报告  2023-12-28  0.0  1.0 -1.2
275  美国成屋签约销售指数月率报告  2024-01-26  8.3  1.5  0.0
276  美国成屋签约销售指数月率报告  2024-02-29 -4.9  1.4  5.7
277  美国成屋签约销售指数月率报告  2024-03-28  1.6  1.4 -4.7
278  美国成屋签约销售指数月率报告  2024-04-25  NaN  NaN  1.6
[279 rows x 5 columns]
```

#### 领先指标

##### 未决房屋销售月率

接口: macro_usa_phs

目标地址: http://data.eastmoney.com/cjsj/foreign_0_5.html

描述: 东方财富-经济数据一览-美国-未决房屋销售月率, 数据区间从 20080201-至今

限量: 单次返回所有历史数据

输入参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| -   | -   | -   |

输出参数

| 名称   | 类型      | 描述      |
|------|---------|---------|
| 时间   | object  | -       |
| 前值   | float64 | 注意单位: % |
| 现值   | float64 | 注意单位: % |
| 发布日期 | object  | -       |

接口示例

```python
import akshare as ak

macro_usa_phs_df = ak.macro_usa_phs()
print(macro_usa_phs_df)
```

数据示例

```
      时间   前值   现值        发布日期
0    2022年10月 -1.5 -5.9  2022-11-18
1    2022年09月 -0.4 -1.5  2022-10-20
2    2022年08月 -5.9 -0.4  2022-09-21
3    2022年07月 -5.4 -5.9  2022-08-18
4    2022年06月 -3.4 -5.4  2022-07-20
..        ...  ...  ...         ...
166  2008年07月  5.3 -3.2  2008-08-28
167  2008年06月 -4.7  5.3  2008-07-29
168  2008年05月  6.3 -4.7  2008-06-28
169  2008年04月  0.0  6.3  2008-05-29
170  2008年01月 -1.5  0.0  2008-02-28
```

##### 美国谘商会消费者信心指数报告

接口: macro_usa_cb_consumer_confidence

目标地址: https://cdn.jin10.com/dc/reports/dc_usa_cb_consumer_confidence_all.js?v=1578576859

描述: 美国谘商会消费者信心指数报告, 数据区间从 19700101-至今

限量: 单次返回所有历史数据

输入参数

| 名称 | 类型 | 描述 |
|----|----|----|
| -  | -  | -  |

输出参数

| 名称  | 类型      | 描述 |
|-----|---------|----|
| 商品  | object  | -  |
| 日期  | object  | -  |
| 今值  | float64 | -  |
| 预测值 | float64 | -  |
| 前值  | float64 | -  |

接口示例

```python
import akshare as ak

macro_usa_cb_consumer_confidence_df = ak.macro_usa_cb_consumer_confidence()
print(macro_usa_cb_consumer_confidence_df)
```

数据示例

```
               商品          日期     今值    预测值     前值
0    美国谘商会消费者信心指数  1970-01-01  126.0    NaN    NaN
1    美国谘商会消费者信心指数  1970-02-01  126.0    NaN  126.0
2    美国谘商会消费者信心指数  1970-03-01  101.7    NaN  126.0
3    美国谘商会消费者信心指数  1970-04-01  101.7    NaN  101.7
4    美国谘商会消费者信心指数  1970-05-01   98.0    NaN  101.7
..            ...         ...    ...    ...    ...
650  美国谘商会消费者信心指数  2023-11-28  102.0  101.0   99.1
651  美国谘商会消费者信心指数  2023-12-20  110.7  103.8  101.0
652  美国谘商会消费者信心指数  2024-01-30  114.8  114.2  108.0
653  美国谘商会消费者信心指数  2024-02-27  106.7  114.8  110.9
654  美国谘商会消费者信心指数  2024-03-26  104.7  106.9  104.8
[655 rows x 5 columns]
```

##### 美国NFIB小型企业信心指数报告

接口: macro_usa_nfib_small_business

目标地址: https://cdn.jin10.com/dc/reports/dc_usa_nfib_small_business_all.js?v=1578576631

描述: 美国NFIB小型企业信心指数报告, 数据区间从 19750201-至今

限量: 单次返回所有历史数据

输入参数

| 名称 | 类型 | 描述 |
|----|----|----|
| -  | -  | -  |

输出参数

| 名称  | 类型      | 描述 |
|-----|---------|----|
| 商品  | object  | -  |
| 日期  | object  | -  |
| 今值  | float64 | -  |
| 预测值 | float64 | -  |
| 前值  | float64 | -  |

接口示例

```python
import akshare as ak

macro_usa_nfib_small_business_df = ak.macro_usa_nfib_small_business()
print(macro_usa_nfib_small_business_df)
```

数据示例

```
                   商品          日期      今值   预测值      前值
0    美国NFIB小型企业信心指数报告  1975-02-01   86.67   NaN     NaN
1    美国NFIB小型企业信心指数报告  1975-05-01   95.16   NaN   86.67
2    美国NFIB小型企业信心指数报告  1975-08-01   99.36   NaN   95.16
3    美国NFIB小型企业信心指数报告  1975-11-01  100.37   NaN   99.36
4    美国NFIB小型企业信心指数报告  1976-02-01  102.01   NaN  100.37
..                ...         ...     ...   ...     ...
501  美国NFIB小型企业信心指数报告  2023-12-12   90.60  90.7   90.70
502  美国NFIB小型企业信心指数报告  2024-01-09   91.90  90.7   90.60
503  美国NFIB小型企业信心指数报告  2024-02-13   89.90  92.3   91.90
504  美国NFIB小型企业信心指数报告  2024-03-12   89.40  90.5   89.90
505  美国NFIB小型企业信心指数报告  2024-04-09     NaN   NaN   89.40
[506 rows x 5 columns]
```

##### 美国密歇根大学消费者信心指数初值报告

接口: macro_usa_michigan_consumer_sentiment

目标地址: https://cdn.jin10.com/dc/reports/dc_usa_michigan_consumer_sentiment_all.js?v=1578576228

描述: 美国密歇根大学消费者信心指数初值报告, 数据区间从 19700301-至今

限量: 单次返回所有历史数据

输入参数

| 名称 | 类型 | 描述 |
|----|----|----|
| -  | -  | -  |

输出参数

| 名称  | 类型      | 描述 |
|-----|---------|----|
| 商品  | object  | -  |
| 日期  | object  | -  |
| 今值  | float64 | -  |
| 预测值 | float64 | -  |
| 前值  | float64 | -  |

接口示例

```python
import akshare as ak

macro_usa_michigan_consumer_sentiment_df = ak.macro_usa_michigan_consumer_sentiment()
print(macro_usa_michigan_consumer_sentiment_df)
```

数据示例

```
                     商品          日期    今值   预测值    前值
0    美国密歇根大学消费者信心指数初值报告  1970-03-01  78.1   NaN   NaN
1    美国密歇根大学消费者信心指数初值报告  1970-06-01  75.4   NaN  78.1
2    美国密歇根大学消费者信心指数初值报告  1970-09-01  77.6   NaN  75.4
3    美国密歇根大学消费者信心指数初值报告  1970-12-01  72.4   NaN  77.6
4    美国密歇根大学消费者信心指数初值报告  1971-03-01  78.1   NaN  72.4
..                  ...         ...   ...   ...   ...
772  美国密歇根大学消费者信心指数初值报告  2024-02-16  79.6  80.0  79.0
773  美国密歇根大学消费者信心指数初值报告  2024-03-01  76.9  79.6  79.0
774  美国密歇根大学消费者信心指数初值报告  2024-03-15  76.5  77.1  76.9
775  美国密歇根大学消费者信心指数初值报告  2024-03-28  79.4  76.5  76.9
776  美国密歇根大学消费者信心指数初值报告  2024-04-12   NaN   NaN  79.4
[777 rows x 5 columns]
```

#### 其他

##### 美国EIA原油库存报告

接口: macro_usa_eia_crude_rate

目标地址: https://cdn.jin10.com/dc/reports/dc_usa_michigan_consumer_sentiment_all.js?v=1578576228

描述: 美国EIA原油库存报告, 数据区间从 19950801-至今

限量: 单次返回所有历史数据

输入参数

| 名称 | 类型 | 描述 |
|----|----|----|
| -  | -  | -  |

输出参数

| 名称  | 类型      | 描述       |
|-----|---------|----------|
| 商品  | object  | -        |
| 日期  | object  | -        |
| 今值  | float64 | 注意单位: 万桶 |
| 预测值 | float64 | 注意单位: 万桶 |
| 前值  | float64 | 注意单位: 万桶 |

接口示例

```python
import akshare as ak

macro_usa_eia_crude_rate_df = ak.macro_usa_eia_crude_rate()
print(macro_usa_eia_crude_rate_df)
```

数据示例

```
             商品          日期     今值   预测值     前值
0     美国EIA原油库存  1982-09-01 -263.0   NaN    NaN
1     美国EIA原油库存  1982-10-01   -8.0   NaN -263.0
2     美国EIA原油库存  1982-11-01  -41.0   NaN   -8.0
3     美国EIA原油库存  1982-12-01  -88.0   NaN  -41.0
4     美国EIA原油库存  1983-01-01   51.0   NaN  -88.0
         ...         ...    ...   ...    ...
1147  美国EIA原油库存  2024-03-13 -153.6  90.0  136.7
1148  美国EIA原油库存  2024-03-20 -195.2 -90.0 -153.6
1149  美国EIA原油库存  2024-03-27  316.5 -70.0 -195.2
1150  美国EIA原油库存  2024-04-03  321.0 -30.0  316.5
1151  美国EIA原油库存  2024-04-10    NaN   NaN  321.0
[1152 rows x 5 columns]
```

##### 美国初请失业金人数报告

接口: macro_usa_initial_jobless

目标地址: https://cdn.jin10.com/dc/reports/dc_usa_michigan_consumer_sentiment_all.js?v=1578576228

描述: 美国初请失业金人数报告, 数据区间从 19700101-至今

限量: 单次返回所有历史数据

输入参数

| 名称 | 类型 | 描述 |
|----|----|----|
| -  | -  | -  |

输出参数

| 名称  | 类型      | 描述       |
|-----|---------|----------|
| 商品  | object  | -        |
| 日期  | object  | -        |
| 今值  | float64 | 注意单位: 万人 |
| 预测值 | float64 | 注意单位: 万人 |
| 前值  | float64 | 注意单位: 万人 |

接口示例

```python
import akshare as ak

macro_usa_initial_jobless_df = ak.macro_usa_initial_jobless()
print(macro_usa_initial_jobless_df)
```

数据示例

```
             商品          日期      今值   预测值      前值
0     美国初请失业金人数  1970-01-01  22.109   NaN     NaN
1     美国初请失业金人数  1970-02-01  24.932   NaN  22.109
2     美国初请失业金人数  1970-03-01  25.850   NaN  24.932
3     美国初请失业金人数  1970-04-01  26.868   NaN  25.850
4     美国初请失业金人数  1970-05-01  33.159   NaN  26.868
         ...         ...     ...   ...     ...
1293  美国初请失业金人数  2024-03-14  20.900  21.8  21.000
1294  美国初请失业金人数  2024-03-21  21.000  21.2  21.200
1295  美国初请失业金人数  2024-03-28  21.000  21.2  21.200
1296  美国初请失业金人数  2024-04-04  22.100  21.3  21.200
1297  美国初请失业金人数  2024-04-11     NaN   NaN  22.100
[1298 rows x 5 columns]
```

##### 美国原油产量报告

接口: macro_usa_crude_inner

目标地址: https://datacenter.jin10.com/reportType/dc_eia_crude_oil_produce

描述: 美国原油产量报告, 数据区间从 19830107-至今, 每周三公布(美国节假日除外), 美国能源信息署(EIA)

限量: 单次返回所有历史数据

1. 报告内容: 美国能源信息署（EIA）在北京时间每周三晚公布EIA报告，除了公布美国原油库存、汽油库存等数据外，报告还包含美国上周国内原油产量的数据。
2. 报告组成：美国国内原油产量、美国本土48州原油产量和美国阿拉斯加州原油产量。
3. 数据关系：美国国内原油产量=美国本土48州原油产量+美国阿拉斯加州原油产量 单位均为万桶/日。
4. 数据解读: 该数据反映了美国原油供应侧的情况，理论而言，当美国国内原油产量录得增加，通常导致油价下跌；当产量减少，则通常导致油价上扬。

输入参数

| 名称 | 类型 | 描述 |
|----|----|----|
| -  | -  | -  |

输出参数

| 名称             | 类型      | 描述 |
|----------------|---------|----|
| 日期             | object  | -  |
| 美国国内原油总量-产量    | float64 | -  |
| 美国国内原油总量-变化    | float64 | -  |
| 美国本土48州原油产量-产量 | float64 | -  |
| 美国本土48州原油产量-变化 | float64 | -  |
| 美国阿拉斯加州原油产量-产量 | float64 | -  |
| 美国阿拉斯加州原油产量-变化 | float64 | -  |

接口示例

```python
import akshare as ak

macro_usa_crude_inner_df = ak.macro_usa_crude_inner()
print(macro_usa_crude_inner_df)
```

数据示例

```
       日期  美国国内原油总量-产量  ...  美国阿拉斯加州原油产量-产量  美国阿拉斯加州原油产量-变化
0     1983-01-07        863.4  ...             0.0             NaN
1     1983-01-14        863.4  ...             0.0             0.0
2     1983-01-21        863.4  ...             0.0             0.0
3     1983-01-28        863.4  ...             0.0             0.0
4     1983-02-04        866.0  ...             0.0             0.0
          ...          ...  ...             ...             ...
2144  2024-03-01       1320.0  ...            43.6             0.4
2145  2024-03-08       1310.0  ...            43.2            -0.4
2146  2024-03-15       1310.0  ...            44.1             0.9
2147  2024-03-22       1310.0  ...            43.2            -0.9
2148  2024-03-29       1310.0  ...            43.2             0.0
[2149 rows x 7 columns]
```

### 欧元区宏观

#### 国民经济运行状况

##### 经济状况

###### 欧元区季度GDP年率报告

接口: macro_euro_gdp_yoy

目标地址: https://datacenter.jin10.com/reportType/dc_eurozone_gdp_yoy

描述: 欧元区季度 GDP 年率报告, 数据区间从 20131114-至今

限量: 单次返回所有历史数据

输入参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| -   | -   | -   |

输出参数

| 名称  | 类型      | 描述      |
|-----|---------|---------|
| 商品  | object  | -       |
| 日期  | object  | -       |
| 今值  | float64 | 注意单位: % |
| 预测值 | float64 | 注意单位: % |
| 前值  | float64 | 注意单位: % |

接口示例

```python
import akshare as ak

macro_euro_gdp_yoy_df = ak.macro_euro_gdp_yoy()
print(macro_euro_gdp_yoy_df)
```

数据示例

```
             商品          日期   今值  预测值   前值
0    欧元区季度GDP年率  2013-11-14 -0.4 -0.3 -0.5
1    欧元区季度GDP年率  2013-12-04 -0.4 -0.4 -0.4
2    欧元区季度GDP年率  2014-01-10 -0.3 -0.4 -0.4
3    欧元区季度GDP年率  2014-02-14  0.5  0.4 -0.3
4    欧元区季度GDP年率  2014-03-05  0.5  0.5  0.5
..          ...         ...  ...  ...  ...
97   欧元区季度GDP年率  2022-07-29  4.0  3.4  5.4
98   欧元区季度GDP年率  2022-08-17  3.9  4.0  4.0
99   欧元区季度GDP年率  2022-09-07  4.1  3.9  5.4
100  欧元区季度GDP年率  2022-10-31  2.1  NaN  4.3
101  欧元区季度GDP年率  2022-11-15  NaN  4.0  2.1
```

##### 物价水平

###### 欧元区CPI月率报告

接口: macro_euro_cpi_mom

目标地址: https://datacenter.jin10.com/reportType/dc_eurozone_cpi_mom

描述: 欧元区 CPI 月率报告, 数据区间从 19900301-至今

限量: 单次返回所有历史数据

输入参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| -   | -   | -   |

输出参数

| 名称  | 类型      | 描述      |
|-----|---------|---------|
| 商品  | object  | -       |
| 日期  | object  | -       |
| 今值  | float64 | 注意单位: % |
| 预测值 | float64 | 注意单位: % |
| 前值  | float64 | 注意单位: % |

接口示例

```python
import akshare as ak

macro_euro_cpi_mom_df = ak.macro_euro_cpi_mom()
print(macro_euro_cpi_mom_df)
```

数据示例

```
          商品          日期   今值  预测值   前值
0    欧元区CPI月率  1990-03-01  0.4  NaN  NaN
1    欧元区CPI月率  1990-04-01  0.2  NaN  0.4
2    欧元区CPI月率  1990-05-01  0.4  NaN  0.2
3    欧元区CPI月率  1990-06-01  0.2  NaN  0.4
4    欧元区CPI月率  1990-07-01  0.1  NaN  0.2
..        ...         ...  ...  ...  ...
421  欧元区CPI月率  2022-09-16  0.6  0.5  0.1
422  欧元区CPI月率  2022-09-30  1.2  0.9  0.6
423  欧元区CPI月率  2022-10-19  1.2  1.2  0.6
424  欧元区CPI月率  2022-10-31  1.5  NaN  1.2
425  欧元区CPI月率  2022-11-17  NaN  1.2  1.2
```

###### 欧元区CPI年率报告

接口: macro_euro_cpi_yoy

目标地址: https://datacenter.jin10.com/reportType/dc_eurozone_cpi_yoy

描述: 欧元区 CPI 年率报告, 数据区间从 19910201-至今

限量: 单次返回所有历史数据

输入参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| -   | -   | -   |

输出参数

| 名称  | 类型      | 描述      |
|-----|---------|---------|
| 商品  | object  | -       |
| 日期  | object  | -       |
| 今值  | float64 | 注意单位: % |
| 预测值 | float64 | 注意单位: % |
| 前值  | float64 | 注意单位: % |

接口示例

```python
import akshare as ak

macro_euro_cpi_yoy_df = ak.macro_euro_cpi_yoy()
print(macro_euro_cpi_yoy_df)
```

数据示例

```
           商品          日期    今值   预测值    前值
0    欧元区CPI年率  1991-02-01   0.0   NaN   NaN
1    欧元区CPI年率  1991-03-01   4.1   NaN   3.9
2    欧元区CPI年率  1991-04-01   3.9   NaN   4.1
3    欧元区CPI年率  1991-05-01   3.9   NaN   3.9
4    欧元区CPI年率  1991-06-01   4.1   NaN   3.9
..        ...         ...   ...   ...   ...
556  欧元区CPI年率  2022-09-16   9.1   9.1   9.1
557  欧元区CPI年率  2022-09-30  10.0   9.7   9.1
558  欧元区CPI年率  2022-10-19   9.9  10.0   9.1
559  欧元区CPI年率  2022-10-31  10.7  10.2   9.9
560  欧元区CPI年率  2022-11-17   NaN  10.0  10.7
```

###### 欧元区PPI月率报告

接口: macro_euro_ppi_mom

目标地址: https://datacenter.jin10.com/reportType/dc_eurozone_ppi_mom

描述: 欧元区 PPI 月率报告, 数据区间从 19810301-至今

限量: 单次返回所有历史数据

输入参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| -   | -   | -   |

输出参数

| 名称  | 类型      | 描述      |
|-----|---------|---------|
| 商品  | object  | -       |
| 日期  | object  | -       |
| 今值  | float64 | 注意单位: % |
| 预测值 | float64 | 注意单位: % |
| 前值  | float64 | 注意单位: % |

接口示例

```python
import akshare as ak

macro_euro_ppi_mom_df = ak.macro_euro_ppi_mom()
print(macro_euro_ppi_mom_df)
```

数据示例

```
           商品          日期   今值  预测值   前值
0    欧元区PPI月率  1981-03-01  1.0  NaN  NaN
1    欧元区PPI月率  1981-04-01  0.7  NaN  1.0
2    欧元区PPI月率  1981-05-01  1.5  NaN  0.7
3    欧元区PPI月率  1981-06-01  0.7  NaN  1.5
4    欧元区PPI月率  1981-07-01  0.6  NaN  0.7
..        ...         ...  ...  ...  ...
499  欧元区PPI月率  2022-07-04  0.7  1.0  1.2
500  欧元区PPI月率  2022-08-03  1.1  1.0  0.5
501  欧元区PPI月率  2022-09-02  4.0  2.5  1.3
502  欧元区PPI月率  2022-10-04  5.0  4.9  4.0
503  欧元区PPI月率  2022-11-04  NaN  1.7  5.0
```

###### 欧元区零售销售月率报告

接口: macro_euro_retail_sales_mom

目标地址: https://datacenter.jin10.com/reportType/dc_eurozone_retail_sales_mom

描述: 欧元区零售销售月率报告, 数据区间从 20000301-至今

限量: 单次返回所有历史数据

输入参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| -   | -   | -   |

输出参数

| 名称  | 类型      | 描述      |
|-----|---------|---------|
| 商品  | object  | -       |
| 日期  | object  | -       |
| 今值  | float64 | 注意单位: % |
| 预测值 | float64 | 注意单位: % |
| 前值  | float64 | 注意单位: % |

接口示例

```python
import akshare as ak

macro_euro_retail_sales_mom_df = ak.macro_euro_retail_sales_mom()
print(macro_euro_retail_sales_mom_df)
```

数据示例

```
            商品          日期   今值  预测值   前值
0    欧元区零售销售月率  2000-03-01  0.7  NaN  NaN
1    欧元区零售销售月率  2000-04-01 -0.3  NaN  0.7
2    欧元区零售销售月率  2000-05-01  0.7  NaN -0.3
3    欧元区零售销售月率  2000-06-01  0.3  NaN  0.7
4    欧元区零售销售月率  2000-07-01  0.1  NaN  0.3
..         ...         ...  ...  ...  ...
271  欧元区零售销售月率  2022-07-06  0.2  0.4 -1.4
272  欧元区零售销售月率  2022-08-03 -1.2  0.1  0.4
273  欧元区零售销售月率  2022-09-05  0.3  0.4 -1.0
274  欧元区零售销售月率  2022-10-06 -0.3 -0.4 -0.4
275  欧元区零售销售月率  2022-11-08  NaN -0.4 -0.3
```

##### 劳动力市场

###### 欧元区季调后就业人数季率报告

接口: macro_euro_employment_change_qoq

目标地址: https://datacenter.jin10.com/reportType/dc_eurozone_employment_change_qoq

描述: 欧元区季调后就业人数季率报告, 数据区间从 20083017-至今

限量: 单次返回所有历史数据

输入参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| -   | -   | -   |

输出参数

| 名称  | 类型      | 描述      |
|-----|---------|---------|
| 商品  | object  | -       |
| 日期  | object  | -       |
| 今值  | float64 | 注意单位: % |
| 预测值 | float64 | 注意单位: % |
| 前值  | float64 | 注意单位: % |

接口示例

```python
import akshare as ak

macro_euro_employment_change_qoq_df = ak.macro_euro_employment_change_qoq()
print(macro_euro_employment_change_qoq_df)
```

数据示例

```
              商品          日期   今值  预测值   前值
0   欧元区季调后就业人数季率  2008-03-17  0.2  0.0  0.3
1   欧元区季调后就业人数季率  2008-06-13  0.3  0.0  0.2
2   欧元区季调后就业人数季率  2008-09-12  0.2  NaN  0.3
3   欧元区季调后就业人数季率  2008-12-16 -0.1  NaN  0.2
4   欧元区季调后就业人数季率  2009-03-16 -0.3  NaN -0.1
..           ...         ...  ...  ...  ...
70  欧元区季调后就业人数季率  2022-03-08  0.5  0.5  0.9
71  欧元区季调后就业人数季率  2022-05-17  0.5  NaN  0.4
72  欧元区季调后就业人数季率  2022-06-08  0.6  0.5  0.4
73  欧元区季调后就业人数季率  2022-08-17  0.3  NaN  0.6
74  欧元区季调后就业人数季率  2022-09-07  0.4  0.3  0.7
```

###### 欧元区失业率报告

接口: macro_euro_unemployment_rate_mom

目标地址: https://datacenter.jin10.com/reportType/dc_eurozone_unemployment_rate_mom

描述: 欧元区失业率报告, 数据区间从 19980501-至今

限量: 单次返回所有历史数据

输入参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| -   | -   | -   |

输出参数

| 名称  | 类型      | 描述      |
|-----|---------|---------|
| 商品  | object  | -       |
| 日期  | object  | -       |
| 今值  | float64 | 注意单位: % |
| 预测值 | float64 | 注意单位: % |
| 前值  | float64 | 注意单位: % |

接口示例

```python
import akshare as ak

macro_euro_unemployment_rate_mom_df = ak.macro_euro_unemployment_rate_mom()
print(macro_euro_unemployment_rate_mom_df)
```

数据示例

```
         商品          日期    今值  预测值    前值
0    欧元区失业率  1998-05-01  10.6  NaN   NaN
1    欧元区失业率  1998-06-01  10.5  NaN  10.6
2    欧元区失业率  1998-07-01  10.5  NaN  10.5
3    欧元区失业率  1998-08-01  10.4  NaN  10.5
4    欧元区失业率  1998-09-01  10.4  NaN  10.4
..      ...         ...   ...  ...   ...
290  欧元区失业率  2022-06-30   6.6  6.8   6.7
291  欧元区失业率  2022-08-01   6.6  6.6   6.6
292  欧元区失业率  2022-09-01   6.6  6.6   6.7
293  欧元区失业率  2022-09-30   6.6  6.6   6.6
294  欧元区失业率  2022-11-03   6.6  6.6   6.7
```

#### 贸易状况

##### 欧元区未季调贸易帐报告

接口: macro_euro_trade_balance

目标地址: https://datacenter.jin10.com/reportType/dc_eurozone_trade_balance_mom

描述: 欧元区未季调贸易帐报告, 数据区间从 19990201-至今

限量: 单次返回所有历史数据

输入参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| -   | -   | -   |

输出参数

| 名称  | 类型      | 描述      |
|-----|---------|---------|
| 商品  | object  | -       |
| 日期  | object  | -       |
| 今值  | float64 | 注意单位: % |
| 预测值 | float64 | 注意单位: % |
| 前值  | float64 | 注意单位: % |

接口示例

```python
import akshare as ak

macro_euro_trade_balance_df = ak.macro_euro_trade_balance()
print(macro_euro_trade_balance_df)
```

数据示例

```
            商品          日期     今值    预测值     前值
0    欧元区未季调贸易帐  1999-02-01  -27.0    NaN    NaN
1    欧元区未季调贸易帐  1999-03-01   19.0    NaN  -27.0
2    欧元区未季调贸易帐  1999-04-01   27.0    NaN   19.0
3    欧元区未季调贸易帐  1999-05-01   21.0    NaN   27.0
4    欧元区未季调贸易帐  1999-06-01   -9.0    NaN   21.0
..         ...         ...    ...    ...    ...
281  欧元区未季调贸易帐  2022-07-15 -263.0    NaN -328.0
282  欧元区未季调贸易帐  2022-08-16 -246.0 -200.0 -284.0
283  欧元区未季调贸易帐  2022-09-15 -340.0 -200.0 -254.0
284  欧元区未季调贸易帐  2022-10-14 -509.0    NaN -339.0
285  欧元区未季调贸易帐  2022-11-15    NaN    NaN -509.0
```

##### 欧元区经常帐报告

接口: macro_euro_current_account_mom

目标地址: https://datacenter.jin10.com/reportType/dc_eurozone_current_account_mom

描述: 欧元区经常帐报告, 数据区间从 ********-至今

限量: 单次返回所有历史数据

输入参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| -   | -   | -   |

输出参数

| 名称  | 类型      | 描述      |
|-----|---------|---------|
| 商品  | object  | -       |
| 日期  | object  | -       |
| 今值  | float64 | 注意单位: % |
| 预测值 | float64 | 注意单位: % |
| 前值  | float64 | 注意单位: % |

接口示例

```python
import akshare as ak

macro_euro_current_account_mom_df = ak.macro_euro_current_account_mom()
print(macro_euro_current_account_mom_df)
```

数据示例

```
         商品          日期     今值    预测值     前值
0    欧元区经常帐  1999-02-01 -150.0    NaN    NaN
1    欧元区经常帐  2003-07-01   10.0    NaN    0.0
2    欧元区经常帐  2008-02-21 -103.0    2.0    7.0
3    欧元区经常帐  2008-03-26 -106.0    0.0 -103.0
4    欧元区经常帐  2008-04-24   43.0    0.0 -106.0
..      ...         ...    ...    ...    ...
175  欧元区经常帐  2022-07-20  -45.0    NaN  -58.0
176  欧元区经常帐  2022-08-19   42.0    NaN  -45.0
177  欧元区经常帐  2022-09-20 -199.0   53.0   42.0
178  欧元区经常帐  2022-10-20 -263.0 -203.0 -199.0
179  欧元区经常帐  2022-11-22    NaN    NaN -263.0
```

#### 产业指标

##### 欧元区工业产出月率报告

接口: macro_euro_industrial_production_mom

目标地址: https://datacenter.jin10.com/reportType/dc_eurozone_industrial_production_mom

描述: 欧元区工业产出月率报告, 数据区间从 ********-至今

限量: 单次返回所有历史数据

输入参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| -   | -   | -   |

输出参数

| 名称  | 类型      | 描述      |
|-----|---------|---------|
| 商品  | object  | -       |
| 日期  | object  | -       |
| 今值  | float64 | 注意单位: % |
| 预测值 | float64 | 注意单位: % |
| 前值  | float64 | 注意单位: % |

接口示例

```python
import akshare as ak

macro_euro_industrial_production_mom_df = ak.macro_euro_industrial_production_mom()
print(macro_euro_industrial_production_mom_df)
```

数据示例

```
            商品          日期   今值  预测值   前值
0    欧元区工业产出月率  1991-03-01 -1.1  NaN  NaN
1    欧元区工业产出月率  1991-04-01 -1.0  NaN -1.1
2    欧元区工业产出月率  1991-05-01 -0.5  NaN -1.0
3    欧元区工业产出月率  1991-06-01 -0.1  NaN -0.5
4    欧元区工业产出月率  1991-07-01  1.9  NaN -0.1
..         ...         ...  ...  ...  ...
380  欧元区工业产出月率  2022-07-13  0.8  0.3  0.5
381  欧元区工业产出月率  2022-08-12  0.7  0.2  2.1
382  欧元区工业产出月率  2022-09-14 -2.3 -1.0  1.1
383  欧元区工业产出月率  2022-10-12  1.5  0.6 -2.3
384  欧元区工业产出月率  2022-11-14  NaN  NaN  1.5
```

##### 欧元区制造业PMI初值报告

接口: macro_euro_manufacturing_pmi

目标地址: https://datacenter.jin10.com/reportType/dc_eurozone_manufacturing_pmi

描述: 欧元区制造业 PMI 初值报告, 数据区间从 20080222-至今

限量: 单次返回所有历史数据

输入参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| -   | -   | -   |

输出参数

| 名称  | 类型      | 描述      |
|-----|---------|---------|
| 商品  | object  | -       |
| 日期  | object  | -       |
| 今值  | float64 | 注意单位: % |
| 预测值 | float64 | 注意单位: % |
| 前值  | float64 | 注意单位: % |

接口示例

```python
import akshare as ak

macro_euro_manufacturing_pmi_df = ak.macro_euro_manufacturing_pmi()
print(macro_euro_manufacturing_pmi_df)
```

数据示例

```
              商品          日期    今值   预测值    前值
0    欧元区制造业PMI初值  2008-02-22  52.3  52.5  52.8
1    欧元区制造业PMI初值  2008-03-03  52.3  52.3  52.3
2    欧元区制造业PMI初值  2008-03-20  52.0  52.0  52.3
3    欧元区制造业PMI初值  2008-04-01  52.0  52.0  52.0
4    欧元区制造业PMI初值  2008-04-23  51.8  51.6  52.0
..           ...         ...   ...   ...   ...
353  欧元区制造业PMI初值  2022-09-23  48.5  48.7  49.6
354  欧元区制造业PMI初值  2022-10-03  48.4  48.5  49.6
355  欧元区制造业PMI初值  2022-10-24  46.6  47.8  48.4
356  欧元区制造业PMI初值  2022-11-02  46.4  46.6  48.4
357  欧元区制造业PMI初值  2022-11-23   NaN   NaN  46.4
```

##### 欧元区服务业PMI终值报告

接口: macro_euro_services_pmi

目标地址: https://datacenter.jin10.com/reportType/dc_eurozone_services_pmi

描述: 欧元区服务业 PMI 终值报告, 数据区间从 20080222-至今

限量: 单次返回所有历史数据

输入参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| -   | -   | -   |

输出参数

| 名称  | 类型      | 描述      |
|-----|---------|---------|
| 商品  | object  | -       |
| 日期  | object  | -       |
| 今值  | float64 | 注意单位: % |
| 预测值 | float64 | 注意单位: % |
| 前值  | float64 | 注意单位: % |

接口示例

```python
import akshare as ak

macro_euro_services_pmi_df = ak.macro_euro_services_pmi()
print(macro_euro_services_pmi_df)
```

数据示例

```
              商品          日期    今值   预测值    前值
0    欧元区服务业PMI终值  2008-02-22  52.3  51.0  50.6
1    欧元区服务业PMI终值  2008-03-05  52.3  52.3  52.3
2    欧元区服务业PMI终值  2008-04-03  51.6   0.0  51.7
3    欧元区服务业PMI终值  2008-04-23  51.8  51.4  51.6
4    欧元区服务业PMI终值  2008-05-06  52.0  51.8  51.8
..           ...         ...   ...   ...   ...
351  欧元区服务业PMI终值  2022-09-23  48.9  49.0  49.8
352  欧元区服务业PMI终值  2022-10-05  48.8  48.9  49.8
353  欧元区服务业PMI终值  2022-10-24  48.2  48.2  48.8
354  欧元区服务业PMI终值  2022-11-04   NaN  48.2  48.2
355  欧元区服务业PMI终值  2022-11-23   NaN  48.2  48.8
```

#### 领先指标

##### 欧元区ZEW经济景气指数报告

接口: macro_euro_zew_economic_sentiment

目标地址: https://datacenter.jin10.com/reportType/dc_eurozone_zew_economic_sentiment

描述: 欧元区 ZEW 经济景气指数报告, 数据区间从 20080212-至今

限量: 单次返回所有历史数据

输入参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| -   | -   | -   |

输出参数

| 名称  | 类型      | 描述      |
|-----|---------|---------|
| 商品  | object  | -       |
| 日期  | object  | -       |
| 今值  | float64 | 注意单位: % |
| 预测值 | float64 | 注意单位: % |
| 前值  | float64 | 注意单位: % |

接口示例

```python
import akshare as ak

macro_euro_zew_economic_sentiment_df = ak.macro_euro_zew_economic_sentiment()
print(macro_euro_zew_economic_sentiment_df)
```

数据示例

```
               商品          日期    今值   预测值    前值
0    欧元区ZEW经济景气指数  2008-02-12 -41.4 -43.0 -41.7
1    欧元区ZEW经济景气指数  2008-03-11 -35.0 -42.0 -41.4
2    欧元区ZEW经济景气指数  2008-04-15 -44.8 -33.0 -35.0
3    欧元区ZEW经济景气指数  2008-05-20 -43.6 -44.2 -44.8
4    欧元区ZEW经济景气指数  2008-06-17 -52.7 -43.9 -43.6
..            ...         ...   ...   ...   ...
174  欧元区ZEW经济景气指数  2022-07-12  51.1   NaN -28.0
175  欧元区ZEW经济景气指数  2022-08-16 -54.9 -57.0 -51.1
176  欧元区ZEW经济景气指数  2022-09-13 -60.7 -58.3 -54.9
177  欧元区ZEW经济景气指数  2022-10-18 -59.7 -61.2 -60.7
178  欧元区ZEW经济景气指数  2022-11-15   NaN   NaN -59.7
```

##### 欧元区Sentix投资者信心指数报告

接口: macro_euro_sentix_investor_confidence

目标地址: https://datacenter.jin10.com/reportType/dc_eurozone_sentix_investor_confidence

描述: 欧元区 Sentix 投资者信心指数报告, 数据区间从 20020801-至今

限量: 单次返回所有历史数据

输入参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| -   | -   | -   |

输出参数

| 名称  | 类型      | 描述      |
|-----|---------|---------|
| 商品  | object  | -       |
| 日期  | object  | -       |
| 今值  | float64 | 注意单位: % |
| 预测值 | float64 | 注意单位: % |
| 前值  | float64 | 注意单位: % |

接口示例

```python
import akshare as ak

macro_euro_sentix_investor_confidence_df = ak.macro_euro_sentix_investor_confidence()
print(macro_euro_sentix_investor_confidence_df)
```

数据示例

```
                   商品          日期    今值   预测值    前值
0    欧元区Sentix投资者信心指数  2002-08-01  13.0   NaN   NaN
1    欧元区Sentix投资者信心指数  2002-10-01  -8.5   NaN  13.0
2    欧元区Sentix投资者信心指数  2003-02-01 -21.8   NaN  -8.5
3    欧元区Sentix投资者信心指数  2003-03-01 -22.8   NaN -21.8
4    欧元区Sentix投资者信心指数  2003-04-01 -19.4   NaN -22.8
..                ...         ...   ...   ...   ...
239  欧元区Sentix投资者信心指数  2022-07-04 -26.4 -19.9 -15.8
240  欧元区Sentix投资者信心指数  2022-08-08 -25.2 -24.7 -26.4
241  欧元区Sentix投资者信心指数  2022-09-05 -31.8 -27.5 -25.2
242  欧元区Sentix投资者信心指数  2022-10-10 -38.3 -34.7 -31.8
243  欧元区Sentix投资者信心指数  2022-11-07   NaN -34.7 -38.3
```

### 德国宏观

#### IFO商业景气指数

接口: macro_germany_ifo

目标地址: https://data.eastmoney.com/cjsj/foreign_1_0.html

描述: 东方财富-数据中心-经济数据一览-IFO商业景气指数

限量: 单次返回所有历史数据

输入参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| -   | -   | -   |

输出参数

| 名称   | 类型      | 描述  |
|------|---------|-----|
| 时间   | object  | -   |
| 前值   | float64 | -   |
| 现值   | float64 | -   |
| 发布日期 | object  | -   |

接口示例

```python
import akshare as ak

macro_germany_ifo_df = ak.macro_germany_ifo()
print(macro_germany_ifo_df)
```

数据示例

```
       时间      前值    现值    发布日期
0    2008年01月  98.3  97.9  2008-01-25
1    2008年02月  97.9  97.8  2008-02-25
2    2008年03月  97.8  97.9  2008-03-25
3    2008年04月  97.9  97.1  2008-04-25
4    2008年05月  97.1  96.6  2008-05-25
..        ...   ...   ...         ...
173  2022年06月  93.3  92.3  2022-06-24
174  2022年07月  92.3  88.6  2022-07-25
175  2022年08月  88.6  88.6  2022-08-25
176  2022年09月  88.6  84.4  2022-09-26
177  2022年10月  84.4  84.3  2022-10-25
```

#### 消费者物价指数月率终值

接口: macro_germany_cpi_monthly

目标地址: https://data.eastmoney.com/cjsj/foreign_1_1.html

描述: 东方财富-数据中心-经济数据一览-德国-消费者物价指数月率终值

限量: 单次返回所有历史数据

输入参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| -   | -   | -   |

输出参数

| 名称   | 类型      | 描述  |
|------|---------|-----|
| 时间   | object  | -   |
| 前值   | float64 | -   |
| 现值   | float64 | -   |
| 发布日期 | object  | -   |

接口示例

```python
import akshare as ak

macro_germany_cpi_monthly_df = ak.macro_germany_cpi_monthly()
print(macro_germany_cpi_monthly_df)
```

数据示例

```
      时间   前值   现值        发布日期
0    2008年01月  0.7 -0.4  2008-02-16
1    2008年02月 -0.4  0.6  2008-03-18
2    2008年03月  0.6  0.5  2008-04-16
3    2008年04月  0.5 -0.3  2008-05-17
4    2008年05月 -0.3  0.7  2008-06-16
..        ...  ...  ...         ...
171  2022年05月  0.7  1.1  2022-06-17
172  2022年06月  1.1 -0.1  2022-07-19
173  2022年07月 -0.1  0.8  2022-08-18
174  2022年08月  0.8  0.4  2022-09-16
175  2022年09月  0.4  2.2  2022-10-19
```

#### 消费者物价指数年率终值

接口: macro_germany_cpi_yearly

目标地址: https://data.eastmoney.com/cjsj/foreign_1_2.html

描述: 东方财富-数据中心-经济数据一览-德国-消费者物价指数年率终值

限量: 单次返回所有历史数据

输入参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| -   | -   | -   |

输出参数

| 名称   | 类型      | 描述  |
|------|---------|-----|
| 时间   | object  | -   |
| 前值   | float64 | -   |
| 现值   | float64 | -   |
| 发布日期 | object  | -   |

接口示例

```python
import akshare as ak

macro_germany_cpi_yearly_df = ak.macro_germany_cpi_yearly()
print(macro_germany_cpi_yearly_df)
```

数据示例

```
     时间   前值    现值        发布日期
0    2008年01月  3.1   2.9  2008-02-16
1    2008年02月  2.9   3.0  2008-03-18
2    2008年03月  3.0   3.3  2008-04-16
3    2008年04月  3.3   2.6  2008-05-17
4    2008年05月  2.6   3.1  2008-06-16
..        ...  ...   ...         ...
172  2022年05月  7.8   8.7  2022-06-17
173  2022年06月  8.7   8.2  2022-07-19
174  2022年07月  8.2   8.5  2022-08-18
175  2022年08月  8.5   8.8  2022-09-16
176  2022年09月  8.8  10.9  2022-10-19
```

#### 贸易帐-季调后

接口: macro_germany_trade_adjusted

目标地址: https://data.eastmoney.com/cjsj/foreign_1_3.html

描述: 东方财富-数据中心-经济数据一览-德国-贸易帐(季调后)

限量: 单次返回所有历史数据

输入参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| -   | -   | -   |

输出参数

| 名称   | 类型      | 描述  |
|------|---------|-----|
| 时间   | object  | -   |
| 前值   | float64 | -   |
| 现值   | float64 | -   |
| 发布日期 | object  | -   |

接口示例

```python
import akshare as ak

macro_germany_trade_adjusted_df = ak.macro_germany_trade_adjusted()
print(macro_germany_trade_adjusted_df)
```

数据示例

```
      时间    前值    现值        发布日期
0    2008年01月  15.5  17.2  2008-03-10
1    2008年02月  17.2  16.3  2008-04-10
2    2008年03月  16.3  16.8  2008-05-09
3    2008年04月  16.8  16.8  2008-06-09
4    2008年05月  16.8  15.3  2008-07-09
..        ...   ...   ...         ...
173  2022年06月   3.2   5.7  2022-08-03
174  2022年07月   5.7   3.4  2022-09-02
175  2022年08月   3.4   1.2  2022-10-05
176  2022年09月   1.2   3.7  2022-11-02
177  2022年10月   3.7   NaN  2022-12-02
```

#### GDP

接口: macro_germany_gdp

目标地址: https://data.eastmoney.com/cjsj/foreign_1_4.html

描述: 东方财富-数据中心-经济数据一览-德国-GDP

限量: 单次返回所有历史数据

输入参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| -   | -   | -   |

输出参数

| 名称   | 类型      | 描述  |
|------|---------|-----|
| 时间   | object  | -   |
| 前值   | float64 | -   |
| 现值   | float64 | -   |
| 发布日期 | object  | -   |

接口示例

```python
import akshare as ak

macro_germany_gdp_df = ak.macro_germany_gdp()
print(macro_germany_gdp_df)
```

数据示例

```
      时间         前值         现值        发布日期
0   2008第1季度   3.911156   2.891346  2008-05-27
1   2008第2季度   2.891346   4.008347  2008-08-27
2   2008第3季度   4.008347   1.783758  2008-11-27
3   2008第4季度   1.783758  -1.075182  2009-02-26
4   2009第1季度  -1.075182  -5.727010  2009-05-27
5   2009第2季度  -5.727010  -6.526663  2009-08-27
6   2009第3季度  -6.526663  -3.618978  2009-11-27
7   2009第4季度  -3.618978  -0.203077  2010-02-26
8   2010第1季度  -0.203077   3.679816  2010-05-27
9   2010第2季度   3.679816   5.068024  2010-08-27
10  2010第3季度   5.068024   4.369179  2010-11-27
11  2010第4季度   4.369179   4.120271  2011-02-26
12  2011第1季度   4.120271   5.333064  2011-05-27
13  2011第2季度   5.333064   3.859840  2011-08-27
14  2011第3季度   3.859840   3.531578  2011-11-27
15  2011第4季度   3.531578   2.565706  2012-02-26
16  2012第1季度   2.565706   2.984836  2012-05-27
17  2012第2季度   2.984836   7.174241  2012-08-27
18  2012第3季度   7.174241   1.725317  2012-11-27
19  2012第4季度   1.725317   0.945122  2013-02-26
20  2013第1季度   0.945122   5.768754  2013-05-27
21  2013第2季度   5.768754   2.757222  2013-08-27
22  2013第3季度   2.757222   7.362522  2013-11-27
23  2013第4季度   7.362522   7.864693  2014-02-26
24  2014第1季度   7.864693   4.547053  2014-05-27
25  2014第2季度   4.547053   2.828708  2014-08-27
26  2014第3季度   2.828708   2.900608  2014-11-27
27  2014第4季度   2.900608   3.192026  2015-02-26
28  2015第1季度   3.192026   2.961665  2015-05-27
29  2015第2季度   2.961665   4.127740  2015-08-27
30  2015第3季度   4.127740   4.167399  2015-11-27
31  2015第4季度   4.167399   4.862430  2016-02-26
32  2016第1季度   4.862430   3.862071  2016-05-27
33  2016第2季度   3.862071   4.796750  2016-08-27
34  2016第3季度   4.796750   2.448969  2016-11-27
35  2016第4季度   2.448969   3.986182  2017-02-26
36  2017第1季度   3.986182   4.704109  2017-05-26
37  2017第2季度   4.704109   2.837963  2017-08-28
38  2017第3季度   2.837963   4.431038  2017-11-23
39  2017第4季度   4.431038   4.909609  2018-02-26
40  2018第1季度   4.909609   3.089480  2018-05-29
41  2018第2季度   3.089480   4.038992  2018-08-14
42  2018第3季度   4.038992   2.223463  2018-11-26
43  2018第4季度   2.223463   2.719435  2019-02-25
44  2019第1季度   2.719435   3.407325  2019-05-16
45  2019第2季度   3.407325   2.314198  2019-08-14
46  2019第3季度   2.314198   4.201790  2019-11-14
47  2019第4季度   4.201790   2.894518  2020-02-14
48  2020第1季度   2.894518   1.800183  2020-05-15
49  2020第2季度   1.800183  -7.679049  2020-08-14
50  2020第3季度  -7.679049  -1.829887  2020-11-24
51  2020第4季度  -1.829887  -0.180657  2021-02-24
52  2021第1季度  -0.180657  -0.562919  2021-05-25
53  2021第2季度  -0.562919  11.520925  2021-08-27
54  2021第3季度  11.520925   6.313908  2021-11-23
55  2021第4季度   6.313908   6.305223  2022-02-25
56  2022第1季度   6.305223   8.602949  2022-05-25
57  2022第2季度   8.602949   7.763706  2022-08-25
```

#### 实际零售销售月率

接口: macro_germany_retail_sale_monthly

目标地址: https://data.eastmoney.com/cjsj/foreign_1_5.html

描述: 东方财富-数据中心-经济数据一览-德国-实际零售销售月率

限量: 单次返回所有历史数据

输入参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| -   | -   | -   |

输出参数

| 名称   | 类型      | 描述  |
|------|---------|-----|
| 时间   | object  | -   |
| 前值   | float64 | -   |
| 现值   | float64 | -   |
| 发布日期 | object  | -   |

接口示例

```python
import akshare as ak

macro_germany_retail_sale_monthly_df = ak.macro_germany_retail_sale_monthly()
print(macro_germany_retail_sale_monthly_df)
```

数据示例

```
      时间    前值    现值        发布日期
0    2008年01月   4.5   4.4  2008-02-13
1    2008年02月   4.4   4.5  2008-03-15
2    2008年03月   4.5   5.1  2008-04-13
3    2008年04月   5.1   4.4  2008-05-14
4    2008年05月   4.4   4.8  2008-06-13
..        ...   ...   ...         ...
173  2022年06月   9.1  10.2  2022-07-13
174  2022年07月  10.2  11.5  2022-08-10
175  2022年08月  11.5  12.7  2022-09-13
176  2022年09月  12.7  14.1  2022-10-13
177  2022年10月  14.1   NaN  2022-11-11
```

#### 实际零售销售年率

接口: macro_germany_retail_sale_yearly

目标地址: https://data.eastmoney.com/cjsj/foreign_1_6.html

描述: 东方财富-数据中心-经济数据一览-德国-实际零售销售年率

限量: 单次返回所有历史数据

输入参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| -   | -   | -   |

输出参数

| 名称   | 类型      | 描述  |
|------|---------|-----|
| 时间   | object  | -   |
| 前值   | float64 | -   |
| 现值   | float64 | -   |
| 发布日期 | object  | -   |

接口示例

```python
import akshare as ak

macro_germany_retail_sale_yearly_df = ak.macro_germany_retail_sale_yearly()
print(macro_germany_retail_sale_yearly_df)
```

数据示例

```
     时间   前值   现值        发布日期
0   2008年12月  3.2  3.9  2009-04-13
1   2009年12月  3.9 -0.1  2010-04-13
2   2010年12月 -0.1  0.4  2011-04-13
3   2011年12月  0.4  2.0  2012-04-12
4   2012年12月  2.0  2.5  2013-04-13
5   2013年12月  2.5  2.5  2014-11-18
6   2014年12月  2.5  1.0  2015-01-16
7   2015年12月  1.0  0.1  2016-01-16
8   2016年12月  0.1  0.6  2017-01-16
9   2017年12月  0.6  2.0  2018-01-16
10  2018年12月  2.0  1.9  2019-01-16
11  2019年12月  1.9  0.7  2020-01-16
12  2020年12月  0.7 -0.1  2021-01-19
13  2021年12月 -0.1  5.3  2022-01-19
14  2022年12月  5.3  NaN  2023-01-17
```

#### ZEW 经济景气指数

接口: macro_germany_zew

目标地址: https://data.eastmoney.com/cjsj/foreign_1_7.html

描述: 东方财富-数据中心-经济数据一览-德国-ZEW 经济景气指数

限量: 单次返回所有历史数据

输入参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| -   | -   | -   |

输出参数

| 名称   | 类型      | 描述  |
|------|---------|-----|
| 时间   | object  | -   |
| 前值   | float64 | -   |
| 现值   | float64 | -   |
| 发布日期 | object  | -   |

接口示例

```python
import akshare as ak

macro_germany_zew_df = ak.macro_germany_zew()
print(macro_germany_zew_df)
```

数据示例

```
     时间    前值    现值        发布日期
0    2008年01月 -37.2 -41.6  2008-01-16
1    2008年02月 -41.6 -39.5  2008-02-16
2    2008年03月 -39.5 -32.0  2008-03-16
3    2008年04月 -32.0 -40.7  2008-04-16
4    2008年05月 -40.7 -41.4  2008-05-16
..        ...   ...   ...         ...
174  2022年07月 -28.0 -53.8  2022-07-12
175  2022年08月 -53.8 -55.3  2022-08-16
176  2022年09月 -55.3 -61.9  2022-09-13
177  2022年10月 -61.9 -59.2  2022-10-18
178  2022年11月 -59.2   NaN  2022-11-15
```

### 瑞士宏观

#### SVME 采购经理人指数

接口: macro_swiss_svme

目标地址: http://data.eastmoney.com/cjsj/foreign_2_0.html

描述: 东方财富-经济数据-瑞士-SVME采购经理人指数

限量: 单次返回所有历史数据

输入参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| -   | -   | -   |

输出参数

| 名称   | 类型      | 描述  |
|------|---------|-----|
| 时间   | object  | -   |
| 前值   | float64 | -   |
| 现值   | float64 | -   |
| 发布日期 | object  | -   |

接口示例

```python
import akshare as ak

macro_swiss_svme_df = ak.macro_swiss_svme()
print(macro_swiss_svme_df)
```

数据示例

```
     时间    前值    现值        发布日期
0    2007年12月   NaN  62.2  2008-01-01
1    2008年01月  62.2  62.3  2008-02-01
2    2008年02月  62.3  60.5  2008-03-03
3    2008年03月  60.5  55.3  2008-04-01
4    2008年04月  55.3  56.7  2008-05-02
..        ...   ...   ...         ...
174  2022年06月  60.0  59.1  2022-07-01
175  2022年07月  59.1  58.0  2022-08-02
176  2022年08月  58.0  56.4  2022-09-01
177  2022年09月  56.4  57.1  2022-10-03
178  2022年10月  57.1  54.9  2022-11-01
```

#### 贸易帐

接口: macro_swiss_trade

目标地址: http://data.eastmoney.com/cjsj/foreign_2_1.html

描述: 东方财富-经济数据-瑞士-贸易帐

限量: 单次返回所有历史数据

输入参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| -   | -   | -   |

输出参数

| 名称   | 类型      | 描述  |
|------|---------|-----|
| 时间   | object  | -   |
| 前值   | float64 | -   |
| 现值   | float64 | -   |
| 发布日期 | object  | -   |

接口示例

```python
import akshare as ak

macro_swiss_trade_df = ak.macro_swiss_trade()
print(macro_swiss_trade_df)
```

数据示例

```
     时间      前值      现值        发布日期
0   2007第4季度     NaN   1.980  2008-01-20
1   2008第1季度   1.980  12.500  2008-04-20
2   2008第2季度  12.500  24.128  2008-07-21
3   2008第3季度  24.128  14.400  2008-10-21
4   2008第4季度  14.400   2.200  2009-01-20
5   2009第1季度   2.200   1.210  2009-04-20
6   2009第2季度   1.210  15.700  2009-07-21
7   2009第3季度  15.700  19.200  2009-10-21
8   2009第4季度  19.200  14.000  2010-01-20
9   2010第1季度  14.000  20.000  2010-04-20
10  2010第2季度  20.000  18.000  2010-07-21
11  2010第3季度  18.000  17.000  2010-10-21
12  2010第4季度  17.000  12.600  2011-01-20
13  2011第1季度  12.600  11.000  2011-04-20
14  2011第2季度  11.000  17.000  2011-07-21
15  2011第3季度  17.000  19.000  2011-10-21
16  2011第4季度  19.000  20.000  2012-01-20
17  2012第1季度  20.000  17.000  2012-04-20
18  2012第2季度  17.000  22.000  2012-07-21
19  2012第3季度  22.000  20.100  2012-10-21
20  2012第4季度  20.100  10.000  2013-01-20
21  2013第1季度  10.000  19.000  2013-04-20
22  2013第2季度  19.000  27.000  2013-07-21
23  2013第3季度  27.000  25.000  2013-10-21
24  2013第4季度  25.000   5.000  2014-01-20
25  2014第1季度   5.000  21.000  2014-04-20
26  2014第2季度  21.000  14.000  2014-07-21
27  2014第3季度  14.000  24.500  2014-10-21
28  2014第4季度  24.500  15.000  2015-01-20
29  2015第1季度  15.000  25.000  2015-04-20
30  2015第2季度  25.000  35.800  2015-07-21
31  2015第3季度  35.800  30.500  2015-10-20
32  2015第4季度  30.500  25.400  2016-01-26
33  2016第1季度  25.400  21.630  2016-04-21
34  2016第2季度  21.630  35.460  2016-07-21
35  2016第3季度  35.460  43.740  2016-10-20
36  2016第4季度  43.740  27.160  2017-01-26
37  2017第1季度  27.160  31.010  2017-04-27
38  2017第2季度  31.010  28.130  2017-07-20
39  2017第3季度  28.130  29.200  2017-10-19
40  2017第4季度  29.200  26.320  2018-01-30
41  2018第1季度  26.320  17.680  2018-04-24
42  2018第2季度  17.680  26.000  2018-07-19
43  2018第3季度  26.000  24.340  2018-10-18
44  2018第4季度  24.340  18.970  2019-01-29
45  2019第1季度  18.970  31.790  2019-04-18
46  2019第2季度  31.790  40.960  2019-07-18
47  2019第3季度  40.960  40.200  2019-10-17
48  2019第4季度  40.200  19.640  2020-01-28
49  2020第1季度  19.640  40.200  2020-04-21
50  2020第2季度  40.200  32.160  2020-07-21
51  2020第3季度  32.160  32.790  2020-10-20
52  2020第4季度  32.790  28.810  2021-01-28
53  2021第1季度  28.810  58.160  2021-04-22
54  2021第2季度  58.160  55.300  2021-07-20
55  2021第3季度  55.300  50.520  2021-10-19
56  2021第4季度  50.520  36.930  2022-01-27
57  2022第1季度  36.930  29.880  2022-04-26
58  2022第2季度  29.880  38.030  2022-07-19
59  2022第3季度  38.030  40.030  2022-10-20
```

#### 消费者物价指数年率

接口: macro_swiss_cpi_yearly

目标地址: http://data.eastmoney.com/cjsj/foreign_2_2.html

描述: 东方财富-经济数据-瑞士-消费者物价指数年率

限量: 单次返回所有历史数据

输入参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| -   | -   | -   |

输出参数

| 名称   | 类型      | 描述  |
|------|---------|-----|
| 时间   | object  | -   |
| 前值   | float64 | -   |
| 现值   | float64 | -   |
| 发布日期 | object  | -   |

接口示例

```python
import akshare as ak

macro_swiss_cpi_yearly_df = ak.macro_swiss_cpi_yearly()
print(macro_swiss_cpi_yearly_df)
```

数据示例

```
    时间   前值   现值        发布日期
0   2007第4季度  NaN  2.0  2008-01-08
1   2008第4季度  2.0  0.7  2009-01-08
2   2009第4季度  0.7  0.3  2010-01-08
3   2010第4季度  0.3  0.5  2011-01-08
4   2011第4季度  0.5 -0.7  2012-01-08
5   2012第4季度 -0.7 -0.4  2013-01-08
6   2013第4季度 -0.4  0.1  2014-01-08
7   2015第4季度 -0.3 -1.3  2016-01-08
8   2016第4季度 -1.3  0.0  2017-01-05
9   2017第4季度  0.0  0.8  2018-01-08
10  2018第4季度  0.8  0.7  2019-01-09
11  2014第4季度  0.1 -0.3  2019-01-09
12  2019第4季度  0.7  0.2  2020-01-07
13  2020第4季度  0.2 -0.8  2021-01-05
14  2021第4季度 -0.8  1.5  2022-01-04
```

#### GDP 季率

接口: macro_swiss_gdp_quarterly

目标地址: http://data.eastmoney.com/cjsj/foreign_2_3.html

描述: 东方财富-经济数据-瑞士-GDP 季率

限量: 单次返回所有历史数据

输入参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| -   | -   | -   |

输出参数

| 名称   | 类型      | 描述  |
|------|---------|-----|
| 时间   | object  | -   |
| 前值   | float64 | -   |
| 现值   | float64 | -   |
| 发布日期 | object  | -   |

接口示例

```python
import akshare as ak

macro_swiss_gdp_quarterly_df = ak.macro_swiss_gdp_quarterly()
print(macro_swiss_gdp_quarterly_df)
```

数据示例

```
    时间   前值   现值        发布日期
0   2007年12月  NaN  1.0  2008-03-01
1   2008年03月  1.0  0.3  2008-05-31
2   2008年06月  0.3  0.4  2008-08-31
3   2008年09月  0.4  0.0  2008-12-01
4   2008年12月  0.0 -0.3  2009-03-02
5   2009年03月 -0.3 -0.8  2009-05-31
6   2009年06月 -0.8 -0.3  2009-08-31
7   2009年09月 -0.3  0.3  2009-12-01
8   2009年12月  0.3  0.7  2010-03-02
9   2010年03月  0.7  0.4  2010-05-31
10  2010年06月  0.4  0.9  2010-08-31
11  2010年09月  0.9  0.7  2010-12-01
12  2010年12月  0.7  0.9  2011-03-02
13  2011年03月  0.9  0.3  2011-05-31
14  2011年06月  0.3  0.4  2011-08-31
15  2011年09月  0.4  0.2  2011-12-01
16  2011年12月  0.2  0.1  2012-03-01
17  2012年03月  0.1  0.7  2012-05-31
18  2012年06月  0.7 -0.1  2012-08-31
19  2012年09月 -0.1  0.6  2012-12-01
20  2012年12月  0.6  0.2  2013-03-02
21  2013年03月  0.2  0.6  2013-05-31
22  2013年06月  0.6  0.5  2013-08-31
23  2013年09月  0.5  0.5  2013-12-01
24  2013年12月  0.5  0.2  2014-03-02
25  2014年03月  0.2  0.5  2014-05-31
26  2014年06月  0.5  0.0  2014-08-31
27  2014年09月  0.0  0.6  2014-12-01
28  2014年12月  0.6  0.6  2015-03-02
29  2015年03月  0.6 -0.2  2015-05-29
30  2015年06月 -0.2  0.2  2015-08-28
31  2015年09月  0.2  0.0  2015-12-01
32  2015年12月  0.0  0.4  2016-03-02
33  2016年03月  0.4  0.1  2016-06-01
34  2016年06月  0.1  0.6  2016-09-06
35  2016年09月  0.6  0.0  2016-12-02
36  2016年12月  0.0  0.1  2017-03-02
37  2017年03月  0.1  0.3  2017-06-01
38  2017年06月  0.3  0.3  2017-09-05
39  2017年09月  0.3  0.6  2017-11-30
40  2017年12月  0.6  0.6  2018-03-01
41  2018年03月  0.6  0.6  2018-05-31
42  2018年06月  0.6  0.7  2018-09-06
43  2018年09月  0.7 -0.2  2018-11-29
44  2018年12月 -0.2  0.2  2019-02-28
45  2019年03月  0.2  0.6  2019-05-28
46  2019年06月  0.6  0.3  2019-09-05
47  2019年09月  0.3  0.4  2019-11-28
48  2019年12月  0.4  0.3  2020-03-03
49  2020年03月  0.3 -2.6  2020-06-03
50  2020年06月 -2.6 -8.2  2020-08-27
51  2020年09月 -8.2  7.2  2020-12-01
52  2020年12月  7.2  0.3  2021-03-03
53  2021年03月  0.3 -0.5  2021-06-01
54  2021年09月  1.8  1.7  2021-09-07
55  2021年06月 -0.5  1.8  2022-01-10
56  2021年12月  1.7  0.3  2022-02-28
57  2022年03月  0.3  0.5  2022-05-31
58  2022年06月  0.5  0.3  2022-09-05
```

#### GDP 年率

接口: macro_swiss_gbd_yearly

目标地址: http://data.eastmoney.com/cjsj/foreign_2_4.html

描述: 东方财富-经济数据-瑞士-GDP 年率

限量: 单次返回所有历史数据

输入参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| -   | -   | -   |

输出参数

| 名称   | 类型      | 描述  |
|------|---------|-----|
| 时间   | object  | -   |
| 前值   | float64 | -   |
| 现值   | float64 | -   |
| 发布日期 | object  | -   |

接口示例

```python
import akshare as ak

macro_swiss_gbd_yearly_df = ak.macro_swiss_gbd_yearly()
print(macro_swiss_gbd_yearly_df)
```

数据示例

```
    时间   前值   现值        发布日期
0   2007年12月  NaN  3.6  2008-03-02
1   2008年12月  3.6 -0.6  2009-03-03
2   2009年12月 -0.6  0.6  2010-03-03
3   2010年12月  0.6  3.1  2011-03-03
4   2011年12月  3.1  1.3  2012-03-02
5   2012年12月  1.3  1.4  2013-03-03
6   2013年12月  1.4  1.7  2014-03-03
7   2014年12月  1.7  1.9  2015-03-03
8   2015年12月  1.9  0.4  2016-03-02
9   2016年12月  0.4  0.6  2017-03-02
10  2017年12月  0.6  1.9  2018-03-01
11  2018年12月  1.9  1.4  2019-02-28
12  2019年12月  1.4  1.5  2020-03-03
13  2020年12月  1.5 -1.6  2021-03-03
14  2021年12月 -1.6  3.7  2022-02-28
```

#### 央行公布利率决议

接口: macro_swiss_gbd_bank_rate

目标地址: http://data.eastmoney.com/cjsj/foreign_2_5.html

描述: 东方财富-经济数据-瑞士-央行公布利率决议

限量: 单次返回所有历史数据

输入参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| -   | -   | -   |

输出参数

| 名称   | 类型      | 描述  |
|------|---------|-----|
| 时间   | object  | -   |
| 前值   | float64 | -   |
| 现值   | float64 | -   |
| 发布日期 | object  | -   |

接口示例

```python
import akshare as ak

macro_swiss_gbd_bank_rate_df = ak.macro_swiss_gbd_bank_rate()
print(macro_swiss_gbd_bank_rate_df)
```

数据示例

```
     时间    前值    现值        发布日期
0   2008年03月   NaN  2.75  2008-03-16
1   2008年06月  2.75  2.75  2008-06-16
2   2008年09月  2.75  2.75  2008-09-16
3   2008年12月  2.75  0.50  2008-12-16
4   2009年03月  0.50  0.25  2009-03-16
5   2009年06月  0.25  0.25  2009-06-16
6   2009年09月  0.25  0.25  2009-09-16
7   2009年12月  0.25  0.25  2009-12-16
8   2010年03月  0.25  0.25  2010-03-16
9   2010年06月  0.25  0.25  2010-06-16
10  2010年09月  0.25  0.25  2010-09-16
11  2010年12月  0.25  0.25  2010-12-16
12  2011年03月  0.25  0.25  2011-03-16
13  2011年06月  0.25  0.25  2011-06-16
14  2011年09月  0.25  0.00  2011-09-16
15  2011年12月  0.00  0.00  2011-12-16
16  2012年03月  0.00  0.00  2012-03-16
17  2012年06月  0.00  0.00  2012-06-16
18  2012年09月  0.00  0.00  2012-09-16
19  2012年12月  0.00  0.00  2012-12-16
20  2013年03月  0.00  0.00  2013-03-16
21  2013年06月  0.00  0.00  2013-06-16
22  2013年09月  0.00  0.00  2013-09-16
23  2013年12月  0.00  0.00  2013-12-16
24  2014年03月  0.00  0.00  2014-03-16
25  2014年06月  0.00  0.00  2014-06-16
26  2014年09月  0.00  0.00  2014-09-16
27  2014年12月  0.00 -0.25  2014-12-11
28  2015年03月 -0.25 -0.75  2015-03-19
29  2015年06月 -0.75 -0.75  2015-06-18
30  2015年09月 -0.75 -0.75  2015-09-17
31  2015年12月 -0.75 -0.75  2015-12-10
32  2016年03月 -0.75 -0.75  2016-03-17
33  2016年06月 -0.75 -0.75  2016-06-16
34  2016年09月 -0.75 -0.75  2016-09-15
35  2016年12月 -0.75 -0.75  2016-12-15
36  2017年03月 -0.75 -0.75  2017-03-16
37  2017年06月 -0.75 -0.75  2017-06-15
38  2017年09月 -0.75 -0.75  2017-09-14
39  2017年12月 -0.75 -0.75  2017-12-14
40  2018年03月 -0.75 -0.75  2018-03-15
41  2018年06月 -0.75 -0.75  2018-06-21
42  2018年09月 -0.75 -0.75  2018-09-20
43  2018年12月 -0.75 -0.75  2018-12-13
44  2019年03月 -0.75 -0.75  2019-03-21
45  2019年06月 -0.75 -0.75  2019-06-13
46  2019年09月 -0.75 -0.75  2019-09-19
47  2019年12月 -0.75 -0.75  2019-12-12
48  2020年03月 -0.75 -0.75  2020-03-19
49  2020年06月 -0.75 -0.75  2020-06-18
50  2020年09月 -0.75 -0.75  2020-09-24
51  2020年12月 -0.75 -0.75  2020-12-14
52  2021年03月 -0.75 -0.75  2021-03-25
53  2021年06月 -0.75 -0.75  2021-06-17
54  2021年09月 -0.75 -0.75  2021-09-23
55  2021年12月 -0.75 -0.75  2021-12-16
56  2022年03月 -0.75 -0.75  2022-03-24
57  2022年06月 -0.75 -0.25  2022-06-16
58  2022年09月 -0.25  0.50  2022-09-22
59  2022年12月  0.50   NaN  2022-12-15
```

### 日本宏观

#### 央行公布利率决议

接口: macro_japan_bank_rate

目标地址: http://data.eastmoney.com/cjsj/foreign_3_0.html

描述: 东方财富-经济数据-日本-央行公布利率决议

限量: 单次返回所有历史数据

输入参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| -   | -   | -   |

输出参数

| 名称   | 类型      | 描述  |
|------|---------|-----|
| 时间   | object  | -   |
| 前值   | float64 | -   |
| 现值   | float64 | -   |
| 发布日期 | object  | -   |

接口示例

```python
import akshare as ak

macro_japan_bank_rate_df = ak.macro_japan_bank_rate()
print(macro_japan_bank_rate_df)
```

数据示例

```
     时间   前值   现值        发布日期
0    2008年01月  NaN  0.5  2008-01-21
1    2008年02月  0.5  0.5  2008-02-21
2    2008年03月  0.5  0.5  2008-03-21
3    2008年04月  0.5  0.5  2008-04-21
4    2008年05月  0.5  0.5  2008-05-21
..        ...  ...  ...         ...
148  2022年05月 -0.1 -0.1  2022-06-17
149  2022年06月 -0.1 -0.1  2022-06-17
150  2022年07月 -0.1  NaN  2022-07-21
151  2022年09月  NaN -0.1  2022-09-22
152  2022年10月 -0.1 -0.1  2022-10-28
```

#### 全国消费者物价指数年率

接口: macro_japan_cpi_yearly

目标地址: http://data.eastmoney.com/cjsj/foreign_3_1.html

描述: 东方财富-经济数据-日本-全国消费者物价指数年率

限量: 单次返回所有历史数据

输入参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| -   | -   | -   |

输出参数

| 名称   | 类型      | 描述  |
|------|---------|-----|
| 时间   | object  | -   |
| 前值   | float64 | -   |
| 现值   | float64 | -   |
| 发布日期 | object  | -   |

接口示例

```python
import akshare as ak

macro_japan_cpi_yearly_df = ak.macro_japan_cpi_yearly()
print(macro_japan_cpi_yearly_df)
```

数据示例

```
      时间   前值   现值        发布日期
0    2008年01月  0.7  0.7  2008-02-25
1    2008年02月  0.7  1.0  2008-03-27
2    2008年03月  1.0  1.2  2008-04-25
3    2008年04月  1.2  0.8  2008-05-26
4    2008年05月  0.8  1.3  2008-06-25
..        ...  ...  ...         ...
173  2022年06月  2.5  2.4  2022-07-22
174  2022年07月  2.4  2.6  2022-08-19
175  2022年08月  2.6  3.0  2022-09-20
176  2022年09月  3.0  3.0  2022-10-21
177  2022年10月  3.0  NaN  2022-11-18
```

#### 全国核心消费者物价指数年率

接口: macro_japan_core_cpi_yearly

目标地址: http://data.eastmoney.com/cjsj/foreign_2_2.html

描述: 东方财富-经济数据-日本-全国核心消费者物价指数年率

限量: 单次返回所有历史数据

输入参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| -   | -   | -   |

输出参数

| 名称   | 类型      | 描述  |
|------|---------|-----|
| 时间   | object  | -   |
| 前值   | float64 | -   |
| 现值   | float64 | -   |
| 发布日期 | object  | -   |

接口示例

```python
import akshare as ak

macro_japan_core_cpi_yearly_df = ak.macro_japan_core_cpi_yearly()
print(macro_japan_core_cpi_yearly_df)
```

数据示例

```
     时间   前值   现值        发布日期
0    2008年01月 -0.1 -0.1  2008-02-25
1    2008年02月 -0.1 -0.1  2008-03-27
2    2008年03月 -0.1  0.1  2008-04-25
3    2008年04月  0.1 -0.1  2008-05-26
4    2008年05月 -0.1 -0.1  2008-06-25
..        ...  ...  ...         ...
173  2022年06月  0.2  0.2  2022-07-22
174  2022年07月  0.2  0.4  2022-08-19
175  2022年08月  0.4  0.7  2022-09-20
176  2022年09月  0.7  0.9  2022-10-21
177  2022年10月  0.9  NaN  2022-11-18
```

#### 失业率

接口: macro_japan_unemployment_rate

目标地址: http://data.eastmoney.com/cjsj/foreign_2_3.html

描述: 东方财富-经济数据-日本-失业率

限量: 单次返回所有历史数据

输入参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| -   | -   | -   |

输出参数

| 名称   | 类型      | 描述  |
|------|---------|-----|
| 时间   | object  | -   |
| 前值   | float64 | -   |
| 现值   | float64 | -   |
| 发布日期 | object  | -   |

接口示例

```python
import akshare as ak

macro_japan_unemployment_rate_df = ak.macro_japan_unemployment_rate()
print(macro_japan_unemployment_rate_df)
```

数据示例

```
      时间   前值   现值        发布日期
0    2008年01月  3.7  3.9  2008-02-29
1    2008年02月  3.9  4.0  2008-03-31
2    2008年03月  4.0  3.8  2008-04-29
3    2008年04月  3.8  3.9  2008-05-30
4    2008年05月  3.9  4.0  2008-06-29
..        ...  ...  ...         ...
173  2022年06月  2.6  2.6  2022-07-29
174  2022年07月  2.6  2.6  2022-08-30
175  2022年08月  2.6  2.5  2022-09-30
176  2022年09月  2.5  2.7  2022-10-28
177  2022年10月  2.7  NaN  2022-11-29
```

#### 领先指标终值

接口: macro_japan_head_indicator

目标地址: http://data.eastmoney.com/cjsj/foreign_3_4.html

描述: 东方财富-经济数据-日本-领先指标终值

限量: 单次返回所有历史数据

输入参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| -   | -   | -   |

输出参数

| 名称   | 类型      | 描述  |
|------|---------|-----|
| 时间   | object  | -   |
| 前值   | float64 | -   |
| 现值   | float64 | -   |
| 发布日期 | object  | -   |

接口示例

```python
import akshare as ak

macro_japan_head_indicator_df = ak.macro_japan_head_indicator()
print(macro_japan_head_indicator_df)
```

数据示例

```
     时间     前值     现值        发布日期
0    2008年01月  102.1  102.1  2008-03-08
1    2008年02月  102.1  102.4  2008-04-08
2    2008年03月  102.4  100.3  2008-05-07
3    2008年04月  100.3  100.4  2008-06-07
4    2008年05月  100.4  100.0  2008-07-07
..        ...    ...    ...         ...
171  2022年04月  100.8  102.9  2022-06-27
172  2022年05月  102.9  101.2  2022-07-27
173  2022年06月  101.2  100.9  2022-08-29
174  2022年07月  100.9   98.9  2022-09-28
175  2022年08月   98.9  101.3  2022-10-26
```

### 英国宏观

#### Halifax 房价指数月率

接口: macro_uk_halifax_monthly

目标地址: http://data.eastmoney.com/cjsj/foreign_4_0.html

描述: 东方财富-经济数据-英国-Halifax 房价指数月率

限量: 单次返回所有历史数据

输入参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| -   | -   | -   |

输出参数

| 名称   | 类型      | 描述  |
|------|---------|-----|
| 时间   | object  | -   |
| 前值   | float64 | -   |
| 现值   | float64 | -   |
| 发布日期 | object  | -   |

接口示例

```python
import akshare as ak

macro_uk_halifax_monthly_df = ak.macro_uk_halifax_monthly()
print(macro_uk_halifax_monthly_df)
```

数据示例

```
     时间   前值   现值        发布日期
0    2008年01月  NaN -0.4  2008-02-07
1    2008年02月 -0.4  0.1  2008-03-09
2    2008年03月  0.1 -2.5  2008-04-07
3    2008年04月 -2.5 -1.5  2008-05-08
4    2008年05月 -1.5 -2.5  2008-06-07
..        ...  ...  ...         ...
174  2022年07月  1.4 -0.1  2022-08-05
175  2022年08月 -0.1  0.3  2022-09-07
176  2022年09月  0.3 -0.1  2022-10-07
177  2022年10月 -0.1 -0.4  2022-11-07
178  2022年11月 -0.4  NaN  2022-12-07
```

#### Halifax 房价指数年率

接口: macro_uk_halifax_yearly

目标地址: http://data.eastmoney.com/cjsj/foreign_4_1.html

描述: 东方财富-经济数据-英国-Halifax 房价指数年率

限量: 单次返回所有历史数据

输入参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| -   | -   | -   |

输出参数

| 名称   | 类型      | 描述  |
|------|---------|-----|
| 时间   | object  | -   |
| 前值   | float64 | -   |
| 现值   | float64 | -   |
| 发布日期 | object  | -   |

接口示例

```python
import akshare as ak

macro_uk_halifax_yearly_df = ak.macro_uk_halifax_yearly()
print(macro_uk_halifax_yearly_df)
```

数据示例

```
      时间    前值    现值        发布日期
0    2008年01月   5.2   4.5  2008-02-07
1    2008年02月   4.5   4.2  2008-03-09
2    2008年03月   4.2   1.1  2008-04-07
3    2008年04月   1.1  -0.9  2008-05-08
4    2008年05月  -0.9  -3.8  2008-06-07
..        ...   ...   ...         ...
174  2022年07月  12.5  11.8  2022-08-05
175  2022年08月  11.8  11.4  2022-09-07
176  2022年09月  11.4   9.8  2022-10-07
177  2022年10月   9.8   8.3  2022-11-07
178  2022年11月   8.3   NaN  2022-12-07
```

#### 贸易帐

接口: macro_uk_trade

目标地址: http://data.eastmoney.com/cjsj/foreign_4_2.html

描述: 东方财富-经济数据-英国-贸易帐

限量: 单次返回所有历史数据

输入参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| -   | -   | -   |

输出参数

| 名称   | 类型      | 描述  |
|------|---------|-----|
| 时间   | object  | -   |
| 前值   | float64 | -   |
| 现值   | float64 | -   |
| 发布日期 | object  | -   |

接口示例

```python
import akshare as ak

macro_uk_trade_df = ak.macro_uk_trade()
print(macro_uk_trade_df)
```

数据示例

```
           时间     前值     现值        发布日期
0    2008年01月  -3157  -3750  2008-03-11
1    2008年02月  -3750  -3749  2008-04-11
2    2008年03月  -3749  -3194  2008-05-10
3    2008年04月  -3194  -3182  2008-06-10
4    2008年05月  -3182  -2696  2008-07-10
..        ...    ...    ...         ...
171  2022年04月 -11881  -9266  2022-06-13
172  2022年05月  -9266  -9119  2022-07-13
173  2022年06月  -9119 -11387  2022-08-12
174  2022年07月 -11387  -7793  2022-09-12
175  2022年08月  -7793  -7080  2022-10-12
```

#### 央行公布利率决议

接口: macro_uk_bank_rate

目标地址: http://data.eastmoney.com/cjsj/foreign_4_3.html

描述: 东方财富-经济数据-英国-央行公布利率决议

限量: 单次返回所有历史数据

输入参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| -   | -   | -   |

输出参数

| 名称   | 类型      | 描述  |
|------|---------|-----|
| 时间   | object  | -   |
| 前值   | float64 | -   |
| 现值   | float64 | -   |
| 发布日期 | object  | -   |

接口示例

```python
import akshare as ak

macro_uk_bank_rate_df = ak.macro_uk_bank_rate()
print(macro_uk_bank_rate_df)
```

数据示例

```
      时间    前值    现值        发布日期
0    2008年01月   NaN  5.50  2008-01-15
1    2008年02月  5.50  5.25  2008-02-15
2    2008年03月  5.25  5.25  2008-03-15
3    2008年04月  5.25  5.00  2008-04-15
4    2008年05月  5.00  5.00  2008-05-15
..        ...   ...   ...         ...
150  2022年05月  0.75  1.00  2022-05-05
151  2022年06月  1.00  1.25  2022-06-16
152  2022年08月  1.25  1.75  2022-08-04
153  2022年09月  1.75  2.25  2022-09-22
154  2022年11月  2.25  3.00  2022-11-03
```

#### 核心消费者物价指数年率

接口: macro_uk_core_cpi_yearly

目标地址: http://data.eastmoney.com/cjsj/foreign_4_4.html

描述: 东方财富-经济数据-英国-核心消费者物价指数年率

限量: 单次返回所有历史数据

输入参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| -   | -   | -   |

输出参数

| 名称   | 类型      | 描述  |
|------|---------|-----|
| 时间   | object  | -   |
| 前值   | float64 | -   |
| 现值   | float64 | -   |
| 发布日期 | object  | -   |

接口示例

```python
import akshare as ak

macro_uk_core_cpi_yearly_df = ak.macro_uk_core_cpi_yearly()
print(macro_uk_core_cpi_yearly_df)
```

数据示例

```
     时间   前值   现值        发布日期
0    2008年01月  1.4  1.3  2008-02-16
1    2008年02月  1.3  1.2  2008-03-18
2    2008年03月  1.2  1.2  2008-04-16
3    2008年04月  1.2  1.4  2008-05-17
4    2008年05月  1.4  1.5  2008-06-16
..        ...  ...  ...         ...
172  2022年05月  6.2  5.9  2022-06-22
173  2022年06月  5.9  5.8  2022-07-20
174  2022年07月  5.8  6.2  2022-08-17
175  2022年08月  6.2  6.3  2022-09-14
176  2022年09月  6.3  6.5  2022-10-19
```

#### 核心消费者物价指数月率

接口: macro_uk_core_cpi_monthly

目标地址: http://data.eastmoney.com/cjsj/foreign_4_7.html

描述: 东方财富-经济数据-英国-核心消费者物价指数月率

限量: 单次返回所有历史数据

输入参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| -   | -   | -   |

输出参数

| 名称   | 类型      | 描述  |
|------|---------|-----|
| 时间   | object  | -   |
| 前值   | float64 | -   |
| 现值   | float64 | -   |
| 发布日期 | object  | -   |

接口示例

```python
import akshare as ak

macro_uk_cpi_monthly_df = ak.macro_uk_cpi_monthly()
print(macro_uk_cpi_monthly_df)
```

数据示例

```
           时间   前值   现值        发布日期
0    2008年01月  0.6 -1.0  2008-02-16
1    2008年02月 -1.0  0.3  2008-03-18
2    2008年03月  0.3  0.4  2008-04-16
3    2008年04月  0.4  0.5  2008-05-17
4    2008年05月  0.5  0.3  2008-06-16
..        ...  ...  ...         ...
170  2022年05月  0.7  0.5  2022-06-22
171  2022年06月  0.5  0.4  2022-07-20
172  2022年07月  0.4  0.3  2022-08-17
173  2022年08月  0.3  0.8  2022-09-14
174  2022年09月  0.8  0.6  2022-10-19
```

#### 消费者物价指数年率

接口: macro_uk_cpi_yearly

目标地址: http://data.eastmoney.com/cjsj/foreign_4_6.html

描述: 东方财富-经济数据-英国-消费者物价指数年率

限量: 单次返回所有历史数据

输入参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| -   | -   | -   |

输出参数

| 名称   | 类型      | 描述  |
|------|---------|-----|
| 时间   | object  | -   |
| 前值   | float64 | -   |
| 现值   | float64 | -   |
| 发布日期 | object  | -   |

接口示例

```python
import akshare as ak

macro_uk_cpi_yearly_df = ak.macro_uk_cpi_yearly()
print(macro_uk_cpi_yearly_df)
```

数据示例

```
           时间         前值         现值        发布日期
0    2008年01月   2.115385   2.228682  2008-02-16
1    2008年02月   2.228682   2.507232  2008-03-18
2    2008年03月   2.507232   2.399232  2008-04-16
3    2008年04月   2.399232   2.966507  2008-05-17
4    2008年05月   2.966507   3.339695  2008-06-16
..        ...        ...        ...         ...
172  2022年05月   9.000000   9.100000  2022-06-22
173  2022年06月   9.100000   9.400000  2022-07-20
174  2022年07月   9.400000  10.100000  2022-08-17
175  2022年08月  10.100000   9.900000  2022-09-14
176  2022年09月   9.900000  10.100000  2022-10-19
```

#### 消费者物价指数月率

接口: macro_uk_cpi_monthly

目标地址: http://data.eastmoney.com/cjsj/foreign_4_7.html

描述: 东方财富-经济数据-英国-消费者物价指数月率

限量: 单次返回所有历史数据

输入参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| -   | -   | -   |

输出参数

| 名称   | 类型      | 描述  |
|------|---------|-----|
| 时间   | object  | -   |
| 前值   | float64 | -   |
| 现值   | float64 | -   |
| 发布日期 | object  | -   |

接口示例

```python
import akshare as ak

macro_uk_cpi_monthly_df = ak.macro_uk_cpi_monthly()
print(macro_uk_cpi_monthly_df)
```

数据示例

```
      时间   前值   现值        发布日期
0    2008年01月  0.6 -1.0  2008-02-16
1    2008年02月 -1.0  0.3  2008-03-18
2    2008年03月  0.3  0.4  2008-04-16
3    2008年04月  0.4  0.5  2008-05-17
4    2008年05月  0.5  0.3  2008-06-16
..        ...  ...  ...         ...
170  2022年05月  0.7  0.5  2022-06-22
171  2022年06月  0.5  0.4  2022-07-20
172  2022年07月  0.4  0.3  2022-08-17
173  2022年08月  0.3  0.8  2022-09-14
174  2022年09月  0.8  0.6  2022-10-19
```

#### 零售销售月率

接口: macro_uk_retail_monthly

目标地址: http://data.eastmoney.com/cjsj/foreign_4_8.html

描述: 东方财富-经济数据-英国-零售销售月率

限量: 单次返回所有历史数据

输入参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| -   | -   | -   |

输出参数

| 名称   | 类型      | 描述  |
|------|---------|-----|
| 时间   | object  | -   |
| 前值   | float64 | -   |
| 现值   | float64 | -   |
| 发布日期 | object  | -   |

接口示例

```python
import akshare as ak

macro_uk_retail_monthly_df = ak.macro_uk_retail_monthly()
print(macro_uk_retail_monthly_df)
```

数据示例

```
           时间   前值   现值        发布日期
0    2008年01月 -0.4  0.8  2008-02-20
1    2008年02月  0.8  1.0  2008-03-22
2    2008年03月  1.0 -0.4  2008-04-20
3    2008年04月 -0.4 -0.2  2008-05-21
4    2008年05月 -0.2  3.5  2008-06-20
..        ...  ...  ...         ...
172  2022年06月 -0.5 -0.1  2022-07-22
173  2022年07月 -0.1  0.3  2022-08-19
174  2022年08月  0.3 -1.6  2022-09-16
175  2022年09月 -1.6 -1.5  2022-10-21
176  2022年10月 -1.5  NaN  2022-11-18
```

#### 零售销售年率

接口: macro_uk_retail_yearly

目标地址: http://data.eastmoney.com/cjsj/foreign_4_9.html

描述: 东方财富-经济数据-英国-零售销售年率

限量: 单次返回所有历史数据

输入参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| -   | -   | -   |

输出参数

| 名称   | 类型      | 描述  |
|------|---------|-----|
| 时间   | object  | -   |
| 前值   | float64 | -   |
| 现值   | float64 | -   |
| 发布日期 | object  | -   |

接口示例

```python
import akshare as ak

macro_uk_retail_yearly_df = ak.macro_uk_retail_yearly()
print(macro_uk_retail_yearly_df)
```

数据示例

```
     时间   前值   现值        发布日期
0    2008年01月  2.7  5.6  2008-02-20
1    2008年02月  5.6  5.5  2008-03-22
2    2008年03月  5.5  4.6  2008-04-20
3    2008年04月  4.6  4.2  2008-05-21
4    2008年05月  4.2  8.1  2008-06-20
..        ...  ...  ...         ...
170  2022年06月 -4.7 -5.8  2022-07-22
171  2022年07月 -5.8 -3.4  2022-08-19
172  2022年08月 -3.4 -5.4  2022-09-16
173  2022年09月 -5.4 -0.4  2022-10-21
174  2022年10月 -0.4  NaN  2022-11-18
```

#### Rightmove 房价指数年率

接口: macro_uk_rightmove_yearly

目标地址: http://data.eastmoney.com/cjsj/foreign_4_10.html

描述: 东方财富-经济数据-英国-Rightmove 房价指数年率

限量: 单次返回所有历史数据

输入参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| -   | -   | -   |

输出参数

| 名称   | 类型      | 描述  |
|------|---------|-----|
| 时间   | object  | -   |
| 前值   | float64 | -   |
| 现值   | float64 | -   |
| 发布日期 | object  | -   |

接口示例

```python
import akshare as ak

macro_uk_rightmove_yearly_df = ak.macro_uk_rightmove_yearly()
print(macro_uk_rightmove_yearly_df)
```

数据示例

```
     时间    前值    现值        发布日期
0    2008年01月   NaN   3.4  2008-01-20
1    2008年02月   3.4   5.8  2008-02-20
2    2008年03月   5.8   5.0  2008-03-20
3    2008年04月   5.0   1.3  2008-04-20
4    2008年05月   1.3   2.2  2008-05-20
..        ...   ...   ...         ...
148  2022年05月   9.9  10.2  2022-05-24
149  2022年06月  10.2   9.7  2022-06-20
150  2022年07月   9.7   9.3  2022-07-18
151  2022年08月   9.3   8.2  2022-08-16
152  2022年09月   8.2   8.7  2022-09-26
```

#### Rightmove 房价指数月率

接口: macro_uk_rightmove_monthly

目标地址: http://data.eastmoney.com/cjsj/foreign_4_11.html

描述: 东方财富-经济数据-英国-Rightmove 房价指数月率

限量: 单次返回所有历史数据

输入参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| -   | -   | -   |

输出参数

| 名称   | 类型      | 描述  |
|------|---------|-----|
| 时间   | object  | -   |
| 前值   | float64 | -   |
| 现值   | float64 | -   |
| 发布日期 | object  | -   |

接口示例

```python
import akshare as ak

macro_uk_rightmove_monthly_df = ak.macro_uk_rightmove_monthly()
print(macro_uk_rightmove_monthly_df)
```

数据示例

```
           时间   前值   现值        发布日期
0    2008年01月  NaN -0.8  2008-01-20
1    2008年02月 -0.8  3.2  2008-02-20
2    2008年03月  3.2  0.8  2008-03-20
3    2008年04月  0.8 -0.1  2008-04-20
4    2008年05月 -0.1  1.2  2008-05-20
..        ...  ...  ...         ...
148  2022年05月  1.6  2.1  2022-05-24
149  2022年06月  2.1  0.3  2022-06-20
150  2022年07月  0.3  0.4  2022-07-18
151  2022年08月  0.4 -1.3  2022-08-16
152  2022年09月 -1.3  0.7  2022-09-26
```

#### GDP 季率初值

接口: macro_uk_gdp_quarterly

目标地址: http://data.eastmoney.com/cjsj/foreign_4_12.html

描述: 东方财富-经济数据-英国-GDP 季率初值

限量: 单次返回所有历史数据

输入参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| -   | -   | -   |

输出参数

| 名称   | 类型      | 描述  |
|------|---------|-----|
| 时间   | object  | -   |
| 前值   | float64 | -   |
| 现值   | float64 | -   |
| 发布日期 | object  | -   |

接口示例

```python
import akshare as ak

macro_uk_gdp_quarterly_df = ak.macro_uk_gdp_quarterly()
print(macro_uk_gdp_quarterly_df)
```

数据示例

```
    时间    前值    现值        发布日期
0   2008第1季度   0.6   0.4  2008-06-29
1   2008第2季度   0.4   0.2  2008-09-29
2   2008第4季度   0.2  -1.5  2009-03-31
3   2009第1季度  -1.5  -1.9  2009-06-29
4   2009第2季度  -1.9  -0.8  2009-09-29
5   2009第3季度  -0.8   0.4  2009-12-30
6   2009第4季度   0.4   1.6  2010-03-31
7   2010第1季度   1.6   0.5  2010-06-29
8   2010第2季度   0.5   0.7  2010-09-29
9   2010第3季度   0.7   1.9  2010-12-30
10  2010第4季度   1.9  -0.1  2011-03-31
11  2011第1季度  -0.1   0.5  2011-06-29
12  2011第2季度   0.5   0.2  2011-09-29
13  2011第3季度   0.2   0.7  2011-12-30
14  2011第4季度   0.7   0.0  2012-03-30
15  2012第1季度   0.0   0.1  2012-06-29
16  2012第2季度   0.1  -0.2  2012-09-29
17  2012第3季度  -0.2   0.8  2012-12-30
18  2012第4季度   0.8  -0.3  2013-03-31
19  2013第1季度  -0.3   0.5  2013-06-29
20  2013第2季度   0.5   0.5  2013-09-29
21  2013第3季度   0.5   0.8  2013-12-30
22  2013第4季度   0.8   0.5  2014-03-31
23  2014第1季度   0.5   0.8  2014-06-29
24  2014第2季度   0.8   0.9  2014-09-29
25  2014第3季度   0.9   0.8  2014-12-30
26  2014第4季度   0.8   0.8  2015-03-31
27  2015第1季度   0.8   0.3  2015-06-30
28  2015第2季度   0.3   0.5  2015-09-30
29  2015第3季度   0.5   0.3  2015-12-23
30  2015第4季度   0.3   0.7  2016-03-31
31  2016第1季度   0.7   0.3  2016-06-30
32  2016第2季度   0.3   0.6  2016-09-30
33  2016第3季度   0.6   0.4  2016-12-23
34  2016第4季度   0.4   0.7  2017-03-31
35  2017第1季度   0.7   0.3  2017-06-30
36  2017第2季度   0.3   0.2  2017-09-29
37  2017第3季度   0.2   0.4  2017-12-22
38  2017第4季度   0.4   0.4  2018-03-29
39  2018第1季度   0.4   0.2  2018-06-29
40  2018第2季度   0.2   0.4  2018-09-28
41  2018第3季度   0.4   0.6  2018-12-28
42  2018第4季度   0.6   0.2  2019-03-29
43  2019第1季度   0.2   0.5  2019-06-28
44  2019第2季度   0.5  -0.2  2019-08-09
45  2019第3季度  -0.2   0.4  2019-12-20
46  2019第4季度   0.4   NaN  2020-03-31
47  2020第1季度   NaN  -2.5  2020-06-30
48  2020第2季度  -2.5 -19.4  2020-09-30
49  2020第3季度 -19.4  17.6  2020-12-22
50  2020第4季度  17.6   1.5  2021-03-31
51  2021第1季度   1.5  -1.2  2021-06-30
52  2021第2季度  -1.2   5.6  2021-09-30
53  2021第3季度   5.6   0.9  2021-12-22
54  2021第4季度   0.9   1.3  2022-03-31
55  2022第1季度   1.3   0.8  2022-06-30
56  2022第2季度   0.8   0.2  2022-09-30
57  2022第3季度   0.2   NaN  2022-12-22
```

#### GDP 年率初值

接口: macro_uk_gdp_yearly

目标地址: http://data.eastmoney.com/cjsj/foreign_4_13.html

描述: 东方财富-经济数据-英国-GDP 年率初值

限量: 单次返回所有历史数据

输入参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| -   | -   | -   |

输出参数

| 名称   | 类型      | 描述  |
|------|---------|-----|
| 时间   | object  | -   |
| 前值   | float64 | -   |
| 现值   | float64 | -   |
| 发布日期 | object  | -   |

接口示例

```python
import akshare as ak

macro_uk_gdp_yearly_df = ak.macro_uk_gdp_yearly()
print(macro_uk_gdp_yearly_df)
```

数据示例

```
     时间   前值   现值        发布日期
0   2009第4季度  NaN  1.1  2010-03-31
1   2010第4季度  1.1  3.0  2011-03-31
2   2011第4季度  3.0  1.5  2012-03-30
3   2012第4季度  1.5  0.4  2013-03-31
4   2013第4季度  0.4  2.4  2014-03-31
5   2014第4季度  2.4  3.5  2015-03-31
6   2015第4季度  3.5  1.7  2016-03-31
7   2016第4季度  1.7  2.0  2017-03-31
8   2017第4季度  2.0  1.3  2018-03-29
9   2018第4季度  1.3  1.4  2019-03-29
10  2019第4季度  1.4  1.1  2020-03-31
11  2020第4季度  1.1 -6.3  2021-03-31
12  2021第4季度 -6.3  6.6  2022-03-31
```

#### 失业率

接口: macro_uk_unemployment_rate

目标地址: http://data.eastmoney.com/cjsj/foreign_4_14.html

描述: 东方财富-经济数据-英国-失业率

限量: 单次返回所有历史数据

输入参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| -   | -   | -   |

输出参数

| 名称   | 类型      | 描述  |
|------|---------|-----|
| 时间   | object  | -   |
| 前值   | float64 | -   |
| 现值   | float64 | -   |
| 发布日期 | object  | -   |

接口示例

```python
import akshare as ak

macro_uk_unemployment_rate_df = ak.macro_uk_unemployment_rate()
print(macro_uk_unemployment_rate_df)
```

数据示例

```
     时间    前值    现值        发布日期
0    2008年01月  5.19  5.21  2008-02-16
1    2008年02月  5.21  5.21  2008-03-18
2    2008年03月  5.21  5.20  2008-04-16
3    2008年04月  5.20  5.30  2008-05-17
4    2008年05月  5.30  5.20  2008-06-16
..        ...   ...   ...         ...
172  2022年05月  3.80  3.80  2022-07-19
173  2022年06月  3.80  3.80  2022-08-16
174  2022年07月  3.80  3.60  2022-09-13
175  2022年08月  3.60  3.50  2022-10-11
176  2022年09月  3.50   NaN  2022-11-15
```

### 澳大利亚宏观

#### 零售销售月率

接口: macro_australia_retail_rate_monthly

目标地址: http://data.eastmoney.com/cjsj/foreign_5_0.html

描述: 东方财富-经济数据-澳大利亚-零售销售月率

限量: 单次返回所有历史数据

输入参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| -   | -   | -   |

输出参数

| 名称   | 类型      | 描述      |
|------|---------|---------|
| 时间   | object  | -       |
| 前值   | float64 | 注意单位: % |
| 现值   | float64 | 注意单位: % |
| 发布日期 | object  | -       |

接口示例

```python
import akshare as ak

macro_australia_retail_rate_monthly_df = ak.macro_australia_retail_rate_monthly()
print(macro_australia_retail_rate_monthly_df)
```

数据示例

```
       时间    前值    现值      发布日期
0    2008年01月  23.4 -23.6  2008-03-06
1    2008年02月 -23.6  -7.7  2008-04-06
2    2008年03月  -7.7   6.1  2008-05-05
3    2008年04月   6.1  -1.5  2008-06-05
4    2008年05月  -1.5   3.7  2008-07-05
..        ...   ...   ...         ...
199  2024年08月   1.2   2.2  2024-10-01
200  2024年09月   2.2  -1.7  2024-10-31
201  2024年10月  -1.7   5.7  2024-12-02
202  2024年11月   5.7   7.8  2025-01-09
203  2024年12月   7.8   NaN  2025-02-03
[204 rows x 4 columns]
```

#### 贸易帐

接口: macro_australia_trade

目标地址: http://data.eastmoney.com/cjsj/foreign_5_1.html

描述: 东方财富-经济数据-澳大利亚-贸易帐

限量: 单次返回所有历史数据

输入参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| -   | -   | -   |

输出参数

| 名称   | 类型      | 描述        |
|------|---------|-----------|
| 时间   | object  | -         |
| 前值   | float64 | 注意单位: 亿澳元 |
| 现值   | float64 | 注意单位: 亿澳元 |
| 发布日期 | object  | -         |

接口示例

```python
import akshare as ak

macro_australia_trade_df = ak.macro_australia_trade()
print(macro_australia_trade_df)
```

数据示例

```
     时间     前值     现值        发布日期
0    2008年01月  -1148  -3854  2008-03-06
1    2008年02月  -3854  -2677  2008-04-06
2    2008年03月  -2677  -1434  2008-05-05
3    2008年04月  -1434  -1939  2008-06-05
4    2008年05月  -1939  -1285  2008-07-05
..        ...    ...    ...         ...
183  2023年04月  16759  11789  2023-06-08
184  2023年05月  11789   9382  2023-07-06
185  2023年06月   9382  10846  2023-08-03
186  2023年07月  10846   6209  2023-09-07
187  2023年08月   6209   7892  2023-10-05
[188 rows x 4 columns]
```

#### 失业率

接口: macro_australia_unemployment_rate

目标地址: http://data.eastmoney.com/cjsj/foreign_5_2.html

描述: 东方财富-经济数据-澳大利亚-失业率

限量: 单次返回所有历史数据

输入参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| -   | -   | -   |

输出参数

| 名称   | 类型      | 描述      |
|------|---------|---------|
| 时间   | object  | -       |
| 前值   | float64 | 注意单位: % |
| 现值   | float64 | 注意单位: % |
| 发布日期 | object  | -       |

接口示例

```python
import akshare as ak

macro_australia_unemployment_rate_df = ak.macro_australia_unemployment_rate()
print(macro_australia_unemployment_rate_df)
```

数据示例

```
      时间        前值        现值        发布日期
0    2008年01月  4.309566  4.221168  2008-02-15
1    2008年02月  4.221168  3.981161  2008-03-17
2    2008年03月  3.981161  4.058254  2008-04-15
3    2008年04月  4.058254  4.266881  2008-05-16
4    2008年05月  4.266881  4.266643  2008-06-15
..        ...       ...       ...         ...
200  2024年09月  4.131262  4.067827  2024-10-17
201  2024年10月  4.067827  4.105942  2024-11-14
202  2024年11月  4.105942  3.926610  2024-12-12
203  2024年12月  3.926610  3.977499  2025-01-16
204  2025年01月  3.977499       NaN  2025-02-20
[205 rows x 4 columns]
```

#### 生产者物价指数季率

接口: macro_australia_ppi_quarterly

目标地址: http://data.eastmoney.com/cjsj/foreign_5_3.html

描述: 东方财富-经济数据-澳大利亚-生产者物价指数季率

限量: 单次返回所有历史数据

输入参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| -   | -   | -   |

输出参数

| 名称   | 类型      | 描述      |
|------|---------|---------|
| 时间   | object  | -       |
| 前值   | float64 | 注意单位: % |
| 现值   | float64 | 注意单位: % |
| 发布日期 | object  | -       |

接口示例

```python
import akshare as ak

macro_australia_ppi_quarterly_df = ak.macro_australia_ppi_quarterly()
print(macro_australia_ppi_quarterly_df)
```

数据示例

```
    时间   前值   现值        发布日期
0   2008年03月  0.7  1.9  2008-04-29
1   2008年06月  1.9  1.0  2008-07-30
2   2008年09月  1.0  1.9  2008-10-30
3   2008年12月  1.9  1.4  2009-01-29
4   2009年03月  1.4 -0.3  2009-04-29
..       ...  ...  ...         ...
63  2023年12月  1.8  0.9  2024-02-02
64  2024年03月  0.9  0.9  2024-04-26
65  2024年06月  0.9  1.0  2024-08-02
66  2024年09月  1.0  0.9  2024-11-01
67  2024年12月  0.9  NaN  2025-01-31
[68 rows x 4 columns]
```

#### 消费者物价指数季率

接口: macro_australia_cpi_quarterly

目标地址: http://data.eastmoney.com/cjsj/foreign_5_4.html

描述: 东方财富-经济数据-澳大利亚-消费者物价指数季率

限量: 单次返回所有历史数据

输入参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| -   | -   | -   |

输出参数

| 名称   | 类型      | 描述      |
|------|---------|---------|
| 时间   | object  | -       |
| 前值   | float64 | 注意单位: % |
| 现值   | float64 | 注意单位: % |
| 发布日期 | object  | -       |

接口示例

```python
import akshare as ak

macro_australia_cpi_quarterly_df = ak.macro_australia_cpi_quarterly()
print(macro_australia_cpi_quarterly_df)
```

数据示例

```
     时间   前值   现值        发布日期
0   2008第1季度  0.9  1.3  2008-06-07
1   2008第2季度  1.3  1.4  2008-09-07
2   2008第3季度  1.4  1.2  2008-12-08
3   2008第4季度  1.2 -0.3  2009-03-09
4   2009第1季度 -0.3  0.1  2009-06-07
..       ...  ...  ...         ...
63  2023第4季度  1.2  0.6  2024-01-31
64  2024第1季度  0.6  1.0  2024-04-24
65  2024第2季度  1.0  1.0  2024-07-31
66  2024第3季度  1.0  0.2  2024-10-30
67  2024第4季度  0.2  NaN  2025-01-29
[68 rows x 4 columns]
```

#### 消费者物价指数年率

接口: macro_australia_cpi_yearly

目标地址: http://data.eastmoney.com/cjsj/foreign_5_5.html

描述: 东方财富-经济数据-澳大利亚-消费者物价指数年率

限量: 单次返回所有历史数据

输入参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| -   | -   | -   |

输出参数

| 名称   | 类型      | 描述      |
|------|---------|---------|
| 时间   | object  | -       |
| 前值   | float64 | 注意单位: % |
| 现值   | float64 | 注意单位: % |
| 发布日期 | object  | -       |

接口示例

```python
import akshare as ak

macro_australia_cpi_yearly_df = ak.macro_australia_cpi_yearly()
print(macro_australia_cpi_yearly_df)
```

数据示例

```
    时间   前值   现值        发布日期
0   2008年03月  2.9  4.3  2008-04-26
1   2008年06月  4.3  4.4  2008-07-27
2   2008年09月  4.4  5.0  2008-10-27
3   2008年12月  5.0  3.7  2009-01-26
4   2009年03月  3.7  2.4  2009-04-26
..       ...  ...  ...         ...
63  2023年12月  5.4  4.1  2024-01-31
64  2024年03月  4.1  3.6  2024-04-24
65  2024年06月  3.6  3.8  2024-07-31
66  2024年09月  3.8  2.8  2024-10-30
67  2024年12月  2.8  NaN  2025-01-29
[68 rows x 4 columns]
```

#### 央行公布利率决议

接口: macro_australia_bank_rate

目标地址: http://data.eastmoney.com/cjsj/foreign_5_6.html

描述: 东方财富-经济数据-澳大利亚-央行公布利率决议

限量: 单次返回所有历史数据

输入参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| -   | -   | -   |

输出参数

| 名称   | 类型      | 描述      |
|------|---------|---------|
| 时间   | object  | -       |
| 前值   | float64 | 注意单位: % |
| 现值   | float64 | 注意单位: % |
| 发布日期 | object  | -       |

接口示例

```python
import akshare as ak

macro_australia_bank_rate_df = ak.macro_australia_bank_rate()
print(macro_australia_bank_rate_df)
```

数据示例

```
     时间    前值    现值        发布日期
0    2008年01月   NaN  7.00  2008-01-05
1    2008年02月  7.00  7.00  2008-02-05
2    2008年03月  7.00  7.25  2008-03-05
3    2008年04月  7.25  7.25  2008-04-05
4    2008年05月  7.25  7.25  2008-05-05
..        ...   ...   ...         ...
186  2024年08月  4.35  4.35  2024-08-06
187  2024年09月  4.35  4.35  2024-09-24
188  2024年11月  4.35  4.35  2024-11-05
189  2024年12月  4.35  4.35  2024-12-10
190  2025年02月  4.35   NaN  2025-02-04
[191 rows x 4 columns]
```

### 加拿大宏观

#### 新屋开工

接口: macro_canada_new_house_rate

目标地址: http://data.eastmoney.com/cjsj/foreign_7_0.html

描述: 东方财富-经济数据-加拿大-新屋开工

限量: 单次返回所有历史数据

输入参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| -   | -   | -   |

输出参数

| 名称   | 类型      | 描述      |
|------|---------|---------|
| 时间   | object  | -       |
| 前值   | float64 | 注意单位: 万 |
| 现值   | float64 | 注意单位: 万 |
| 发布日期 | object  | -       |

接口示例

```python
import akshare as ak

macro_canada_new_house_rate_df = ak.macro_canada_new_house_rate()
print(macro_canada_new_house_rate_df)
```

数据示例

```
     时间     前值     现值        发布日期
0    2022年10月  29.96  26.71  2022-11-16
1    2022年09月  26.74  29.96  2022-10-14
2    2022年08月  27.53  26.74  2022-09-16
3    2022年07月  27.37  27.53  2022-08-16
4    2022年06月  28.73  27.37  2022-07-18
..        ...    ...    ...         ...
173  2008年05月  21.39  22.13  2008-06-09
174  2008年04月  25.47  21.39  2008-05-10
175  2008年03月  25.69  25.47  2008-04-09
176  2008年02月  22.27  25.69  2008-03-11
177  2008年01月  18.75  22.27  2008-02-09
```

#### 失业率

接口: macro_canada_unemployment_rate

目标地址: http://data.eastmoney.com/cjsj/foreign_7_1.html

描述: 东方财富-经济数据-加拿大-失业率

限量: 单次返回所有历史数据

输入参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| -   | -   | -   |

输出参数

| 名称   | 类型      | 描述      |
|------|---------|---------|
| 时间   | object  | -       |
| 前值   | float64 | 注意单位: % |
| 现值   | float64 | 注意单位: % |
| 发布日期 | object  | -       |

接口示例

```python
import akshare as ak

macro_canada_unemployment_rate_df = ak.macro_canada_unemployment_rate()
print(macro_canada_unemployment_rate_df)
```

数据示例

```
     时间   前值   现值        发布日期
0    2022年11月  5.2  NaN  2022-12-02
1    2022年10月  5.2  5.2  2022-11-04
2    2022年09月  5.4  5.2  2022-10-07
3    2022年08月  4.9  5.4  2022-09-09
4    2022年07月  4.9  4.9  2022-08-05
..        ...  ...  ...         ...
174  2008年05月  6.1  6.1  2008-06-08
175  2008年04月  6.0  6.1  2008-05-09
176  2008年03月  5.8  6.0  2008-04-08
177  2008年02月  5.8  5.8  2008-03-10
178  2008年01月  5.9  5.8  2008-02-08
```

#### 贸易帐

接口: macro_canada_trade

目标地址: http://data.eastmoney.com/cjsj/foreign_7_2.html

描述: 东方财富-经济数据-加拿大-贸易帐

限量: 单次返回所有历史数据

输入参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| -   | -   | -   |

输出参数

| 名称   | 类型      | 描述        |
|------|---------|-----------|
| 时间   | object  | -         |
| 前值   | float64 | 注意单位: 亿加元 |
| 现值   | float64 | 注意单位: 亿加元 |
| 发布日期 | object  | -         |

接口示例

```python
import akshare as ak

macro_canada_trade_df = ak.macro_canada_trade()
print(macro_canada_trade_df)
```

数据示例

```
       时间    前值      现值        发布日期
0    2022年10月  1138     NaN  2022-12-06
1    2022年09月   550  1138.0  2022-11-03
2    2022年08月  2373   550.0  2022-10-05
3    2022年07月  4881  2373.0  2022-09-07
4    2022年06月  4769  4881.0  2022-08-04
..        ...   ...     ...         ...
173  2008年05月  5110  5540.0  2008-07-06
174  2008年04月  5530  5110.0  2008-06-06
175  2008年03月  4940  5530.0  2008-05-06
176  2008年02月  3260  4940.0  2008-04-07
177  2008年01月  2350  3260.0  2008-03-07
```

#### 零售销售月率

接口: macro_canada_retail_rate_monthly

目标地址: http://data.eastmoney.com/cjsj/foreign_7_3.html

描述: 东方财富-经济数据-加拿大-零售销售月率

限量: 单次返回所有历史数据

输入参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| -   | -   | -   |

输出参数

| 名称   | 类型      | 描述      |
|------|---------|---------|
| 时间   | object  | -       |
| 前值   | float64 | 注意单位: % |
| 现值   | float64 | 注意单位: % |
| 发布日期 | object  | -       |

接口示例

```python
import akshare as ak

macro_canada_retail_rate_monthly_df = ak.macro_canada_retail_rate_monthly()
print(macro_canada_retail_rate_monthly_df)
```

数据示例

```
           时间   前值   现值        发布日期
0    2022年10月  1.2  NaN  2022-12-20
1    2022年09月  0.5  1.2  2022-11-22
2    2022年08月 -0.1  0.5  2022-10-21
3    2022年07月  1.9 -0.1  2022-09-23
4    2022年06月  5.2  1.9  2022-08-19
..        ...  ...  ...         ...
167  2008年05月  0.6  0.4  2008-07-22
168  2008年04月  0.1  0.6  2008-06-22
169  2008年03月 -0.7  0.1  2008-05-22
170  2008年02月  1.5 -0.7  2008-04-23
171  2008年01月  0.6  1.5  2008-03-23
```

#### 央行公布利率决议

接口: macro_canada_bank_rate

目标地址: http://data.eastmoney.com/cjsj/foreign_7_4.html

描述: 东方财富-经济数据-加拿大-央行公布利率决议

限量: 单次返回所有历史数据

输入参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| -   | -   | -   |

输出参数

| 名称   | 类型      | 描述      |
|------|---------|---------|
| 时间   | object  | -       |
| 前值   | float64 | 注意单位: % |
| 现值   | float64 | 注意单位: % |
| 发布日期 | object  | -       |

接口示例

```python
import akshare as ak

macro_canada_bank_rate_df = ak.macro_canada_bank_rate()
print(macro_canada_bank_rate_df)
```

数据示例

```
     时间    前值    现值        发布日期
0    2022年12月  3.75   NaN  2022-12-07
1    2022年10月  3.25  3.75  2022-10-26
2    2022年09月  2.50  3.25  2022-09-07
3    2022年07月  1.50  2.50  2022-07-13
4    2022年06月  1.00  1.50  2022-06-01
..        ...   ...   ...         ...
145  2008年05月  3.00  3.00  2008-05-15
146  2008年04月  3.50  3.00  2008-04-15
147  2008年03月  4.00  3.50  2008-03-15
148  2008年02月  4.00  4.00  2008-02-15
149  2008年01月   NaN  4.00  2008-01-15
```

#### 核心消费者物价指数年率

接口: macro_canada_core_cpi_yearly

目标地址: http://data.eastmoney.com/cjsj/foreign_7_5.html

描述: 东方财富-经济数据-加拿大-核心消费者物价指数年率

限量: 单次返回所有历史数据

输入参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| -   | -   | -   |

输出参数

| 名称   | 类型      | 描述      |
|------|---------|---------|
| 时间   | object  | -       |
| 前值   | float64 | 注意单位: % |
| 现值   | float64 | 注意单位: % |
| 发布日期 | object  | -       |

接口示例

```python
import akshare as ak

macro_canada_core_cpi_yearly_df = ak.macro_canada_core_cpi_yearly()
print(macro_canada_core_cpi_yearly_df)
```

数据示例

```
      时间        前值        现值        发布日期
0    2022年11月  5.800000       NaN  2022-12-21
1    2022年10月  6.000000  5.800000  2022-11-16
2    2022年09月  5.800000  6.000000  2022-10-19
3    2022年08月  6.100000  5.800000  2022-09-20
4    2022年07月  6.200000  6.100000  2022-08-16
..        ...       ...       ...         ...
173  2008年05月  1.459854  1.455869  2008-06-21
174  2008年04月  1.278539  1.459854  2008-05-22
175  2008年03月  1.466544  1.278539  2008-04-21
176  2008年02月  1.381215  1.466544  2008-03-23
177  2008年01月  1.476015  1.381215  2008-02-21
```

#### 核心消费者物价指数月率

接口: macro_canada_core_cpi_monthly

目标地址: http://data.eastmoney.com/cjsj/foreign_7_6.html

描述: 东方财富-经济数据-加拿大-核心消费者物价指数月率

限量: 单次返回所有历史数据

输入参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| -   | -   | -   |

输出参数

| 名称   | 类型      | 描述      |
|------|---------|---------|
| 时间   | object  | -       |
| 前值   | float64 | 注意单位: % |
| 现值   | float64 | 注意单位: % |
| 发布日期 | object  | -       |

接口示例

```python
import akshare as ak

macro_canada_core_cpi_monthly_df = ak.macro_canada_core_cpi_monthly()
print(macro_canada_core_cpi_monthly_df)
```

数据示例

```
     时间        前值        现值        发布日期
0    2022年11月  0.400000       NaN  2022-12-21
1    2022年10月  0.400000  0.400000  2022-11-16
2    2022年09月  0.000000  0.400000  2022-10-19
3    2022年08月  0.500000  0.000000  2022-09-20
4    2022年07月  0.300000  0.500000  2022-08-16
..        ...       ...       ...         ...
173  2008年05月  0.270514  0.269784  2008-06-21
174  2008年04月  0.180668  0.270514  2008-05-22
175  2008年03月  0.544959  0.180668  2008-04-21
176  2008年02月  0.090909  0.544959  2008-03-23
177  2008年01月 -0.271985  0.090909  2008-02-21
```

#### 消费者物价指数年率

接口: macro_canada_cpi_yearly

目标地址: http://data.eastmoney.com/cjsj/foreign_7_7.html

描述: 东方财富-经济数据-加拿大-消费者物价指数年率

限量: 单次返回所有历史数据

输入参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| -   | -   | -   |

输出参数

| 名称   | 类型      | 描述      |
|------|---------|---------|
| 时间   | object  | -       |
| 前值   | float64 | 注意单位: % |
| 现值   | float64 | 注意单位: % |
| 发布日期 | object  | -       |

接口示例

```python
import akshare as ak

macro_canada_cpi_yearly_df = ak.macro_canada_cpi_yearly()
print(macro_canada_cpi_yearly_df)
```

数据示例

```
      时间        前值        现值        发布日期
0    2022年11月  6.900000       NaN  2022-12-21
1    2022年10月  6.900000  6.900000  2022-11-16
2    2022年09月  7.000000  6.900000  2022-10-19
3    2022年08月  7.600000  7.000000  2022-09-20
4    2022年07月  8.100000  7.600000  2022-08-16
..        ...       ...       ...         ...
174  2008年05月  1.702509  2.230152  2008-06-20
175  2008年04月  1.350135  1.702509  2008-05-21
176  2008年03月  1.814882  1.350135  2008-04-20
177  2008年02月  2.193784  1.814882  2008-03-22
178  2008年01月  2.376600  2.193784  2008-02-20
```

#### 消费者物价指数月率

接口: macro_canada_cpi_monthly

目标地址: http://data.eastmoney.com/cjsj/foreign_7_8.html

描述: 东方财富-经济数据-加拿大-消费者物价指数月率

限量: 单次返回所有历史数据

输入参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| -   | -   | -   |

输出参数

| 名称   | 类型      | 描述      |
|------|---------|---------|
| 时间   | object  | -       |
| 前值   | float64 | 注意单位: % |
| 现值   | float64 | 注意单位: % |
| 发布日期 | object  | -       |

接口示例

```python
import akshare as ak

macro_canada_cpi_monthly_df = ak.macro_canada_cpi_monthly()
print(macro_canada_cpi_monthly_df)
```

数据示例

```
           时间        前值        现值        发布日期
0    2022年10月  0.065531  0.720367  2022-11-16
1    2022年09月 -0.326584  0.065531  2022-10-19
2    2022年08月  0.130804 -0.326584  2022-09-20
3    2022年07月  0.658328  0.130804  2022-08-16
4    2022年06月  1.401869  0.658328  2022-07-20
..        ...       ...       ...         ...
172  2008年05月  0.799290  0.969163  2008-06-20
173  2008年04月  0.356506  0.799290  2008-05-21
174  2008年03月  0.357782  0.356506  2008-04-20
175  2008年02月 -0.178571  0.357782  2008-03-22
176  2008年01月  0.089366 -0.178571  2008-02-20
```

#### GDP 月率

接口: macro_canada_gdp_monthly

目标地址: http://data.eastmoney.com/cjsj/foreign_7_9.html

描述: 东方财富-经济数据-加拿大-GDP 月率

限量: 单次返回所有历史数据

输入参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| -   | -   | -   |

输出参数

| 名称   | 类型      | 描述      |
|------|---------|---------|
| 时间   | object  | -       |
| 前值   | float64 | 注意单位: % |
| 现值   | float64 | 注意单位: % |
| 发布日期 | object  | -       |

接口示例

```python
import akshare as ak

macro_canada_gdp_monthly_df = ak.macro_canada_gdp_monthly()
print(macro_canada_gdp_monthly_df)
```

数据示例

```
           时间   前值   现值        发布日期
0    2022年09月  0.1  NaN  2022-11-29
1    2022年08月  0.1  0.1  2022-10-28
2    2022年07月  0.1  0.1  2022-09-29
3    2022年06月  0.0  0.1  2022-08-31
4    2022年05月  0.3  0.0  2022-07-29
..        ...  ...  ...         ...
167  2008年05月  0.4 -0.1  2008-07-31
168  2008年04月 -0.2  0.4  2008-07-01
169  2008年03月 -0.2 -0.2  2008-05-31
170  2008年02月  0.6 -0.2  2008-05-02
171  2008年01月 -0.7  0.6  2008-04-01
```

### 重要机构

#### 全球最大黄金 ETF—SPDR Gold Trust 持仓报告

接口: macro_cons_gold

目标地址: https://datacenter.jin10.com/reportType/dc_etf_gold

描述: 全球最大黄金 ETF—SPDR Gold Trust 持仓报告, 数据区间从 20041119-至今

限量: 单次返回所有历史数据

输入参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| -   | -   | -   |

输出参数

| 名称    | 类型      | 描述       |
|-------|---------|----------|
| 商品    | object  | -        |
| 日期    | object  | -        |
| 总库存   | float64 | 注意单位: 吨  |
| 增持/减持 | float64 | 注意单位: 吨  |
| 总价值   | float64 | 注意单位: 美元 |

接口示例

```python
import akshare as ak

macro_cons_gold_df = ak.macro_cons_gold()
print(macro_cons_gold_df)
```

数据示例

```
      商品      日期     总库存  增持/减持  总价值
0     黄金  2004-11-19   57.85  49.76  8.288069e+08
1     黄金  2004-11-22   87.09  29.24  1.253785e+09
2     黄金  2004-11-24   96.42   9.33  1.390569e+09
3     黄金  2004-11-26  100.46   4.04  1.456603e+09
4     黄金  2004-11-29  103.57   3.11  1.502482e+09
   ..         ...     ...    ...           ...
2401  黄金  2024-03-22  835.33  -3.17  5.830850e+10
2402  黄金  2024-03-26  830.15  -5.18  5.816290e+10
2403  黄金  2024-04-01  826.98  -3.17  5.885568e+10
2404  黄金  2024-04-02  829.00   2.02  6.033517e+10
2405  黄金  2024-04-03  830.73   1.73  6.087828e+10
[2406 rows x 5 columns]
```

#### 全球最大白银ETF--iShares Silver Trust持仓报告

接口: macro_cons_silver

目标地址: https://datacenter.jin10.com/reportType/dc_etf_sliver

描述: 全球最大白银 ETF--iShares Silver Trust 持仓报告, 数据区间从 20041202-至今

限量: 单次返回所有历史数据

输入参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| -   | -   | -   |

输出参数

| 名称    | 类型      | 描述       |
|-------|---------|----------|
| 商品    | object  | -        |
| 日期    | object  | -        |
| 总库存   | float64 | 注意单位: 吨  |
| 增持/减持 | float64 | 注意单位: 吨  |
| 总价值   | float64 | 注意单位: 美元 |

接口示例

```python
import akshare as ak

macro_cons_silver_df = ak.macro_cons_silver()
print(macro_cons_silver_df)
```

数据示例

```
      商品      日期      总库存   增持/减持   总价值
0     白银  2014-12-02  10891.14   68.54   5505643206
1     白银  2014-12-03  10806.20  -84.94   5614273871
2     白银  2014-12-05  10737.66  -68.54   5626921446
3     白银  2014-12-11  10648.28  -89.38   5839725280
4     白银  2014-12-13  10606.57  -41.71   5820097631
   ..         ...       ...     ...          ...
1183  白银  2024-03-26  13159.26   52.62  10392678118
1184  白银  2024-03-27  13190.55   31.29  10392678118
1185  白银  2024-04-01  13399.59  209.04  10567503086
1186  白银  2024-04-02  13487.76   88.17  11118260677
1187  白银  2024-04-03  13601.92  114.16  11476815280
[1188 rows x 5 columns]
```

#### 欧佩克报告

接口: macro_cons_opec_month

目标地址: https://datacenter.jin10.com/reportType/dc_opec_report

描述: 欧佩克报告, 数据区间从 20170118-至今

限量: 单次返回所有历史数据, 以网页数据为准.

输入参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| -   | -   | -   |

输出参数

| 名称    | 类型      | 描述  |
|-------|---------|-----|
| 日期    | object  | -   |
| 阿尔及利亚 | float64 | -   |
| 安哥拉   | float64 | -   |
| 厄瓜多尔  | float64 | -   |
| 加蓬    | float64 | -   |
| 伊朗    | float64 | -   |
| 伊拉克   | float64 | -   |
| 科威特   | float64 | -   |
| 利比亚   | float64 | -   |
| 尼日利亚  | float64 | -   |
| 沙特    | float64 | -   |
| 阿联酋   | float64 | -   |
| 委内瑞拉  | float64 | -   |
| 欧佩克产量 | float64 | -   |

接口示例

```python
import akshare as ak

macro_cons_opec_month_df = ak.macro_cons_opec_month()
print(macro_cons_opec_month_df)
```

数据示例

```
      日期  阿尔及利亚  安哥拉 加蓬  伊朗  ...   尼日利亚  沙特    阿联酋  委内瑞拉 欧佩克产量
0   2016/12  108.7  167.4  20.9  372.5  ...  147.4  1044.3  309.0  203.4  3302.9
1   2017/01  105.3  165.8  20.3  378.0  ...  153.3   980.9  295.8  200.7  3202.6
2   2017/02  105.7  163.9  19.8  381.9  ...  156.4   995.2  293.3  199.8  3208.6
3   2017/03  105.1  159.9  20.2  379.2  ...  145.6   990.5  290.9  198.2  3177.0
4   2017/04  105.6  166.7  20.5  379.2  ...  149.6   993.4  290.6  196.7  3197.4
..      ...    ...    ...   ...    ...  ...    ...     ...    ...    ...     ...
70  2023/01  101.5  115.5  18.3  255.7  ...  133.6  1031.9  304.5   68.6  2887.6
71  2023/02  101.7  108.4  19.6  257.1  ...  138.0  1036.1  304.2   70.0  2892.4
72  2023/03  101.3  100.7  20.3  256.7  ...  135.4  1040.5  303.8   69.5  2879.7
73  2023/04  100.5  108.5  20.5  263.0  ...  118.0  1050.0  302.8   72.4  2860.3
74  2023/05   97.4  114.5  21.0  267.9  ...  126.9   997.7  289.4   73.5  2806.5
[75 rows x 13 columns]
```

#### 伦敦金属交易所

##### 持仓报告

接口: macro_euro_lme_holding

目标地址: https://datacenter.jin10.com/reportType/dc_lme_traders_report

描述: 伦敦金属交易所(LME)-持仓报告, 数据区间从 20151022-至今

限量: 单次返回所有历史数据

输入参数

| 名称 | 类型 | 描述 |
|----|----|----|
| -  | -  | -  |

输出参数

| 名称     | 类型 | 描述  |
|--------|----|-----|
| 日期     | -  | -   |
| 铜-多头仓位 | -  | -   |
| ...    | -  | ... |
| 铝-净仓位  | -  | -   |

接口示例

```python
import akshare as ak

macro_euro_lme_holding_df = ak.macro_euro_lme_holding()
print(macro_euro_lme_holding_df)
```

数据示例

```
        日期     铜-多头仓位    铜-空头仓位  ...     铝-多头仓位     铝-空头仓位      铝-净仓位
0    2015-10-22  107991.00 -84529.00  ...  327120.00 -304606.00  631726.00
1    2015-10-23  107881.00 -85003.00  ...  326996.00 -304797.00  631793.00
2    2015-10-26  108904.00 -84484.00  ...  327814.00 -305974.00  633788.00
3    2015-10-27  108818.00 -83438.00  ...  331239.00 -305348.00  636587.00
4    2015-10-28  109631.00 -84558.00  ...  331586.00 -302432.00  634018.00
..          ...        ...       ...  ...        ...        ...        ...
751  2021-08-06    8785.16  12656.84  ...   37711.94   42438.71   -4726.77
752  2021-08-13    8872.87  12349.74  ...   37007.09   41819.17   -4812.08
753  2021-08-20    7676.48  10610.98  ...   35348.31   41029.67   -5681.36
754  2021-08-27    8262.28  11853.99  ...   36410.21   40359.70   -3949.49
755  2021-09-03    7453.93  10574.36  ...   35312.19   42067.01   -6754.82
[756 rows x 19 columns]
```

##### 库存报告

接口: macro_euro_lme_stock

目标地址: https://datacenter.jin10.com/reportType/dc_lme_report

描述: 伦敦金属交易所(LME)-库存报告, 数据区间从 20140702-至今

限量: 单次返回所有历史数据

输入参数

| 名称 | 类型 | 描述 |
|----|----|----|
| -  | -  | -  |

输出参数

| 名称     | 类型  | 描述  |
|--------|-----|-----|
| 日期     | -   | -   |
| 铜-库存   | -   | -   |
| ...    | ... | ... |
| 镍-注销仓单 | -   | -   |

接口示例

```python
import akshare as ak

macro_euro_lme_stock_df = ak.macro_euro_lme_stock()
print(macro_euro_lme_stock_df)
```

数据示例

```
        日期    铜-库存  铜-注册仓单  铜-注销仓单  ...   铝-注销仓单    镍-库存  镍-注册仓单  镍-注销仓单
0     2014-07-02  156775  128475   28300  ...  2990950  305394  192672  112722
1     2014-07-03  157050  129250   27800  ...  2983375  304938  192972  111966
2     2014-07-04  156500  129250   27250  ...  2974775  304536  192900  111636
3     2014-07-07  159350  126350   33000  ...  2965600  305394  194334  111060
4     2014-07-08  158050  126975   31075  ...  2963275  305280  196404  108876
          ...     ...     ...     ...  ...      ...     ...     ...     ...
2144  2024-03-25  117900   99250   18650  ...   218650   77454   71178    6276
2145  2024-03-26  117400   97725   19675  ...   214825   77076   71148    5928
2146  2024-03-27  113100   97725   15375  ...   214075   76836   70716    6120
2147  2024-03-28  112475   97775   14700  ...   212025   77148   70374    6774
2148  2024-04-02  111925   97775   14150  ...   209175   77772   68916    8856
[2149 rows x 19 columns]
```

#### 美国商品期货交易委员会

##### 外汇类非商业持仓报告

接口: macro_usa_cftc_nc_holding

目标地址: https://datacenter.jin10.com/reportType/dc_cftc_nc_report

描述: 美国商品期货交易委员会CFTC外汇类非商业持仓报告, 数据区间从 19830107-至今

限量: 单次返回所有历史数据

输入参数

| 名称 | 类型 | 描述 |
|----|----|----|
| -  | -  | -  |

输出参数

| 名称      | 类型      | 描述 |
|---------|---------|----|
| 日期      | object  | -  |
| 美元-多头仓位 | float64 | -  |
| ...     | ...     | -  |
| 澳元-净仓位  | float64 | -  |

接口示例

```python
import akshare as ak

macro_usa_cftc_nc_holding_df = ak.macro_usa_cftc_nc_holding()
print(macro_usa_cftc_nc_holding_df)
```

数据示例

```
              日期   美元-多头仓位   美元-空头仓位  ...  澳元-多头仓位   澳元-空头仓位    澳元-净仓位
0     1986-01-15       0.0       0.0  ...      0.0       0.0       0.0
1     1986-01-31       0.0       0.0  ...      0.0       0.0       0.0
2     1986-02-14       0.0       0.0  ...      0.0       0.0       0.0
3     1986-02-28       0.0       0.0  ...      0.0       0.0       0.0
4     1986-03-14       0.0       0.0  ...      0.0       0.0       0.0
          ...       ...       ...  ...      ...       ...       ...
1802  2024-02-27  577840.0  509268.0  ...  49640.0  128816.0  -79176.0
1803  2024-03-05  608135.0  540058.0  ...  53966.0  138709.0  -84743.0
1804  2024-03-12  581059.0  517852.0  ...  41591.0  132431.0  -90840.0
1805  2024-03-19  632995.0  528809.0  ...  38207.0  145745.0 -107538.0
1806  2024-03-26  656753.0  513199.0  ...  34438.0  139890.0 -105452.0
[1807 rows x 28 columns]
```

##### 商品类非商业持仓报告

接口: macro_usa_cftc_c_holding

目标地址: https://datacenter.jin10.com/reportType/dc_cftc_c_report

描述: 美国商品期货交易委员会CFTC商品类非商业持仓报告, 数据区间从 19830107-至今

限量: 单次返回所有历史数据

输入参数

| 名称 | 类型 | 描述 |
|----|----|----|
| -  | -  | -  |

输出参数

| 名称        | 类型      | 描述 |
|-----------|---------|----|
| 日期        | object  | -  |
| 纽约原油-多头仓位 | float64 | -  |
| ...       | ...     | -  |
| 玉米-净仓位    | float64 | -  |

接口示例

```python
import akshare as ak

macro_usa_cftc_c_holding_df = ak.macro_usa_cftc_c_holding()
print(macro_usa_cftc_c_holding_df)
```

数据示例

```
            日期  纽约原油-多头仓位  纽约原油-空头仓位  ...   玉米-多头仓位   玉米-空头仓位    玉米-净仓位
0     1986-01-15        0.0        0.0  ...   45615.0   16565.0   29050.0
1     1986-01-31        0.0        0.0  ...   32135.0   34145.0   -2010.0
2     1986-02-14        0.0        0.0  ...   14030.0   50275.0  -36245.0
3     1986-02-28        0.0        0.0  ...   17735.0   59835.0  -42100.0
4     1986-03-14        0.0        0.0  ...   18725.0   58090.0  -39365.0
          ...        ...        ...  ...       ...       ...       ...
1802  2024-02-27   353996.0   129206.0  ...  295676.0  528280.0 -232604.0
1803  2024-03-05   342161.0   103649.0  ...  291829.0  522716.0 -230887.0
1804  2024-03-12   329318.0    95530.0  ...  297244.0  486274.0 -189030.0
1805  2024-03-19   366016.0    88259.0  ...  294129.0  469642.0 -175513.0
1806  2024-03-26   376903.0    98876.0  ...  293932.0  471953.0 -178021.0
[1807 rows x 37 columns]
```

##### 外汇类商业持仓报告

接口: macro_usa_cftc_merchant_currency_holding

目标地址: https://datacenter.jin10.com/reportType/dc_cftc_merchant_currency

描述: 美国商品期货交易委员会CFTC外汇类商业持仓报告, 数据区间从 19860115-至今

限量: 单次返回所有历史数据

输入参数

| 名称 | 类型 | 描述 |
|----|----|----|
| -  | -  | -  |

输出参数

| 名称      | 类型      | 描述 |
|---------|---------|----|
| 日期      | object  | -  |
| 美元-多头仓位 | float64 | -  |
| ...     | ...     | -  |
| 澳元-净仓位  | float64 | -  |

接口示例

```python
import akshare as ak

macro_usa_cftc_merchant_currency_holding_df = ak.macro_usa_cftc_merchant_currency_holding()
print(macro_usa_cftc_merchant_currency_holding_df)
```

数据示例

```
           日期    美元-多头仓位    美元-空头仓位  ...   澳元-多头仓位  澳元-空头仓位    澳元-净仓位
0     1986-01-15        0.0        0.0  ...       0.0      0.0       0.0
1     1986-01-31        0.0        0.0  ...       0.0      0.0       0.0
2     1986-02-14        0.0        0.0  ...       0.0      0.0       0.0
3     1986-02-28        0.0        0.0  ...       0.0      0.0       0.0
4     1986-03-14        0.0        0.0  ...       0.0      0.0       0.0
          ...        ...        ...  ...       ...      ...       ...
1802  2024-02-27   919724.0   975823.0  ...  129292.0  39515.0   89777.0
1803  2024-03-05   969755.0  1022287.0  ...  151903.0  56158.0   95745.0
1804  2024-03-12  1024121.0  1063261.0  ...  151731.0  54053.0   97678.0
1805  2024-03-19   945525.0  1044421.0  ...  158919.0  42857.0  116062.0
1806  2024-03-26   887615.0  1034843.0  ...  162079.0  44539.0  117540.0
[1807 rows x 28 columns]
```

##### 商品类商业持仓报告

接口: macro_usa_cftc_merchant_goods_holding

目标地址: https://datacenter.jin10.com/reportType/dc_cftc_merchant_goods

描述: 美国商品期货交易委员会 CFTC 商品类商业持仓报告, 数据区间从 19860115-至今

限量: 单次返回所有历史数据

输入参数

| 名称 | 类型 | 描述 |
|----|----|----|
| -  | -  | -  |

输出参数

| 名称        | 类型      | 描述 |
|-----------|---------|----|
| 日期        | object  | -  |
| 纽约原油-多头仓位 | float64 | -  |
| ...       | ...     | -  |
| 玉米-净仓位    | float64 | -  |

接口示例

```python
import akshare as ak

macro_usa_cftc_merchant_goods_holding_df = ak.macro_usa_cftc_merchant_goods_holding()
print(macro_usa_cftc_merchant_goods_holding_df)
```

数据示例

```
      日期  纽约原油-多头仓位  纽约原油-空头仓位  ...   玉米-多头仓位   玉米-空头仓位    玉米-净仓位
0     1986-01-15        0.0        0.0  ...  307790.0  307815.0     -25.0
1     1986-01-31        0.0        0.0  ...  323200.0  247390.0   75810.0
2     1986-02-14        0.0        0.0  ...  303885.0  205150.0   98735.0
3     1986-02-28        0.0        0.0  ...  288625.0  204725.0   83900.0
4     1986-03-14        0.0        0.0  ...  276865.0  220480.0   56385.0
          ...        ...        ...  ...       ...       ...       ...
1802  2024-02-27   584071.0   832325.0  ...  670777.0  420149.0  250628.0
1803  2024-03-05   555705.0   827360.0  ...  646676.0  407215.0  239461.0
1804  2024-03-12   600736.0   865790.0  ...  658122.0  459072.0  199050.0
1805  2024-03-19   568958.0   877361.0  ...  683677.0  500187.0  183490.0
1806  2024-03-26   592280.0   907819.0  ...  697082.0  502483.0  194599.0
[1807 rows x 37 columns]
```

#### 芝加哥交易所

##### 贵金属

接口: macro_usa_cme_merchant_goods_holding

目标地址: https://datacenter.jin10.com/org

描述: CME-贵金属, 数据区间从 20180405-至今

限量: 单次返回所有历史数据

输入参数

| 名称 | 类型 | 描述 |
|----|----|----|
| -  | -  | -  |

输出参数

| 名称  | 类型      | 描述 |
|-----|---------|----|
| 日期  | object  | -  |
| 品种  | object  | -  |
| 成交量 | float64 | -  |

接口示例

```python
import akshare as ak

macro_usa_cme_merchant_goods_holding_df = ak.macro_usa_cme_merchant_goods_holding()
print(macro_usa_cme_merchant_goods_holding_df)
```

数据示例

```
               日期     品种     成交量
0      2018-04-05   铜-看跌     597
1      2018-04-05  黄金-期货  288119
2      2018-04-05  黄金-期权   54434
3      2018-04-05  黄金-看涨   36821
4      2018-04-05  黄金-看跌   17613
           ...    ...     ...
30184  2024-04-04   铜-看跌    5704
30185  2024-04-04   铜-看涨   17858
30186  2024-04-04   铜-期权   23562
30187  2024-04-04  铂金-看涨    1077
30188  2024-04-04  黄金-期货  245856
[30189 rows x 3 columns]
```

### 全球宏观

#### 宏观日历

接口: macro_info_ws

目标地址: https://wallstreetcn.com/calendar

描述: 华尔街见闻-日历-宏观

限量: 单次返回指定 date 的数据

输入参数

| 名称   | 类型  | 描述              |
|------|-----|-----------------|
| date | str | date="20240514" |

输出参数

| 名称  | 类型      | 描述 |
|-----|---------|----|
| 时间  | object  | -  |
| 地区  | object  | -  |
| 事件  | object  | -  |
| 重要性 | int64   | -  |
| 今值  | float64 | -  |
| 预期  | float64 | -  |
| 前值  | float64 | -  |
| 链接  | object  | -  |

接口示例

```python
import akshare as ak

macro_info_ws_df = ak.macro_info_ws(date="20240514")
print(macro_info_ws_df)
```

数据示例

```
                     时间  ...                                                 链接
0   2024-05-14 01:00:00  ...
1   2024-05-14 07:50:00  ...  https://wallstreetcn.com/calendar/JP111746/ove...
2   2024-05-14 07:50:00  ...  https://wallstreetcn.com/calendar/JP111747/ove...
3   2024-05-14 10:00:00  ...
4   2024-05-14 12:02:00  ...
5   2024-05-14 14:00:00  ...  https://wallstreetcn.com/calendar/UK121576/ove...
6   2024-05-14 14:00:00  ...  https://wallstreetcn.com/calendar/UK121563/ove...
7   2024-05-14 14:00:00  ...  https://wallstreetcn.com/calendar/UK121574/ove...
8   2024-05-14 14:00:00  ...  https://wallstreetcn.com/calendar/UK121573/ove...
9   2024-05-14 14:00:00  ...  https://wallstreetcn.com/calendar/UK121578/ove...
10  2024-05-14 14:00:00  ...  https://wallstreetcn.com/calendar/UK121577/ove...
11  2024-05-14 14:00:00  ...  https://wallstreetcn.com/calendar/DE112128/ove...
12  2024-05-14 14:00:00  ...  https://wallstreetcn.com/calendar/DE112139/ove...
13  2024-05-14 14:00:00  ...  https://wallstreetcn.com/calendar/DE112152/ove...
14  2024-05-14 14:00:00  ...  https://wallstreetcn.com/calendar/DE112151/ove...
15  2024-05-14 17:00:00  ...  https://wallstreetcn.com/calendar/EA152021/ove...
16  2024-05-14 17:00:00  ...  https://wallstreetcn.com/calendar/DE152230/ove...
17  2024-05-14 17:00:00  ...  https://wallstreetcn.com/calendar/DE152229/ove...
18  2024-05-14 18:00:00  ...  https://wallstreetcn.com/calendar/US151153/ove...
19  2024-05-14 20:30:00  ...  https://wallstreetcn.com/calendar/US111043/ove...
20  2024-05-14 20:30:00  ...  https://wallstreetcn.com/calendar/US111042/ove...
21  2024-05-14 20:30:00  ...  https://wallstreetcn.com/calendar/US111049/ove...
22  2024-05-14 20:30:00  ...  https://wallstreetcn.com/calendar/US111048/ove...
23  2024-05-14 20:30:00  ...  https://wallstreetcn.com/calendar/CA173202/ove...
24  2024-05-14 21:15:00  ...
25  2024-05-14 22:00:00  ...
26  2024-05-15 00:00:00  ...
27  2024-05-15 00:00:00  ...
[28 rows x 8 columns]
```

#### 全球宏观事件

接口: news_economic_baidu

目标地址: https://gushitong.baidu.com/calendar

描述: 全球宏观指标重大事件

限量: 单次返回指定 date 的所有历史数据

输入参数

| 名称   | 类型  | 描述              |
|------|-----|-----------------|
| date | str | date="20241107" |

输出参数

| 名称  | 类型      | 描述      |
|-----|---------|---------|
| 日期  | object  | -       |
| 时间  | object  | -       |
| 地区  | object  | -       |
| 事件  | object  | -       |
| 公布  | float64 | -       |
| 预期  | float64 | -       |
| 前值  | float64 | -       |
| 重要性 | float64 | 数值越大越重要 |

接口示例

```python
import akshare as ak

news_economic_baidu_df = ak.news_economic_baidu(date="20241107")
print(news_economic_baidu_df)
```

数据示例

```
        日期     时间   地区  ...      预期       前值  重要性
0   2024-11-07  01:30   巴西  ...     NaN      -16.72    1
1   2024-11-07  02:00   巴西  ...   49.76       50.23    1
2   2024-11-07  05:30   巴西  ...   11.25       10.75    1
3   2024-11-07  06:30   美国  ...     NaN  3091171.16    1
4   2024-11-07  06:30   美国  ...     NaN     6004.39    1
..         ...    ...  ...  ...     ...         ...  ...
95  2024-11-07  20:00   英国  ...    4.75        5.00    2
96  2024-11-07  21:00  俄罗斯  ...     NaN     6285.00    1
97  2024-11-07  21:00   巴西  ...     NaN      -11.40    1
98  2024-11-07  21:00   巴西  ...     NaN       -0.40    1
99  2024-11-07  21:30   美国  ...  187.50      186.20    2
[100 rows x 8 columns]
```

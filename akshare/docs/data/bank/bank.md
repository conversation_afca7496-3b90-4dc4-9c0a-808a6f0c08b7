## [AKShare](https://github.com/akfamily/akshare) 银行数据

### 银保监分局本级行政处罚

接口: bank_fjcf_table_detail

目标地址: https://www.cbirc.gov.cn/cn/view/pages/ItemDetail.html?docId=881574&itemId=4115&generaltype=9

描述: 首页-政务信息-行政处罚-银保监分局本级-XXXX行政处罚信息公开表, 是信息公开表不是处罚决定书书

限量: 单次返回银保监分局本级行政处罚中的指定页数的所有表格数据

输入参数

| 名称    | 类型  | 描述                                          |
|-------|-----|---------------------------------------------|
| page  | int | page=5; 获取前 5 页数据, 并返回处理好后的数据框              |
| item  | int | item="分局本级"; choice of {"机关", "本级", "分局本级"} |
| begin | int | begin=1; 开始页面                               |

输出参数-分局本级

| 名称           | 类型 | 描述 |
|--------------|----|----|
| 行政处罚决定书文号    | -  | -  |
| 姓名           | -  | -  |
| 单位           | -  | -  |
| 单位名称         | -  | -  |
| 主要负责人姓名      | -  | -  |
| 主要违法违规事实（案由） | -  | -  |
| 行政处罚依据       | -  | -  |
| 行政处罚决定       | -  | -  |
| 作出处罚决定的机关名称  | -  | -  |
| 作出处罚决定的日期    | -  | -  |

接口示例

```python
import akshare as ak

bank_fjcf_table_detail_df = ak.bank_fjcf_table_detail(page=5, item="分局本级")
print(bank_fjcf_table_detail_df)
```

数据示例

```
      行政处罚决定书文号  ...             处罚公布日期
0    楚金罚决字〔2024〕9号  ...  2024-02-08 18:38:00
1    楚金罚决字〔2024〕8号  ...  2024-02-08 18:28:00
2    楚金罚决字〔2024〕7号  ...  2024-02-08 18:18:00
3    遵金罚决字〔2024〕4号  ...  2024-02-08 17:04:00
4    遵金罚决字〔2024〕3号  ...  2024-02-08 17:03:00
..             ...  ...                  ...
85  吉金监罚决字〔2024〕9号  ...  2024-02-02 12:19:02
86  吉金监罚决字〔2024〕8号  ...  2024-02-02 12:18:30
87  吉金监罚决字〔2024〕7号  ...  2024-02-02 12:17:47
88  吉金监罚决字〔2024〕6号  ...  2024-02-02 12:17:08
89  吉金监罚决字〔2024〕5号  ...  2024-02-02 11:47:50
[90 rows x 12 columns]
```

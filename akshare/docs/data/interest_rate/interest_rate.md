## [AKShare](https://github.com/akfamily/akshare) 利率数据

### 主要央行利率

#### 美联储利率决议报告

接口: macro_bank_usa_interest_rate

目标地址: https://datacenter.jin10.com/reportType/dc_usa_interest_rate_decision

描述: 美联储利率决议报告, 数据区间从 ********-至今

限量: 单次返回所有历史数据

输入参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| -   | -   | -   |

输出参数

| 名称  | 类型      | 描述      |
|-----|---------|---------|
| 商品  | object  | -       |
| 日期  | object  | -       |
| 今值  | float64 | 注意单位: % |
| 预测值 | float64 | 注意单位: % |
| 前值  | float64 | 注意单位: % |

接口示例

```python
import akshare as ak

macro_bank_usa_interest_rate_df = ak.macro_bank_usa_interest_rate()
print(macro_bank_usa_interest_rate_df)
```

数据示例

```
            商品          日期     今值   预测值     前值
0    美联储利率决议报告  1982-09-28  10.25   NaN    NaN
1    美联储利率决议报告  1982-10-02  10.00   NaN  10.25
2    美联储利率决议报告  1982-10-08   9.50   NaN  10.00
3    美联储利率决议报告  1982-11-20   9.00   NaN   9.50
4    美联储利率决议报告  1982-12-15   8.50   NaN   9.00
..         ...         ...    ...   ...    ...
282  美联储利率决议报告  2024-06-13   5.50  5.50   5.50
283  美联储利率决议报告  2024-08-01   5.50  5.50   5.50
284  美联储利率决议报告  2024-09-19   5.00  5.25   5.50
285  美联储利率决议报告  2024-11-08    NaN  4.75   5.00
286  美联储利率决议报告  2024-12-19    NaN   NaN    NaN
[287 rows x 5 columns]
```

#### 欧洲央行决议报告

接口: macro_bank_euro_interest_rate

目标地址: https://datacenter.jin10.com/reportType/dc_interest_rate_decision

描述: 欧洲央行决议报告, 数据区间从 ********-至今

限量: 单次返回所有历史数据

输入参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| -   | -   | -   |

输出参数

| 名称  | 类型      | 描述      |
|-----|---------|---------|
| 商品  | object  | -       |
| 日期  | object  | -       |
| 今值  | float64 | 注意单位: % |
| 预测值 | float64 | 注意单位: % |
| 前值  | float64 | 注意单位: % |

接口示例

```python
import akshare as ak

macro_bank_euro_interest_rate_df = ak.macro_bank_euro_interest_rate()
print(macro_bank_euro_interest_rate_df)
```

数据示例

```
           商品          日期    今值   预测值    前值
0    欧洲央行决议报告  1999-01-01  3.00   NaN   NaN
1    欧洲央行决议报告  1999-02-01  3.00   NaN  3.00
2    欧洲央行决议报告  1999-03-01  3.00   NaN  3.00
3    欧洲央行决议报告  1999-04-01  3.00   NaN  3.00
4    欧洲央行决议报告  1999-05-01  2.50   NaN  3.00
..        ...         ...   ...   ...   ...
268  欧洲央行决议报告  2024-06-06  4.25  4.25  4.50
269  欧洲央行决议报告  2024-07-18  4.25  4.25  4.25
270  欧洲央行决议报告  2024-09-12  3.65  3.65  4.25
271  欧洲央行决议报告  2024-10-17  3.40  3.40  3.65
272  欧洲央行决议报告  2024-12-12   NaN   NaN   NaN
[273 rows x 5 columns]
```

#### 新西兰联储决议报告

接口: macro_bank_newzealand_interest_rate

目标地址: https://datacenter.jin10.com/reportType/dc_newzealand_interest_rate_decision

描述: 新西兰联储决议报告, 数据区间从 ********-至今

限量: 单次返回所有历史数据

输入参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| -   | -   | -   |

输出参数

| 名称  | 类型      | 描述      |
|-----|---------|---------|
| 商品  | object  | -       |
| 日期  | object  | -       |
| 今值  | float64 | 注意单位: % |
| 预测值 | float64 | 注意单位: % |
| 前值  | float64 | 注意单位: % |

接口示例

```python
import akshare as ak

macro_bank_newzealand_interest_rate_df = ak.macro_bank_newzealand_interest_rate()
print(macro_bank_newzealand_interest_rate_df)
```

数据示例

```
            商品          日期    今值   预测值    前值
0    新西兰利率决议报告  1999-04-01  4.50   NaN   NaN
1    新西兰利率决议报告  1999-05-01  4.50   NaN  4.50
2    新西兰利率决议报告  1999-06-01  4.50   NaN  4.50
3    新西兰利率决议报告  1999-07-01  4.50   NaN  4.50
4    新西兰利率决议报告  1999-08-01  4.50   NaN  4.50
..         ...         ...   ...   ...   ...
230  新西兰利率决议报告  2024-05-22  5.50  5.50  5.50
231  新西兰利率决议报告  2024-07-10  5.50  5.50  5.50
232  新西兰利率决议报告  2024-08-14  5.25  5.50  5.50
233  新西兰利率决议报告  2024-10-09  4.75  4.75  5.25
234  新西兰利率决议报告  2024-11-27   NaN   NaN  4.75
[235 rows x 5 columns]
```

#### 中国央行决议报告

接口: macro_bank_china_interest_rate

目标地址: https://datacenter.jin10.com/reportType/dc_china_interest_rate_decision

描述: 中国央行决议报告, 数据区间从 ********-至今

限量: 单次返回所有历史数据

输入参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| -   | -   | -   |

输出参数

| 名称  | 类型      | 描述      |
|-----|---------|---------|
| 商品  | object  | -       |
| 日期  | object  | -       |
| 今值  | float64 | 注意单位: % |
| 预测值 | float64 | 注意单位: % |
| 前值  | float64 | 注意单位: % |

接口示例

```python
import akshare as ak

macro_bank_china_interest_rate_df = ak.macro_bank_china_interest_rate()
print(macro_bank_china_interest_rate_df)
```

数据示例

```
           商品          日期    今值  预测值    前值
0    中国央行决议报告  1991-05-01  8.64  NaN   NaN
1    中国央行决议报告  1991-06-01  8.64  NaN  8.64
2    中国央行决议报告  1991-07-01  8.64  NaN  8.64
3    中国央行决议报告  1991-08-01  8.64  NaN  8.64
4    中国央行决议报告  1991-09-01  8.64  NaN  8.64
..        ...         ...   ...  ...   ...
213  中国央行决议报告  2015-08-25  4.60  NaN  4.85
214  中国央行决议报告  2015-10-23  4.35  NaN  4.60
215  中国央行决议报告  2019-09-20  4.20  NaN  4.25
216  中国央行决议报告  2019-10-21  4.20  NaN  4.20
217  中国央行决议报告  2019-11-20  4.15  4.2  4.20
[218 rows x 5 columns]
```

#### 瑞士央行利率决议报告

接口: macro_bank_switzerland_interest_rate

目标地址: https://datacenter.jin10.com/reportType/dc_switzerland_interest_rate_decision

描述: 瑞士央行利率决议报告, 数据区间从 ********-至今

限量: 单次返回所有历史数据

输入参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| -   | -   | -   |

输出参数

| 名称  | 类型      | 描述      |
|-----|---------|---------|
| 商品  | object  | -       |
| 日期  | object  | -       |
| 今值  | float64 | 注意单位: % |
| 预测值 | float64 | 注意单位: % |
| 前值  | float64 | 注意单位: % |

接口示例

```python
import akshare as ak

macro_bank_switzerland_interest_rate_df = ak.macro_bank_switzerland_interest_rate()
print(macro_bank_switzerland_interest_rate_df)
```

数据示例

```
          商品          日期    今值   预测值    前值
0   瑞士央行决议报告  2008-03-13  2.75  2.75  2.75
1   瑞士央行决议报告  2008-06-19  2.75  2.75  2.75
2   瑞士央行决议报告  2008-09-18  2.75  2.75  2.75
3   瑞士央行决议报告  2008-10-08  2.50   NaN  2.75
4   瑞士央行决议报告  2008-12-11  0.50  0.50  1.00
..       ...         ...   ...   ...   ...
67  瑞士央行决议报告  2023-12-14  1.75  1.75  1.75
68  瑞士央行决议报告  2024-03-21  1.50  1.75  1.75
69  瑞士央行决议报告  2024-06-20  1.25  1.50  1.50
70  瑞士央行决议报告  2024-09-26  1.00  1.00  1.25
71  瑞士央行决议报告  2024-12-12   NaN   NaN   NaN
[72 rows x 5 columns]
```

#### 英国央行决议报告

接口: macro_bank_english_interest_rate

目标地址: https://datacenter.jin10.com/reportType/dc_english_interest_rate_decision

描述: 英国央行决议报告, 数据区间从 ********-至今

限量: 单次返回所有历史数据

输入参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| -   | -   | -   |

输出参数

| 名称  | 类型      | 描述      |
|-----|---------|---------|
| 商品  | object  | -       |
| 日期  | object  | -       |
| 今值  | float64 | 注意单位: % |
| 预测值 | float64 | 注意单位: % |
| 前值  | float64 | 注意单位: % |

接口示例

```python
import akshare as ak

macro_bank_english_interest_rate_df = ak.macro_bank_english_interest_rate()
print(macro_bank_english_interest_rate_df)
```

数据示例

```
           商品          日期    今值   预测值    前值
0    英国央行决议报告  1970-01-01  8.00   NaN   NaN
1    英国央行决议报告  1970-02-01  8.00   NaN  8.00
2    英国央行决议报告  1970-03-01  8.00   NaN  8.00
3    英国央行决议报告  1970-04-01  7.50   NaN  8.00
4    英国央行决议报告  1970-05-01  7.00   NaN  7.50
..        ...         ...   ...   ...   ...
627  英国央行决议报告  2024-06-20  5.25  5.25  5.25
628  英国央行决议报告  2024-08-01  5.00  5.00  5.25
629  英国央行决议报告  2024-09-19  5.00  5.00  5.00
630  英国央行决议报告  2024-11-07   NaN  4.75  5.00
631  英国央行决议报告  2024-12-19   NaN   NaN   NaN
[632 rows x 5 columns]
```

#### 澳洲联储决议报告

接口: macro_bank_australia_interest_rate

目标地址: https://datacenter.jin10.com/reportType/dc_australia_interest_rate_decision

描述: 澳洲联储决议报告, 数据区间从 ********-至今

限量: 单次返回所有历史数据

输入参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| -   | -   | -   |

输出参数

| 名称  | 类型      | 描述      |
|-----|---------|---------|
| 商品  | object  | -       |
| 日期  | object  | -       |
| 今值  | float64 | 注意单位: % |
| 预测值 | float64 | 注意单位: % |
| 前值  | float64 | 注意单位: % |

接口示例

```python
import akshare as ak

macro_bank_australia_interest_rate_df = ak.macro_bank_australia_interest_rate()
print(macro_bank_australia_interest_rate_df)
```

数据示例

```
           商品          日期     今值   预测值    前值
0    澳洲联储决议报告  1980-02-01   7.92   NaN   NaN
1    澳洲联储决议报告  1980-03-01   8.20   NaN  7.92
2    澳洲联储决议报告  1980-04-01   9.25   NaN  8.20
3    澳洲联储决议报告  1980-05-01   8.98   NaN  9.25
4    澳洲联储决议报告  1980-06-01  10.74   NaN  8.98
..        ...         ...    ...   ...   ...
517  澳洲联储决议报告  2024-06-18   4.35  4.35  4.35
518  澳洲联储决议报告  2024-08-06   4.35  4.35  4.35
519  澳洲联储决议报告  2024-09-24   4.35  4.35  4.35
520  澳洲联储决议报告  2024-11-05   4.35  4.35  4.35
521  澳洲联储决议报告  2024-12-10    NaN   NaN   NaN
[522 rows x 5 columns]
```

#### 日本利率决议报告

接口: macro_bank_japan_interest_rate

目标地址: https://datacenter.jin10.com/reportType/dc_japan_interest_rate_decision

描述: 日本利率决议报告, 数据区间从 ********-至今

限量: 单次返回所有历史数据

输入参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| -   | -   | -   |

输出参数

| 名称  | 类型      | 描述      |
|-----|---------|---------|
| 商品  | object  | -       |
| 日期  | object  | -       |
| 今值  | float64 | 注意单位: % |
| 预测值 | float64 | 注意单位: % |
| 前值  | float64 | 注意单位: % |

接口示例

```python
import akshare as ak

macro_bank_japan_interest_rate_df = ak.macro_bank_japan_interest_rate()
print(macro_bank_japan_interest_rate_df)
```

数据示例

```
           商品          日期    今值   预测值    前值
0    日本央行决议报告  2008-02-14  0.50  0.50  0.50
1    日本央行决议报告  2008-03-07  0.50  0.50  0.50
2    日本央行决议报告  2008-04-09  0.50  0.50  0.50
3    日本央行决议报告  2008-04-30  0.50  0.50  0.50
4    日本央行决议报告  2008-06-13  0.50  0.50  0.50
..        ...         ...   ...   ...   ...
190  日本央行决议报告  2024-06-14  0.10  0.10  0.10
191  日本央行决议报告  2024-07-31  0.25  0.10  0.10
192  日本央行决议报告  2024-09-20  0.25  0.25  0.25
193  日本央行决议报告  2024-10-31  0.25  0.25  0.25
194  日本央行决议报告  2024-12-19   NaN   NaN  0.25
[195 rows x 5 columns]
```

#### 俄罗斯利率决议报告

接口: macro_bank_russia_interest_rate

目标地址: https://datacenter.jin10.com/reportType/dc_russia_interest_rate_decision

描述: 俄罗斯利率决议报告, 数据区间从 ********-至今

限量: 单次返回所有历史数据

输入参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| -   | -   | -   |

输出参数

| 名称  | 类型      | 描述      |
|-----|---------|---------|
| 商品  | object  | -       |
| 日期  | object  | -       |
| 今值  | float64 | 注意单位: % |
| 预测值 | float64 | 注意单位: % |
| 前值  | float64 | 注意单位: % |

接口示例

```python
import akshare as ak

macro_bank_russia_interest_rate_df = ak.macro_bank_russia_interest_rate()
print(macro_bank_russia_interest_rate_df)
```

数据示例

```
            商品          日期    今值   预测值    前值
0    俄罗斯央行决议报告  2003-06-01   6.5   NaN   NaN
1    俄罗斯央行决议报告  2003-07-01   6.5   NaN   6.5
2    俄罗斯央行决议报告  2003-08-01   6.5   NaN   6.5
3    俄罗斯央行决议报告  2003-09-01   6.5   NaN   6.5
4    俄罗斯央行决议报告  2003-10-01   6.5   NaN   6.5
..         ...         ...   ...   ...   ...
214  俄罗斯央行决议报告  2024-04-26  16.0  16.0  16.0
215  俄罗斯央行决议报告  2024-06-07  16.0  16.0  16.0
216  俄罗斯央行决议报告  2024-07-26  18.0  18.0  16.0
217  俄罗斯央行决议报告  2024-09-13  19.0  18.0  18.0
218  俄罗斯央行决议报告  2024-10-25  21.0  20.0  19.0
[219 rows x 5 columns]
```

#### 印度利率决议报告

接口: macro_bank_india_interest_rate

目标地址: https://datacenter.jin10.com/reportType/dc_india_interest_rate_decision

描述: 印度利率决议报告, 数据区间从 ********-至今

限量: 单次返回所有历史数据

输入参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| -   | -   | -   |

输出参数

| 名称  | 类型      | 描述      |
|-----|---------|---------|
| 商品  | object  | -       |
| 日期  | object  | -       |
| 今值  | float64 | 注意单位: % |
| 预测值 | float64 | 注意单位: % |
| 前值  | float64 | 注意单位: % |

接口示例

```python
import akshare as ak

macro_bank_india_interest_rate_df = ak.macro_bank_india_interest_rate()
print(macro_bank_india_interest_rate_df)
```

数据示例

```
           商品          日期     今值  预测值     前值
0    印度央行决议报告  2000-08-01   7.38  NaN    NaN
1    印度央行决议报告  2000-09-01  13.35  NaN   7.38
2    印度央行决议报告  2000-10-01  10.52  NaN  13.35
3    印度央行决议报告  2000-11-01   8.61  NaN  10.52
4    印度央行决议报告  2000-12-01   8.00  NaN   8.61
..        ...         ...    ...  ...    ...
222  印度央行决议报告  2024-02-08   6.50  6.5   6.50
223  印度央行决议报告  2024-04-05   6.50  6.5   6.50
224  印度央行决议报告  2024-06-07   6.50  6.5   6.50
225  印度央行决议报告  2024-08-08   6.50  6.5   6.50
226  印度央行决议报告  2024-10-09   6.50  6.5   6.50
[227 rows x 5 columns]
```

#### 巴西利率决议报告

接口: macro_bank_brazil_interest_rate

目标地址: https://datacenter.jin10.com/reportType/dc_brazil_interest_rate_decision

描述: 巴西利率决议报告, 数据区间从********-至今

限量: 单次返回所有历史数据

输入参数

| 名称  | 类型  | 描述  |
|-----|-----|-----|
| -   | -   | -   |

输出参数

| 名称  | 类型      | 描述      |
|-----|---------|---------|
| 商品  | object  | -       |
| 日期  | object  | -       |
| 今值  | float64 | 注意单位: % |
| 预测值 | float64 | 注意单位: % |
| 前值  | float64 | 注意单位: % |

接口示例

```python
import akshare as ak

macro_bank_brazil_interest_rate_df = ak.macro_bank_brazil_interest_rate()
print(macro_bank_brazil_interest_rate_df)
```

数据示例

```
           商品          日期     今值    预测值     前值
0    巴西央行决议报告  2008-02-01  11.25    NaN    NaN
1    巴西央行决议报告  2008-04-01  11.25    NaN  11.25
2    巴西央行决议报告  2008-05-01  11.75    NaN  11.25
3    巴西央行决议报告  2008-07-01  12.25    NaN  11.75
4    巴西央行决议报告  2008-08-01  13.00    NaN  12.25
..        ...         ...    ...    ...    ...
145  巴西央行决议报告  2024-06-20  10.50  10.50  10.50
146  巴西央行决议报告  2024-08-01  10.50  10.50  10.50
147  巴西央行决议报告  2024-09-19  10.75  10.75  10.50
148  巴西央行决议报告  2024-11-07    NaN  11.25  10.75
149  巴西央行决议报告  2024-12-12    NaN    NaN    NaN
[150 rows x 5 columns]
```

### 银行间拆借利率

接口: rate_interbank

目标地址: https://data.eastmoney.com/shibor/shibor.aspx?m=sg&t=88&d=99333&cu=sgd&type=009065&p=79

描述: 东方财富-拆借利率一览-具体市场的具体品种的具体指标的拆借利率数据

限量: 返回所有历史数据

输入参数

| 名称        | 类型  | 描述                                                                                                                                 |
|-----------|-----|------------------------------------------------------------------------------------------------------------------------------------|
| market    | str | market="上海银行同业拆借市场"; 参见 **市场-品种-指标一览表**                                                                                            |
| symbol    | str | symbol="Shibor人民币"; 参见 **市场-品种-指标一览表**                                                                                             |
| indicator | str | indicator="隔夜"; 参见 **市场-品种-指标一览表**                                                                                                 |

市场-品种-指标一览表

| market      | symbol    | indicator |
|-------------|-----------|-----------|
| 上海银行同业拆借市场  | Shibor人民币 | 隔夜        |
| 上海银行同业拆借市场  | Shibor人民币 | 1周        |
| 上海银行同业拆借市场  | Shibor人民币 | 2周        |
| 上海银行同业拆借市场  | Shibor人民币 | 1月        |
| 上海银行同业拆借市场  | Shibor人民币 | 3月        |
| 上海银行同业拆借市场  | Shibor人民币 | 6月        |
| 上海银行同业拆借市场  | Shibor人民币 | 9月        |
| 上海银行同业拆借市场  | Shibor人民币 | 1年        |
| 中国银行同业拆借市场  | Chibor人民币 | 隔夜        |
| 中国银行同业拆借市场  | Chibor人民币 | 1周        |
| 中国银行同业拆借市场  | Chibor人民币 | 2周        |
| 中国银行同业拆借市场  | Chibor人民币 | 3周        |
| 中国银行同业拆借市场  | Chibor人民币 | 1月        |
| 中国银行同业拆借市场  | Chibor人民币 | 2月        |
| 中国银行同业拆借市场  | Chibor人民币 | 3月        |
| 中国银行同业拆借市场  | Chibor人民币 | 4月        |
| 中国银行同业拆借市场  | Chibor人民币 | 6月        |
| 中国银行同业拆借市场  | Chibor人民币 | 9月        |
| 中国银行同业拆借市场  | Chibor人民币 | 1年        |
| 伦敦银行同业拆借市场  | Libor英镑   | 隔夜        |
| 伦敦银行同业拆借市场  | Libor英镑   | 1周        |
| 伦敦银行同业拆借市场  | Libor英镑   | 1月        |
| 伦敦银行同业拆借市场  | Libor英镑   | 2月        |
| 伦敦银行同业拆借市场  | Libor英镑   | 3月        |
| 伦敦银行同业拆借市场  | Libor英镑   | 8月        |
| 伦敦银行同业拆借市场  | Libor美元   | 隔夜        |
| 伦敦银行同业拆借市场  | Libor美元   | 1周        |
| 伦敦银行同业拆借市场  | Libor美元   | 1月        |
| 伦敦银行同业拆借市场  | Libor美元   | 2月        |
| 伦敦银行同业拆借市场  | Libor美元   | 3月        |
| 伦敦银行同业拆借市场  | Libor美元   | 8月        |
| 伦敦银行同业拆借市场  | Libor欧元   | 隔夜        |
| 伦敦银行同业拆借市场  | Libor欧元   | 1周        |
| 伦敦银行同业拆借市场  | Libor欧元   | 1月        |
| 伦敦银行同业拆借市场  | Libor欧元   | 2月        |
| 伦敦银行同业拆借市场  | Libor欧元   | 3月        |
| 伦敦银行同业拆借市场  | Libor欧元   | 8月        |
| 伦敦银行同业拆借市场  | Libor日元   | 隔夜        |
| 伦敦银行同业拆借市场  | Libor日元   | 1周        |
| 伦敦银行同业拆借市场  | Libor日元   | 1月        |
| 伦敦银行同业拆借市场  | Libor日元   | 2月        |
| 伦敦银行同业拆借市场  | Libor日元   | 3月        |
| 伦敦银行同业拆借市场  | Libor日元   | 8月        |
| 欧洲银行同业拆借市场  | Euribor欧元 | 1周        |
| 欧洲银行同业拆借市场  | Euribor欧元 | 2周        |
| 欧洲银行同业拆借市场  | Euribor欧元 | 3周        |
| 欧洲银行同业拆借市场  | Euribor欧元 | 1月        |
| 欧洲银行同业拆借市场  | Euribor欧元 | 2月        |
| 欧洲银行同业拆借市场  | Euribor欧元 | 3月        |
| 欧洲银行同业拆借市场  | Euribor欧元 | 4月        |
| 欧洲银行同业拆借市场  | Euribor欧元 | 5月        |
| 欧洲银行同业拆借市场  | Euribor欧元 | 6月        |
| 欧洲银行同业拆借市场  | Euribor欧元 | 7月        |
| 欧洲银行同业拆借市场  | Euribor欧元 | 8月        |
| 欧洲银行同业拆借市场  | Euribor欧元 | 9月        |
| 欧洲银行同业拆借市场  | Euribor欧元 | 10月       |
| 欧洲银行同业拆借市场  | Euribor欧元 | 11月       |
| 欧洲银行同业拆借市场  | Euribor欧元 | 1年        |
| 香港银行同业拆借市场  | Hibor港币   | 隔夜        |
| 香港银行同业拆借市场  | Hibor港币   | 1周        |
| 香港银行同业拆借市场  | Hibor港币   | 2周        |
| 香港银行同业拆借市场  | Hibor港币   | 1月        |
| 香港银行同业拆借市场  | Hibor港币   | 2月        |
| 香港银行同业拆借市场  | Hibor港币   | 3月        |
| 香港银行同业拆借市场  | Hibor港币   | 4月        |
| 香港银行同业拆借市场  | Hibor港币   | 5月        |
| 香港银行同业拆借市场  | Hibor港币   | 6月        |
| 香港银行同业拆借市场  | Hibor港币   | 7月        |
| 香港银行同业拆借市场  | Hibor港币   | 8月        |
| 香港银行同业拆借市场  | Hibor港币   | 9月        |
| 香港银行同业拆借市场  | Hibor港币   | 10月       |
| 香港银行同业拆借市场  | Hibor港币   | 11月       |
| 香港银行同业拆借市场  | Hibor港币   | 1年        |
| 香港银行同业拆借市场  | Hibor美元   | 隔夜        |
| 香港银行同业拆借市场  | Hibor美元   | 1周        |
| 香港银行同业拆借市场  | Hibor美元   | 2周        |
| 香港银行同业拆借市场  | Hibor美元   | 1月        |
| 香港银行同业拆借市场  | Hibor美元   | 2月        |
| 香港银行同业拆借市场  | Hibor美元   | 3月        |
| 香港银行同业拆借市场  | Hibor美元   | 4月        |
| 香港银行同业拆借市场  | Hibor美元   | 5月        |
| 香港银行同业拆借市场  | Hibor美元   | 6月        |
| 香港银行同业拆借市场  | Hibor美元   | 7月        |
| 香港银行同业拆借市场  | Hibor美元   | 8月        |
| 香港银行同业拆借市场  | Hibor美元   | 9月        |
| 香港银行同业拆借市场  | Hibor美元   | 10月       |
| 香港银行同业拆借市场  | Hibor美元   | 11月       |
| 香港银行同业拆借市场  | Hibor美元   | 1年        |
| 香港银行同业拆借市场  | Hibor人民币  | 隔夜        |
| 香港银行同业拆借市场  | Hibor人民币  | 1周        |
| 香港银行同业拆借市场  | Hibor人民币  | 2周        |
| 香港银行同业拆借市场  | Hibor人民币  | 1月        |
| 香港银行同业拆借市场  | Hibor人民币  | 2月        |
| 香港银行同业拆借市场  | Hibor人民币  | 3月        |
| 香港银行同业拆借市场  | Hibor人民币  | 6月        |
| 香港银行同业拆借市场  | Hibor人民币  | 1年        |
| 新加坡银行同业拆借市场 | Sibor星元   | 1月        |
| 新加坡银行同业拆借市场 | Sibor星元   | 2月        |
| 新加坡银行同业拆借市场 | Sibor星元   | 3月        |
| 新加坡银行同业拆借市场 | Sibor星元   | 6月        |
| 新加坡银行同业拆借市场 | Sibor星元   | 9月        |
| 新加坡银行同业拆借市场 | Sibor星元   | 1年        |
| 新加坡银行同业拆借市场 | Sibor美元   | 1月        |
| 新加坡银行同业拆借市场 | Sibor美元   | 2月        |
| 新加坡银行同业拆借市场 | Sibor美元   | 3月        |
| 新加坡银行同业拆借市场 | Sibor美元   | 6月        |
| 新加坡银行同业拆借市场 | Sibor美元   | 9月        |
| 新加坡银行同业拆借市场 | Sibor美元   | 1年        |

输出参数

| 名称  | 类型      | 描述       |
|-----|---------|----------|
| 日期  | object  | -        |
| 利率  | float64 | 注意单位: %  |
| 涨跌  | float64 | 注意单位: BP |

示例代码

```python
import akshare as ak

rate_interbank_df = ak.rate_interbank(market="上海银行同业拆借市场", symbol="Shibor人民币", indicator="3月")
print(rate_interbank_df)
```

数据示例

```
      报告日      利率    涨跌
0     2006-10-08  2.6110  0.00
1     2006-10-09  2.6248  1.38
2     2006-10-10  2.6325  0.77
3     2006-10-11  2.6338  0.13
4     2006-10-12  2.6380  0.42
...          ...     ...   ...
4391  2024-05-07  1.9960 -0.50
4392  2024-05-08  1.9900 -0.60
4393  2024-05-09  1.9910  0.10
4394  2024-05-10  1.9890 -0.20
4395  2024-05-11  1.9880 -0.10
[4396 rows x 3 columns]
```

### 回购定盘利率-历史

接口: repo_rate_hist

目标地址: https://www.chinamoney.com.cn/chinese/bkfrr/

描述: 回购定盘利率数据

限量: 单次返回指定日期间(一年)的所有历史数据

输入参数

| 名称         | 类型  | 描述                                     |
|------------|-----|----------------------------------------|
| start_date | str | start_date="********"; 开始时间与结束时间需要在一年内 |
| end_date   | str | end_date="********"; 开始时间与结束时间需要在一年内   |

输出参数

| 名称     | 类型      | 描述      |
|--------|---------|---------|
| date   | object  | -       |
| FR001  | float64 | 注意单位: % |
| FR007  | float64 | 注意单位: % |
| FR014  | float64 | 注意单位: % |
| FDR001 | float64 | 注意单位: % |
| FDR007 | float64 | 注意单位: % |
| FDR014 | float64 | 注意单位: % |

接口示例

```python
import akshare as ak

repo_rate_hist_df = ak.repo_rate_hist(start_date="20231001", end_date="20240101")
print(repo_rate_hist_df)
```

数据示例

```
          date   FR001   FR007  FR014  FDR001  FDR007  FDR014
0   2023-10-07  1.7000  1.8500   1.82  1.7000  1.8224    1.82
1   2023-10-08  1.5600  1.8000   1.78  1.5300  1.7500    1.75
2   2023-10-09  1.8100  2.0100   1.85  1.7400  1.8000    1.80
3   2023-10-10  1.9300  2.0500   2.22  1.8400  1.8600    1.90
4   2023-10-11  1.9600  2.1400   2.20  1.8700  1.9200    2.00
..         ...     ...     ...    ...     ...     ...     ...
57  2023-12-25  1.6210  1.8117   3.90  1.5900  1.8000    3.15
58  2023-12-26  1.5500  4.3500   3.80  1.4969  1.8305    3.10
59  2023-12-27  1.5100  4.0000   3.10  1.4400  1.8728    3.05
60  2023-12-28  1.5000  3.2000   3.00  1.3900  1.9572    2.95
61  2023-12-29  1.9149  2.4000   2.45  1.7400  2.3000    2.40
[62 rows x 7 columns]
```

### 回购定盘利率-近期

接口: repo_rate_query

目标地址: https://www.chinamoney.com.cn/chinese/bkfrr/

描述: 回购定盘利率数据

限量: 单次返回指定 symbol 的近期数据

输入参数

| 名称     | 类型  | 描述                                                 |
|--------|-----|----------------------------------------------------|
| symbol | str | symbol="回购定盘利率"; choice of {"回购定盘利率", "银银间回购定盘利率"} |

输出参数

| 名称     | 类型      | 描述      |
|--------|---------|---------|
| date   | object  | -       |
| FR001  | float64 | 注意单位: % |
| FR007  | float64 | 注意单位: % |
| FR014  | float64 | 注意单位: % |

接口示例

```python
import akshare as ak

repo_rate_query_df = ak.repo_rate_query(symbol="回购定盘利率")
print(repo_rate_query_df)
```

数据示例

```
           date   FR001  FR007  FR014
0    2021-01-19  2.3000   2.46   2.55
1    2021-01-20  2.5000   2.65   2.80
2    2021-01-21  2.6500   2.60   2.70
3    2021-01-22  2.5500   2.40   2.70
4    2021-01-25  2.5000   2.50   2.78
..          ...     ...    ...    ...
746  2024-01-15  1.8300   2.25   2.20
747  2024-01-16  1.9528   2.25   2.38
748  2024-01-17  2.0107   2.30   2.55
749  2024-01-18  1.8800   2.20   2.30
750  2024-01-19  1.8600   2.15   2.40
[751 rows x 4 columns]
```

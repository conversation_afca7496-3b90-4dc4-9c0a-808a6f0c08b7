# [AKShare](https://github.com/akfamily/akshare) 指标计算

## 已实现波动率指标

### YZ 已实现波动率

接口: volatility_yz_rv

目标地址: https://github.com/hugogobato/Yang-Zhang-s-Realized-Volatility-Automated-Estimation-in-Python

描述: 波动率-已实现波动率-Yang-Zhang已实现波动率

限量: 单次返回日频率的已实现波动率数据

输入参数

| 名称   | 类型               | 描述                                    |
|------|------------------|---------------------------------------|
| data | pandas.DataFrame | 包含日期和 OHLC(开高低收) 价格的 pandas.DataFrame |

输出参数

| 名称   | 类型      | 描述  |
|------|---------|-----|
| date | object  | -   |
| rv   | float64 | -   |

接口示例

```python
import akshare as ak

stock_df = ak.rv_from_stock_zh_a_hist_min_em(
    symbol="000001",
    start_date="2021-10-20 09:30:00",
    end_date="2024-11-01 15:00:00",
    period="5",
    adjust=""
)
volatility_yz_rv_df = ak.volatility_yz_rv(data=stock_df)
print(volatility_yz_rv_df)
```

数据示例

```
          date        rv
0   2024-09-10  0.001955
1   2024-09-11  0.002207
2   2024-09-12  0.002113
3   2024-09-13  0.002216
4   2024-09-18  0.002039
5   2024-09-19  0.002631
6   2024-09-20  0.002043
7   2024-09-23  0.002116
8   2024-09-24  0.002374
9   2024-09-25  0.003624
10  2024-09-26  0.003392
11  2024-09-27  0.005944
12  2024-09-30  0.008488
13  2024-10-08  0.011529
14  2024-10-09  0.008031
15  2024-10-10  0.006964
16  2024-10-11  0.004970
17  2024-10-14  0.004435
18  2024-10-15  0.003706
19  2024-10-16  0.004293
20  2024-10-17  0.003534
21  2024-10-18  0.004322
22  2024-10-21  0.004417
23  2024-10-22  0.002922
24  2024-10-23  0.002124
25  2024-10-24  0.001814
26  2024-10-25  0.001605
27  2024-10-28  0.002125
28  2024-10-29  0.002045
29  2024-10-30  0.002428
30  2024-10-31  0.002718
31  2024-11-01  0.002932
```

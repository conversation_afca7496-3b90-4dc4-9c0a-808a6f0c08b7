#!/usr/bin/env python
# -*- coding:utf-8 -*-
"""
精简版金融标的搜索工具
使用Tkinter和SQLite
"""

import tkinter as tk
from tkinter import ttk
import sqlite3
import os

# 数据库设置
DB_PATH = "financial_data.db"

def init_database():
    """初始化数据库"""
    # 确保数据库目录存在
    db_dir = os.path.dirname(DB_PATH)
    if db_dir and not os.path.exists(db_dir):
        os.makedirs(db_dir, exist_ok=True)
    conn = sqlite3.connect(DB_PATH)
    c = conn.cursor()
    
    # 创建金融标的表
    c.execute('''CREATE TABLE IF NOT EXISTS instruments (
                 id INTEGER PRIMARY KEY,
                 name TEXT NOT NULL,
                 symbol TEXT NOT NULL,
                 category TEXT NOT NULL)''')
    
    # 添加示例数据
    sample_data = [
        ('贵州茅台', '600519', '股票'),
        ('比特币', 'BTC', '加密货币'),
        ('天弘余额宝', '000198', '基金')
    ]
    
    for data in sample_data:
        c.execute("INSERT OR IGNORE INTO instruments (name, symbol, category) VALUES (?,?,?)", data)
    
    conn.commit()
    conn.close()

class SearchTool:
    def __init__(self, root):
        self.root = root
        self.root.title("金融标的搜索工具")
        self.root.geometry("600x400")
        
        # 初始化数据库
        init_database()
        
        # 创建界面
        self.create_widgets()
        
        # 显示初始数据
        self.show_data()

    def create_widgets(self):
        """创建界面组件"""
        # 搜索面板
        search_frame = ttk.Frame(self.root)
        search_frame.pack(fill=tk.X, padx=10, pady=10)
        
        # 搜索输入框
        self.search_var = tk.StringVar()
        ttk.Entry(search_frame, textvariable=self.search_var, width=30).pack(side=tk.LEFT, padx=5)
        self.search_var.trace("w", self.on_search_change)
        
        # 结果表格
        columns = ("id", "name", "symbol", "category")
        self.tree = ttk.Treeview(self.root, columns=columns, show="headings")
        
        # 设置列标题
        self.tree.heading("id", text="ID")
        self.tree.heading("name", text="名称")
        self.tree.heading("symbol", text="代码")
        self.tree.heading("category", text="类别")
        
        # 设置列宽
        self.tree.column("id", width=50)
        self.tree.column("name", width=200)
        self.tree.column("symbol", width=100)
        self.tree.column("category", width=100)
        
        # 添加滚动条
        scrollbar = ttk.Scrollbar(self.root, orient=tk.VERTICAL, command=self.tree.yview)
        self.tree.configure(yscroll=scrollbar.set)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.tree.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)

    def show_data(self, query=None):
        """显示数据"""
        try:
            self.tree.delete(*self.tree.get_children())
            conn = sqlite3.connect(DB_PATH)
            c = conn.cursor()
            
            if query:
                c.execute("SELECT id, name, symbol, category FROM instruments WHERE name LIKE ? OR symbol LIKE ?", 
                         (f"%{query}%", f"%{query}%"))
            else:
                c.execute("SELECT id, name, symbol, category FROM instruments")
            
            for row in c.fetchall():
                self.tree.insert("", tk.END, values=row)
            
            conn.close()
        except Exception as e:
            print(f"数据库错误: {str(e)}")

    def on_search_change(self, *args):
        """搜索框内容变化时触发"""
        query = self.search_var.get().strip()
        self.show_data(query)

if __name__ == "__main__":
    root = tk.Tk()
    app = SearchTool(root)
    root.mainloop()

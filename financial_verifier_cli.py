#!/usr/bin/env python
# -*- coding:utf-8 -*-
"""
金融标的命令行验证工具
测试能否获取行情数据并记录结果
"""

import sqlite3
import os
import time
import argparse
import akshare as ak
import ccxt

# 数据库设置
DB_PATH = "financial_data.db"

def init_database():
    """初始化数据库"""
    conn = sqlite3.connect(DB_PATH)
    c = conn.cursor()
    
    # 创建金融标的表
    c.execute('''CREATE TABLE IF NOT EXISTS instruments (
                 id INTEGER PRIMARY KEY,
                 name TEXT NOT NULL,
                 symbol TEXT NOT NULL,
                 category TEXT NOT NULL,
                 test_status TEXT DEFAULT '未测试',
                 test_time TEXT)''')
    
    # 添加示例数据
    sample_data = [
        ('贵州茅台', '600519', '股票'),
        ('比特币', 'BTC', '加密货币'),
        ('天弘余额宝', '000198', '基金'),
        ('腾讯控股', '00700', '股票'),
        ('以太坊', 'ETH', '加密货币')
    ]
    
    for data in sample_data:
        c.execute("INSERT OR IGNORE INTO instruments (name, symbol, category) VALUES (?,?,?)", data)
    
    conn.commit()
    conn.close()
    print(f"数据库已初始化: {DB_PATH}")

def test_instrument(symbol, category):
    """测试能否获取行情数据"""
    try:
        start_time = time.time()
        status = "❌未知错误"
        
        if category == "股票":
            # 获取A股实时行情数据
            df = ak.stock_zh_a_spot_em()
            if symbol in df["代码"].values:
                status = "✅成功"
            else:
                status = "❌代码不存在"
                
        elif category == "基金":
            # 获取基金实时净值
            df = ak.fund_em_open_fund_daily()
            if symbol in df["基金代码"].values:
                status = "✅成功"
            else:
                status = "❌代码不存在"
                
        elif category == "加密货币":
            # 使用CCXT获取加密货币行情
            exchange = ccxt.binance()
            try:
                ticker = exchange.fetch_ticker(f"{symbol}/USDT")
                if ticker["last"]:
                    status = "✅成功"
                else:
                    status = "❌无行情数据"
            except ccxt.BadSymbol:
                status = "❌交易对不存在"
            except Exception:
                status = "❌API错误"
                
        else:
            status = "❌不支持类型"
            
        elapsed = time.time() - start_time
        test_time = f"{time.strftime('%Y-%m-%d %H:%M:%S')} ({elapsed:.2f}s)"
        return status, test_time
        
    except Exception as e:
        return f"❌错误: {str(e)}", time.strftime("%Y-%m-%d %H:%M:%S")

def test_all_instruments():
    """测试所有金融标的"""
    conn = sqlite3.connect(DB_PATH)
    c = conn.cursor()
    
    # 获取所有标的
    c.execute("SELECT id, name, symbol, category FROM instruments")
    instruments = c.fetchall()
    
    total = len(instruments)
    print(f"开始测试 {total} 个金融标的...")
    
    for i, (id, name, symbol, category) in enumerate(instruments, 1):
        print(f"测试中 ({i}/{total}): {name} ({symbol}) [{category}]", end="", flush=True)
        
        # 执行测试
        status, test_time = test_instrument(symbol, category)
        
        # 更新数据库
        c.execute("UPDATE instruments SET test_status=?, test_time=? WHERE id=?", 
                 (status, test_time, id))
        conn.commit()
        
        print(f" - 结果: {status}")
    
    conn.close()
    print("所有标的测试完成！")

def show_results():
    """显示测试结果"""
    conn = sqlite3.connect(DB_PATH)
    c = conn.cursor()
    
    c.execute("SELECT name, symbol, category, test_status, test_time FROM instruments")
    results = c.fetchall()
    
    if not results:
        print("没有找到任何金融标的")
        return
    
    print("\n金融标的结果:")
    print("-" * 80)
    print(f"{'名称':<15}{'代码':<10}{'类别':<10}{'状态':<15}{'测试时间':<30}")
    print("-" * 80)
    
    for name, symbol, category, status, test_time in results:
        print(f"{name:<15}{symbol:<10}{category:<10}{status:<15}{test_time or '未测试':<30}")
    
    conn.close()

def add_instrument(name, symbol, category):
    """添加新的金融标的"""
    conn = sqlite3.connect(DB_PATH)
    c = conn.cursor()
    
    c.execute("INSERT INTO instruments (name, symbol, category) VALUES (?,?,?)", 
             (name, symbol, category))
    conn.commit()
    conn.close()
    print(f"已添加: {name} ({symbol}) [{category}]")

if __name__ == "__main__":
    # 确保数据库存在
    if not os.path.exists(DB_PATH):
        init_database()
    
    parser = argparse.ArgumentParser(description="金融标的命令行验证工具")
    subparsers = parser.add_subparsers(dest="command")
    
    # test-all 命令
    test_parser = subparsers.add_parser("test-all", help="测试所有金融标的")
    
    # add 命令
    add_parser = subparsers.add_parser("add", help="添加新的金融标的")
    add_parser.add_argument("name", help="标的名称")
    add_parser.add_argument("symbol", help="标的代码")
    add_parser.add_argument("category", help="标的类别", choices=["股票", "基金", "加密货币"])
    
    # show 命令
    show_parser = subparsers.add_parser("show", help="显示测试结果")
    
    args = parser.parse_args()
    
    if args.command == "test-all":
        test_all_instruments()
    elif args.command == "add":
        add_instrument(args.name, args.symbol, args.category)
    elif args.command == "show":
        show_results()
    else:
        parser.print_help()

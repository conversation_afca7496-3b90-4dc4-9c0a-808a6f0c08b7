#!/usr/bin/env python
# -*- coding:utf-8 -*-
"""
合并新旧金融标的数据
确保不丢失任何记录
"""

import pandas as pd
import os
from pathlib import Path

# 文件路径
DATA_DIR = Path("financial_data")
OLD_CSV_PATH = DATA_DIR / "financial_instruments.csv"
NEW_CSV_PATH = DATA_DIR / "financial_instruments_merged.csv"
DB_PATH = DATA_DIR / "financial_database.db"

def merge_data():
    """合并新旧数据文件"""
    print("开始合并金融标的数据...")
    
    # 读取数据库中的最新数据
    print(f"从数据库读取最新数据: {DB_PATH}")
    from sqlite3 import connect
    conn = connect(DB_PATH)
    new_df = pd.read_sql_query("SELECT * FROM instruments", conn)
    conn.close()
    print(f"数据库中读取到 {len(new_df)} 条记录")
    
    # 读取旧CSV数据
    if os.path.exists(OLD_CSV_PATH):
        print(f"读取原有CSV文件: {OLD_CSV_PATH}")
        old_df = pd.read_csv(OLD_CSV_PATH)
        print(f"原有CSV中读取到 {len(old_df)} 条记录")
        
        # 检查列名匹配
        if set(new_df.columns) != set(old_df.columns):
            print(f"警告: 新旧数据列名不完全匹配!")
            print(f"新数据列名: {list(new_df.columns)}")
            print(f"旧数据列名: {list(old_df.columns)}")
            
            # 确保列名匹配
            common_columns = list(set(new_df.columns) & set(old_df.columns))
            print(f"使用共同列: {common_columns}")
            
            # 选择共同列
            new_df = new_df[common_columns]
            old_df = old_df[common_columns]
        
        # 合并数据，去除重复项
        print("合并数据并去除重复项...")
        # 使用symbol和category作为判断重复的依据
        merged_df = pd.concat([new_df, old_df])
        merged_df = merged_df.drop_duplicates(subset=['symbol', 'category'], keep='first')
        
        print(f"合并后共 {len(merged_df)} 条记录")
        
        # 按id重排序
        if 'id' in merged_df.columns:
            merged_df = merged_df.sort_values(by='id').reset_index(drop=True)
            # 修复ID列，确保连续
            merged_df['id'] = range(1, len(merged_df) + 1)
        
        # 保存合并后的文件
        merged_df.to_csv(NEW_CSV_PATH, index=False)
        print(f"合并结果已保存至: {NEW_CSV_PATH}")
        
        # 备份原文件
        backup_path = OLD_CSV_PATH.with_suffix('.csv.bak')
        if os.path.exists(OLD_CSV_PATH):
            import shutil
            shutil.copy2(OLD_CSV_PATH, backup_path)
            print(f"原CSV文件已备份至: {backup_path}")
            
        # 将合并后的数据保存到原文件位置
        merged_df.to_csv(OLD_CSV_PATH, index=False)
        print(f"合并结果已更新至原CSV文件: {OLD_CSV_PATH}")
        
        # 将合并后的数据更新到数据库
        conn = connect(DB_PATH)
        cursor = conn.cursor()
        # 清空现有表
        cursor.execute("DELETE FROM instruments")
        conn.commit()
        
        # 准备批量插入的数据
        records = []
        skipped = 0
        for _, row in merged_df.iterrows():
            # 确保必需字段不为空
            name = row.get('name')
            symbol = row.get('symbol')
            category = row.get('category')
            
            # 检查必要字段
            if not symbol or symbol == '' or pd.isna(symbol):
                skipped += 1
                continue
                
            if not name or name == '' or pd.isna(name):
                name = f'未命名标的-{symbol}'
                
            if not category or category == '' or pd.isna(category):
                category = '其他'
            
            record = (
                name, 
                symbol,
                category,
                row.get('exchange', ''),
                row.get('source', 'merged'),
                row.get('timestamp', '')
            )
            records.append(record)
        
        print(f"共跳过 {skipped} 条无效记录（缺失symbol）")
        
        # 批量插入数据
        if records:
            cursor.executemany(
                "INSERT INTO instruments (name, symbol, category, exchange, source, timestamp) VALUES (?,?,?,?,?,?)",
                records
            )
            print(f"已成功插入 {len(records)} 条记录")
        else:
            print("没有有效记录可插入")
        conn.commit()
        conn.close()
        print(f"数据库已更新，共插入 {len(records)} 条记录")
        
    else:
        print(f"未找到原CSV文件: {OLD_CSV_PATH}，跳过合并")
        # 直接导出当前数据库数据到CSV
        new_df.to_csv(OLD_CSV_PATH, index=False)
        print(f"当前数据库数据已导出至: {OLD_CSV_PATH}")
        new_df.to_csv(NEW_CSV_PATH, index=False)
        print(f"当前数据库数据已导出至: {NEW_CSV_PATH}")
    
    print("数据合并完成!")
    
    return True

if __name__ == "__main__":
    merge_data()

#!/usr/bin/env python
# -*- coding:utf-8 -*-
"""
最小化金融标的验证工具
专注于核心验证功能
"""

import sqlite3
import os
import time
import akshare as ak
import ccxt

# 数据库设置
DB_PATH = "financial_data.db"

def init_database():
    """初始化数据库"""
    conn = sqlite3.connect(DB_PATH)
    c = conn.cursor()
    c.execute('''CREATE TABLE IF NOT EXISTS instruments (
                 id INTEGER PRIMARY KEY,
                 name TEXT,
                 symbol TEXT,
                 category TEXT,
                 test_status TEXT DEFAULT '未测试',
                 test_time TEXT)''')
    
    # 添加示例数据
    sample_data = [
        ('贵州茅台', '600519', '股票'),
        ('比特币', 'BTC', '加密货币'),
        ('天弘余额宝', '000198', '基金'),
        ('腾讯控股', '00700', '股票'),
        ('以太坊', 'ETH', '加密货币')
    ]
    
    for data in sample_data:
        c.execute("INSERT OR IGNORE INTO instruments (name, symbol, category) VALUES (?,?,?)", data)
    
    conn.commit()
    conn.close()

def test_instrument(symbol, category):
    """测试能否获取行情数据"""
    try:
        start_time = time.time()
        
        if category == "股票":
            df = ak.stock_zh_a_spot_em()
            if symbol in df["代码"].values: return "✅成功", time.strftime("%Y-%m-%d %H:%M:%S")
            return "❌代码不存在", time.strftime("%Y-%m-%d %H:%M:%S")
            
        elif category == "基金":
            df = ak.fund_em_open_fund_daily()
            if symbol in df["基金代码"].values: return "✅成功", time.strftime("%Y-%m-%d %H:%M:%S")
            return "❌代码不存在", time.strftime("%Y-%m-%d %H:%M:%S")
            
        elif category == "加密货币":
            exchange = ccxt.binance()
            try:
                ticker = exchange.fetch_ticker(f"{symbol}/USDT")
                if ticker["last"]: return "✅成功", time.strftime("%Y-%m-%d %H:%M:%S")
                return "❌无行情数据", time.strftime("%Y-%m-%d %H:%极
[响应被工具使用结果中断。一次只能使用一个工具，并且应将其放在消息的末尾。]

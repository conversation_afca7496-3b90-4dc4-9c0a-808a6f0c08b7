#!/usr/bin/env python
# -*- coding:utf-8 -*-
"""
金融标的搜索与管理工具 - 修复版
使用Tkinter内置GUI库，无需额外安装
"""

import sys
import sqlite3
import tkinter as tk
from tkinter import ttk

# 数据库设置
DB_PATH = "financial_data/financial_instruments.db"

def init_database():
    """初始化数据库"""
    import os
    os.makedirs(os.path.dirname(DB_PATH), exist_ok=True)
    conn = sqlite3.connect(DB_PATH)
    c = conn.cursor()
    
    # 创建金融标的表
    c.execute('''CREATE TABLE IF NOT EXISTS instruments (
                 id INTEGER PRIMARY KEY,
                 category TEXT NOT NULL,
                 name TEXT NOT NULL,
                 symbol TEXT NOT NULL,
                 source TEXT NOT NULL,
                 verified INTEGER DEFAULT 0)''')  # 0=未验证, 1=已验证
    
    # 创建索引
    c.execute('''CREATE INDEX IF NOT EXISTS idx_name ON instruments (name)''')
    c.execute('''CREATE INDEX IF NOT EXISTS idx_symbol ON instruments (symbol)''')
    c.execute('''CREATE INDEX IF NOT EXISTS idx_category ON instruments (category)''')
    
    # 添加示例数据
    try:
        c.execute("INSERT OR IGNORE INTO instruments (category, name, symbol, source, verified) VALUES (?,?,?,?,?)",
                 ('股票', '贵州茅台', '600519', 'akshare', 1))
        c.execute("INSERT OR IGNORE INTO instruments (category, name, symbol, source, verified) VALUES (?,?,?,?,?)",
                 ('加密货币', '比特币', 'BTC/USDT', 'ccxt', 1))
        c.execute("INSERT OR IGNORE INTO instruments (category, name, symbol, source, verified) VALUES (?,?,?,?,?)",
                 ('基金', '天弘余额宝', '000198', 'akshare', 1))
    except:
        pass
    
    conn.commit()
    conn.close()

class FinancialSearchApp:
    def __init__(self, root):
        self.root = root
        self.root.title("金融标的搜索与管理工具")
        self.root.geometry("1000x600")
        
        # 初始化数据库
        init_database()
        
        # 创建主框架
        main_frame = ttk.Frame(root, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 控制面板
        control_frame = ttk.Frame(main_frame)
        control_frame.pack(fill=tk.X, pady=5)
        
        # 获取数据按钮
        self.fetch_btn = ttk.Button(control_frame, text="获取数据", command=self.fetch_data)
        self.fetch_btn.pack(side=tk.LEFT, padx=5)
        
        # 全部显示按钮
        self.show_all_btn = ttk.Button(control_frame, text="显示全部", command=self.show_all_data)
        self.show_all_btn.pack(side=tk.LEFT, padx=5)
        
        # 搜索面板
        search_frame = ttk.Frame(main_frame)
        search_frame.pack(fill=tk.X, pady=5)
        
        # 搜索输入框
        self.search_var = tk.StringVar()
        self.search_entry = ttk.Entry(search_frame, textvariable=self.search_var, width=40)
        self.search_entry.pack(side=tk.LEFT, padx=5)
        self.search_entry.bind("<Return>", lambda event: self.search_data())
        
        # 类别选择
        self.category_var = tk.StringVar(value="所有类别")
        self.category_combo = ttk.Combobox(search_frame, textvariable=self.category_var, 
                                          values=["所有类别", "股票", "基金", "加密货币", "债券"],
                                          state="readonly", width=15)
        self.category_combo.pack(side=tk.LEFT, padx=5)
        
        # 搜索按钮
        self.search_btn = ttk.Button(search_frame, text="搜索", command=self.search_data)
        self.search_btn.pack(side=tk.LEFT, padx=5)
        
        # 结果表格
        tree_frame = ttk.Frame(main_frame)
        tree_frame.pack(fill=tk.BOTH, expand=True)
        
        # 创建树形视图
        columns = ("id", "category", "name", "symbol", "status")
        self.tree = ttk.Treeview(tree_frame, columns=columns, show="headings")
        
        # 设置列标题
        self.tree.heading("id", text="ID")
        self.ttree.heading("category", text="类别")
        self.tree.heading("name", text="名称")
        self.tree.heading("symbol", text="代码")
        self.tree.heading("status", text="状态")
        
        # 设置列宽
        self.tree.column("id", width=50)
        self.tree.column("category", width=100)
        self.tree.column("name", width=300)
        self.tree.column("symbol", width=150)
        self.tree.column("status", width=100)
        
        # 添加滚动条
        scrollbar = ttk.Scrollbar(tree_frame, orient=tk.VERTICAL, command=self.tree.yview)
        self.tree.configure(yscroll=scrollbar.set)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        
        # 状态栏
        self.status_var = tk.StringVar(value="就绪 | 数据库已初始化")
        status_bar = ttk.Label(root, textvariable=self.status_var, relief=tk.SUNKEN, anchor=tk.W)
        status_bar.pack(side=tk.BOTTOM, fill=tk.X)
        
        # 显示初始数据
        self.show_all_data()

    def show_all_data(self):
        """显示所有数据"""
        try:
            conn = sqlite3.connect(DB_PATH)
            c = conn.cursor()
            c.execute("SELECT id, category, name, symbol, verified FROM instruments")
            results = c.fetchall()
            conn.close()
            
            self.display_results(results)
            self.status_var.set(f"显示全部数据: {len(results)} 条记录")
        except Exception as e:
            self.status_var.set(f"获取数据出错: {str(e)}")

    def search_data(self):
        """搜索数据"""
        query = self.search_var.get().strip()
        if not query:
            self.status_var.set("请输入搜索关键词")
            return
            
        category = self.category_var.get()
        if category == "所有类别":
            category = None
            
        try:
            conn = sqlite3.connect(DB_PATH)
            c = conn.cursor()
            
            if category:
                c.execute("SELECT id, category, name, symbol, verified FROM instruments WHERE (name LIKE ? OR symbol LIKE ?) AND category = ?", 
                         (f"%{query}%", f"%{query}%", category))
            else:
                c.execute("SELECT id, category, name, symbol, verified FROM instruments WHERE name LIKE ? OR symbol LIKE ?", 
                         (f"%{query}%", f"%{query}%"))
            
            results = c.fetchall()
            conn.close()
            
            self.display_results(results)
            self.status_var.set(f"找到 {len(results)} 条记录")
        except Exception as e:
            self.status_var.set(f"搜索出错: {str(e)}")

    def fetch_data(self):
        """获取数据（模拟）"""
        try:
            # 模拟获取数据
            conn = sqlite3.connect(DB_PATH)
            c = conn.cursor()
            
            # 添加新数据
            c.execute("INSERT OR IGNORE INTO instruments (category, name, symbol, source, verified) VALUES (?,?,?,?,?)",
                     ('股票', '腾讯控股', '00700', 'akshare', 1))
            c.execute("INSERT OR IGNORE INTO instruments (category, name, symbol, source, verified) VALUES (?,?,?,?,?)",
                     ('加密货币', '以太坊', 'ETH/USDT', 'ccxt', 1))
            
            conn.commit()
            conn.close()
            
            # 刷新显示
            self.show_all_data()
            self.status_var.set("已添加新数据: 腾讯控股(00700), 以太坊(ETH/USDT)")
            
        except Exception as e:
            self.status_var.set(f"获取数据出错: {str(e)}")

    def display_results(self, results):
        """在表格中显示结果"""
        # 清空现有数据
        for item in self.tree.get_children():
            self.tree.delete(item)
            
        # 添加新数据
        for row in results:
            status = "已验证" if row[4] == 1 else "未验证"
            self.tree.insert("", tk.END, values=(row[0], row[1], row[2], row[3], status))

if __name__ == "__main__":
    root = tk.Tk()
    app = FinancialSearchApp(root)
    root.mainloop()

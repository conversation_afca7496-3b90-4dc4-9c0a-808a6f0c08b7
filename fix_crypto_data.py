#!/usr/bin/env python
# -*- coding:utf-8 -*-
"""
修复加密货币数据问题：
专门获取更多USDT交易对的加密货币数据
"""

import sqlite3
import pandas as pd
import os
import time
import logging
from pathlib import Path
import ccxt

# 配置
DATA_DIR = Path("financial_data")
DB_PATH = DATA_DIR / "financial_database.db"
CSV_PATH = DATA_DIR / "financial_instruments.csv"
BACKUP_CSV = DATA_DIR / "financial_instruments.csv.bak" 

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

def get_db_data():
    """从数据库获取数据"""
    try:
        conn = sqlite3.connect(DB_PATH)
        df = pd.read_sql_query("SELECT * FROM instruments", conn)
        conn.close()
        logging.info(f"从数据库读取了 {len(df)} 条记录")
        return df
    except Exception as e:
        logging.error(f"从数据库读取数据失败: {e}")
        return pd.DataFrame()

def collect_usdt_crypto_pairs():
    """专门收集USDT交易对数据"""
    logging.info("开始收集USDT交易对数据...")
    
    usdt_pairs = []
    
    try:
        # 使用CCXT库连接交易所API
        exchanges = ['binance', 'huobi', 'okx', 'kucoin']
        
        for exchange_id in exchanges:
            try:
                logging.info(f"尝试从 {exchange_id} 获取USDT交易对...")
                exchange_class = getattr(ccxt, exchange_id)
                exchange = exchange_class()
                markets = exchange.load_markets()
                
                # 筛选USDT交易对
                for symbol, market in markets.items():
                    if '/USDT' in symbol:
                        # 提取基础货币符号（例如从BTC/USDT提取BTC）
                        base_currency = symbol.split('/')[0]
                        
                        usdt_pairs.append({
                            'symbol': symbol,
                            'name': f"{base_currency}/USDT",
                            'category': '加密货币',
                            'exchange': exchange_id,
                            'source': 'ccxt',
                            'timestamp': time.strftime("%Y-%m-%d %H:%M:%S")
                        })
                
                logging.info(f"从 {exchange_id} 获取到 {len([p for p in usdt_pairs if p['exchange'] == exchange_id])} 个USDT交易对")
                
            except Exception as e:
                logging.warning(f"从 {exchange_id} 获取数据失败: {e}")
                
        # 如果无法获取数据，添加一些常见的USDT交易对作为示例
        if not usdt_pairs:
            logging.warning("无法从交易所获取数据，使用常见USDT交易对作为示例")
            common_pairs = [
                {'symbol': 'BTC/USDT', 'name': 'Bitcoin/USDT', 'exchange': 'binance'},
                {'symbol': 'ETH/USDT', 'name': 'Ethereum/USDT', 'exchange': 'binance'},
                {'symbol': 'BNB/USDT', 'name': 'Binance Coin/USDT', 'exchange': 'binance'},
                {'symbol': 'ADA/USDT', 'name': 'Cardano/USDT', 'exchange': 'binance'},
                {'symbol': 'XRP/USDT', 'name': 'Ripple/USDT', 'exchange': 'binance'},
                {'symbol': 'SOL/USDT', 'name': 'Solana/USDT', 'exchange': 'binance'},
                {'symbol': 'DOT/USDT', 'name': 'Polkadot/USDT', 'exchange': 'binance'},
                {'symbol': 'DOGE/USDT', 'name': 'Dogecoin/USDT', 'exchange': 'binance'},
                {'symbol': 'AVAX/USDT', 'name': 'Avalanche/USDT', 'exchange': 'binance'},
                {'symbol': 'MATIC/USDT', 'name': 'Polygon/USDT', 'exchange': 'binance'},
                {'symbol': 'LTC/USDT', 'name': 'Litecoin/USDT', 'exchange': 'binance'},
                {'symbol': 'LINK/USDT', 'name': 'Chainlink/USDT', 'exchange': 'binance'},
                {'symbol': 'UNI/USDT', 'name': 'Uniswap/USDT', 'exchange': 'binance'},
                {'symbol': 'ATOM/USDT', 'name': 'Cosmos/USDT', 'exchange': 'binance'},
                {'symbol': 'XLM/USDT', 'name': 'Stellar/USDT', 'exchange': 'binance'},
                {'symbol': 'VET/USDT', 'name': 'VeChain/USDT', 'exchange': 'binance'},
                {'symbol': 'ALGO/USDT', 'name': 'Algorand/USDT', 'exchange': 'binance'},
                {'symbol': 'FIL/USDT', 'name': 'Filecoin/USDT', 'exchange': 'binance'},
                {'symbol': 'THETA/USDT', 'name': 'Theta Network/USDT', 'exchange': 'binance'},
                {'symbol': 'TRX/USDT', 'name': 'TRON/USDT', 'exchange': 'binance'},
            ]
            
            for pair in common_pairs:
                pair['category'] = '加密货币'
                pair['source'] = 'manual'
                pair['timestamp'] = time.strftime("%Y-%m-%d %H:%M:%S")
                usdt_pairs.append(pair)
                
            logging.info(f"添加了 {len(common_pairs)} 个常见USDT交易对作为示例")
        
        # 去重
        unique_symbols = set()
        unique_pairs = []
        
        for pair in usdt_pairs:
            if pair['symbol'] not in unique_symbols:
                unique_symbols.add(pair['symbol'])
                unique_pairs.append(pair)
        
        logging.info(f"去重后共有 {len(unique_pairs)} 个USDT交易对")
        return pd.DataFrame(unique_pairs)
        
    except Exception as e:
        logging.error(f"收集USDT交易对数据失败: {e}")
        return pd.DataFrame()

def update_crypto_data(df, crypto_df):
    """更新加密货币数据"""
    if crypto_df.empty:
        logging.warning("没有新的加密货币数据")
        return df
    
    # 删除原有的加密货币数据
    df = df[df['category'] != '加密货币']
    logging.info(f"已删除原有的加密货币数据")
    
    # 获取最大ID
    max_id = df['id'].max() if 'id' in df.columns and not df.empty else 0
    
    # 添加ID列
    crypto_df['id'] = range(max_id + 1, max_id + 1 + len(crypto_df))
    
    # 合并数据
    result_df = pd.concat([df, crypto_df], ignore_index=True)
    logging.info(f"已添加 {len(crypto_df)} 条USDT加密货币交易对数据")
    
    return result_df

def save_to_database(df):
    """保存数据到数据库"""
    conn = sqlite3.connect(DB_PATH)
    
    # 删除现有数据
    conn.execute("DELETE FROM instruments")
    conn.commit()
    
    # 导入数据
    df.to_sql('instruments', conn, if_exists='replace', index=False)
    conn.commit()
    conn.close()
    logging.info(f"已将 {len(df)} 条记录保存到数据库")

def save_to_csv(df):
    """保存数据到CSV文件"""
    # 备份现有文件
    if os.path.exists(CSV_PATH):
        import shutil
        shutil.copy2(CSV_PATH, BACKUP_CSV)
        logging.info(f"已备份原CSV文件: {BACKUP_CSV}")
    
    # 保存新数据
    df.to_csv(CSV_PATH, index=False, encoding="utf-8-sig")
    logging.info(f"已将 {len(df)} 条记录保存到CSV文件: {CSV_PATH}")

def main():
    """主函数"""
    logging.info("开始修复加密货币数据问题...")
    
    # 1. 获取现有数据
    df = get_db_data()
    if df.empty:
        logging.error("无法获取数据库数据，退出")
        return
    
    # 2. 收集USDT加密货币交易对
    crypto_df = collect_usdt_crypto_pairs()
    
    # 3. 更新加密货币数据
    df = update_crypto_data(df, crypto_df)
    
    # 4. 保存结果
    save_to_database(df)
    save_to_csv(df)
    
    # 5. 显示数据统计
    category_stats = df['category'].value_counts()
    logging.info("\n数据统计:")
    for category, count in category_stats.items():
        logging.info(f"  {category}: {count}条")
    
    logging.info("加密货币数据修复完成!")

if __name__ == "__main__":
    main()
